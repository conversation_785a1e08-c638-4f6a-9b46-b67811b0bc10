"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/designs/page",{

/***/ "(app-pages-browser)/./src/app/designs/page.tsx":
/*!**********************************!*\
  !*** ./src/app/designs/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DesignsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-2x2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _data_designs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/designs */ \"(app-pages-browser)/./src/data/designs.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction DesignsPage() {\n    _s();\n    const [selectedImageIndex, setSelectedImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGalleryOpen, setIsGalleryOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentImageIndexes, setCurrentImageIndexes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        1: 0,\n        2: 0,\n        3: 0,\n        4: 0,\n        5: 0,\n        6: 0,\n        7: 0,\n        8: 0,\n        9: 0,\n        10: 0,\n        11: 0,\n        12: 0\n    });\n    // حالة عرض البطاقات للهواتف المحمولة\n    const [isSingleColumn, setIsSingleColumn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تحميل حالة العرض من localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DesignsPage.useEffect\": ()=>{\n            const savedViewMode = localStorage.getItem('yasmin-designs-view-mode');\n            if (savedViewMode === 'single') {\n                setIsSingleColumn(true);\n            }\n        }\n    }[\"DesignsPage.useEffect\"], []);\n    // حفظ حالة العرض في localStorage\n    const toggleViewMode = ()=>{\n        const newMode = !isSingleColumn;\n        setIsSingleColumn(newMode);\n        localStorage.setItem('yasmin-designs-view-mode', newMode ? 'single' : 'double');\n    };\n    // دوال التنقل بين صور البطاقة\n    const nextCardImage = (designId, e)=>{\n        e.stopPropagation();\n        setCurrentImageIndexes((prev)=>({\n                ...prev,\n                [designId]: (prev[designId] + 1) % 3\n            }));\n    };\n    const prevCardImage = (designId, e)=>{\n        e.stopPropagation();\n        setCurrentImageIndexes((prev)=>({\n                ...prev,\n                [designId]: prev[designId] === 0 ? 2 : prev[designId] - 1\n            }));\n    };\n    const setCardImage = (designId, imageIndex, e)=>{\n        e.stopPropagation();\n        setCurrentImageIndexes((prev)=>({\n                ...prev,\n                [designId]: imageIndex\n            }));\n    };\n    // دوال إدارة المعرض\n    const openGallery = (index)=>{\n        setSelectedImageIndex(index);\n        setIsGalleryOpen(true);\n        document.body.style.overflow = 'hidden';\n    };\n    const closeGallery = ()=>{\n        setIsGalleryOpen(false);\n        setSelectedImageIndex(null);\n        document.body.style.overflow = 'unset';\n    };\n    const nextImage = ()=>{\n        if (selectedImageIndex !== null) {\n            const designId = _data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].id;\n            setCurrentImageIndexes((prev)=>({\n                    ...prev,\n                    [designId]: (prev[designId] + 1) % 3\n                }));\n        }\n    };\n    const prevImage = ()=>{\n        if (selectedImageIndex !== null) {\n            const designId = _data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].id;\n            setCurrentImageIndexes((prev)=>({\n                    ...prev,\n                    [designId]: prev[designId] === 0 ? 2 : prev[designId] - 1\n                }));\n        }\n    };\n    // إدارة مفتاح Escape\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DesignsPage.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"DesignsPage.useEffect.handleKeyDown\": (event)=>{\n                    if (event.key === 'Escape' && isGalleryOpen) {\n                        closeGallery();\n                    }\n                    if (event.key === 'ArrowRight' && isGalleryOpen) {\n                        nextImage();\n                    }\n                    if (event.key === 'ArrowLeft' && isGalleryOpen) {\n                        prevImage();\n                    }\n                }\n            }[\"DesignsPage.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"DesignsPage.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleKeyDown);\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"DesignsPage.useEffect\"];\n        }\n    }[\"DesignsPage.useEffect\"], [\n        isGalleryOpen,\n        selectedImageIndex\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"العودة إلى الرئيسية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl sm:text-5xl lg:text-6xl font-bold mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                                children: \"تصاميمنا الجاهزة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed mb-6\",\n                            children: \"استكشفي مجموعتنا الكاملة من التصاميم الجاهزة واختاري ما يناسب ذوقك ومناسبتك\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 max-w-2xl mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-800 font-medium text-center\",\n                                children: \"✨ الفساتين الجاهزة متوفرة للشراء المباشر - لا يتطلب حجز موعد\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.2\n                    },\n                    className: \"sm:hidden mb-6 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleViewMode,\n                        className: \"bg-white/80 backdrop-blur-sm border border-pink-200 rounded-xl p-3 flex items-center space-x-2 space-x-reverse hover:bg-white hover:shadow-lg transition-all duration-300\",\n                        \"aria-label\": isSingleColumn ? 'تبديل إلى العرض الثنائي' : 'تبديل إلى العرض الفردي',\n                        children: isSingleColumn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5 text-pink-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-700\",\n                                    children: \"عرض ثنائي\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5 text-pink-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-700\",\n                                    children: \"عرض فردي\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-8 mb-12 \".concat(isSingleColumn ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'),\n                    children: _data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns.map((design, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            className: \"group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-[4/5] bg-gradient-to-br from-pink-100 via-rose-100 to-purple-100 relative overflow-hidden cursor-pointer\",\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            openGallery(index);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: design.images[currentImageIndexes[design.id]],\n                                                alt: \"\".concat(design.title, \" - صورة \").concat(currentImageIndexes[design.id] + 1),\n                                                className: \"w-full h-full object-cover transition-opacity duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>prevCardImage(design.id, e),\n                                                className: \"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-all duration-300 z-10\",\n                                                \"aria-label\": \"الصورة السابقة\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>nextCardImage(design.id, e),\n                                                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-all duration-300 z-10\",\n                                                \"aria-label\": \"الصورة التالية\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1 space-x-reverse opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                children: design.images.map((_, imgIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>setCardImage(design.id, imgIndex, e),\n                                                        className: \"w-2 h-2 rounded-full transition-colors duration-300 \".concat(currentImageIndexes[design.id] === imgIndex ? 'bg-white' : 'bg-white/50'),\n                                                        \"aria-label\": \"عرض الصورة \".concat(imgIndex + 1)\n                                                    }, imgIndex, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white/90 rounded-full p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-6 h-6 text-pink-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"absolute top-3 left-3 bg-white/80 hover:bg-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-all duration-300 hover:scale-110\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4 text-pink-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/designs/\".concat(design.id),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"cursor-pointer hover:bg-gray-50 transition-colors duration-300 p-2 -m-2 rounded-lg mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-gradient-to-r from-pink-100 to-rose-100 text-pink-700 px-2 py-1 rounded-full text-xs font-medium\",\n                                                                children: design.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-bold text-gray-800 mb-2 group-hover:text-pink-600 transition-colors duration-300\",\n                                                            children: design.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                            children: design.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"https://wa.me/+966598862609?text=أريد استفسار عن \".concat(design.title),\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"w-full bg-gradient-to-r from-pink-500 to-purple-500 text-white py-2 px-3 rounded-lg text-sm font-medium hover:shadow-lg transition-all duration-300 text-center\",\n                                                    children: \"استفسار واتساب\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, this)\n                        }, design.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this),\n                isGalleryOpen && selectedImageIndex !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4\",\n                    onClick: closeGallery,\n                    role: \"dialog\",\n                    \"aria-modal\": \"true\",\n                    \"aria-labelledby\": \"gallery-title\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-4xl w-full\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: closeGallery,\n                                className: \"absolute top-4 right-4 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-2 transition-colors duration-300\",\n                                \"aria-label\": \"إغلاق المعرض\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: prevImage,\n                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-colors duration-300\",\n                                \"aria-label\": \"الصورة السابقة\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: nextImage,\n                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-colors duration-300\",\n                                \"aria-label\": \"الصورة التالية\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                className: \"bg-white rounded-2xl overflow-hidden shadow-2xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-[4/5] bg-gradient-to-br from-pink-100 via-rose-100 to-purple-100 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: _data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].images[currentImageIndexes[_data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].id]],\n                                        alt: \"\".concat(_data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].title, \" - صورة \").concat(currentImageIndexes[_data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].id] + 1),\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 17\n                                }, this)\n                            }, selectedImageIndex, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_s(DesignsPage, \"h/Mt8eqLX1bfXwV+gq54vhL/T6M=\");\n_c = DesignsPage;\nvar _c;\n$RefreshReg$(_c, \"DesignsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/designs/page.tsx\n"));

/***/ })

});