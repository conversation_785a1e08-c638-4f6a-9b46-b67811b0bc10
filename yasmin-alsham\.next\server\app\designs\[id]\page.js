/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/designs/[id]/page";
exports.ids = ["app/designs/[id]/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdesigns%2F%5Bid%5D%2Fpage&page=%2Fdesigns%2F%5Bid%5D%2Fpage&appPaths=%2Fdesigns%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fdesigns%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdesigns%2F%5Bid%5D%2Fpage&page=%2Fdesigns%2F%5Bid%5D%2Fpage&appPaths=%2Fdesigns%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fdesigns%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/designs/[id]/page.tsx */ \"(rsc)/./src/app/designs/[id]/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'designs',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/designs/[id]/page\",\n        pathname: \"/designs/[id]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdesigns%2F%5Bid%5D%2Fpage&page=%2Fdesigns%2F%5Bid%5D%2Fpage&appPaths=%2Fdesigns%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fdesigns%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Kufi_Arabic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-kufi%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoKufi%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Kufi_Arabic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-kufi%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoKufi%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdesigns%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdesigns%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/designs/[id]/page.tsx */ \"(rsc)/./src/app/designs/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2toYWxlJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1lBU01JTiUyMEFMU0hBTSU1QyU1Q3lhc21pbi1hbHNoYW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkZXNpZ25zJTVDJTVDJTVCaWQlNUQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQW1KIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxraGFsZVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxZQVNNSU4gQUxTSEFNXFxcXHlhc21pbi1hbHNoYW1cXFxcc3JjXFxcXGFwcFxcXFxkZXNpZ25zXFxcXFtpZF1cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdesigns%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca2hhbGVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcWUFTTUlOIEFMU0hBTVxceWFzbWluLWFsc2hhbVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/designs/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/designs/[id]/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\designs\\[id]\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b8e683c6119e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGtoYWxlXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFlBU01JTiBBTFNIQU1cXHlhc21pbi1hbHNoYW1cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImI4ZTY4M2M2MTE5ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_variable_font_cairo_subsets_arabic_latin_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"variable\":\"--font-cairo\",\"subsets\":[\"arabic\",\"latin\"],\"display\":\"swap\"}],\"variableName\":\"cairo\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Cairo\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-cairo\\\",\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"cairo\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_variable_font_cairo_subsets_arabic_latin_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_variable_font_cairo_subsets_arabic_latin_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Kufi_Arabic_arguments_variable_font_noto_kufi_subsets_arabic_display_swap_variableName_notoKufi___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Noto_Kufi_Arabic\",\"arguments\":[{\"variable\":\"--font-noto-kufi\",\"subsets\":[\"arabic\"],\"display\":\"swap\"}],\"variableName\":\"notoKufi\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Noto_Kufi_Arabic\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-noto-kufi\\\",\\\"subsets\\\":[\\\"arabic\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoKufi\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Kufi_Arabic_arguments_variable_font_noto_kufi_subsets_arabic_display_swap_variableName_notoKufi___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Kufi_Arabic_arguments_variable_font_noto_kufi_subsets_arabic_display_swap_variableName_notoKufi___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"ياسمين الشام - تفصيل فساتين حسب الطلب\",\n    description: \"محل ياسمين الشام لتفصيل الفساتين النسائية بأناقة دمشقية. حجز مواعيد، استعلام عن الطلبات، وأفضل الأقمشة.\",\n    keywords: \"تفصيل فساتين، خياطة نسائية، ياسمين الشام، فساتين دمشقية، حجز موعد\",\n    authors: [\n        {\n            name: \"ياسمين الشام\"\n        }\n    ],\n    openGraph: {\n        title: \"ياسمين الشام - تفصيل فساتين حسب الطلب\",\n        description: \"محل ياسمين الشام لتفصيل الفساتين النسائية بأناقة دمشقية\",\n        type: \"website\",\n        locale: \"ar_SA\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_variable_font_cairo_subsets_arabic_latin_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Kufi_Arabic_arguments_variable_font_noto_kufi_subsets_arabic_display_swap_variableName_notoKufi___WEBPACK_IMPORTED_MODULE_3___default().variable)} font-cairo antialiased bg-gradient-to-br from-rose-50 to-pink-50 min-h-screen`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Kufi_Arabic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-kufi%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoKufi%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Kufi_Arabic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-kufi%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoKufi%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdesigns%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdesigns%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/designs/[id]/page.tsx */ \"(ssr)/./src/app/designs/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2toYWxlJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1lBU01JTiUyMEFMU0hBTSU1QyU1Q3lhc21pbi1hbHNoYW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkZXNpZ25zJTVDJTVDJTVCaWQlNUQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQW1KIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxraGFsZVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxZQVNNSU4gQUxTSEFNXFxcXHlhc21pbi1hbHNoYW1cXFxcc3JjXFxcXGFwcFxcXFxkZXNpZ25zXFxcXFtpZF1cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdesigns%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/designs/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/designs/[id]/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DesignDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Heart,ShoppingBag,Star,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Heart,ShoppingBag,Star,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Heart,ShoppingBag,Star,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Heart,ShoppingBag,Star,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Heart,ShoppingBag,Star,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Heart,ShoppingBag,Star,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Heart,ShoppingBag,Star,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _data_designs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/data/designs */ \"(ssr)/./src/data/designs.ts\");\n/* harmony import */ var _store_shopStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/shopStore */ \"(ssr)/./src/store/shopStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction DesignDetailPage() {\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const designId = parseInt(params.id);\n    const design = _data_designs__WEBPACK_IMPORTED_MODULE_4__.allDesigns.find((d)=>d.id === designId);\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isGalleryOpen, setIsGalleryOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSize, setSelectedSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedColor, setSelectedColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // متجر التسوق\n    const { addToFavorites, removeFromFavorites, isFavorite, addToCart } = (0,_store_shopStore__WEBPACK_IMPORTED_MODULE_5__.useShopStore)();\n    const [addedToCart, setAddedToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DesignDetailPage.useEffect\": ()=>{\n            if (design?.sizes && design.sizes.length > 0) {\n                setSelectedSize(design.sizes[0]);\n            }\n            if (design?.colors && design.colors.length > 0) {\n                setSelectedColor(design.colors[0]);\n            }\n        }\n    }[\"DesignDetailPage.useEffect\"], [\n        design\n    ]);\n    if (!design) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-800 mb-4\",\n                        children: \"التصميم غير موجود\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/designs\",\n                        className: \"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"العودة إلى التصاميم\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this);\n    }\n    const handleToggleFavorite = ()=>{\n        const product = {\n            id: design.id.toString(),\n            name: design.title,\n            price: design.price,\n            image: design.images[0],\n            description: design.description,\n            category: design.category\n        };\n        if (isFavorite(product.id)) {\n            removeFromFavorites(product.id);\n        } else {\n            addToFavorites(product);\n        }\n    };\n    const handleAddToCart = ()=>{\n        const product = {\n            id: design.id.toString(),\n            name: design.title,\n            price: design.price,\n            image: design.images[0],\n            description: design.description,\n            category: design.category\n        };\n        addToCart(product, 1, selectedSize, selectedColor);\n        setAddedToCart(true);\n        setTimeout(()=>{\n            setAddedToCart(false);\n        }, 2000);\n    };\n    const nextImage = ()=>{\n        setCurrentImageIndex((prev)=>(prev + 1) % design.images.length);\n    };\n    const prevImage = ()=>{\n        setCurrentImageIndex((prev)=>prev === 0 ? design.images.length - 1 : prev - 1);\n    };\n    const openGallery = ()=>{\n        setIsGalleryOpen(true);\n        document.body.style.overflow = 'hidden';\n    };\n    const closeGallery = ()=>{\n        setIsGalleryOpen(false);\n        document.body.style.overflow = 'unset';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-16 lg:pt-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6\n                },\n                className: \"fixed top-20 lg:top-24 left-4 lg:left-8 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    href: \"/designs\",\n                    className: \"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300 bg-white/80 backdrop-blur-sm px-3 py-2 rounded-lg shadow-sm border border-pink-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4 lg:w-5 lg:h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm lg:text-base\",\n                            children: \"العودة إلى التصاميم\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-2 gap-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative aspect-[4/5] bg-gradient-to-br from-pink-100 via-rose-100 to-purple-100 rounded-2xl overflow-hidden mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: design.images[currentImageIndex],\n                                                alt: `${design.title} - صورة ${currentImageIndex + 1}`,\n                                                className: \"w-full h-full object-cover cursor-pointer\",\n                                                onClick: openGallery\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 15\n                                            }, this),\n                                            design.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: prevImage,\n                                                        className: \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-all duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: nextImage,\n                                                        className: \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-all duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this),\n                                    design.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 overflow-x-auto\",\n                                        children: design.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setCurrentImageIndex(index),\n                                                className: `flex-shrink-0 w-20 h-24 rounded-lg overflow-hidden border-2 transition-all duration-300 ${currentImageIndex === index ? 'border-pink-500' : 'border-gray-200'}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: image,\n                                                    alt: `${design.title} - صورة ${index + 1}`,\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-pink-100 to-rose-100 text-pink-700 px-3 py-1 rounded-full text-sm font-medium\",\n                                                children: design.category\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl lg:text-4xl font-bold text-gray-800 mt-4 mb-4\",\n                                                children: design.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg text-gray-600 leading-relaxed\",\n                                                children: design.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-pink-600\",\n                                        children: (0,_store_shopStore__WEBPACK_IMPORTED_MODULE_5__.formatPrice)(design.price)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this),\n                                    design.rating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    ...Array(5)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: `w-5 h-5 ${i < Math.floor(design.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: [\n                                                    design.rating,\n                                                    \" (\",\n                                                    design.reviews_count,\n                                                    \" تقييم)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    design.sizes && design.sizes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-800 mb-3\",\n                                                children: \"المقاس\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: design.sizes.map((size)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedSize(size),\n                                                        className: `px-4 py-2 border rounded-lg transition-all duration-300 ${selectedSize === size ? 'border-pink-500 bg-pink-50 text-pink-700' : 'border-gray-300 hover:border-pink-300'}`,\n                                                        children: size\n                                                    }, size, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    design.colors && design.colors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-800 mb-3\",\n                                                children: \"اللون\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: design.colors.map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedColor(color),\n                                                        className: `px-4 py-2 border rounded-lg transition-all duration-300 ${selectedColor === color ? 'border-pink-500 bg-pink-50 text-pink-700' : 'border-gray-300 hover:border-pink-300'}`,\n                                                        children: color\n                                                    }, color, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleAddToCart,\n                                                disabled: addedToCart,\n                                                className: `flex-1 flex items-center justify-center space-x-2 space-x-reverse py-3 px-6 rounded-full text-lg font-medium transition-all duration-300 ${addedToCart ? 'bg-green-500 text-white' : 'bg-gradient-to-r from-pink-500 to-purple-600 text-white hover:shadow-lg'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: addedToCart ? 'تم الإضافة للسلة' : 'أضف للسلة'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleToggleFavorite,\n                                                className: `p-3 rounded-full border-2 transition-all duration-300 ${isFavorite(design.id.toString()) ? 'border-red-500 bg-red-500 text-white' : 'border-pink-300 text-pink-600 hover:bg-pink-50'}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: `w-6 h-6 ${isFavorite(design.id.toString()) ? 'fill-current' : ''}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: `https://wa.me/+966598862609?text=أريد استفسار عن ${design.title}`,\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"block w-full text-center py-3 px-6 border border-green-500 text-green-600 rounded-full hover:bg-green-50 transition-colors duration-300 font-medium\",\n                                        children: \"استفسار عبر واتساب\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    isGalleryOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4\",\n                        onClick: closeGallery,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative max-w-4xl w-full\",\n                            onClick: (e)=>e.stopPropagation(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: closeGallery,\n                                    className: \"absolute top-4 right-4 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-2 transition-colors duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: design.images[currentImageIndex],\n                                    alt: `${design.title} - صورة ${currentImageIndex + 1}`,\n                                    className: \"w-full h-auto max-h-[80vh] object-contain rounded-lg\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this),\n                                design.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: prevImage,\n                                            className: \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-colors duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: nextImage,\n                                            className: \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-colors duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/designs/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/data/designs.ts":
/*!*****************************!*\
  !*** ./src/data/designs.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allDesigns: () => (/* binding */ allDesigns)\n/* harmony export */ });\nconst allDesigns = [\n    {\n        id: 1,\n        title: 'فستان زفاف كلاسيكي',\n        description: 'فستان زفاف أنيق بتصميم كلاسيكي مع تطريز يدوي رائع يجمع بين الأناقة والفخامة',\n        category: 'فساتين زفاف',\n        price: 1299,\n        images: [\n            '/wedding-dress-1.jpg.jpg',\n            '/wedding-dress-1a.jpg.jpg',\n            '/wedding-dress-1b.jpg.jpg'\n        ],\n        fabric: 'شيفون حريري',\n        colors: [\n            'أبيض',\n            'كريمي',\n            'عاجي'\n        ],\n        sizes: [\n            'XS',\n            'S',\n            'M',\n            'L',\n            'XL',\n            'XXL'\n        ],\n        features: [\n            'تطريز يدوي فاخر',\n            'قماش شيفون حريري عالي الجودة',\n            'تصميم كلاسيكي خالد',\n            'قصة مناسبة لجميع أنواع الجسم',\n            'تفاصيل دقيقة ومتقنة'\n        ],\n        occasions: [\n            'حفلات الزفاف',\n            'المناسبات الرسمية',\n            'الحفلات الخاصة'\n        ],\n        care_instructions: [\n            'تنظيف جاف فقط',\n            'تجنب التعرض المباشر لأشعة الشمس',\n            'تخزين في مكان جاف وبارد',\n            'كي على درجة حرارة منخفضة'\n        ],\n        rating: 4.9,\n        reviews_count: 127\n    },\n    {\n        id: 2,\n        title: 'فستان سهرة راقي',\n        description: 'فستان سهرة طويل بقصة عصرية ولمسات ذهبية تضفي لمسة من الفخامة والأناقة',\n        category: 'فساتين سهرة',\n        price: 899,\n        images: [\n            '/wedding-dress-2.jpg.jpg',\n            '/wedding-dress-2a.jpg.jpg',\n            '/wedding-dress-2b.jpg.jpg'\n        ],\n        fabric: 'ساتان مطرز',\n        colors: [\n            'أسود',\n            'أزرق داكن',\n            'بورجندي'\n        ],\n        sizes: [\n            'XS',\n            'S',\n            'M',\n            'L',\n            'XL'\n        ],\n        features: [\n            'تطريز ذهبي فاخر',\n            'قصة طويلة أنيقة',\n            'تصميم عصري مميز',\n            'خامات عالية الجودة'\n        ],\n        occasions: [\n            'حفلات السهرة',\n            'المناسبات الرسمية',\n            'الحفلات الخاصة'\n        ],\n        care_instructions: [\n            'تنظيف جاف فقط',\n            'تجنب التعرض للرطوبة',\n            'تخزين معلق في الخزانة'\n        ],\n        rating: 4.7,\n        reviews_count: 89\n    },\n    {\n        id: 3,\n        title: 'فستان كوكتيل أنيق',\n        description: 'فستان كوكتيل قصير بتصميم عصري ومميز يناسب المناسبات الاجتماعية والحفلات',\n        category: 'فساتين كوكتيل',\n        price: 599,\n        images: [\n            '/wedding-dress-3.jpg.jpg',\n            '/wedding-dress-3a.jpg.jpg',\n            '/wedding-dress-3b.jpg.jpg'\n        ],\n        fabric: 'شيفون مطرز',\n        colors: [\n            'وردي',\n            'أبيض',\n            'بيج'\n        ],\n        sizes: [\n            'XS',\n            'S',\n            'M',\n            'L'\n        ],\n        features: [\n            'قصة قصيرة عصرية',\n            'تطريز دقيق',\n            'تصميم مريح',\n            'مناسب للحفلات'\n        ],\n        occasions: [\n            'حفلات الكوكتيل',\n            'المناسبات الاجتماعية',\n            'الحفلات النهارية'\n        ],\n        care_instructions: [\n            'يمكن غسله بالماء البارد',\n            'تجفيف في الهواء',\n            'كي على درجة حرارة متوسطة'\n        ],\n        rating: 4.8,\n        reviews_count: 156\n    },\n    {\n        id: 4,\n        title: 'فستان مناسبات خاصة',\n        description: 'فستان أنيق للمناسبات الخاصة بتصميم راقي وتفاصيل مميزة',\n        category: 'فساتين مناسبات',\n        price: 749,\n        images: [\n            '/wedding-dress-4.jpg.jpg',\n            '/wedding-dress-4a.jpg.jpg',\n            '/wedding-dress-4b.jpg.jpg'\n        ],\n        fabric: 'حرير طبيعي',\n        colors: [\n            'أزرق',\n            'أخضر',\n            'بنفسجي'\n        ],\n        sizes: [\n            'S',\n            'M',\n            'L',\n            'XL'\n        ],\n        features: [\n            'حرير طبيعي فاخر',\n            'تصميم راقي',\n            'قصة مريحة',\n            'تفاصيل مميزة'\n        ],\n        occasions: [\n            'المناسبات الخاصة',\n            'الحفلات العائلية',\n            'المناسبات الرسمية'\n        ],\n        care_instructions: [\n            'تنظيف جاف فقط',\n            'تجنب أشعة الشمس المباشرة',\n            'تخزين في مكان بارد وجاف'\n        ],\n        rating: 4.6,\n        reviews_count: 73\n    },\n    {\n        id: 5,\n        title: 'فستان زفاف ملكي',\n        description: 'فستان زفاف فاخر بتصميم ملكي مع تنورة واسعة وتطريز كريستالي',\n        category: 'فساتين زفاف',\n        price: 1599,\n        images: [\n            '/wedding-dress-5.jpg.jpg',\n            '/wedding-dress-5a.jpg.jpg',\n            '/wedding-dress-5b.jpg.jpg'\n        ],\n        fabric: 'تول وساتان',\n        colors: [\n            'أبيض',\n            'عاجي'\n        ],\n        sizes: [\n            'XS',\n            'S',\n            'M',\n            'L',\n            'XL',\n            'XXL'\n        ],\n        features: [\n            'تصميم ملكي فاخر',\n            'تطريز كريستالي',\n            'تنورة واسعة',\n            'ذيل طويل أنيق'\n        ],\n        occasions: [\n            'حفلات الزفاف الفاخرة',\n            'المناسبات الملكية'\n        ],\n        care_instructions: [\n            'تنظيف جاف متخصص فقط',\n            'تخزين في كيس خاص',\n            'تجنب الطي'\n        ],\n        rating: 4.9,\n        reviews_count: 201\n    },\n    {\n        id: 6,\n        title: 'فستان سهرة كلاسيكي',\n        description: 'فستان سهرة بتصميم كلاسيكي خالد مع لمسات عصرية أنيقة',\n        category: 'فساتين سهرة',\n        price: 799,\n        images: [\n            '/wedding-dress-6.jpg.jpg',\n            '/wedding-dress-6a.jpg.jpg',\n            '/wedding-dress-6b.jpg.jpg'\n        ],\n        fabric: 'شيفون وساتان',\n        colors: [\n            'أسود',\n            'أحمر',\n            'أزرق داكن'\n        ],\n        sizes: [\n            'XS',\n            'S',\n            'M',\n            'L',\n            'XL'\n        ],\n        features: [\n            'تصميم كلاسيكي',\n            'لمسات عصرية',\n            'قصة أنيقة',\n            'خامات فاخرة'\n        ],\n        occasions: [\n            'حفلات السهرة',\n            'المناسبات الرسمية',\n            'الحفلات الخاصة'\n        ],\n        care_instructions: [\n            'تنظيف جاف',\n            'تجنب الكي المباشر',\n            'تخزين معلق'\n        ],\n        rating: 4.5,\n        reviews_count: 94\n    },\n    {\n        id: 7,\n        title: 'فستان كوكتيل عصري',\n        description: 'فستان كوكتيل بتصميم عصري جريء مع تفاصيل مميزة وقصة عملية',\n        category: 'فساتين كوكتيل',\n        price: 649,\n        images: [\n            '/wedding-dress-7.jpg.jpg',\n            '/wedding-dress-7a.jpg.jpg',\n            '/wedding-dress-7b.jpg.jpg'\n        ],\n        fabric: 'كريب مطاطي',\n        colors: [\n            'أسود',\n            'أحمر',\n            'أزرق'\n        ],\n        sizes: [\n            'XS',\n            'S',\n            'M',\n            'L'\n        ],\n        features: [\n            'تصميم عصري',\n            'قصة عملية',\n            'خامة مريحة',\n            'تفاصيل مميزة'\n        ],\n        occasions: [\n            'حفلات الكوكتيل',\n            'المناسبات الاجتماعية',\n            'الحفلات المسائية'\n        ],\n        care_instructions: [\n            'غسيل بالماء البارد',\n            'تجفيف طبيعي',\n            'كي على درجة منخفضة'\n        ],\n        rating: 4.7,\n        reviews_count: 112\n    },\n    {\n        id: 8,\n        title: 'فستان مناسبات راقي',\n        description: 'فستان راقي للمناسبات الخاصة بتصميم أنيق وتفاصيل فاخرة',\n        category: 'فساتين مناسبات',\n        price: 849,\n        images: [\n            '/wedding-dress-8.jpg.jpg',\n            '/wedding-dress-8a.jpg.jpg',\n            '/wedding-dress-8b.jpg.jpg'\n        ],\n        fabric: 'ساتان مطرز',\n        colors: [\n            'ذهبي',\n            'فضي',\n            'وردي'\n        ],\n        sizes: [\n            'S',\n            'M',\n            'L',\n            'XL'\n        ],\n        features: [\n            'تطريز فاخر',\n            'تصميم راقي',\n            'خامات عالية الجودة',\n            'قصة مميزة'\n        ],\n        occasions: [\n            'المناسبات الراقية',\n            'الحفلات الخاصة',\n            'المناسبات الرسمية'\n        ],\n        care_instructions: [\n            'تنظيف جاف فقط',\n            'تجنب الرطوبة',\n            'تخزين في مكان جاف'\n        ],\n        rating: 4.6,\n        reviews_count: 67\n    },\n    {\n        id: 9,\n        title: 'فستان زفاف رومانسي',\n        description: 'فستان زفاف رومانسي بتصميم حالم مع تفاصيل من الدانتيل والورود',\n        category: 'فساتين زفاف',\n        price: 1199,\n        images: [\n            '/wedding-dress-9.jpg.jpg',\n            '/wedding-dress-9a.jpg.jpg',\n            '/wedding-dress-9b.jpg.jpg'\n        ],\n        fabric: 'دانتيل وتول',\n        colors: [\n            'أبيض',\n            'كريمي',\n            'وردي فاتح'\n        ],\n        sizes: [\n            'XS',\n            'S',\n            'M',\n            'L',\n            'XL'\n        ],\n        features: [\n            'دانتيل فرنسي',\n            'تصميم رومانسي',\n            'تفاصيل ورود',\n            'قصة حالمة'\n        ],\n        occasions: [\n            'حفلات الزفاف',\n            'المناسبات الرومانسية'\n        ],\n        care_instructions: [\n            'تنظيف جاف متخصص',\n            'تجنب الشد',\n            'تخزين مسطح'\n        ],\n        rating: 4.8,\n        reviews_count: 143\n    },\n    {\n        id: 10,\n        title: 'فستان سهرة فاخر',\n        description: 'فستان سهرة فاخر بتصميم مميز مع تطريز كريستالي وقصة أنيقة',\n        category: 'فساتين سهرة',\n        price: 999,\n        images: [\n            '/wedding-dress-10.jpg.jpg',\n            '/wedding-dress-10a.jpg.jpg',\n            '/wedding-dress-10b.jpg.jpg'\n        ],\n        fabric: 'ساتان وكريستال',\n        colors: [\n            'أسود',\n            'أزرق ملكي',\n            'بورجندي'\n        ],\n        sizes: [\n            'XS',\n            'S',\n            'M',\n            'L'\n        ],\n        features: [\n            'تطريز كريستالي',\n            'تصميم فاخر',\n            'قصة أنيقة',\n            'خامات راقية'\n        ],\n        occasions: [\n            'حفلات السهرة الفاخرة',\n            'المناسبات الراقية'\n        ],\n        care_instructions: [\n            'تنظيف جاف فقط',\n            'تجنب الاحتكاك',\n            'تخزين في كيس خاص'\n        ],\n        rating: 4.7,\n        reviews_count: 98\n    },\n    {\n        id: 11,\n        title: 'فستان كوكتيل مميز',\n        description: 'فستان كوكتيل مميز بتصميم جريء وألوان زاهية مناسب للحفلات',\n        category: 'فساتين كوكتيل',\n        price: 699,\n        images: [\n            '/wedding-dress-11.jpg.jpg',\n            '/wedding-dress-11a.jpg.jpg',\n            '/wedding-dress-11b.jpg.jpg'\n        ],\n        fabric: 'شيفون ملون',\n        colors: [\n            'أحمر',\n            'أخضر',\n            'أزرق فاتح'\n        ],\n        sizes: [\n            'XS',\n            'S',\n            'M',\n            'L'\n        ],\n        features: [\n            'ألوان زاهية',\n            'تصميم جريء',\n            'قصة عملية',\n            'مناسب للحفلات'\n        ],\n        occasions: [\n            'حفلات الكوكتيل',\n            'الحفلات الصيفية',\n            'المناسبات الاجتماعية'\n        ],\n        care_instructions: [\n            'غسيل بالماء البارد',\n            'تجفيف في الظل',\n            'كي بحذر'\n        ],\n        rating: 4.6,\n        reviews_count: 134\n    },\n    {\n        id: 12,\n        title: 'فستان مناسبات كلاسيكي',\n        description: 'فستان كلاسيكي للمناسبات بتصميم خالد وأناقة لا تنتهي',\n        category: 'فساتين مناسبات',\n        price: 799,\n        images: [\n            '/wedding-dress-12.jpg.jpg',\n            '/wedding-dress-12a.jpg.jpg',\n            '/wedding-dress-12b.jpg.jpg'\n        ],\n        fabric: 'حرير وساتان',\n        colors: [\n            'أسود',\n            'أزرق داكن',\n            'بني'\n        ],\n        sizes: [\n            'S',\n            'M',\n            'L',\n            'XL'\n        ],\n        features: [\n            'تصميم كلاسيكي',\n            'أناقة خالدة',\n            'خامات فاخرة',\n            'قصة مريحة'\n        ],\n        occasions: [\n            'المناسبات الرسمية',\n            'الحفلات الكلاسيكية',\n            'المناسبات العائلية'\n        ],\n        care_instructions: [\n            'تنظيف جاف',\n            'تجنب أشعة الشمس',\n            'تخزين معلق'\n        ],\n        rating: 4.4,\n        reviews_count: 76\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvZGF0YS9kZXNpZ25zLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFrQk8sTUFBTUEsYUFBdUI7SUFDbEM7UUFDRUMsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFFBQVE7WUFDTjtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxRQUFRO1FBQ1JDLFFBQVE7WUFBQztZQUFRO1lBQVM7U0FBTztRQUNqQ0MsT0FBTztZQUFDO1lBQU07WUFBSztZQUFLO1lBQUs7WUFBTTtTQUFNO1FBQ3pDQyxVQUFVO1lBQ1I7WUFDQTtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFdBQVc7WUFBQztZQUFnQjtZQUFxQjtTQUFpQjtRQUNsRUMsbUJBQW1CO1lBQ2pCO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFDREMsUUFBUTtRQUNSQyxlQUFlO0lBQ2pCO0lBQ0E7UUFDRWIsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFFBQVE7WUFDTjtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxRQUFRO1FBQ1JDLFFBQVE7WUFBQztZQUFRO1lBQWE7U0FBVTtRQUN4Q0MsT0FBTztZQUFDO1lBQU07WUFBSztZQUFLO1lBQUs7U0FBSztRQUNsQ0MsVUFBVTtZQUNSO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFDREMsV0FBVztZQUFDO1lBQWdCO1lBQXFCO1NBQWlCO1FBQ2xFQyxtQkFBbUI7WUFDakI7WUFDQTtZQUNBO1NBQ0Q7UUFDREMsUUFBUTtRQUNSQyxlQUFlO0lBQ2pCO0lBQ0E7UUFDRWIsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFFBQVE7WUFDTjtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxRQUFRO1FBQ1JDLFFBQVE7WUFBQztZQUFRO1lBQVE7U0FBTTtRQUMvQkMsT0FBTztZQUFDO1lBQU07WUFBSztZQUFLO1NBQUk7UUFDNUJDLFVBQVU7WUFDUjtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFdBQVc7WUFBQztZQUFrQjtZQUF3QjtTQUFtQjtRQUN6RUMsbUJBQW1CO1lBQ2pCO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFFBQVE7UUFDUkMsZUFBZTtJQUNqQjtJQUNBO1FBQ0ViLElBQUk7UUFDSkMsT0FBTztRQUNQQyxhQUFhO1FBQ2JDLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxRQUFRO1lBQ047WUFDQTtZQUNBO1NBQ0Q7UUFDREMsUUFBUTtRQUNSQyxRQUFRO1lBQUM7WUFBUTtZQUFRO1NBQVM7UUFDbENDLE9BQU87WUFBQztZQUFLO1lBQUs7WUFBSztTQUFLO1FBQzVCQyxVQUFVO1lBQ1I7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxXQUFXO1lBQUM7WUFBb0I7WUFBb0I7U0FBb0I7UUFDeEVDLG1CQUFtQjtZQUNqQjtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxRQUFRO1FBQ1JDLGVBQWU7SUFDakI7SUFDQTtRQUNFYixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsUUFBUTtZQUNOO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFFBQVE7UUFDUkMsUUFBUTtZQUFDO1lBQVE7U0FBTztRQUN4QkMsT0FBTztZQUFDO1lBQU07WUFBSztZQUFLO1lBQUs7WUFBTTtTQUFNO1FBQ3pDQyxVQUFVO1lBQ1I7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxXQUFXO1lBQUM7WUFBd0I7U0FBb0I7UUFDeERDLG1CQUFtQjtZQUNqQjtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxRQUFRO1FBQ1JDLGVBQWU7SUFDakI7SUFDQTtRQUNFYixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsUUFBUTtZQUNOO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFFBQVE7UUFDUkMsUUFBUTtZQUFDO1lBQVE7WUFBUTtTQUFZO1FBQ3JDQyxPQUFPO1lBQUM7WUFBTTtZQUFLO1lBQUs7WUFBSztTQUFLO1FBQ2xDQyxVQUFVO1lBQ1I7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxXQUFXO1lBQUM7WUFBZ0I7WUFBcUI7U0FBaUI7UUFDbEVDLG1CQUFtQjtZQUNqQjtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxRQUFRO1FBQ1JDLGVBQWU7SUFDakI7SUFDQTtRQUNFYixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsUUFBUTtZQUNOO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFFBQVE7UUFDUkMsUUFBUTtZQUFDO1lBQVE7WUFBUTtTQUFPO1FBQ2hDQyxPQUFPO1lBQUM7WUFBTTtZQUFLO1lBQUs7U0FBSTtRQUM1QkMsVUFBVTtZQUNSO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFDREMsV0FBVztZQUFDO1lBQWtCO1lBQXdCO1NBQW1CO1FBQ3pFQyxtQkFBbUI7WUFDakI7WUFDQTtZQUNBO1NBQ0Q7UUFDREMsUUFBUTtRQUNSQyxlQUFlO0lBQ2pCO0lBQ0E7UUFDRWIsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFFBQVE7WUFDTjtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxRQUFRO1FBQ1JDLFFBQVE7WUFBQztZQUFRO1lBQU87U0FBTztRQUMvQkMsT0FBTztZQUFDO1lBQUs7WUFBSztZQUFLO1NBQUs7UUFDNUJDLFVBQVU7WUFDUjtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFdBQVc7WUFBQztZQUFxQjtZQUFrQjtTQUFvQjtRQUN2RUMsbUJBQW1CO1lBQ2pCO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFFBQVE7UUFDUkMsZUFBZTtJQUNqQjtJQUNBO1FBQ0ViLElBQUk7UUFDSkMsT0FBTztRQUNQQyxhQUFhO1FBQ2JDLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxRQUFRO1lBQ047WUFDQTtZQUNBO1NBQ0Q7UUFDREMsUUFBUTtRQUNSQyxRQUFRO1lBQUM7WUFBUTtZQUFTO1NBQVk7UUFDdENDLE9BQU87WUFBQztZQUFNO1lBQUs7WUFBSztZQUFLO1NBQUs7UUFDbENDLFVBQVU7WUFDUjtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFdBQVc7WUFBQztZQUFnQjtTQUF1QjtRQUNuREMsbUJBQW1CO1lBQ2pCO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFFBQVE7UUFDUkMsZUFBZTtJQUNqQjtJQUNBO1FBQ0ViLElBQUk7UUFDSkMsT0FBTztRQUNQQyxhQUFhO1FBQ2JDLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxRQUFRO1lBQ047WUFDQTtZQUNBO1NBQ0Q7UUFDREMsUUFBUTtRQUNSQyxRQUFRO1lBQUM7WUFBUTtZQUFhO1NBQVU7UUFDeENDLE9BQU87WUFBQztZQUFNO1lBQUs7WUFBSztTQUFJO1FBQzVCQyxVQUFVO1lBQ1I7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxXQUFXO1lBQUM7WUFBd0I7U0FBb0I7UUFDeERDLG1CQUFtQjtZQUNqQjtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxRQUFRO1FBQ1JDLGVBQWU7SUFDakI7SUFDQTtRQUNFYixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsUUFBUTtZQUNOO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFFBQVE7UUFDUkMsUUFBUTtZQUFDO1lBQVE7WUFBUTtTQUFZO1FBQ3JDQyxPQUFPO1lBQUM7WUFBTTtZQUFLO1lBQUs7U0FBSTtRQUM1QkMsVUFBVTtZQUNSO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFDREMsV0FBVztZQUFDO1lBQWtCO1lBQW1CO1NBQXVCO1FBQ3hFQyxtQkFBbUI7WUFDakI7WUFDQTtZQUNBO1NBQ0Q7UUFDREMsUUFBUTtRQUNSQyxlQUFlO0lBQ2pCO0lBQ0E7UUFDRWIsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFFBQVE7WUFDTjtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxRQUFRO1FBQ1JDLFFBQVE7WUFBQztZQUFRO1lBQWE7U0FBTTtRQUNwQ0MsT0FBTztZQUFDO1lBQUs7WUFBSztZQUFLO1NBQUs7UUFDNUJDLFVBQVU7WUFDUjtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFdBQVc7WUFBQztZQUFxQjtZQUFzQjtTQUFxQjtRQUM1RUMsbUJBQW1CO1lBQ2pCO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFFBQVE7UUFDUkMsZUFBZTtJQUNqQjtDQUNEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGtoYWxlXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFlBU01JTiBBTFNIQU1cXHlhc21pbi1hbHNoYW1cXHNyY1xcZGF0YVxcZGVzaWducy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgaW50ZXJmYWNlIERlc2lnbiB7XG4gIGlkOiBudW1iZXJcbiAgdGl0bGU6IHN0cmluZ1xuICBkZXNjcmlwdGlvbjogc3RyaW5nXG4gIGNhdGVnb3J5OiBzdHJpbmdcbiAgaW1hZ2VzOiBzdHJpbmdbXVxuICBwcmljZTogbnVtYmVyXG5cbiAgZmFicmljPzogc3RyaW5nXG4gIGNvbG9ycz86IHN0cmluZ1tdXG4gIHNpemVzPzogc3RyaW5nW11cbiAgZmVhdHVyZXM/OiBzdHJpbmdbXVxuICBvY2Nhc2lvbnM/OiBzdHJpbmdbXVxuICBjYXJlX2luc3RydWN0aW9ucz86IHN0cmluZ1tdXG4gIHJhdGluZz86IG51bWJlclxuICByZXZpZXdzX2NvdW50PzogbnVtYmVyXG59XG5cbmV4cG9ydCBjb25zdCBhbGxEZXNpZ25zOiBEZXNpZ25bXSA9IFtcbiAge1xuICAgIGlkOiAxLFxuICAgIHRpdGxlOiAn2YHYs9iq2KfZhiDYstmB2KfZgSDZg9mE2KfYs9mK2YPZiicsXG4gICAgZGVzY3JpcHRpb246ICfZgdiz2KrYp9mGINiy2YHYp9mBINij2YbZitmCINio2KrYtdmF2YrZhSDZg9mE2KfYs9mK2YPZiiDZhdi5INiq2LfYsdmK2LIg2YrYr9mI2Yog2LHYp9im2Lkg2YrYrNmF2Lkg2KjZitmGINin2YTYo9mG2KfZgtipINmI2KfZhNmB2K7Yp9mF2KknLFxuICAgIGNhdGVnb3J5OiAn2YHYs9in2KrZitmGINiy2YHYp9mBJyxcbiAgICBwcmljZTogMTI5OSxcbiAgICBpbWFnZXM6IFtcbiAgICAgICcvd2VkZGluZy1kcmVzcy0xLmpwZy5qcGcnLFxuICAgICAgJy93ZWRkaW5nLWRyZXNzLTFhLmpwZy5qcGcnLFxuICAgICAgJy93ZWRkaW5nLWRyZXNzLTFiLmpwZy5qcGcnXG4gICAgXSxcbiAgICBmYWJyaWM6ICfYtNmK2YHZiNmGINit2LHZitix2YonLFxuICAgIGNvbG9yczogWyfYo9io2YrYticsICfZg9ix2YrZhdmKJywgJ9i52KfYrNmKJ10sXG4gICAgc2l6ZXM6IFsnWFMnLCAnUycsICdNJywgJ0wnLCAnWEwnLCAnWFhMJ10sXG4gICAgZmVhdHVyZXM6IFtcbiAgICAgICfYqti32LHZitiyINmK2K/ZiNmKINmB2KfYrtixJyxcbiAgICAgICfZgtmF2KfYtCDYtNmK2YHZiNmGINit2LHZitix2Yog2LnYp9mE2Yog2KfZhNis2YjYr9ipJyxcbiAgICAgICfYqti12YXZitmFINmD2YTYp9iz2YrZg9mKINiu2KfZhNivJyxcbiAgICAgICfZgti12Kkg2YXZhtin2LPYqNipINmE2KzZhdmK2Lkg2KPZhtmI2KfYuSDYp9mE2KzYs9mFJyxcbiAgICAgICfYqtmB2KfYtdmK2YQg2K/ZgtmK2YLYqSDZiNmF2KrZgtmG2KknXG4gICAgXSxcbiAgICBvY2Nhc2lvbnM6IFsn2K3ZgdmE2KfYqiDYp9mE2LLZgdin2YEnLCAn2KfZhNmF2YbYp9iz2KjYp9iqINin2YTYsdiz2YXZitipJywgJ9in2YTYrdmB2YTYp9iqINin2YTYrtin2LXYqSddLFxuICAgIGNhcmVfaW5zdHJ1Y3Rpb25zOiBbXG4gICAgICAn2KrZhti42YrZgSDYrNin2YEg2YHZgti3JyxcbiAgICAgICfYqtis2YbYqCDYp9mE2KrYudix2LYg2KfZhNmF2KjYp9i02LEg2YTYo9i02LnYqSDYp9mE2LTZhdizJyxcbiAgICAgICfYqtiu2LLZitmGINmB2Yog2YXZg9in2YYg2KzYp9mBINmI2KjYp9ix2K8nLFxuICAgICAgJ9mD2Yog2LnZhNmJINiv2LHYrNipINit2LHYp9ix2Kkg2YXZhtiu2YHYttipJ1xuICAgIF0sXG4gICAgcmF0aW5nOiA0LjksXG4gICAgcmV2aWV3c19jb3VudDogMTI3XG4gIH0sXG4gIHtcbiAgICBpZDogMixcbiAgICB0aXRsZTogJ9mB2LPYqtin2YYg2LPZh9ix2Kkg2LHYp9mC2YonLFxuICAgIGRlc2NyaXB0aW9uOiAn2YHYs9iq2KfZhiDYs9mH2LHYqSDYt9mI2YrZhCDYqNmC2LXYqSDYudi12LHZitipINmI2YTZhdiz2KfYqiDYsNmH2KjZitipINiq2LbZgdmKINmE2YXYs9ipINmF2YYg2KfZhNmB2K7Yp9mF2Kkg2YjYp9mE2KPZhtin2YLYqScsXG4gICAgY2F0ZWdvcnk6ICfZgdiz2KfYqtmK2YYg2LPZh9ix2KknLFxuICAgIHByaWNlOiA4OTksXG4gICAgaW1hZ2VzOiBbXG4gICAgICAnL3dlZGRpbmctZHJlc3MtMi5qcGcuanBnJyxcbiAgICAgICcvd2VkZGluZy1kcmVzcy0yYS5qcGcuanBnJyxcbiAgICAgICcvd2VkZGluZy1kcmVzcy0yYi5qcGcuanBnJ1xuICAgIF0sXG4gICAgZmFicmljOiAn2LPYp9iq2KfZhiDZhdi32LHYsicsXG4gICAgY29sb3JzOiBbJ9ij2LPZiNivJywgJ9ij2LLYsdmCINiv2KfZg9mGJywgJ9io2YjYsdis2YbYr9mKJ10sXG4gICAgc2l6ZXM6IFsnWFMnLCAnUycsICdNJywgJ0wnLCAnWEwnXSxcbiAgICBmZWF0dXJlczogW1xuICAgICAgJ9iq2LfYsdmK2LIg2LDZh9io2Yog2YHYp9iu2LEnLFxuICAgICAgJ9mC2LXYqSDYt9mI2YrZhNipINij2YbZitmC2KknLFxuICAgICAgJ9iq2LXZhdmK2YUg2LnYtdix2Yog2YXZhdmK2LInLFxuICAgICAgJ9iu2KfZhdin2Kog2LnYp9mE2YrYqSDYp9mE2KzZiNiv2KknXG4gICAgXSxcbiAgICBvY2Nhc2lvbnM6IFsn2K3ZgdmE2KfYqiDYp9mE2LPZh9ix2KknLCAn2KfZhNmF2YbYp9iz2KjYp9iqINin2YTYsdiz2YXZitipJywgJ9in2YTYrdmB2YTYp9iqINin2YTYrtin2LXYqSddLFxuICAgIGNhcmVfaW5zdHJ1Y3Rpb25zOiBbXG4gICAgICAn2KrZhti42YrZgSDYrNin2YEg2YHZgti3JyxcbiAgICAgICfYqtis2YbYqCDYp9mE2KrYudix2LYg2YTZhNix2LfZiNio2KknLFxuICAgICAgJ9iq2K7YstmK2YYg2YXYudmE2YIg2YHZiiDYp9mE2K7Ystin2YbYqSdcbiAgICBdLFxuICAgIHJhdGluZzogNC43LFxuICAgIHJldmlld3NfY291bnQ6IDg5XG4gIH0sXG4gIHtcbiAgICBpZDogMyxcbiAgICB0aXRsZTogJ9mB2LPYqtin2YYg2YPZiNmD2KrZitmEINij2YbZitmCJyxcbiAgICBkZXNjcmlwdGlvbjogJ9mB2LPYqtin2YYg2YPZiNmD2KrZitmEINmC2LXZitixINio2KrYtdmF2YrZhSDYudi12LHZiiDZiNmF2YXZitiyINmK2YbYp9iz2Kgg2KfZhNmF2YbYp9iz2KjYp9iqINin2YTYp9is2KrZhdin2LnZitipINmI2KfZhNit2YHZhNin2KonLFxuICAgIGNhdGVnb3J5OiAn2YHYs9in2KrZitmGINmD2YjZg9iq2YrZhCcsXG4gICAgcHJpY2U6IDU5OSxcbiAgICBpbWFnZXM6IFtcbiAgICAgICcvd2VkZGluZy1kcmVzcy0zLmpwZy5qcGcnLFxuICAgICAgJy93ZWRkaW5nLWRyZXNzLTNhLmpwZy5qcGcnLFxuICAgICAgJy93ZWRkaW5nLWRyZXNzLTNiLmpwZy5qcGcnXG4gICAgXSxcbiAgICBmYWJyaWM6ICfYtNmK2YHZiNmGINmF2LfYsdiyJyxcbiAgICBjb2xvcnM6IFsn2YjYsdiv2YonLCAn2KPYqNmK2LYnLCAn2KjZitisJ10sXG4gICAgc2l6ZXM6IFsnWFMnLCAnUycsICdNJywgJ0wnXSxcbiAgICBmZWF0dXJlczogW1xuICAgICAgJ9mC2LXYqSDZgti12YrYsdipINi52LXYsdmK2KknLFxuICAgICAgJ9iq2LfYsdmK2LIg2K/ZgtmK2YInLFxuICAgICAgJ9iq2LXZhdmK2YUg2YXYsdmK2K0nLFxuICAgICAgJ9mF2YbYp9iz2Kgg2YTZhNit2YHZhNin2KonXG4gICAgXSxcbiAgICBvY2Nhc2lvbnM6IFsn2K3ZgdmE2KfYqiDYp9mE2YPZiNmD2KrZitmEJywgJ9in2YTZhdmG2KfYs9io2KfYqiDYp9mE2KfYrNiq2YXYp9i52YrYqScsICfYp9mE2K3ZgdmE2KfYqiDYp9mE2YbZh9in2LHZitipJ10sXG4gICAgY2FyZV9pbnN0cnVjdGlvbnM6IFtcbiAgICAgICfZitmF2YPZhiDYutiz2YTZhyDYqNin2YTZhdin2KEg2KfZhNio2KfYsdivJyxcbiAgICAgICfYqtis2YHZitmBINmB2Yog2KfZhNmH2YjYp9ihJyxcbiAgICAgICfZg9mKINi52YTZiSDYr9ix2KzYqSDYrdix2KfYsdipINmF2KrZiNiz2LfYqSdcbiAgICBdLFxuICAgIHJhdGluZzogNC44LFxuICAgIHJldmlld3NfY291bnQ6IDE1NlxuICB9LFxuICB7XG4gICAgaWQ6IDQsXG4gICAgdGl0bGU6ICfZgdiz2KrYp9mGINmF2YbYp9iz2KjYp9iqINiu2KfYtdipJyxcbiAgICBkZXNjcmlwdGlvbjogJ9mB2LPYqtin2YYg2KPZhtmK2YIg2YTZhNmF2YbYp9iz2KjYp9iqINin2YTYrtin2LXYqSDYqNiq2LXZhdmK2YUg2LHYp9mC2Yog2YjYqtmB2KfYtdmK2YQg2YXZhdmK2LLYqScsXG4gICAgY2F0ZWdvcnk6ICfZgdiz2KfYqtmK2YYg2YXZhtin2LPYqNin2KonLFxuICAgIHByaWNlOiA3NDksXG4gICAgaW1hZ2VzOiBbXG4gICAgICAnL3dlZGRpbmctZHJlc3MtNC5qcGcuanBnJyxcbiAgICAgICcvd2VkZGluZy1kcmVzcy00YS5qcGcuanBnJyxcbiAgICAgICcvd2VkZGluZy1kcmVzcy00Yi5qcGcuanBnJ1xuICAgIF0sXG4gICAgZmFicmljOiAn2K3YsdmK2LEg2LfYqNmK2LnZiicsXG4gICAgY29sb3JzOiBbJ9ij2LLYsdmCJywgJ9ij2K7YttixJywgJ9io2YbZgdiz2KzZiiddLFxuICAgIHNpemVzOiBbJ1MnLCAnTScsICdMJywgJ1hMJ10sXG4gICAgZmVhdHVyZXM6IFtcbiAgICAgICfYrdix2YrYsSDYt9io2YrYudmKINmB2KfYrtixJyxcbiAgICAgICfYqti12YXZitmFINix2KfZgtmKJyxcbiAgICAgICfZgti12Kkg2YXYsdmK2K3YqScsXG4gICAgICAn2KrZgdin2LXZitmEINmF2YXZitiy2KknXG4gICAgXSxcbiAgICBvY2Nhc2lvbnM6IFsn2KfZhNmF2YbYp9iz2KjYp9iqINin2YTYrtin2LXYqScsICfYp9mE2K3ZgdmE2KfYqiDYp9mE2LnYp9im2YTZitipJywgJ9in2YTZhdmG2KfYs9io2KfYqiDYp9mE2LHYs9mF2YrYqSddLFxuICAgIGNhcmVfaW5zdHJ1Y3Rpb25zOiBbXG4gICAgICAn2KrZhti42YrZgSDYrNin2YEg2YHZgti3JyxcbiAgICAgICfYqtis2YbYqCDYo9i02LnYqSDYp9mE2LTZhdizINin2YTZhdio2KfYtNix2KknLFxuICAgICAgJ9iq2K7YstmK2YYg2YHZiiDZhdmD2KfZhiDYqNin2LHYryDZiNis2KfZgSdcbiAgICBdLFxuICAgIHJhdGluZzogNC42LFxuICAgIHJldmlld3NfY291bnQ6IDczXG4gIH0sXG4gIHtcbiAgICBpZDogNSxcbiAgICB0aXRsZTogJ9mB2LPYqtin2YYg2LLZgdin2YEg2YXZhNmD2YonLFxuICAgIGRlc2NyaXB0aW9uOiAn2YHYs9iq2KfZhiDYstmB2KfZgSDZgdin2K7YsSDYqNiq2LXZhdmK2YUg2YXZhNmD2Yog2YXYuSDYqtmG2YjYsdipINmI2KfYs9i52Kkg2YjYqti32LHZitiyINmD2LHZitiz2KrYp9mE2YonLFxuICAgIGNhdGVnb3J5OiAn2YHYs9in2KrZitmGINiy2YHYp9mBJyxcbiAgICBwcmljZTogMTU5OSxcbiAgICBpbWFnZXM6IFtcbiAgICAgICcvd2VkZGluZy1kcmVzcy01LmpwZy5qcGcnLFxuICAgICAgJy93ZWRkaW5nLWRyZXNzLTVhLmpwZy5qcGcnLFxuICAgICAgJy93ZWRkaW5nLWRyZXNzLTViLmpwZy5qcGcnXG4gICAgXSxcbiAgICBmYWJyaWM6ICfYqtmI2YQg2YjYs9in2KrYp9mGJyxcbiAgICBjb2xvcnM6IFsn2KPYqNmK2LYnLCAn2LnYp9is2YonXSxcbiAgICBzaXplczogWydYUycsICdTJywgJ00nLCAnTCcsICdYTCcsICdYWEwnXSxcbiAgICBmZWF0dXJlczogW1xuICAgICAgJ9iq2LXZhdmK2YUg2YXZhNmD2Yog2YHYp9iu2LEnLFxuICAgICAgJ9iq2LfYsdmK2LIg2YPYsdmK2LPYqtin2YTZiicsXG4gICAgICAn2KrZhtmI2LHYqSDZiNin2LPYudipJyxcbiAgICAgICfYsNmK2YQg2LfZiNmK2YQg2KPZhtmK2YInXG4gICAgXSxcbiAgICBvY2Nhc2lvbnM6IFsn2K3ZgdmE2KfYqiDYp9mE2LLZgdin2YEg2KfZhNmB2KfYrtix2KknLCAn2KfZhNmF2YbYp9iz2KjYp9iqINin2YTZhdmE2YPZitipJ10sXG4gICAgY2FyZV9pbnN0cnVjdGlvbnM6IFtcbiAgICAgICfYqtmG2LjZitmBINis2KfZgSDZhdiq2K7Ytdi1INmB2YLYtycsXG4gICAgICAn2KrYrtiy2YrZhiDZgdmKINmD2YrYsyDYrtin2LUnLFxuICAgICAgJ9iq2KzZhtioINin2YTYt9mKJ1xuICAgIF0sXG4gICAgcmF0aW5nOiA0LjksXG4gICAgcmV2aWV3c19jb3VudDogMjAxXG4gIH0sXG4gIHtcbiAgICBpZDogNixcbiAgICB0aXRsZTogJ9mB2LPYqtin2YYg2LPZh9ix2Kkg2YPZhNin2LPZitmD2YonLFxuICAgIGRlc2NyaXB0aW9uOiAn2YHYs9iq2KfZhiDYs9mH2LHYqSDYqNiq2LXZhdmK2YUg2YPZhNin2LPZitmD2Yog2K7Yp9mE2K8g2YXYuSDZhNmF2LPYp9iqINi52LXYsdmK2Kkg2KPZhtmK2YLYqScsXG4gICAgY2F0ZWdvcnk6ICfZgdiz2KfYqtmK2YYg2LPZh9ix2KknLFxuICAgIHByaWNlOiA3OTksXG4gICAgaW1hZ2VzOiBbXG4gICAgICAnL3dlZGRpbmctZHJlc3MtNi5qcGcuanBnJyxcbiAgICAgICcvd2VkZGluZy1kcmVzcy02YS5qcGcuanBnJyxcbiAgICAgICcvd2VkZGluZy1kcmVzcy02Yi5qcGcuanBnJ1xuICAgIF0sXG4gICAgZmFicmljOiAn2LTZitmB2YjZhiDZiNiz2KfYqtin2YYnLFxuICAgIGNvbG9yczogWyfYo9iz2YjYrycsICfYo9it2YXYsScsICfYo9iy2LHZgiDYr9in2YPZhiddLFxuICAgIHNpemVzOiBbJ1hTJywgJ1MnLCAnTScsICdMJywgJ1hMJ10sXG4gICAgZmVhdHVyZXM6IFtcbiAgICAgICfYqti12YXZitmFINmD2YTYp9iz2YrZg9mKJyxcbiAgICAgICfZhNmF2LPYp9iqINi52LXYsdmK2KknLFxuICAgICAgJ9mC2LXYqSDYo9mG2YrZgtipJyxcbiAgICAgICfYrtin2YXYp9iqINmB2KfYrtix2KknXG4gICAgXSxcbiAgICBvY2Nhc2lvbnM6IFsn2K3ZgdmE2KfYqiDYp9mE2LPZh9ix2KknLCAn2KfZhNmF2YbYp9iz2KjYp9iqINin2YTYsdiz2YXZitipJywgJ9in2YTYrdmB2YTYp9iqINin2YTYrtin2LXYqSddLFxuICAgIGNhcmVfaW5zdHJ1Y3Rpb25zOiBbXG4gICAgICAn2KrZhti42YrZgSDYrNin2YEnLFxuICAgICAgJ9iq2KzZhtioINin2YTZg9mKINin2YTZhdio2KfYtNixJyxcbiAgICAgICfYqtiu2LLZitmGINmF2LnZhNmCJ1xuICAgIF0sXG4gICAgcmF0aW5nOiA0LjUsXG4gICAgcmV2aWV3c19jb3VudDogOTRcbiAgfSxcbiAge1xuICAgIGlkOiA3LFxuICAgIHRpdGxlOiAn2YHYs9iq2KfZhiDZg9mI2YPYqtmK2YQg2LnYtdix2YonLFxuICAgIGRlc2NyaXB0aW9uOiAn2YHYs9iq2KfZhiDZg9mI2YPYqtmK2YQg2KjYqti12YXZitmFINi52LXYsdmKINis2LHZitihINmF2Lkg2KrZgdin2LXZitmEINmF2YXZitiy2Kkg2YjZgti12Kkg2LnZhdmE2YrYqScsXG4gICAgY2F0ZWdvcnk6ICfZgdiz2KfYqtmK2YYg2YPZiNmD2KrZitmEJyxcbiAgICBwcmljZTogNjQ5LFxuICAgIGltYWdlczogW1xuICAgICAgJy93ZWRkaW5nLWRyZXNzLTcuanBnLmpwZycsXG4gICAgICAnL3dlZGRpbmctZHJlc3MtN2EuanBnLmpwZycsXG4gICAgICAnL3dlZGRpbmctZHJlc3MtN2IuanBnLmpwZydcbiAgICBdLFxuICAgIGZhYnJpYzogJ9mD2LHZitioINmF2LfYp9i32YonLFxuICAgIGNvbG9yczogWyfYo9iz2YjYrycsICfYo9it2YXYsScsICfYo9iy2LHZgiddLFxuICAgIHNpemVzOiBbJ1hTJywgJ1MnLCAnTScsICdMJ10sXG4gICAgZmVhdHVyZXM6IFtcbiAgICAgICfYqti12YXZitmFINi52LXYsdmKJyxcbiAgICAgICfZgti12Kkg2LnZhdmE2YrYqScsXG4gICAgICAn2K7Yp9mF2Kkg2YXYsdmK2K3YqScsXG4gICAgICAn2KrZgdin2LXZitmEINmF2YXZitiy2KknXG4gICAgXSxcbiAgICBvY2Nhc2lvbnM6IFsn2K3ZgdmE2KfYqiDYp9mE2YPZiNmD2KrZitmEJywgJ9in2YTZhdmG2KfYs9io2KfYqiDYp9mE2KfYrNiq2YXYp9i52YrYqScsICfYp9mE2K3ZgdmE2KfYqiDYp9mE2YXYs9in2KbZitipJ10sXG4gICAgY2FyZV9pbnN0cnVjdGlvbnM6IFtcbiAgICAgICfYutiz2YrZhCDYqNin2YTZhdin2KEg2KfZhNio2KfYsdivJyxcbiAgICAgICfYqtis2YHZitmBINi32KjZiti52YonLFxuICAgICAgJ9mD2Yog2LnZhNmJINiv2LHYrNipINmF2YbYrtmB2LbYqSdcbiAgICBdLFxuICAgIHJhdGluZzogNC43LFxuICAgIHJldmlld3NfY291bnQ6IDExMlxuICB9LFxuICB7XG4gICAgaWQ6IDgsXG4gICAgdGl0bGU6ICfZgdiz2KrYp9mGINmF2YbYp9iz2KjYp9iqINix2KfZgtmKJyxcbiAgICBkZXNjcmlwdGlvbjogJ9mB2LPYqtin2YYg2LHYp9mC2Yog2YTZhNmF2YbYp9iz2KjYp9iqINin2YTYrtin2LXYqSDYqNiq2LXZhdmK2YUg2KPZhtmK2YIg2YjYqtmB2KfYtdmK2YQg2YHYp9iu2LHYqScsXG4gICAgY2F0ZWdvcnk6ICfZgdiz2KfYqtmK2YYg2YXZhtin2LPYqNin2KonLFxuICAgIHByaWNlOiA4NDksXG4gICAgaW1hZ2VzOiBbXG4gICAgICAnL3dlZGRpbmctZHJlc3MtOC5qcGcuanBnJyxcbiAgICAgICcvd2VkZGluZy1kcmVzcy04YS5qcGcuanBnJyxcbiAgICAgICcvd2VkZGluZy1kcmVzcy04Yi5qcGcuanBnJ1xuICAgIF0sXG4gICAgZmFicmljOiAn2LPYp9iq2KfZhiDZhdi32LHYsicsXG4gICAgY29sb3JzOiBbJ9iw2YfYqNmKJywgJ9mB2LbZiicsICfZiNix2K/ZiiddLFxuICAgIHNpemVzOiBbJ1MnLCAnTScsICdMJywgJ1hMJ10sXG4gICAgZmVhdHVyZXM6IFtcbiAgICAgICfYqti32LHZitiyINmB2KfYrtixJyxcbiAgICAgICfYqti12YXZitmFINix2KfZgtmKJyxcbiAgICAgICfYrtin2YXYp9iqINi52KfZhNmK2Kkg2KfZhNis2YjYr9ipJyxcbiAgICAgICfZgti12Kkg2YXZhdmK2LLYqSdcbiAgICBdLFxuICAgIG9jY2FzaW9uczogWyfYp9mE2YXZhtin2LPYqNin2Kog2KfZhNix2KfZgtmK2KknLCAn2KfZhNit2YHZhNin2Kog2KfZhNiu2KfYtdipJywgJ9in2YTZhdmG2KfYs9io2KfYqiDYp9mE2LHYs9mF2YrYqSddLFxuICAgIGNhcmVfaW5zdHJ1Y3Rpb25zOiBbXG4gICAgICAn2KrZhti42YrZgSDYrNin2YEg2YHZgti3JyxcbiAgICAgICfYqtis2YbYqCDYp9mE2LHYt9mI2KjYqScsXG4gICAgICAn2KrYrtiy2YrZhiDZgdmKINmF2YPYp9mGINis2KfZgSdcbiAgICBdLFxuICAgIHJhdGluZzogNC42LFxuICAgIHJldmlld3NfY291bnQ6IDY3XG4gIH0sXG4gIHtcbiAgICBpZDogOSxcbiAgICB0aXRsZTogJ9mB2LPYqtin2YYg2LLZgdin2YEg2LHZiNmF2KfZhtiz2YonLFxuICAgIGRlc2NyaXB0aW9uOiAn2YHYs9iq2KfZhiDYstmB2KfZgSDYsdmI2YXYp9mG2LPZiiDYqNiq2LXZhdmK2YUg2K3Yp9mE2YUg2YXYuSDYqtmB2KfYtdmK2YQg2YXZhiDYp9mE2K/Yp9mG2KrZitmEINmI2KfZhNmI2LHZiNivJyxcbiAgICBjYXRlZ29yeTogJ9mB2LPYp9iq2YrZhiDYstmB2KfZgScsXG4gICAgcHJpY2U6IDExOTksXG4gICAgaW1hZ2VzOiBbXG4gICAgICAnL3dlZGRpbmctZHJlc3MtOS5qcGcuanBnJyxcbiAgICAgICcvd2VkZGluZy1kcmVzcy05YS5qcGcuanBnJyxcbiAgICAgICcvd2VkZGluZy1kcmVzcy05Yi5qcGcuanBnJ1xuICAgIF0sXG4gICAgZmFicmljOiAn2K/Yp9mG2KrZitmEINmI2KrZiNmEJyxcbiAgICBjb2xvcnM6IFsn2KPYqNmK2LYnLCAn2YPYsdmK2YXZiicsICfZiNix2K/ZiiDZgdin2KrYrSddLFxuICAgIHNpemVzOiBbJ1hTJywgJ1MnLCAnTScsICdMJywgJ1hMJ10sXG4gICAgZmVhdHVyZXM6IFtcbiAgICAgICfYr9in2YbYqtmK2YQg2YHYsdmG2LPZiicsXG4gICAgICAn2KrYtdmF2YrZhSDYsdmI2YXYp9mG2LPZiicsXG4gICAgICAn2KrZgdin2LXZitmEINmI2LHZiNivJyxcbiAgICAgICfZgti12Kkg2K3Yp9mE2YXYqSdcbiAgICBdLFxuICAgIG9jY2FzaW9uczogWyfYrdmB2YTYp9iqINin2YTYstmB2KfZgScsICfYp9mE2YXZhtin2LPYqNin2Kog2KfZhNix2YjZhdin2YbYs9mK2KknXSxcbiAgICBjYXJlX2luc3RydWN0aW9uczogW1xuICAgICAgJ9iq2YbYuNmK2YEg2KzYp9mBINmF2KrYrti12LUnLFxuICAgICAgJ9iq2KzZhtioINin2YTYtNivJyxcbiAgICAgICfYqtiu2LLZitmGINmF2LPYt9itJ1xuICAgIF0sXG4gICAgcmF0aW5nOiA0LjgsXG4gICAgcmV2aWV3c19jb3VudDogMTQzXG4gIH0sXG4gIHtcbiAgICBpZDogMTAsXG4gICAgdGl0bGU6ICfZgdiz2KrYp9mGINiz2YfYsdipINmB2KfYrtixJyxcbiAgICBkZXNjcmlwdGlvbjogJ9mB2LPYqtin2YYg2LPZh9ix2Kkg2YHYp9iu2LEg2KjYqti12YXZitmFINmF2YXZitiyINmF2Lkg2KrYt9ix2YrYsiDZg9ix2YrYs9iq2KfZhNmKINmI2YLYtdipINij2YbZitmC2KknLFxuICAgIGNhdGVnb3J5OiAn2YHYs9in2KrZitmGINiz2YfYsdipJyxcbiAgICBwcmljZTogOTk5LFxuICAgIGltYWdlczogW1xuICAgICAgJy93ZWRkaW5nLWRyZXNzLTEwLmpwZy5qcGcnLFxuICAgICAgJy93ZWRkaW5nLWRyZXNzLTEwYS5qcGcuanBnJyxcbiAgICAgICcvd2VkZGluZy1kcmVzcy0xMGIuanBnLmpwZydcbiAgICBdLFxuICAgIGZhYnJpYzogJ9iz2KfYqtin2YYg2YjZg9ix2YrYs9iq2KfZhCcsXG4gICAgY29sb3JzOiBbJ9ij2LPZiNivJywgJ9ij2LLYsdmCINmF2YTZg9mKJywgJ9io2YjYsdis2YbYr9mKJ10sXG4gICAgc2l6ZXM6IFsnWFMnLCAnUycsICdNJywgJ0wnXSxcbiAgICBmZWF0dXJlczogW1xuICAgICAgJ9iq2LfYsdmK2LIg2YPYsdmK2LPYqtin2YTZiicsXG4gICAgICAn2KrYtdmF2YrZhSDZgdin2K7YsScsXG4gICAgICAn2YLYtdipINij2YbZitmC2KknLFxuICAgICAgJ9iu2KfZhdin2Kog2LHYp9mC2YrYqSdcbiAgICBdLFxuICAgIG9jY2FzaW9uczogWyfYrdmB2YTYp9iqINin2YTYs9mH2LHYqSDYp9mE2YHYp9iu2LHYqScsICfYp9mE2YXZhtin2LPYqNin2Kog2KfZhNix2KfZgtmK2KknXSxcbiAgICBjYXJlX2luc3RydWN0aW9uczogW1xuICAgICAgJ9iq2YbYuNmK2YEg2KzYp9mBINmB2YLYtycsXG4gICAgICAn2KrYrNmG2Kgg2KfZhNin2K3YqtmD2KfZgycsXG4gICAgICAn2KrYrtiy2YrZhiDZgdmKINmD2YrYsyDYrtin2LUnXG4gICAgXSxcbiAgICByYXRpbmc6IDQuNyxcbiAgICByZXZpZXdzX2NvdW50OiA5OFxuICB9LFxuICB7XG4gICAgaWQ6IDExLFxuICAgIHRpdGxlOiAn2YHYs9iq2KfZhiDZg9mI2YPYqtmK2YQg2YXZhdmK2LInLFxuICAgIGRlc2NyaXB0aW9uOiAn2YHYs9iq2KfZhiDZg9mI2YPYqtmK2YQg2YXZhdmK2LIg2KjYqti12YXZitmFINis2LHZitihINmI2KPZhNmI2KfZhiDYstin2YfZitipINmF2YbYp9iz2Kgg2YTZhNit2YHZhNin2KonLFxuICAgIGNhdGVnb3J5OiAn2YHYs9in2KrZitmGINmD2YjZg9iq2YrZhCcsXG4gICAgcHJpY2U6IDY5OSxcbiAgICBpbWFnZXM6IFtcbiAgICAgICcvd2VkZGluZy1kcmVzcy0xMS5qcGcuanBnJyxcbiAgICAgICcvd2VkZGluZy1kcmVzcy0xMWEuanBnLmpwZycsXG4gICAgICAnL3dlZGRpbmctZHJlc3MtMTFiLmpwZy5qcGcnXG4gICAgXSxcbiAgICBmYWJyaWM6ICfYtNmK2YHZiNmGINmF2YTZiNmGJyxcbiAgICBjb2xvcnM6IFsn2KPYrdmF2LEnLCAn2KPYrti22LEnLCAn2KPYstix2YIg2YHYp9iq2K0nXSxcbiAgICBzaXplczogWydYUycsICdTJywgJ00nLCAnTCddLFxuICAgIGZlYXR1cmVzOiBbXG4gICAgICAn2KPZhNmI2KfZhiDYstin2YfZitipJyxcbiAgICAgICfYqti12YXZitmFINis2LHZitihJyxcbiAgICAgICfZgti12Kkg2LnZhdmE2YrYqScsXG4gICAgICAn2YXZhtin2LPYqCDZhNmE2K3ZgdmE2KfYqidcbiAgICBdLFxuICAgIG9jY2FzaW9uczogWyfYrdmB2YTYp9iqINin2YTZg9mI2YPYqtmK2YQnLCAn2KfZhNit2YHZhNin2Kog2KfZhNi12YrZgdmK2KknLCAn2KfZhNmF2YbYp9iz2KjYp9iqINin2YTYp9is2KrZhdin2LnZitipJ10sXG4gICAgY2FyZV9pbnN0cnVjdGlvbnM6IFtcbiAgICAgICfYutiz2YrZhCDYqNin2YTZhdin2KEg2KfZhNio2KfYsdivJyxcbiAgICAgICfYqtis2YHZitmBINmB2Yog2KfZhNi42YQnLFxuICAgICAgJ9mD2Yog2KjYrdiw2LEnXG4gICAgXSxcbiAgICByYXRpbmc6IDQuNixcbiAgICByZXZpZXdzX2NvdW50OiAxMzRcbiAgfSxcbiAge1xuICAgIGlkOiAxMixcbiAgICB0aXRsZTogJ9mB2LPYqtin2YYg2YXZhtin2LPYqNin2Kog2YPZhNin2LPZitmD2YonLFxuICAgIGRlc2NyaXB0aW9uOiAn2YHYs9iq2KfZhiDZg9mE2KfYs9mK2YPZiiDZhNmE2YXZhtin2LPYqNin2Kog2KjYqti12YXZitmFINiu2KfZhNivINmI2KPZhtin2YLYqSDZhNinINiq2YbYqtmH2YonLFxuICAgIGNhdGVnb3J5OiAn2YHYs9in2KrZitmGINmF2YbYp9iz2KjYp9iqJyxcbiAgICBwcmljZTogNzk5LFxuICAgIGltYWdlczogW1xuICAgICAgJy93ZWRkaW5nLWRyZXNzLTEyLmpwZy5qcGcnLFxuICAgICAgJy93ZWRkaW5nLWRyZXNzLTEyYS5qcGcuanBnJyxcbiAgICAgICcvd2VkZGluZy1kcmVzcy0xMmIuanBnLmpwZydcbiAgICBdLFxuICAgIGZhYnJpYzogJ9it2LHZitixINmI2LPYp9iq2KfZhicsXG4gICAgY29sb3JzOiBbJ9ij2LPZiNivJywgJ9ij2LLYsdmCINiv2KfZg9mGJywgJ9io2YbZiiddLFxuICAgIHNpemVzOiBbJ1MnLCAnTScsICdMJywgJ1hMJ10sXG4gICAgZmVhdHVyZXM6IFtcbiAgICAgICfYqti12YXZitmFINmD2YTYp9iz2YrZg9mKJyxcbiAgICAgICfYo9mG2KfZgtipINiu2KfZhNiv2KknLFxuICAgICAgJ9iu2KfZhdin2Kog2YHYp9iu2LHYqScsXG4gICAgICAn2YLYtdipINmF2LHZitit2KknXG4gICAgXSxcbiAgICBvY2Nhc2lvbnM6IFsn2KfZhNmF2YbYp9iz2KjYp9iqINin2YTYsdiz2YXZitipJywgJ9in2YTYrdmB2YTYp9iqINin2YTZg9mE2KfYs9mK2YPZitipJywgJ9in2YTZhdmG2KfYs9io2KfYqiDYp9mE2LnYp9im2YTZitipJ10sXG4gICAgY2FyZV9pbnN0cnVjdGlvbnM6IFtcbiAgICAgICfYqtmG2LjZitmBINis2KfZgScsXG4gICAgICAn2KrYrNmG2Kgg2KPYtNi52Kkg2KfZhNi02YXYsycsXG4gICAgICAn2KrYrtiy2YrZhiDZhdi52YTZgidcbiAgICBdLFxuICAgIHJhdGluZzogNC40LFxuICAgIHJldmlld3NfY291bnQ6IDc2XG4gIH1cbl1cbiJdLCJuYW1lcyI6WyJhbGxEZXNpZ25zIiwiaWQiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiY2F0ZWdvcnkiLCJwcmljZSIsImltYWdlcyIsImZhYnJpYyIsImNvbG9ycyIsInNpemVzIiwiZmVhdHVyZXMiLCJvY2Nhc2lvbnMiLCJjYXJlX2luc3RydWN0aW9ucyIsInJhdGluZyIsInJldmlld3NfY291bnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/data/designs.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/shopStore.ts":
/*!********************************!*\
  !*** ./src/store/shopStore.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   generateWhatsAppMessage: () => (/* binding */ generateWhatsAppMessage),\n/* harmony export */   useShopStore: () => (/* binding */ useShopStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* __next_internal_client_entry_do_not_use__ useShopStore,formatPrice,generateWhatsAppMessage auto */ \n\n// إنشاء المتجر\nconst useShopStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // المفضلة\n        favorites: [],\n        addToFavorites: (product)=>{\n            const { favorites } = get();\n            if (!favorites.find((item)=>item.id === product.id)) {\n                set({\n                    favorites: [\n                        ...favorites,\n                        product\n                    ]\n                });\n            }\n        },\n        removeFromFavorites: (productId)=>{\n            const { favorites } = get();\n            set({\n                favorites: favorites.filter((item)=>item.id !== productId)\n            });\n        },\n        isFavorite: (productId)=>{\n            const { favorites } = get();\n            return favorites.some((item)=>item.id === productId);\n        },\n        clearFavorites: ()=>{\n            set({\n                favorites: []\n            });\n        },\n        // السلة\n        cart: [],\n        addToCart: (product, quantity = 1, size, color)=>{\n            const { cart } = get();\n            const existingItem = cart.find((item)=>item.id === product.id && item.selectedSize === size && item.selectedColor === color);\n            if (existingItem) {\n                // إذا كان المنتج موجود، زيادة الكمية\n                set({\n                    cart: cart.map((item)=>item.id === product.id && item.selectedSize === size && item.selectedColor === color ? {\n                            ...item,\n                            quantity: item.quantity + quantity\n                        } : item)\n                });\n            } else {\n                // إضافة منتج جديد\n                const newItem = {\n                    ...product,\n                    quantity,\n                    selectedSize: size,\n                    selectedColor: color\n                };\n                set({\n                    cart: [\n                        ...cart,\n                        newItem\n                    ]\n                });\n            }\n        },\n        removeFromCart: (productId)=>{\n            const { cart } = get();\n            set({\n                cart: cart.filter((item)=>item.id !== productId)\n            });\n        },\n        updateCartItemQuantity: (productId, quantity)=>{\n            const { cart } = get();\n            if (quantity <= 0) {\n                set({\n                    cart: cart.filter((item)=>item.id !== productId)\n                });\n            } else {\n                set({\n                    cart: cart.map((item)=>item.id === productId ? {\n                            ...item,\n                            quantity\n                        } : item)\n                });\n            }\n        },\n        clearCart: ()=>{\n            set({\n                cart: []\n            });\n        },\n        getCartTotal: ()=>{\n            const { cart } = get();\n            return cart.reduce((total, item)=>total + item.price * item.quantity, 0);\n        },\n        getCartItemsCount: ()=>{\n            const { cart } = get();\n            return cart.reduce((total, item)=>total + item.quantity, 0);\n        },\n        // حالة التحميل\n        isLoading: false,\n        setLoading: (loading)=>{\n            set({\n                isLoading: loading\n            });\n        }\n    }), {\n    name: 'yasmin-alsham-shop',\n    partialize: (state)=>({\n            favorites: state.favorites,\n            cart: state.cart\n        })\n}));\n// دالة مساعدة لتنسيق السعر\nconst formatPrice = (price)=>{\n    return `${price.toLocaleString('ar-SA')} ريال`;\n};\n// دالة مساعدة لإنشاء رسالة واتساب للسلة\nconst generateWhatsAppMessage = (cart)=>{\n    if (cart.length === 0) return '';\n    let message = '🌸 *طلب جديد من ياسمين الشام* 🌸\\n\\n';\n    message += '📋 *تفاصيل الطلب:*\\n';\n    cart.forEach((item, index)=>{\n        message += `\\n${index + 1}. *${item.name}*\\n`;\n        message += `   💰 السعر: ${formatPrice(item.price)}\\n`;\n        message += `   📦 الكمية: ${item.quantity}\\n`;\n        if (item.selectedSize) {\n            message += `   📏 المقاس: ${item.selectedSize}\\n`;\n        }\n        if (item.selectedColor) {\n            message += `   🎨 اللون: ${item.selectedColor}\\n`;\n        }\n        message += `   💵 المجموع الفرعي: ${formatPrice(item.price * item.quantity)}\\n`;\n    });\n    const total = cart.reduce((sum, item)=>sum + item.price * item.quantity, 0);\n    message += `\\n💰 *إجمالي الطلب: ${formatPrice(total)}*\\n\\n`;\n    message += '📞 يرجى التواصل معي لتأكيد الطلب وترتيب التسليم.\\n';\n    message += '🙏 شكراً لكم';\n    return encodeURIComponent(message);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/shopStore.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/zustand","vendor-chunks/motion-utils"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdesigns%2F%5Bid%5D%2Fpage&page=%2Fdesigns%2F%5Bid%5D%2Fpage&appPaths=%2Fdesigns%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fdesigns%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();