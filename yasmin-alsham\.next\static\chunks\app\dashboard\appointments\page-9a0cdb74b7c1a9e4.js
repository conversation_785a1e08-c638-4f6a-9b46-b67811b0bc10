(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[876],{1364:(e,t,r)=>{"use strict";r.d(t,{D:()=>l});var s=r(5453),a=r(6786);let n=()=>Date.now().toString(36)+Math.random().toString(36).substr(2),l=(0,s.v)()((0,a.Zr)((e,t)=>({appointments:[],orders:[],workers:[],isLoading:!1,error:null,addAppointment:t=>{let r={...t,id:n(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({appointments:[...e.appointments,r],error:null})),console.log("✅ تم إضافة موعد جديد:",r)},updateAppointment:(t,r)=>{e(e=>({appointments:e.appointments.map(e=>e.id===t?{...e,...r,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الموعد:",t)},deleteAppointment:t=>{e(e=>({appointments:e.appointments.filter(e=>e.id!==t),error:null})),console.log("✅ تم حذف الموعد:",t)},getAppointment:e=>t().appointments.find(t=>t.id===e),addOrder:t=>{let r={...t,id:n(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({orders:[...e.orders,r],error:null})),console.log("✅ تم إضافة طلب جديد:",r)},updateOrder:(t,r)=>{e(e=>({orders:e.orders.map(e=>e.id===t?{...e,...r,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الطلب:",t)},deleteOrder:t=>{e(e=>({orders:e.orders.filter(e=>e.id!==t),error:null})),console.log("✅ تم حذف الطلب:",t)},getOrder:e=>t().orders.find(t=>t.id===e),addWorker:t=>{let r={...t,id:n(),role:"worker",is_active:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({workers:[...e.workers,r],error:null}));{let e=JSON.parse(localStorage.getItem("yasmin-users")||"[]");e.push({id:r.id,email:r.email,password:r.password,full_name:r.full_name,role:"worker",is_active:!0}),localStorage.setItem("yasmin-users",JSON.stringify(e))}console.log("✅ تم إضافة عامل جديد:",r)},updateWorker:(t,r)=>{if(e(e=>({workers:e.workers.map(e=>e.id===t?{...e,...r,updatedAt:new Date().toISOString()}:e),error:null})),r.email||r.password||r.full_name){let e=JSON.parse(localStorage.getItem("yasmin-users")||"[]"),s=e.findIndex(e=>e.id===t);-1!==s&&(r.email&&(e[s].email=r.email),r.password&&(e[s].password=r.password),r.full_name&&(e[s].full_name=r.full_name),localStorage.setItem("yasmin-users",JSON.stringify(e)))}console.log("✅ تم تحديث العامل:",t)},deleteWorker:t=>{e(e=>({workers:e.workers.filter(e=>e.id!==t),error:null}));{let e=JSON.parse(localStorage.getItem("yasmin-users")||"[]").filter(e=>e.id!==t);localStorage.setItem("yasmin-users",JSON.stringify(e))}console.log("✅ تم حذف العامل:",t)},getWorker:e=>t().workers.find(t=>t.id===e),startOrderWork:(t,r)=>{e(e=>({orders:e.orders.map(e=>e.id===t&&e.assignedWorker===r?{...e,status:"in_progress",updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم بدء العمل في الطلب:",t)},completeOrder:function(t,r){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];e(e=>({orders:e.orders.map(e=>e.id===t&&e.assignedWorker===r?{...e,status:"completed",completedImages:s.length>0?s:void 0,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم إنهاء الطلب:",t)},clearError:()=>{e({error:null})},loadData:()=>{e({isLoading:!0}),e({isLoading:!1})},getStats:()=>{let e=t();return{totalAppointments:e.appointments.length,totalOrders:e.orders.length,totalWorkers:e.workers.length,pendingAppointments:e.appointments.filter(e=>"pending"===e.status).length,activeOrders:e.orders.filter(e=>["pending","in_progress"].includes(e.status)).length,completedOrders:e.orders.filter(e=>"completed"===e.status).length,totalRevenue:e.orders.filter(e=>"completed"===e.status).reduce((e,t)=>e+t.price,0)}}}),{name:"yasmin-data-storage",partialize:e=>({appointments:e.appointments,orders:e.orders,workers:e.workers})}))},1838:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var s=r(5155),a=r(2115),n=r(6408),l=r(5695),i=r(6874),o=r.n(i),d=r(3294),c=r(1364),m=r(9137),p=r(4186),u=r(646),g=r(4861),x=r(2138),h=r(4616),b=r(7924),y=r(6932),f=r(9074),v=r(9420);function j(){let{user:e}=(0,d.n)(),{appointments:t,updateAppointment:r,deleteAppointment:i}=(0,c.D)(),{t:j,isArabic:w}=(0,m.B)(),N=(0,l.useRouter)();(0,a.useEffect)(()=>{e?"admin"!==e.role&&N.push("/dashboard"):N.push("/login")},[e,N]);let[S,k]=(0,a.useState)(""),[_,D]=(0,a.useState)("all"),[O,A]=(0,a.useState)("all"),I=e=>{let t={pending:{label:j("pending"),color:"text-blue-600",bgColor:"bg-blue-100",icon:p.A},confirmed:{label:j("confirmed"),color:"text-green-600",bgColor:"bg-green-100",icon:u.A},completed:{label:j("completed"),color:"text-purple-600",bgColor:"bg-purple-100",icon:u.A},cancelled:{label:j("cancelled"),color:"text-red-600",bgColor:"bg-red-100",icon:g.A}};return t[e]||t.pending},L=e=>{r(e,{status:"confirmed"})},C=e=>{r(e,{status:"completed"})},J=e=>{r(e,{status:"cancelled"})},P=e=>new Date(e).toLocaleDateString("ar-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),W=e=>{let[t,r]=e.split(":"),s=parseInt(t),a=s>=12?j("pm"):j("am");return"".concat(s>12?s-12:0===s?12:s,":").concat(r," ").concat(a)},E=e=>e===new Date().toISOString().split("T")[0],T=e=>{let t=new Date;return t.setDate(t.getDate()+1),e===t.toISOString().split("T")[0]},U=t.filter(e=>{let t=e.clientName.toLowerCase().includes(S.toLowerCase())||e.clientPhone.includes(S)||e.id.toLowerCase().includes(S.toLowerCase()),r="all"===_||e.status===_,s=!0;if("today"===O)s=E(e.appointmentDate);else if("tomorrow"===O)s=T(e.appointmentDate);else if("week"===O){let t=new Date(e.appointmentDate),r=new Date,a=new Date;a.setDate(r.getDate()+7),s=t>=r&&t<=a}return t&&r&&s});return e?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-8",children:(0,s.jsxs)(o(),{href:"/dashboard",className:"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300",children:[(0,s.jsx)(x.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:j("back_to_dashboard")})]})}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold mb-2",children:(0,s.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent",children:j("appointments_management")})}),(0,s.jsx)("p",{className:"text-lg text-gray-600",children:j("view_manage_appointments")})]}),(0,s.jsxs)(o(),{href:"/book-appointment",className:"btn-primary inline-flex items-center justify-center space-x-2 space-x-reverse px-6 py-3 group",children:[(0,s.jsx)(h.A,{className:"w-5 h-5 group-hover:scale-110 transition-transform duration-300"}),(0,s.jsx)("span",{children:j("book_new_appointment")})]})]}),(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 mb-8",children:(0,s.jsxs)("div",{className:"grid md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(b.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,s.jsx)("input",{type:"text",value:S,onChange:e=>k(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:j("search_appointments_placeholder")})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(y.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,s.jsxs)("select",{value:_,onChange:e=>D(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",children:[(0,s.jsx)("option",{value:"all",children:j("all_statuses")}),(0,s.jsx)("option",{value:"pending",children:j("pending")}),(0,s.jsx)("option",{value:"confirmed",children:j("confirmed")}),(0,s.jsx)("option",{value:"completed",children:j("completed")}),(0,s.jsx)("option",{value:"cancelled",children:j("cancelled")})]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(f.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,s.jsxs)("select",{value:O,onChange:e=>A(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",children:[(0,s.jsx)("option",{value:"all",children:j("all_dates")}),(0,s.jsx)("option",{value:"today",children:j("today")}),(0,s.jsx)("option",{value:"tomorrow",children:j("tomorrow")}),(0,s.jsx)("option",{value:"week",children:j("this_week")})]})]})]})}),(0,s.jsx)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"space-y-6",children:0===U.length?(0,s.jsxs)("div",{className:"text-center py-12 bg-white/80 backdrop-blur-sm rounded-2xl border border-pink-100",children:[(0,s.jsx)(f.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-medium text-gray-600 mb-2",children:j("no_appointments")}),(0,s.jsx)("p",{className:"text-gray-500",children:j("no_appointments_found")})]}):U.map((e,t)=>(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*t},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border transition-all duration-300 hover:shadow-lg ".concat(E(e.appointmentDate)?"border-pink-300 bg-pink-50/50":"border-pink-100"),children:(0,s.jsxs)("div",{className:"grid lg:grid-cols-4 gap-6",children:[(0,s.jsxs)("div",{className:"lg:col-span-2",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-1",children:e.clientName}),(0,s.jsxs)("div",{className:"space-y-1 text-sm text-gray-600",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)(v.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.clientPhone})]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["#",e.id]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat(I(e.status).bgColor," ").concat(I(e.status).color),children:I(e.status).label}),E(e.appointmentDate)&&(0,s.jsx)("span",{className:"px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs font-medium",children:j("today")})]})]}),e.notes&&(0,s.jsx)("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:(0,s.jsx)("p",{className:"text-sm text-gray-700",children:e.notes})})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-1",children:j("date_time")}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse text-sm text-gray-600",children:[(0,s.jsx)(f.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:P(e.appointmentDate)})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse text-sm text-gray-600",children:[(0,s.jsx)(p.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:W(e.appointmentTime)})]})]})]}),(0,s.jsx)("div",{className:"flex items-center space-x-2 space-x-reverse",children:(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:[j("created_on")," ",new Date(e.createdAt).toLocaleDateString(w?"ar-US":"en-US")]})})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-2",children:["pending"===e.status&&(0,s.jsx)("button",{onClick:()=>L(e.id),className:"btn-primary py-2 px-4 text-sm",children:j("confirm_appointment")}),"confirmed"===e.status&&(0,s.jsx)("button",{onClick:()=>C(e.id),className:"btn-secondary py-2 px-4 text-sm",children:j("mark_attended")}),"cancelled"!==e.status&&"completed"!==e.status&&(0,s.jsx)("button",{onClick:()=>J(e.id),className:"text-red-600 hover:text-red-700 py-2 px-4 text-sm border border-red-200 rounded-lg hover:bg-red-50 transition-all duration-300",children:j("cancel_appointment")})]})]})},e.id))}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"mt-12 grid grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:t.filter(e=>"pending"===e.status).length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:j("pending")})]}),(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-1",children:t.filter(e=>"confirmed"===e.status).length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:j("confirmed")})]}),(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-orange-600 mb-1",children:t.filter(e=>E(e.appointmentDate)).length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:j("today")})]}),(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-purple-600 mb-1",children:t.filter(e=>"completed"===e.status).length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:j("completed")})]})]})]})}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:j("loading")})]})})}},3294:(e,t,r)=>{"use strict";r.d(t,{n:()=>l});var s=r(5453),a=r(6786);let n=()=>{let e=localStorage.getItem("yasmin-users");if(e)return JSON.parse(e);let t=[{id:"1",email:"<EMAIL>",password:"admin123",full_name:"مدير النظام",role:"admin",is_active:!0}];return localStorage.setItem("yasmin-users",JSON.stringify(t)),t},l=(0,s.v)()((0,a.Zr)((e,t)=>({user:null,isLoading:!1,error:null,signIn:async(t,r)=>{e({isLoading:!0,error:null});try{console.log("\uD83D\uDD10 بدء عملية تسجيل الدخول...",{email:t}),await new Promise(e=>setTimeout(e,1500));let s=n().find(e=>e.email.toLowerCase()===t.toLowerCase()&&e.password===r);if(!s)return console.log("❌ بيانات تسجيل الدخول غير صحيحة"),e({error:"بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور.",isLoading:!1}),!1;{console.log("✅ تم العثور على المستخدم:",s.full_name);let t={id:s.id,email:s.email,full_name:s.full_name,role:s.role,is_active:s.is_active,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),token:"demo-token-".concat(s.id,"-").concat(Date.now())};return localStorage.setItem("yasmin-auth-user",JSON.stringify(t)),console.log("\uD83D\uDCBE تم حفظ المستخدم في localStorage"),e({user:t,isLoading:!1,error:null}),console.log("\uD83C\uDF89 تم تسجيل الدخول بنجاح!"),!0}}catch(t){return console.error("\uD83D\uDCA5 خطأ في تسجيل الدخول:",t),e({error:"حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.",isLoading:!1}),!1}},signOut:async()=>{e({isLoading:!0});try{await new Promise(e=>setTimeout(e,500)),localStorage.removeItem("yasmin-auth-user"),e({user:null,isLoading:!1,error:null})}catch(t){console.error("خطأ في تسجيل الخروج:",t),e({isLoading:!1,error:"خطأ في تسجيل الخروج"})}},setUser:t=>{e({user:t}),t?localStorage.setItem("yasmin-auth-user",JSON.stringify(t)):localStorage.removeItem("yasmin-auth-user")},clearError:()=>{e({error:null})},checkAuth:async()=>{e({isLoading:!0});try{{let t=localStorage.getItem("yasmin-auth-user");if(t){let r=JSON.parse(t);e({user:r,isLoading:!1});return}}e({user:null,isLoading:!1})}catch(t){console.error("خطأ في التحقق من المصادقة:",t),e({user:null,isLoading:!1})}},isAuthenticated:()=>{let e=t();return null!==e.user&&e.user.is_active}}),{name:"yasmin-auth-storage",partialize:e=>({user:e.user})}))},4554:(e,t,r)=>{Promise.resolve().then(r.bind(r,1838))}},e=>{var t=t=>e(e.s=t);e.O(0,[165,874,480,518,441,684,358],()=>t(4554)),_N_E=e.O()}]);