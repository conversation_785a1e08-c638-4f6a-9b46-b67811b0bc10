"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[701],{1297:(e,r,t)=>{t.d(r,{A:()=>m});var a=t(5155),s=t(2115),n=t(6408),l=t(9588),i=t(1951),o=t(2178),d=t(5690),c=t(2525),u=t(9137);function m(e){let{voiceNotes:r=[],onVoiceNotesChange:t,disabled:m=!1}=e,[p,g]=(0,s.useState)(!1),[h,x]=(0,s.useState)(null),{t:f}=(0,u.B)(),[y,b]=(0,s.useState)(0),[v,w]=(0,s.useState)(null),[N,j]=(0,s.useState)(null),S=(0,s.useRef)(null),k=(0,s.useRef)(new Map),_=(0,s.useRef)(null),A=(0,s.useRef)([]),O=e=>{let r=atob(e.split(",")[1]),t=Array(r.length);for(let e=0;e<r.length;e++)t[e]=r.charCodeAt(e);return new Blob([new Uint8Array(t)],{type:"audio/webm"})},I=async()=>{try{j(null);let e=await navigator.mediaDevices.getUserMedia({audio:!0}),a=new MediaRecorder(e);S.current=a,A.current=[],a.ondataavailable=e=>{e.data.size>0&&A.current.push(e.data)},a.onstop=()=>{let a=new Blob(A.current,{type:"audio/webm"});w(a);let s=new FileReader;s.onloadend=()=>{let e=s.result,a={id:Date.now().toString(),data:e,timestamp:Date.now(),duration:y},n=[...r,a];t(n)},s.readAsDataURL(a),e.getTracks().forEach(e=>e.stop())},a.start(),g(!0),b(0),_.current=setInterval(()=>{b(e=>e+1)},1e3)}catch(e){console.error("خطأ في بدء التسجيل:",e),j(f("microphone_access_error"))}},D=e=>{let r=k.current;if(h&&h!==e.id){let e=r.get(h);e&&e.pause()}let t=r.get(e.id);if(!t){let a=O(e.data);(t=new Audio(URL.createObjectURL(a))).onended=()=>x(null),r.set(e.id,t)}h===e.id?(t.pause(),x(null)):(t.play(),x(e.id))},L=e=>{let a=k.current,s=a.get(e);s&&(s.pause(),a.delete(e)),h===e&&x(null),t(r.filter(r=>r.id!==e))},C=e=>{let r=Math.floor(e/60);return"".concat(r,":").concat((e%60).toString().padStart(2,"0"))},R=e=>new Date(e).toLocaleString("ar-SA",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return(0,s.useEffect)(()=>()=>{_.current&&clearInterval(_.current);let e=k.current;e.forEach(e=>e.pause()),e.clear()},[]),(0,a.jsxs)("div",{className:"space-y-4",children:[N&&(0,a.jsx)("div",{className:"p-3 bg-red-50 text-red-800 border border-red-200 rounded-lg text-sm",children:N}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 border border-gray-200",children:p?(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)(n.P.div,{animate:{scale:[1,1.1,1]},transition:{duration:1,repeat:1/0},className:"inline-flex items-center space-x-2 space-x-reverse mb-4",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-red-600 font-medium",children:"جاري التسجيل..."})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-800 mb-4",children:C(y)}),(0,a.jsxs)("button",{type:"button",onClick:()=>{S.current&&p&&(S.current.stop(),g(!1),_.current&&(clearInterval(_.current),_.current=null))},className:"inline-flex items-center space-x-2 space-x-reverse px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-300",children:[(0,a.jsx)(i.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:f("stop_recording")})]})]}):(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("button",{type:"button",onClick:I,disabled:m,className:"inline-flex items-center space-x-2 space-x-reverse px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)(l.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:f("start_recording")})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:f("click_to_record_voice_note")})]})}),r.length>0&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-700",children:[f("voice_notes")," (",r.length,")"]})}),r.map((e,r)=>(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*r},className:"bg-white rounded-lg p-4 border border-gray-200 shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,a.jsx)("button",{type:"button",onClick:()=>D(e),className:"p-2 bg-pink-600 text-white rounded-full hover:bg-pink-700 transition-colors duration-300",children:h===e.id?(0,a.jsx)(o.A,{className:"w-4 h-4"}):(0,a.jsx)(d.A,{className:"w-4 h-4"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-sm font-medium text-gray-800",children:[f("voice_note")," #",r+1]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[R(e.timestamp),e.duration&&" • ".concat(C(e.duration))]})]})]}),(0,a.jsx)("button",{type:"button",onClick:()=>L(e.id),disabled:m,className:"p-2 text-red-600 hover:bg-red-50 rounded-full transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed",title:f("delete"),children:(0,a.jsx)(c.A,{className:"w-4 h-4"})})]})},e.id))]})]})}},1364:(e,r,t)=>{t.d(r,{D:()=>l});var a=t(5453),s=t(6786);let n=()=>Date.now().toString(36)+Math.random().toString(36).substr(2),l=(0,a.v)()((0,s.Zr)((e,r)=>({appointments:[],orders:[],workers:[],isLoading:!1,error:null,addAppointment:r=>{let t={...r,id:n(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({appointments:[...e.appointments,t],error:null})),console.log("✅ تم إضافة موعد جديد:",t)},updateAppointment:(r,t)=>{e(e=>({appointments:e.appointments.map(e=>e.id===r?{...e,...t,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الموعد:",r)},deleteAppointment:r=>{e(e=>({appointments:e.appointments.filter(e=>e.id!==r),error:null})),console.log("✅ تم حذف الموعد:",r)},getAppointment:e=>r().appointments.find(r=>r.id===e),addOrder:r=>{let t={...r,id:n(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({orders:[...e.orders,t],error:null})),console.log("✅ تم إضافة طلب جديد:",t)},updateOrder:(r,t)=>{e(e=>({orders:e.orders.map(e=>e.id===r?{...e,...t,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الطلب:",r)},deleteOrder:r=>{e(e=>({orders:e.orders.filter(e=>e.id!==r),error:null})),console.log("✅ تم حذف الطلب:",r)},getOrder:e=>r().orders.find(r=>r.id===e),addWorker:r=>{let t={...r,id:n(),role:"worker",is_active:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({workers:[...e.workers,t],error:null}));{let e=JSON.parse(localStorage.getItem("yasmin-users")||"[]");e.push({id:t.id,email:t.email,password:t.password,full_name:t.full_name,role:"worker",is_active:!0}),localStorage.setItem("yasmin-users",JSON.stringify(e))}console.log("✅ تم إضافة عامل جديد:",t)},updateWorker:(r,t)=>{if(e(e=>({workers:e.workers.map(e=>e.id===r?{...e,...t,updatedAt:new Date().toISOString()}:e),error:null})),t.email||t.password||t.full_name){let e=JSON.parse(localStorage.getItem("yasmin-users")||"[]"),a=e.findIndex(e=>e.id===r);-1!==a&&(t.email&&(e[a].email=t.email),t.password&&(e[a].password=t.password),t.full_name&&(e[a].full_name=t.full_name),localStorage.setItem("yasmin-users",JSON.stringify(e)))}console.log("✅ تم تحديث العامل:",r)},deleteWorker:r=>{e(e=>({workers:e.workers.filter(e=>e.id!==r),error:null}));{let e=JSON.parse(localStorage.getItem("yasmin-users")||"[]").filter(e=>e.id!==r);localStorage.setItem("yasmin-users",JSON.stringify(e))}console.log("✅ تم حذف العامل:",r)},getWorker:e=>r().workers.find(r=>r.id===e),startOrderWork:(r,t)=>{e(e=>({orders:e.orders.map(e=>e.id===r&&e.assignedWorker===t?{...e,status:"in_progress",updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم بدء العمل في الطلب:",r)},completeOrder:function(r,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];e(e=>({orders:e.orders.map(e=>e.id===r&&e.assignedWorker===t?{...e,status:"completed",completedImages:a.length>0?a:void 0,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم إنهاء الطلب:",r)},clearError:()=>{e({error:null})},loadData:()=>{e({isLoading:!0}),e({isLoading:!1})},getStats:()=>{let e=r();return{totalAppointments:e.appointments.length,totalOrders:e.orders.length,totalWorkers:e.workers.length,pendingAppointments:e.appointments.filter(e=>"pending"===e.status).length,activeOrders:e.orders.filter(e=>["pending","in_progress"].includes(e.status)).length,completedOrders:e.orders.filter(e=>"completed"===e.status).length,totalRevenue:e.orders.filter(e=>"completed"===e.status).reduce((e,r)=>e+r.price,0)}}}),{name:"yasmin-data-storage",partialize:e=>({appointments:e.appointments,orders:e.orders,workers:e.workers})}))},2642:(e,r,t)=>{t.d(r,{A:()=>x});var a=t(5155),s=t(2115),n=t(5339);let l=e=>/^\d*\.?\d*$/.test(e),i=e=>/^\d*$/.test(e),o=e=>/^(\+)?\d*$/.test(e),d=e=>e.replace(/[^\d.]/g,""),c=e=>e.replace(/[^\d]/g,""),u=e=>e.startsWith("+")?"+"+e.slice(1).replace(/[^\d]/g,""):e.replace(/[^\d]/g,""),m=e=>{let r=parseFloat(e);return!isNaN(r)&&r>0},p=e=>{let r=parseFloat(e);return!isNaN(r)&&r>0},g=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"ar",t={ar:{measurement:"يرجى إدخال رقم صحيح للمقاس",price:"يرجى إدخال سعر صحيح",phone:"يرجى إدخال رقم هاتف صحيح",orderNumber:"يرجى إدخال رقم طلب صحيح",numeric:"يرجى إدخال أرقام فقط",positive:"يرجى إدخال رقم أكبر من الصفر"},en:{measurement:"Please enter a valid measurement",price:"Please enter a valid price",phone:"Please enter a valid phone number",orderNumber:"Please enter a valid order number",numeric:"Please enter numbers only",positive:"Please enter a number greater than zero"}};return t[r][e]||t[r].numeric},h=(e,r,t,a)=>{let s=e,n=!0,h=null;switch(r){case"measurement":m(s=d(e))||""===s||""===s||(h=g("measurement"));break;case"price":p(s=d(e))||""===s||""===s||(h=g("price"));break;case"phone":o(s=u(e))||(h=g("phone"));break;case"orderNumber":i(s=c(e))||(h=g("orderNumber"));break;case"integer":i(s=c(e))||(h=g("numeric"));break;case"decimal":l(s=d(e))||(h=g("numeric"))}t(s),a&&a(h)};function x(e){let{value:r,onChange:t,type:l,placeholder:i,className:o="",disabled:d=!1,required:c=!1,label:u,id:m}=e,[p,g]=(0,s.useState)(null),x="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors duration-200 ".concat(p?"border-red-500 bg-red-50":"border-gray-300"," ").concat(d?"bg-gray-100 cursor-not-allowed":""," ").concat(o);return(0,a.jsxs)("div",{className:"space-y-2",children:[u&&(0,a.jsxs)("label",{htmlFor:m,className:"block text-sm font-medium text-gray-700",children:[u,c&&(0,a.jsx)("span",{className:"text-red-500 mr-1",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{id:m,type:"text",value:r,onChange:e=>{h(e.target.value,l,t,g)},placeholder:i,className:x,disabled:d,required:c,inputMode:"phone"===l?"tel":"numeric",autoComplete:"phone"===l?"tel":"off"}),p&&(0,a.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2",children:(0,a.jsx)(n.A,{className:"w-5 h-5 text-red-500"})})]}),p&&(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center space-x-1 space-x-reverse",children:[(0,a.jsx)(n.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:p})]})]})}},3294:(e,r,t)=>{t.d(r,{n:()=>l});var a=t(5453),s=t(6786);let n=()=>{let e=localStorage.getItem("yasmin-users");if(e)return JSON.parse(e);let r=[{id:"1",email:"<EMAIL>",password:"admin123",full_name:"مدير النظام",role:"admin",is_active:!0}];return localStorage.setItem("yasmin-users",JSON.stringify(r)),r},l=(0,a.v)()((0,s.Zr)((e,r)=>({user:null,isLoading:!1,error:null,signIn:async(r,t)=>{e({isLoading:!0,error:null});try{console.log("\uD83D\uDD10 بدء عملية تسجيل الدخول...",{email:r}),await new Promise(e=>setTimeout(e,1500));let a=n().find(e=>e.email.toLowerCase()===r.toLowerCase()&&e.password===t);if(!a)return console.log("❌ بيانات تسجيل الدخول غير صحيحة"),e({error:"بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور.",isLoading:!1}),!1;{console.log("✅ تم العثور على المستخدم:",a.full_name);let r={id:a.id,email:a.email,full_name:a.full_name,role:a.role,is_active:a.is_active,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),token:"demo-token-".concat(a.id,"-").concat(Date.now())};return localStorage.setItem("yasmin-auth-user",JSON.stringify(r)),console.log("\uD83D\uDCBE تم حفظ المستخدم في localStorage"),e({user:r,isLoading:!1,error:null}),console.log("\uD83C\uDF89 تم تسجيل الدخول بنجاح!"),!0}}catch(r){return console.error("\uD83D\uDCA5 خطأ في تسجيل الدخول:",r),e({error:"حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.",isLoading:!1}),!1}},signOut:async()=>{e({isLoading:!0});try{await new Promise(e=>setTimeout(e,500)),localStorage.removeItem("yasmin-auth-user"),e({user:null,isLoading:!1,error:null})}catch(r){console.error("خطأ في تسجيل الخروج:",r),e({isLoading:!1,error:"خطأ في تسجيل الخروج"})}},setUser:r=>{e({user:r}),r?localStorage.setItem("yasmin-auth-user",JSON.stringify(r)):localStorage.removeItem("yasmin-auth-user")},clearError:()=>{e({error:null})},checkAuth:async()=>{e({isLoading:!0});try{{let r=localStorage.getItem("yasmin-auth-user");if(r){let t=JSON.parse(r);e({user:t,isLoading:!1});return}}e({user:null,isLoading:!1})}catch(r){console.error("خطأ في التحقق من المصادقة:",r),e({user:null,isLoading:!1})}},isAuthenticated:()=>{let e=r();return null!==e.user&&e.user.is_active}}),{name:"yasmin-auth-storage",partialize:e=>({user:e.user})}))},5552:(e,r,t)=>{t.d(r,{A:()=>m});var a=t(5155),s=t(2115),n=t(760),l=t(6408),i=t(7213),o=t(9869),d=t(4416),c=t(4616),u=t(9137);function m(e){let{images:r,onImagesChange:t,maxImages:m=10}=e,[p,g]=(0,s.useState)(!1),h=(0,s.useRef)(null),{t:x}=(0,u.B)(),f=e=>{if(!e)return;let a=[],s=m-r.length;Array.from(e).slice(0,s).forEach(n=>{if(n.type.startsWith("image/")){let l=new FileReader;l.onload=n=>{var l;let i=null==(l=n.target)?void 0:l.result;i&&(a.push(i),a.length===Math.min(e.length,s)&&t([...r,...a]))},l.readAsDataURL(n)}})},y=e=>{t(r.filter((r,t)=>t!==e))},b=()=>{var e;null==(e=h.current)||e.click()};return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"border-2 border-dashed rounded-lg p-6 text-center transition-all duration-300 cursor-pointer ".concat(p?"border-pink-400 bg-pink-50":r.length>=m?"border-gray-200 bg-gray-50 cursor-not-allowed":"border-gray-300 hover:border-pink-400 hover:bg-pink-50"),onDrop:e=>{e.preventDefault(),g(!1),f(e.dataTransfer.files)},onDragOver:e=>{e.preventDefault(),g(!0)},onDragLeave:e=>{e.preventDefault(),g(!1)},onClick:r.length<m?b:void 0,children:[(0,a.jsx)("input",{ref:h,type:"file",accept:"image/*",multiple:!0,onChange:e=>f(e.target.files),className:"hidden",disabled:r.length>=m}),r.length>=m?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.A,{className:"w-12 h-12 text-gray-400 mx-auto"}),(0,a.jsxs)("p",{className:"text-gray-500",children:[x("max_images_reached")," (",m,")"]})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(o.A,{className:"w-12 h-12 text-gray-400 mx-auto"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-medium text-gray-700",children:x(p?"drop_images_here":"click_or_drag_images")}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[x("image_upload_format")," (",x("max_images_text")," ",m," ",x("images_text"),")"]}),(0,a.jsxs)("p",{className:"text-xs text-gray-400 mt-1",children:[r.length," ",x("of")," ",m," ",x("images_text")]})]})]})]}),r.length>0&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-700",children:"الصور المرفوعة:"}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4",children:(0,a.jsxs)(n.N,{children:[r.map((e,r)=>(0,a.jsxs)(l.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:"relative group",children:[(0,a.jsx)("div",{className:"aspect-square rounded-lg overflow-hidden border border-gray-200",children:(0,a.jsx)("img",{src:e,alt:"صورة ".concat(r+1),className:"w-full h-full object-cover"})}),(0,a.jsx)("button",{onClick:()=>y(r),className:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-red-600",children:(0,a.jsx)(d.A,{className:"w-3 h-3"})}),(0,a.jsx)("div",{className:"absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded",children:r+1})]},r)),r.length<m&&(0,a.jsx)(l.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},className:"aspect-square border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center cursor-pointer hover:border-pink-400 hover:bg-pink-50 transition-all duration-300",onClick:b,children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(c.A,{className:"w-8 h-8 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:x("add_image")})]})})]})})]})]})}}}]);