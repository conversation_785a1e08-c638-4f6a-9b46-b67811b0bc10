"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/designs/page",{

/***/ "(app-pages-browser)/./src/app/designs/page.tsx":
/*!**********************************!*\
  !*** ./src/app/designs/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DesignsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-2x2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _data_designs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/designs */ \"(app-pages-browser)/./src/data/designs.ts\");\n/* harmony import */ var _store_shopStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/shopStore */ \"(app-pages-browser)/./src/store/shopStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DesignsPage() {\n    _s();\n    const [selectedImageIndex, setSelectedImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGalleryOpen, setIsGalleryOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentImageIndexes, setCurrentImageIndexes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        1: 0,\n        2: 0,\n        3: 0,\n        4: 0,\n        5: 0,\n        6: 0,\n        7: 0,\n        8: 0,\n        9: 0,\n        10: 0,\n        11: 0,\n        12: 0\n    });\n    // حالة عرض البطاقات للهواتف المحمولة\n    const [isSingleColumn, setIsSingleColumn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // متجر التسوق\n    const { addToFavorites, removeFromFavorites, isFavorite, addToCart } = (0,_store_shopStore__WEBPACK_IMPORTED_MODULE_4__.useShopStore)();\n    const [addedToCart, setAddedToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // تحميل حالة العرض من localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DesignsPage.useEffect\": ()=>{\n            const savedViewMode = localStorage.getItem('yasmin-designs-view-mode');\n            if (savedViewMode === 'single') {\n                setIsSingleColumn(true);\n            }\n        }\n    }[\"DesignsPage.useEffect\"], []);\n    // حفظ حالة العرض في localStorage\n    const toggleViewMode = ()=>{\n        const newMode = !isSingleColumn;\n        setIsSingleColumn(newMode);\n        localStorage.setItem('yasmin-designs-view-mode', newMode ? 'single' : 'double');\n    };\n    // دوال التعامل مع المفضلة والسلة\n    const handleToggleFavorite = (design, e)=>{\n        e.stopPropagation();\n        const product = {\n            id: design.id.toString(),\n            name: design.title,\n            price: design.price || 299,\n            image: design.images[0],\n            description: design.description,\n            category: design.category\n        };\n        if (isFavorite(product.id)) {\n            removeFromFavorites(product.id);\n        } else {\n            addToFavorites(product);\n        }\n    };\n    const handleAddToCart = (design, e)=>{\n        e.stopPropagation();\n        const product = {\n            id: design.id.toString(),\n            name: design.title,\n            price: design.price || 299,\n            image: design.images[0],\n            description: design.description,\n            category: design.category\n        };\n        addToCart(product);\n        setAddedToCart((prev)=>[\n                ...prev,\n                design.id\n            ]);\n        setTimeout(()=>{\n            setAddedToCart((prev)=>prev.filter((id)=>id !== design.id));\n        }, 2000);\n    };\n    // دوال التنقل بين صور البطاقة\n    const nextCardImage = (designId, e)=>{\n        e.stopPropagation();\n        setCurrentImageIndexes((prev)=>({\n                ...prev,\n                [designId]: (prev[designId] + 1) % 3\n            }));\n    };\n    const prevCardImage = (designId, e)=>{\n        e.stopPropagation();\n        setCurrentImageIndexes((prev)=>({\n                ...prev,\n                [designId]: prev[designId] === 0 ? 2 : prev[designId] - 1\n            }));\n    };\n    const setCardImage = (designId, imageIndex, e)=>{\n        e.stopPropagation();\n        setCurrentImageIndexes((prev)=>({\n                ...prev,\n                [designId]: imageIndex\n            }));\n    };\n    // دوال إدارة المعرض\n    const openGallery = (index)=>{\n        setSelectedImageIndex(index);\n        setIsGalleryOpen(true);\n        document.body.style.overflow = 'hidden';\n    };\n    const closeGallery = ()=>{\n        setIsGalleryOpen(false);\n        setSelectedImageIndex(null);\n        document.body.style.overflow = 'unset';\n    };\n    const nextImage = ()=>{\n        if (selectedImageIndex !== null) {\n            const designId = _data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].id;\n            setCurrentImageIndexes((prev)=>({\n                    ...prev,\n                    [designId]: (prev[designId] + 1) % 3\n                }));\n        }\n    };\n    const prevImage = ()=>{\n        if (selectedImageIndex !== null) {\n            const designId = _data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].id;\n            setCurrentImageIndexes((prev)=>({\n                    ...prev,\n                    [designId]: prev[designId] === 0 ? 2 : prev[designId] - 1\n                }));\n        }\n    };\n    // إدارة مفتاح Escape\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DesignsPage.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"DesignsPage.useEffect.handleKeyDown\": (event)=>{\n                    if (event.key === 'Escape' && isGalleryOpen) {\n                        closeGallery();\n                    }\n                    if (event.key === 'ArrowRight' && isGalleryOpen) {\n                        nextImage();\n                    }\n                    if (event.key === 'ArrowLeft' && isGalleryOpen) {\n                        prevImage();\n                    }\n                }\n            }[\"DesignsPage.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"DesignsPage.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleKeyDown);\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"DesignsPage.useEffect\"];\n        }\n    }[\"DesignsPage.useEffect\"], [\n        isGalleryOpen,\n        selectedImageIndex\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"العودة إلى الرئيسية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl sm:text-5xl lg:text-6xl font-bold mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                                children: \"تصاميمنا الجاهزة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed mb-6\",\n                            children: \"استكشفي مجموعتنا الكاملة من التصاميم الجاهزة واختاري ما يناسب ذوقك ومناسبتك\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 max-w-2xl mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-800 font-medium text-center\",\n                                children: \"✨ الفساتين الجاهزة متوفرة للشراء المباشر - لا يتطلب حجز موعد\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.2\n                    },\n                    className: \"sm:hidden mb-6 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleViewMode,\n                        className: \"bg-white/80 backdrop-blur-sm border border-pink-200 rounded-xl p-3 flex items-center space-x-2 space-x-reverse hover:bg-white hover:shadow-lg transition-all duration-300\",\n                        \"aria-label\": isSingleColumn ? 'تبديل إلى العرض الثنائي' : 'تبديل إلى العرض الفردي',\n                        children: isSingleColumn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5 text-pink-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-700\",\n                                    children: \"عرض ثنائي\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5 text-pink-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-700\",\n                                    children: \"عرض فردي\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-8 mb-12 \".concat(isSingleColumn ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'),\n                    children: _data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns.map((design, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            className: \"group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-[4/5] bg-gradient-to-br from-pink-100 via-rose-100 to-purple-100 relative overflow-hidden cursor-pointer\",\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            openGallery(index);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: design.images[currentImageIndexes[design.id]],\n                                                alt: \"\".concat(design.title, \" - صورة \").concat(currentImageIndexes[design.id] + 1),\n                                                className: \"w-full h-full object-cover transition-opacity duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>prevCardImage(design.id, e),\n                                                className: \"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-all duration-300 z-10\",\n                                                \"aria-label\": \"الصورة السابقة\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>nextCardImage(design.id, e),\n                                                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-all duration-300 z-10\",\n                                                \"aria-label\": \"الصورة التالية\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1 space-x-reverse opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                children: design.images.map((_, imgIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>setCardImage(design.id, imgIndex, e),\n                                                        className: \"w-2 h-2 rounded-full transition-colors duration-300 \".concat(currentImageIndexes[design.id] === imgIndex ? 'bg-white' : 'bg-white/50'),\n                                                        \"aria-label\": \"عرض الصورة \".concat(imgIndex + 1)\n                                                    }, imgIndex, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white/90 rounded-full p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-6 h-6 text-pink-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>handleToggleFavorite(design, e),\n                                                className: \"absolute top-3 left-3 rounded-full p-2 opacity-0 group-hover:opacity-100 transition-all duration-300 hover:scale-110 \".concat(isFavorite(design.id.toString()) ? 'bg-red-500 text-white' : 'bg-white/80 hover:bg-white text-pink-500'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4 \".concat(isFavorite(design.id.toString()) ? 'fill-current' : '')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/designs/\".concat(design.id),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"cursor-pointer hover:bg-gray-50 transition-colors duration-300 p-2 -m-2 rounded-lg mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-gradient-to-r from-pink-100 to-rose-100 text-pink-700 px-2 py-1 rounded-full text-xs font-medium\",\n                                                                children: design.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-bold text-gray-800 mb-2 group-hover:text-pink-600 transition-colors duration-300\",\n                                                            children: design.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                            children: design.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"https://wa.me/+966598862609?text=أريد استفسار عن \".concat(design.title),\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"w-full bg-gradient-to-r from-pink-500 to-purple-500 text-white py-2 px-3 rounded-lg text-sm font-medium hover:shadow-lg transition-all duration-300 text-center\",\n                                                    children: \"استفسار واتساب\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, this)\n                        }, design.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this),\n                isGalleryOpen && selectedImageIndex !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4\",\n                    onClick: closeGallery,\n                    role: \"dialog\",\n                    \"aria-modal\": \"true\",\n                    \"aria-labelledby\": \"gallery-title\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-4xl w-full\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: closeGallery,\n                                className: \"absolute top-4 right-4 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-2 transition-colors duration-300\",\n                                \"aria-label\": \"إغلاق المعرض\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: prevImage,\n                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-colors duration-300\",\n                                \"aria-label\": \"الصورة السابقة\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: nextImage,\n                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-colors duration-300\",\n                                \"aria-label\": \"الصورة التالية\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                className: \"bg-white rounded-2xl overflow-hidden shadow-2xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-[4/5] bg-gradient-to-br from-pink-100 via-rose-100 to-purple-100 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: _data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].images[currentImageIndexes[_data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].id]],\n                                        alt: \"\".concat(_data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].title, \" - صورة \").concat(currentImageIndexes[_data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].id] + 1),\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 17\n                                }, this)\n                            }, selectedImageIndex, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                    lineNumber: 354,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n            lineNumber: 161,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n_s(DesignsPage, \"Jo//TfkgCnar91prmNOYQwjQkbg=\", false, function() {\n    return [\n        _store_shopStore__WEBPACK_IMPORTED_MODULE_4__.useShopStore\n    ];\n});\n_c = DesignsPage;\nvar _c;\n$RefreshReg$(_c, \"DesignsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/designs/page.tsx\n"));

/***/ })

});