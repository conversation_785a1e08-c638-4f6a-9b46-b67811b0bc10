(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[611],{569:(e,s,a)=>{Promise.resolve().then(a.bind(a,3590))},1243:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},2657:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3590:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>V});var t=a(5155),r=a(2115),l=a(6408),n=a(5695),i=a(6874),c=a.n(i),d=a(3294),o=a(1364),x=a(9137),m=a(760),h=a(4416),p=a(1007),g=a(7108),u=a(9074),b=a(4186),j=a(5670),y=a(6140),v=a(7213),N=a(1497),f=a(646),k=a(1297);function w(e){let{order:s,workers:a,isOpen:r,onClose:n}=e,{user:i}=(0,d.n)(),{t:c,isArabic:o}=(0,x.B)();if(!s)return null;let w=e=>{let s={pending:{label:c("pending"),color:"text-yellow-600",bgColor:"bg-yellow-100"},in_progress:{label:c("in_progress"),color:"text-blue-600",bgColor:"bg-blue-100"},completed:{label:c("completed"),color:"text-green-600",bgColor:"bg-green-100"},delivered:{label:c("delivered"),color:"text-purple-600",bgColor:"bg-purple-100"},cancelled:{label:c("cancelled"),color:"text-red-600",bgColor:"bg-red-100"}};return s[e]||s.pending},_=e=>new Date(e).toLocaleDateString("ar-US",{year:"numeric",month:"long",day:"numeric"});return(0,t.jsx)(m.N,{children:r&&(0,t.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,t.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-black/50 backdrop-blur-sm",onClick:n}),(0,t.jsxs)(l.P.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},className:"relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)("div",{className:"sticky top-0 bg-white border-b border-gray-200 p-6 rounded-t-2xl",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:c("order_details")}),(0,t.jsx)("div",{className:"flex items-center space-x-3 space-x-reverse",children:(0,t.jsx)("button",{onClick:n,className:"p-2 text-gray-400 hover:text-gray-600 transition-colors duration-300",children:(0,t.jsx)(h.A,{className:"w-6 h-6"})})})]})}),(0,t.jsxs)("div",{className:"p-6 space-y-8",children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-pink-600"}),(0,t.jsxs)("span",{children:[c("customer_information"),o&&(0,t.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Customer Information)"})]})]}),(0,t.jsx)("div",{className:"space-y-3 bg-gray-50 p-4 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,t.jsx)(p.A,{className:"w-4 h-4 text-gray-600"}),(0,t.jsxs)("span",{className:"font-medium",children:[c("name"),o&&(0,t.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Name)"})]}),(0,t.jsx)("span",{children:s.clientName})]})})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 text-pink-600"}),(0,t.jsxs)("span",{children:[c("order_details"),o&&(0,t.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Order Details)"})]})]}),(0,t.jsxs)("div",{className:"space-y-3 bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("span",{className:"font-medium",children:[c("description"),o&&(0,t.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Description)"})]}),(0,t.jsx)("p",{className:"mt-1",children:s.description})]}),s.fabric&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("span",{className:"font-medium",children:[c("fabric_type"),o&&(0,t.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Fabric Type)"})]}),(0,t.jsx)("p",{className:"mt-1",children:s.fabric})]})]})]})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("h4",{className:"font-bold text-gray-800",children:[c("status"),o&&(0,t.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Status)"})]}),(0,t.jsx)("span",{className:"px-3 py-2 rounded-full text-sm font-medium ".concat(w(s.status).bgColor," ").concat(w(s.status).color),children:w(s.status).label})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("h4",{className:"font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(u.A,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:[c("order_date"),o&&(0,t.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Order Date)"})]})]}),(0,t.jsx)("p",{children:_(s.createdAt)})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("h4",{className:"font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(b.A,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:[c("delivery_date"),o&&(0,t.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Delivery Date)"})]})]}),(0,t.jsx)("p",{children:_((e=>{let s=new Date(e);return s.setDate(s.getDate()-2),s.toISOString()})(s.dueDate))})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(j.A,{className:"w-5 h-5 text-pink-600"}),(0,t.jsxs)("span",{children:[c("assigned_worker"),o&&(0,t.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Assigned Worker)"})]})]}),(0,t.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg",children:(0,t.jsx)("p",{className:"text-lg",children:(e=>{if(!e)return c("not_specified");let s=a.find(s=>s.id===e);return s?s.full_name:c("not_specified")})(s.assignedWorker)})})]}),Object.values(s.measurements).some(e=>void 0!==e)&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(y.A,{className:"w-5 h-5 text-pink-600"}),(0,t.jsxs)("span",{children:[c("measurements_cm"),o&&(0,t.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Measurements)"})]})]}),(s.measurements.shoulder||s.measurements.shoulderCircumference||s.measurements.chest||s.measurements.waist||s.measurements.hips)&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("h4",{className:"text-md font-semibold text-gray-700 border-b border-pink-200 pb-1",children:[c("basic_measurements"),o&&(0,t.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Basic Measurements)"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[s.measurements.shoulder&&(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[c("shoulder"),o&&(0,t.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Shoulder)"})]}),(0,t.jsx)("p",{className:"text-lg font-bold",children:s.measurements.shoulder})]}),s.measurements.shoulderCircumference&&(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[c("shoulder_circumference"),o&&(0,t.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Shoulder Circumference)"})]}),(0,t.jsx)("p",{className:"text-lg font-bold",children:s.measurements.shoulderCircumference})]}),s.measurements.chest&&(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[c("chest_bust"),o&&(0,t.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Chest/Bust)"})]}),(0,t.jsx)("p",{className:"text-lg font-bold",children:s.measurements.chest})]}),s.measurements.waist&&(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[c("waist"),o&&(0,t.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Waist)"})]}),(0,t.jsx)("p",{className:"text-lg font-bold",children:s.measurements.waist})]}),s.measurements.hips&&(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[c("hips"),o&&(0,t.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Hips)"})]}),(0,t.jsx)("p",{className:"text-lg font-bold",children:s.measurements.hips})]})]})]}),(s.measurements.dartLength||s.measurements.bodiceLength||s.measurements.neckline||s.measurements.armpit)&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("h4",{className:"text-md font-semibold text-gray-700 border-b border-pink-200 pb-1",children:[c("advanced_tailoring_measurements"),o&&(0,t.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Advanced Tailoring)"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[s.measurements.dartLength&&(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[c("dart_length"),o&&(0,t.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Dart Length)"})]}),(0,t.jsx)("p",{className:"text-lg font-bold",children:s.measurements.dartLength})]}),s.measurements.bodiceLength&&(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[c("bodice_length"),o&&(0,t.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Bodice Length)"})]}),(0,t.jsx)("p",{className:"text-lg font-bold",children:s.measurements.bodiceLength})]}),s.measurements.neckline&&(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[c("neckline"),o&&(0,t.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Neckline)"})]}),(0,t.jsx)("p",{className:"text-lg font-bold",children:s.measurements.neckline})]}),s.measurements.armpit&&(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[c("armpit"),o&&(0,t.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Armpit)"})]}),(0,t.jsx)("p",{className:"text-lg font-bold",children:s.measurements.armpit})]})]})]}),(s.measurements.sleeveLength||s.measurements.forearm||s.measurements.cuff)&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("h4",{className:"text-md font-semibold text-gray-700 border-b border-pink-200 pb-1",children:[c("sleeve_measurements"),o&&(0,t.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Sleeve Measurements)"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[s.measurements.sleeveLength&&(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[c("sleeve_length"),o&&(0,t.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Sleeve Length)"})]}),(0,t.jsx)("p",{className:"text-lg font-bold",children:s.measurements.sleeveLength})]}),s.measurements.forearm&&(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[c("forearm"),o&&(0,t.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Forearm)"})]}),(0,t.jsx)("p",{className:"text-lg font-bold",children:s.measurements.forearm})]}),s.measurements.cuff&&(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[c("cuff"),o&&(0,t.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Cuff)"})]}),(0,t.jsx)("p",{className:"text-lg font-bold",children:s.measurements.cuff})]})]})]}),(s.measurements.frontLength||s.measurements.backLength)&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("h4",{className:"text-md font-semibold text-gray-700 border-b border-pink-200 pb-1",children:[c("length_measurements"),o&&(0,t.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Length Measurements)"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[s.measurements.frontLength&&(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[c("front_length"),o&&(0,t.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Front Length)"})]}),(0,t.jsx)("p",{className:"text-lg font-bold",children:s.measurements.frontLength})]}),s.measurements.backLength&&(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[c("back_length"),o&&(0,t.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Back Length)"})]}),(0,t.jsx)("p",{className:"text-lg font-bold",children:s.measurements.backLength})]})]})]}),(s.measurements.length||s.measurements.shoulders||s.measurements.sleeves)&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("h4",{className:"text-md font-semibold text-gray-700 border-b border-gray-300 pb-1 text-gray-500",children:[c("additional_measurements"),o&&(0,t.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Additional)"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[s.measurements.length&&(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[c("dress_length"),o&&(0,t.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Dress Length)"})]}),(0,t.jsx)("p",{className:"text-lg font-bold",children:s.measurements.length})]}),s.measurements.shoulders&&(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[c("shoulder_width"),o&&(0,t.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Shoulder Width)"})]}),(0,t.jsx)("p",{className:"text-lg font-bold",children:s.measurements.shoulders})]}),s.measurements.sleeves&&(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[c("sleeve_length_old"),o&&(0,t.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Sleeve Length)"})]}),(0,t.jsx)("p",{className:"text-lg font-bold",children:s.measurements.sleeves})]})]})]})]}),s.images&&s.images.length>0&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(v.A,{className:"w-5 h-5 text-pink-600"}),(0,t.jsxs)("span",{children:[c("design_images"),o&&(0,t.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Design Images)"})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:s.images.map((e,s)=>(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("div",{className:"aspect-square rounded-lg overflow-hidden border border-gray-200",children:(0,t.jsx)("img",{src:e,alt:"".concat(c("design_image_alt")," ").concat(s+1),className:"w-full h-full object-cover cursor-pointer hover:scale-105 transition-transform duration-300",onClick:()=>window.open(e,"_blank")})}),(0,t.jsx)("div",{className:"absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded",children:s+1})]},s))})]}),s.notes&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(N.A,{className:"w-5 h-5 text-pink-600"}),(0,t.jsxs)("span",{children:[c("notes"),o&&(0,t.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Notes)"})]})]}),(0,t.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg",children:(0,t.jsx)("p",{children:s.notes})})]}),s.voiceNotes&&s.voiceNotes.length>0&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(N.A,{className:"w-5 h-5 text-pink-600"}),(0,t.jsxs)("span",{children:[c("voice_notes"),o&&(0,t.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Voice Notes)"})]})]}),(0,t.jsx)(k.A,{voiceNotes:s.voiceNotes,onVoiceNotesChange:()=>{},disabled:!0})]}),(null==i?void 0:i.role)==="admin"&&s.completedImages&&s.completedImages.length>0&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(f.A,{className:"w-5 h-5 text-green-600"}),(0,t.jsx)("span",{children:c("completed_work_images")})]}),(0,t.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg border border-green-200",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse mb-3",children:[(0,t.jsx)(f.A,{className:"w-4 h-4 text-green-600"}),(0,t.jsx)("span",{className:"text-sm font-medium text-green-800",children:c("completed_work_description")})]}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:s.completedImages.map((e,s)=>(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("div",{className:"aspect-square rounded-lg overflow-hidden border border-green-300",children:(0,t.jsx)("img",{src:e,alt:"".concat(c("completed_work_image_alt")," ").concat(s+1),className:"w-full h-full object-cover cursor-pointer hover:scale-105 transition-transform duration-300",onClick:()=>window.open(e,"_blank")})}),(0,t.jsx)("div",{className:"absolute bottom-2 left-2 bg-green-600/80 text-white text-xs px-2 py-1 rounded",children:s+1})]},s))})]})]})]}),(0,t.jsx)("div",{className:"sticky bottom-0 bg-white border-t border-gray-200 p-6 rounded-b-2xl",children:(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)("button",{onClick:n,className:"btn-secondary px-6 py-2",children:c("close")})})})]})]})})}var _=a(5339),A=a(4229),C=a(5552),S=a(2642);function L(e){var s,a,n,i,c,d,o,u,b,w,L,D,P,q,M,W,I,F,O,z,T,B,E,V,H,R,G,U,J;let{order:K,workers:Q,isOpen:X,onClose:Y,onSave:Z}=e,{t:$}=(0,x.B)(),[ee,es]=(0,r.useState)({}),[ea,et]=(0,r.useState)(!1),[er,el]=(0,r.useState)(null);(0,r.useEffect)(()=>{K&&es({clientName:K.clientName,clientPhone:K.clientPhone,description:K.description,fabric:K.fabric,price:K.price,status:K.status,assignedWorker:K.assignedWorker,dueDate:K.dueDate,notes:K.notes,voiceNotes:K.voiceNotes||[],images:K.images||[],measurements:{...K.measurements}})},[K]);let en=(e,s)=>{es(a=>({...a,[e]:s}))},ei=(e,s)=>{es(a=>({...a,measurements:{...a.measurements,[e]:s}}))},ec=async e=>{if(e.preventDefault(),K){if(!ee.clientName||!ee.clientPhone||!ee.description||!ee.price||!ee.dueDate)return void el({type:"error",text:$("fill_required_fields")});et(!0),el(null);try{await new Promise(e=>setTimeout(e,1e3));let e=Object.keys(ee.measurements||{}).reduce((e,s)=>{var a;let t=null==(a=ee.measurements)?void 0:a[s];return e[s]=t&&""!==t?Number(t):void 0,e},{});Z(K.id,{...ee,price:Number(ee.price),measurements:e,updatedAt:new Date().toISOString()}),el({type:"success",text:$("order_updated_success")}),setTimeout(()=>{Y(),el(null)},1500)}catch(e){el({type:"error",text:$("order_update_error")})}finally{et(!1)}}};return K?(0,t.jsx)(m.N,{children:X&&(0,t.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,t.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-black/50 backdrop-blur-sm",onClick:Y}),(0,t.jsxs)(l.P.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},className:"relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)("div",{className:"sticky top-0 bg-white border-b border-gray-200 p-6 rounded-t-2xl",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:$("edit_order")}),(0,t.jsx)("button",{onClick:Y,className:"p-2 text-gray-400 hover:text-gray-600 transition-colors duration-300",children:(0,t.jsx)(h.A,{className:"w-6 h-6"})})]})}),er&&(0,t.jsxs)("div",{className:"mx-6 mt-4 p-4 rounded-lg flex items-center space-x-3 space-x-reverse ".concat("success"===er.type?"bg-green-50 text-green-800 border border-green-200":"bg-red-50 text-red-800 border border-red-200"),children:["success"===er.type?(0,t.jsx)(f.A,{className:"w-5 h-5 text-green-600"}):(0,t.jsx)(_.A,{className:"w-5 h-5 text-red-600"}),(0,t.jsx)("span",{children:er.text})]}),(0,t.jsxs)("form",{onSubmit:ec,className:"p-6 space-y-8",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-pink-600"}),(0,t.jsx)("span",{children:$("customer_information")})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:$("client_name_required")}),(0,t.jsx)("input",{type:"text",value:ee.clientName||"",onChange:e=>en("clientName",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,t.jsx)("div",{children:(0,t.jsx)(S.A,{value:ee.clientPhone||"",onChange:e=>en("clientPhone",e),type:"phone",label:$("phone_required"),required:!0,disabled:ea})})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(g.A,{className:"w-5 h-5 text-pink-600"}),(0,t.jsx)("span",{children:$("order_details")})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:$("order_description_required")}),(0,t.jsx)("input",{type:"text",value:ee.description||"",onChange:e=>en("description",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:$("fabric_type_optional")}),(0,t.jsx)("input",{type:"text",value:ee.fabric||"",onChange:e=>en("fabric",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"})]}),(0,t.jsx)("div",{children:(0,t.jsx)(S.A,{value:(null==(s=ee.price)?void 0:s.toString())||"",onChange:e=>en("price",e?Number(e):""),type:"price",label:$("price_sar_required"),required:!0,disabled:ea})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:$("delivery_date_required")}),(0,t.jsx)("input",{type:"date",value:ee.dueDate||"",onChange:e=>en("dueDate",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(j.A,{className:"w-5 h-5 text-pink-600"}),(0,t.jsx)("span",{children:$("status_and_worker")})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:$("order_status")}),(0,t.jsxs)("select",{value:ee.status||"",onChange:e=>en("status",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",children:[(0,t.jsx)("option",{value:"pending",children:$("status_pending")}),(0,t.jsx)("option",{value:"in_progress",children:$("status_in_progress")}),(0,t.jsx)("option",{value:"completed",children:$("status_completed")}),(0,t.jsx)("option",{value:"delivered",children:$("status_delivered")}),(0,t.jsx)("option",{value:"cancelled",children:$("status_cancelled")})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:$("responsible_worker")}),(0,t.jsxs)("select",{value:ee.assignedWorker||"",onChange:e=>en("assignedWorker",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",children:[(0,t.jsx)("option",{value:"",children:$("choose_worker")}),Q.filter(e=>e.is_active).map(e=>(0,t.jsxs)("option",{value:e.id,children:[e.full_name," - ",e.specialty]},e.id))]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(v.A,{className:"w-5 h-5 text-pink-600"}),(0,t.jsx)("span",{children:$("design_images")})]}),(0,t.jsx)(C.A,{images:ee.images||[],onImagesChange:e=>en("images",e),maxImages:5})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(y.A,{className:"w-5 h-5 text-pink-600"}),(0,t.jsx)("span",{children:$("measurements_cm")})]}),(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2",children:$("basic_measurements")}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,t.jsx)("div",{children:(0,t.jsx)(S.A,{value:(null==(n=ee.measurements)||null==(a=n.shoulder)?void 0:a.toString())||"",onChange:e=>ei("shoulder",e),type:"measurement",label:$("shoulder"),placeholder:$("cm_placeholder"),disabled:ea})}),(0,t.jsx)("div",{children:(0,t.jsx)(S.A,{value:(null==(c=ee.measurements)||null==(i=c.shoulderCircumference)?void 0:i.toString())||"",onChange:e=>ei("shoulderCircumference",e),type:"measurement",label:$("shoulder_circumference"),placeholder:$("cm_placeholder"),disabled:ea})}),(0,t.jsx)("div",{children:(0,t.jsx)(S.A,{value:(null==(o=ee.measurements)||null==(d=o.chest)?void 0:d.toString())||"",onChange:e=>ei("chest",e),type:"measurement",label:$("chest"),placeholder:$("cm_placeholder"),disabled:ea})}),(0,t.jsx)("div",{children:(0,t.jsx)(S.A,{value:(null==(b=ee.measurements)||null==(u=b.waist)?void 0:u.toString())||"",onChange:e=>ei("waist",e),type:"measurement",label:$("waist"),placeholder:$("cm_placeholder"),disabled:ea})}),(0,t.jsx)("div",{children:(0,t.jsx)(S.A,{value:(null==(L=ee.measurements)||null==(w=L.hips)?void 0:w.toString())||"",onChange:e=>ei("hips",e),type:"measurement",label:$("hips"),placeholder:$("cm_placeholder"),disabled:ea})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2",children:$("advanced_measurements")}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,t.jsx)("div",{children:(0,t.jsx)(S.A,{value:(null==(P=ee.measurements)||null==(D=P.dartLength)?void 0:D.toString())||"",onChange:e=>ei("dartLength",e),type:"measurement",label:$("dart_length"),placeholder:$("cm_placeholder"),disabled:ea})}),(0,t.jsx)("div",{children:(0,t.jsx)(S.A,{value:(null==(M=ee.measurements)||null==(q=M.bodiceLength)?void 0:q.toString())||"",onChange:e=>ei("bodiceLength",e),type:"measurement",label:$("bodice_length"),placeholder:$("cm_placeholder"),disabled:ea})}),(0,t.jsx)("div",{children:(0,t.jsx)(S.A,{value:(null==(I=ee.measurements)||null==(W=I.neckline)?void 0:W.toString())||"",onChange:e=>ei("neckline",e),type:"measurement",label:$("neckline"),placeholder:$("cm_placeholder"),disabled:ea})}),(0,t.jsx)("div",{children:(0,t.jsx)(S.A,{value:(null==(O=ee.measurements)||null==(F=O.armpit)?void 0:F.toString())||"",onChange:e=>ei("armpit",e),type:"measurement",label:$("armpit"),placeholder:$("cm_placeholder"),disabled:ea})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2",children:$("sleeve_measurements")}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,t.jsx)("div",{children:(0,t.jsx)(S.A,{value:(null==(T=ee.measurements)||null==(z=T.sleeveLength)?void 0:z.toString())||"",onChange:e=>ei("sleeveLength",e),type:"measurement",label:$("sleeve_length"),placeholder:$("cm_placeholder"),disabled:ea})}),(0,t.jsx)("div",{children:(0,t.jsx)(S.A,{value:(null==(E=ee.measurements)||null==(B=E.forearm)?void 0:B.toString())||"",onChange:e=>ei("forearm",e),type:"measurement",label:$("forearm"),placeholder:$("cm_placeholder"),disabled:ea})}),(0,t.jsx)("div",{children:(0,t.jsx)(S.A,{value:(null==(H=ee.measurements)||null==(V=H.cuff)?void 0:V.toString())||"",onChange:e=>ei("cuff",e),type:"measurement",label:$("cuff"),placeholder:$("cm_placeholder"),disabled:ea})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2",children:$("length_measurements")}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,t.jsx)("div",{children:(0,t.jsx)(S.A,{value:(null==(G=ee.measurements)||null==(R=G.frontLength)?void 0:R.toString())||"",onChange:e=>ei("frontLength",e),type:"measurement",label:$("front_length"),placeholder:$("cm_placeholder"),disabled:ea})}),(0,t.jsx)("div",{children:(0,t.jsx)(S.A,{value:(null==(J=ee.measurements)||null==(U=J.backLength)?void 0:U.toString())||"",onChange:e=>ei("backLength",e),type:"measurement",label:$("back_length"),placeholder:$("cm_placeholder"),disabled:ea})})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(N.A,{className:"w-5 h-5 text-pink-600"}),(0,t.jsx)("span",{children:$("notes_section")})]}),(0,t.jsx)("textarea",{value:ee.notes||"",onChange:e=>en("notes",e.target.value),rows:4,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:$("additional_notes_placeholder")}),(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsx)(k.A,{voiceNotes:ee.voiceNotes||[],onVoiceNotesChange:e=>en("voiceNotes",e),disabled:ea})})]})]}),(0,t.jsx)("div",{className:"sticky bottom-0 bg-white border-t border-gray-200 p-6 rounded-b-2xl",children:(0,t.jsxs)("div",{className:"flex gap-4 justify-end",children:[(0,t.jsx)("button",{type:"button",onClick:Y,className:"btn-secondary px-6 py-2",disabled:ea,children:$("cancel")}),(0,t.jsx)("button",{onClick:ec,disabled:ea,className:"btn-primary px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 space-x-reverse",children:ea?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,t.jsx)("span",{children:$("saving")})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(A.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:$("save_changes")})]})})]})})]})]})}):null}let D=(0,a(9946).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);function P(e){let{onImagesChange:s,maxImages:a=3,disabled:n=!1}=e,[i,c]=(0,r.useState)([]),[d,o]=(0,r.useState)(!1),x=(0,r.useRef)(null),m=e=>{if(!e||n)return;let t=[],r=a-i.length;Array.from(e).slice(0,r).forEach(a=>{if(a.type.startsWith("image/")){let l=new FileReader;l.onload=a=>{var l;let n=null==(l=a.target)?void 0:l.result;if(t.push(n),t.length===Math.min(e.length,r)){let e=[...i,...t];c(e),s(e)}},l.readAsDataURL(a)}})},p=e=>{if(n)return;let a=i.filter((s,a)=>a!==e);c(a),s(a)};return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"صور العمل المكتمل"}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:[i.length,"/",a," صور"]})]}),(0,t.jsxs)("div",{onDrop:e=>{e.preventDefault(),o(!1),m(e.dataTransfer.files)},onDragOver:e=>{e.preventDefault(),n||o(!0)},onDragLeave:e=>{e.preventDefault(),o(!1)},onClick:()=>{!n&&x.current&&x.current.click()},className:"\n          relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-300\n          ".concat(d?"border-pink-400 bg-pink-50":"border-gray-300 hover:border-pink-400 hover:bg-pink-50","\n          ").concat(n?"opacity-50 cursor-not-allowed":"","\n          ").concat(i.length>=a?"opacity-50 cursor-not-allowed":"","\n        "),children:[(0,t.jsx)("input",{ref:x,type:"file",multiple:!0,accept:"image/*",onChange:e=>m(e.target.files),className:"hidden",disabled:n||i.length>=a}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D,{className:"w-12 h-12 text-gray-400 mx-auto"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-700",children:i.length>=a?"تم الوصول للحد الأقصى من الصور":"اضغط لرفع صور العمل المكتمل"}),i.length<a&&(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"أو اسحب الصور هنا • JPG, PNG, GIF"})]})]})]}),i.length>0&&(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:i.map((e,s)=>(0,t.jsxs)(l.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:"relative group",children:[(0,t.jsx)("div",{className:"aspect-square rounded-lg overflow-hidden bg-gray-100",children:(0,t.jsx)("img",{src:e,alt:"صورة العمل ".concat(s+1),className:"w-full h-full object-cover"})}),!n&&(0,t.jsx)("button",{onClick:e=>{e.stopPropagation(),p(s)},className:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-red-600",children:(0,t.jsx)(h.A,{className:"w-3 h-3"})}),(0,t.jsx)("div",{className:"absolute bottom-2 left-2 right-2",children:(0,t.jsxs)("div",{className:"bg-black/50 text-white text-xs px-2 py-1 rounded backdrop-blur-sm",children:["صورة ",s+1]})})]},s))}),i.length>0&&(0,t.jsx)("div",{className:"text-xs text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200",children:(0,t.jsxs)("div",{className:"flex items-start space-x-2 space-x-reverse",children:[(0,t.jsx)(v.A,{className:"w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-blue-800 mb-1",children:"ملاحظة مهمة:"}),(0,t.jsx)("p",{className:"text-blue-700",children:"هذه الصور ستكون مرئية للمدير فقط ولن تظهر للعملاء في صفحة تتبع الطلبات. تُستخدم لتوثيق جودة العمل المكتمل."})]})]})})]})}var q=a(1243),M=a(2525),W=a(8749),I=a(2657);function F(e){let{isOpen:s,onClose:a,onConfirm:n,orderInfo:i}=e,{t:c}=(0,x.B)(),{user:o}=(0,d.n)(),[p,g]=(0,r.useState)(""),[u,b]=(0,r.useState)(""),[j,y]=(0,r.useState)(!1),[v,N]=(0,r.useState)(!1),[f,k]=(0,r.useState)(""),w=async e=>{if(e.preventDefault(),k(""),N(!0),!p||!u){k(c("please_fill_all_fields")),N(!1);return}if(p!==(null==o?void 0:o.email)){k(c("email_does_not_match")),N(!1);return}if("admin123"!==u){k(c("incorrect_password")),N(!1);return}await new Promise(e=>setTimeout(e,1e3)),N(!1),n(),_()},_=()=>{g(""),b(""),k(""),y(!1),a()};return(0,t.jsx)(m.N,{children:s&&(0,t.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,t.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-black/50 backdrop-blur-sm",onClick:_}),(0,t.jsxs)(l.P.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},className:"relative bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,t.jsx)("div",{className:"p-2 bg-red-100 rounded-full",children:(0,t.jsx)(q.A,{className:"w-6 h-6 text-red-600"})}),(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:c("confirm_delete_order")})]}),(0,t.jsx)("button",{onClick:_,className:"p-2 hover:bg-gray-100 rounded-full transition-colors",children:(0,t.jsx)(h.A,{className:"w-5 h-5 text-gray-500"})})]}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3 space-x-reverse",children:[(0,t.jsx)(M.A,{className:"w-5 h-5 text-red-600 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-red-800 mb-2",children:c("warning_delete_order")}),(0,t.jsx)("p",{className:"text-sm text-red-700",children:c("delete_order_warning_message")})]})]})}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:c("order_details")}),(0,t.jsxs)("div",{className:"space-y-1 text-sm text-gray-600",children:[(0,t.jsxs)("p",{children:[(0,t.jsxs)("span",{className:"font-medium",children:[c("order_id"),":"]})," #",i.id]}),(0,t.jsxs)("p",{children:[(0,t.jsxs)("span",{className:"font-medium",children:[c("client_name"),":"]})," ",i.clientName]}),(0,t.jsxs)("p",{children:[(0,t.jsxs)("span",{className:"font-medium",children:[c("description"),":"]})," ",i.description]})]})]}),(0,t.jsxs)("form",{onSubmit:w,className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("admin_email")}),(0,t.jsx)("input",{type:"email",value:p,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:c("enter_admin_email"),required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("admin_password")}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:j?"text":"password",value:u,onChange:e=>b(e.target.value),className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:c("enter_admin_password"),required:!0}),(0,t.jsx)("button",{type:"button",onClick:()=>y(!j),className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:j?(0,t.jsx)(W.A,{className:"w-4 h-4"}):(0,t.jsx)(I.A,{className:"w-4 h-4"})})]})]}),f&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,t.jsx)("p",{className:"text-sm text-red-700",children:f})}),(0,t.jsxs)("div",{className:"flex space-x-3 space-x-reverse pt-4",children:[(0,t.jsx)("button",{type:"button",onClick:_,className:"flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors",children:c("cancel")}),(0,t.jsx)("button",{type:"submit",disabled:v,className:"flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 space-x-reverse",children:v?(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(M.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:c("confirm_delete")})]})})]})]})]})]})]})})}var O=a(2138),z=a(4616),T=a(7924),B=a(6932),E=a(3717);function V(){let{user:e}=(0,d.n)(),{orders:s,workers:a,updateOrder:i,deleteOrder:m,startOrderWork:j,completeOrder:y}=(0,o.D)(),{t:v,language:N,changeLanguage:k,isArabic:A}=(0,x.B)(),C=(0,n.useRouter)();(0,r.useEffect)(()=>{e||C.push("/login")},[e,C]);let[D,q]=(0,r.useState)(""),[W,V]=(0,r.useState)("text"),[H,R]=(0,r.useState)("all"),[G,U]=(0,r.useState)(null),[J,K]=(0,r.useState)(!1),[Q,X]=(0,r.useState)(!1),[Y,Z]=(0,r.useState)(!1),[$,ee]=(0,r.useState)([]),[es,ea]=(0,r.useState)(!1),[et,er]=(0,r.useState)(!1),[el,en]=(0,r.useState)(null),[ei,ec]=(0,r.useState)(!1),ed=e=>{let s={pending:{label:v("pending"),color:"text-yellow-600",bgColor:"bg-yellow-100",icon:b.A},in_progress:{label:v("in_progress"),color:"text-blue-600",bgColor:"bg-blue-100",icon:g.A},completed:{label:v("completed"),color:"text-green-600",bgColor:"bg-green-100",icon:f.A},delivered:{label:v("delivered"),color:"text-purple-600",bgColor:"bg-purple-100",icon:f.A},cancelled:{label:v("cancelled"),color:"text-red-600",bgColor:"bg-red-100",icon:_.A}};return s[e]||s.pending},eo=e=>{if(!e)return null;let s=a.find(s=>s.id===e);return s?s.full_name:null},ex=e=>{U(e),K(!0)},em=e=>{U(e),X(!0)},eh=()=>{K(!1),X(!1),Z(!1),U(null),ee([])},ep=e=>{en(e),er(!0)},eg=async s=>{if(e&&"worker"===e.role){ea(!0);try{await new Promise(e=>setTimeout(e,500)),j(s,e.id)}finally{ea(!1)}}},eu=e=>{U(e),Z(!0)},eb=async()=>{if(G&&e&&"worker"===e.role){ea(!0);try{await new Promise(e=>setTimeout(e,1e3)),y(G.id,e.id,$),Z(!1),U(null),ee([])}finally{ea(!1)}}},ej=e=>new Date(e).toLocaleDateString("ar-US",{year:"numeric",month:"short",day:"numeric",calendar:"gregory"}),ey=s.filter(s=>{let a=(null==e?void 0:e.role)==="admin"||s.assignedWorker===(null==e?void 0:e.id),t="orderNumber"===W?s.id.toLowerCase().includes(D.toLowerCase()):s.clientName.toLowerCase().includes(D.toLowerCase())||s.id.toLowerCase().includes(D.toLowerCase())||s.description.toLowerCase().includes(D.toLowerCase()),r="all"===H||s.status===H;return a&&t&&r});return e?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-8",children:(0,t.jsxs)(c(),{href:"/dashboard",className:"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300",children:[(0,t.jsx)(O.A,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:[v("back_to_dashboard")," ",v("dashboard")]})]})}),(0,t.jsxs)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold mb-2",children:(0,t.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent",children:v("orders")})}),(0,t.jsx)("p",{className:"text-lg text-gray-600",children:v("view_manage_orders")})]}),(0,t.jsx)("div",{className:"flex items-center gap-3",children:"admin"===e.role&&(0,t.jsxs)(c(),{href:"/dashboard/add-order",className:"btn-primary inline-flex items-center justify-center space-x-2 space-x-reverse px-6 py-3 group",children:[(0,t.jsx)(z.A,{className:"w-5 h-5 group-hover:scale-110 transition-transform duration-300"}),(0,t.jsx)("span",{children:v("add_new_order")})]})})]}),(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 mb-8",children:(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex space-x-2 space-x-reverse",children:[(0,t.jsx)("button",{onClick:()=>{V("text"),q("")},className:"px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ".concat("text"===W?"bg-pink-500 text-white":"bg-gray-100 text-gray-600 hover:bg-gray-200"),children:v("search_by_text")}),(0,t.jsx)("button",{onClick:()=>{V("orderNumber"),q("")},className:"px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ".concat("orderNumber"===W?"bg-pink-500 text-white":"bg-gray-100 text-gray-600 hover:bg-gray-200"),children:v("search_by_order_number")})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(T.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),"orderNumber"===W?(0,t.jsx)(S.A,{value:D,onChange:q,type:"orderNumber",placeholder:v("enter_order_number"),className:"pr-10"}):(0,t.jsx)("input",{type:"text",value:D,onChange:e=>q(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:v("search_placeholder")})]})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(B.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,t.jsxs)("select",{value:H,onChange:e=>R(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",children:[(0,t.jsx)("option",{value:"all",children:v("all_orders")}),(0,t.jsx)("option",{value:"pending",children:v("pending")}),(0,t.jsx)("option",{value:"in_progress",children:v("in_progress")}),(0,t.jsx)("option",{value:"completed",children:v("completed")}),(0,t.jsx)("option",{value:"delivered",children:v("delivered")})]})]})]})}),(0,t.jsx)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"space-y-6",children:0===ey.length?(0,t.jsxs)("div",{className:"text-center py-12 bg-white/80 backdrop-blur-sm rounded-2xl border border-pink-100",children:[(0,t.jsx)(g.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-xl font-medium text-gray-600 mb-2",children:"worker"===e.role?v("no_orders_assigned"):v("no_orders_found")}),(0,t.jsx)("p",{className:"text-gray-500",children:"worker"===e.role?v("no_orders_assigned_desc"):v("no_orders_found_desc")})]}):ey.map((s,a)=>(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*a},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 hover:shadow-lg transition-all duration-300",children:(0,t.jsxs)("div",{className:"grid lg:grid-cols-4 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-1",children:s.clientName}),(0,t.jsx)("p",{className:"text-pink-600 font-medium",children:s.description}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["#",s.id]})]}),(0,t.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat(ed(s.status).bgColor," ").concat(ed(s.status).color),children:ed(s.status).label})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(u.A,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:[v("order_date_label"),": ",ej(s.createdAt)]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(b.A,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:[v("delivery_date_label"),": ",ej(s.dueDate)]})]}),s.assignedWorker&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(p.A,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:[v("worker_label"),": ",eo(s.assignedWorker)]})]}),s.fabric&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(g.A,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:[v("fabric_label")," ",s.fabric]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:v("price_label")}),(0,t.jsxs)("p",{className:"text-lg font-bold text-green-600",children:[s.price," ",v("sar")]})]}),s.notes&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-1",children:v("notes_label")}),(0,t.jsx)("p",{className:"text-sm text-gray-600 bg-gray-50 p-2 rounded",children:s.notes})]})]}),(0,t.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,t.jsxs)("button",{onClick:()=>ex(s),className:"btn-secondary py-2 px-4 text-sm inline-flex items-center justify-center space-x-1 space-x-reverse",children:[(0,t.jsx)(I.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:v("view")})]}),"admin"===e.role&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("button",{onClick:()=>em(s),className:"btn-primary py-2 px-4 text-sm inline-flex items-center justify-center space-x-1 space-x-reverse",children:[(0,t.jsx)(E.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:v("edit")})]}),(0,t.jsxs)("button",{onClick:()=>ep(s),className:"bg-red-500 hover:bg-red-600 text-white py-2 px-4 text-sm inline-flex items-center justify-center space-x-1 space-x-reverse rounded-lg transition-colors duration-200",children:[(0,t.jsx)(M.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:v("delete")})]})]}),"worker"===e.role&&s.assignedWorker===e.id&&(0,t.jsxs)(t.Fragment,{children:["pending"===s.status&&(0,t.jsxs)("button",{onClick:()=>eg(s.id),disabled:es,className:"bg-blue-600 text-white py-2 px-4 text-sm rounded-lg hover:bg-blue-700 transition-colors duration-300 inline-flex items-center justify-center space-x-1 space-x-reverse disabled:opacity-50",children:[(0,t.jsx)(g.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:v("start_work")})]}),"in_progress"===s.status&&(0,t.jsxs)("button",{onClick:()=>eu(s),disabled:es,className:"bg-green-600 text-white py-2 px-4 text-sm rounded-lg hover:bg-green-700 transition-colors duration-300 inline-flex items-center justify-center space-x-1 space-x-reverse disabled:opacity-50",children:[(0,t.jsx)(f.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:v("complete_order")})]})]})]})]})},s.id))}),(0,t.jsxs)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"mt-12 grid grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-yellow-600 mb-1",children:s.filter(s=>("admin"===e.role||s.assignedWorker===e.id)&&"pending"===s.status).length}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:v("pending")})]}),(0,t.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:s.filter(s=>("admin"===e.role||s.assignedWorker===e.id)&&"in_progress"===s.status).length}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:v("in_progress")})]}),(0,t.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-1",children:s.filter(s=>("admin"===e.role||s.assignedWorker===e.id)&&"completed"===s.status).length}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:v("completed")})]}),(0,t.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-purple-600 mb-1",children:s.filter(s=>("admin"===e.role||s.assignedWorker===e.id)&&"delivered"===s.status).length}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:v("delivered")})]})]}),(0,t.jsx)(w,{order:G,workers:a,isOpen:J,onClose:eh}),(0,t.jsx)(L,{order:G,workers:a,isOpen:Q,onClose:eh,onSave:(e,s)=>{i(e,s),X(!1),U(null)}}),Y&&G&&(0,t.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm",onClick:eh}),(0,t.jsxs)(l.P.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},className:"relative bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)("div",{className:"sticky top-0 bg-white border-b border-gray-200 p-6 rounded-t-2xl",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:v("complete_order_modal_title")}),(0,t.jsx)("button",{onClick:eh,className:"text-gray-400 hover:text-gray-600",children:(0,t.jsx)(h.A,{className:"w-6 h-6"})})]})}),(0,t.jsxs)("div",{className:"p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("h4",{className:"text-lg font-medium text-gray-800 mb-2",children:[v("order_label")," ",G.description]}),(0,t.jsxs)("p",{className:"text-gray-600",children:[v("for_client")," ",G.clientName]})]}),(0,t.jsx)(P,{onImagesChange:ee,maxImages:3,disabled:es}),(0,t.jsx)("div",{className:"bg-yellow-50 p-4 rounded-lg border border-yellow-200",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3 space-x-reverse",children:[(0,t.jsx)(_.A,{className:"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-yellow-800 mb-1",children:v("important_warning")}),(0,t.jsx)("p",{className:"text-yellow-700 text-sm",children:v("complete_order_warning")})]})]})}),(0,t.jsxs)("div",{className:"flex gap-4 justify-end",children:[(0,t.jsx)("button",{onClick:eh,disabled:es,className:"btn-secondary px-6 py-2",children:v("cancel")}),(0,t.jsx)("button",{onClick:eb,disabled:es,className:"bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 space-x-reverse",children:es?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,t.jsx)("span",{children:v("completing")})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(f.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:v("complete_order")})]})})]})]})]})]}),(0,t.jsx)(F,{isOpen:et,onClose:()=>{er(!1),en(null)},onConfirm:()=>{el&&(m(el.id),er(!1),en(null),ec(!0),setTimeout(()=>{ec(!1)},3e3))},orderInfo:el}),ei&&(0,t.jsx)("div",{className:"fixed top-4 right-4 z-50",children:(0,t.jsxs)(l.P.div,{initial:{opacity:0,x:100},animate:{opacity:1,x:0},exit:{opacity:0,x:100},className:"bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(f.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:v("order_deleted_successfully")})]})})]})}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:v("loading")})]})})}},3717:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4186:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},5670:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},6932:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7108:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7924:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8749:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},9074:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[165,874,69,312,518,701,441,684,358],()=>s(569)),_N_E=e.O()}]);