(()=>{var e={};e.id=54,e.ids=[54],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40074:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d={children:["",{children:["designs",{children:["[designId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,69726)),"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\designs\\[designId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\designs\\[designId]\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/designs/[designId]/page",pathname:"/designs/[designId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63972:(e,s,t)=>{Promise.resolve().then(t.bind(t,69726))},69726:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[designId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\designs\\[designId]\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},93828:(e,s,t)=>{Promise.resolve().then(t.bind(t,97060))},97060:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var r=t(60687),a=t(43210),i=t(26001),n=t(85814),l=t.n(n),o=t(30474),d=t(16189),c=t(70334),p=t(13861),x=t(67760),m=t(11860),u=t(14952),h=t(47033),g=t(89637);function f(){let e=parseInt((0,d.useParams)().designId),[s,t]=(0,a.useState)(null),[n,f]=(0,a.useState)(!1),v=g.Z.find(s=>s.id===e);if(!v)return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"التصميم غير موجود"}),(0,r.jsxs)(l(),{href:"/designs",className:"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300",children:[(0,r.jsx)(c.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"العودة إلى التصاميم"})]})]})});let b=e=>{t(e),f(!0),document.body.style.overflow="hidden"},j=()=>{f(!1),t(null),document.body.style.overflow="unset"};return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-8",children:(0,r.jsxs)(l(),{href:"/designs",className:"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300",children:[(0,r.jsx)(c.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"العودة إلى التصاميم"})]})}),(0,r.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12",children:[(0,r.jsxs)(i.P.div,{initial:{opacity:0,x:-30},animate:{opacity:1,x:0},transition:{duration:.8},children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"aspect-[4/5] bg-gradient-to-br from-pink-100 via-rose-100 to-purple-100 rounded-2xl overflow-hidden cursor-pointer group relative",onClick:()=>b(0),children:[(0,r.jsx)(o.default,{src:v.images[0],alt:v.title,fill:!0,className:"object-cover transition-all duration-700 group-hover:scale-110",sizes:"(max-width: 768px) 100vw, 50vw"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center",children:(0,r.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white/90 rounded-full p-3",children:(0,r.jsx)(p.A,{className:"w-8 h-8 text-pink-600"})})}),(0,r.jsx)("div",{className:"absolute top-4 right-4",children:(0,r.jsx)("div",{className:"bg-gradient-to-r from-pink-500 to-rose-500 text-white px-3 py-1 rounded-full text-sm font-medium",children:v.category})})]})}),(0,r.jsx)("div",{className:"grid grid-cols-4 gap-3",children:v.images.map((e,s)=>(0,r.jsx)("div",{className:"aspect-square bg-gradient-to-br from-pink-100 to-rose-100 rounded-lg cursor-pointer hover:scale-105 transition-transform duration-300 overflow-hidden relative",onClick:()=>b(s),children:(0,r.jsx)(o.default,{src:e,alt:`${v.title} - صورة ${s+1}`,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 25vw, 12vw"})},s))})]}),(0,r.jsxs)(i.P.div,{initial:{opacity:0,x:30},animate:{opacity:1,x:0},transition:{duration:.8,delay:.2},className:"space-y-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl lg:text-4xl font-bold text-gray-800 mb-6",children:v.title}),(0,r.jsx)("p",{className:"text-lg text-gray-600 leading-relaxed",children:v.description})]}),v.features&&v.features.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-4",children:"مميزات التصميم"}),(0,r.jsx)("div",{className:"grid gap-3",children:v.features.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-pink-500 rounded-full"}),(0,r.jsx)("span",{className:"text-gray-700",children:e})]},s))})]}),v.sizes&&v.sizes.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-4",children:"المقاسات المتاحة"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-3",children:v.sizes.map((e,s)=>(0,r.jsx)("span",{className:"px-4 py-2 bg-white border border-pink-200 text-gray-700 rounded-lg text-sm font-medium hover:bg-pink-50 transition-colors duration-300",children:e},s))})]}),v.occasions&&v.occasions.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-4",children:"مناسب للمناسبات"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-3",children:v.occasions.map((e,s)=>(0,r.jsx)("span",{className:"px-4 py-2 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 rounded-full text-sm font-medium",children:e},s))})]}),(0,r.jsx)("div",{className:"flex",children:(0,r.jsx)("a",{href:`https://wa.me/+966598862609?text=أريد استفسار عن ${v.title}`,target:"_blank",rel:"noopener noreferrer",className:"w-full bg-gradient-to-r from-pink-600 to-purple-600 text-white py-4 px-6 rounded-lg font-bold text-lg hover:shadow-lg transform hover:scale-105 transition-all duration-300 text-center",children:"استفسار عبر الواتساب"})}),v.care_instructions&&v.care_instructions.length>0&&(0,r.jsxs)("div",{className:"bg-gradient-to-r from-pink-50 to-rose-50 p-6 rounded-xl border border-pink-200",children:[(0,r.jsxs)("h3",{className:"text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2 space-x-reverse",children:[(0,r.jsx)(x.A,{className:"w-5 h-5 text-pink-600"}),(0,r.jsx)("span",{children:"تعليمات العناية"})]}),(0,r.jsx)("div",{className:"grid gap-2",children:v.care_instructions.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-pink-500 rounded-full"}),(0,r.jsx)("span",{className:"text-gray-700 text-sm",children:e})]},s))})]})]})]}),n&&null!==s&&(0,r.jsx)("div",{className:"fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4",onClick:j,role:"dialog","aria-modal":"true","aria-labelledby":"gallery-title",children:(0,r.jsxs)("div",{className:"relative max-w-4xl w-full",onClick:e=>e.stopPropagation(),children:[(0,r.jsx)("button",{onClick:j,className:"absolute top-4 right-4 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-2 transition-colors duration-300","aria-label":"إغلاق المعرض",children:(0,r.jsx)(m.A,{className:"w-6 h-6"})}),(0,r.jsx)("button",{onClick:()=>{null!==s&&t(0===s?v.images.length-1:s-1)},className:"absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-colors duration-300","aria-label":"الصورة السابقة",children:(0,r.jsx)(u.A,{className:"w-6 h-6"})}),(0,r.jsx)("button",{onClick:()=>{null!==s&&t((s+1)%v.images.length)},className:"absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-colors duration-300","aria-label":"الصورة التالية",children:(0,r.jsx)(h.A,{className:"w-6 h-6"})}),(0,r.jsxs)(i.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.3},className:"bg-white rounded-2xl overflow-hidden shadow-2xl",children:[(0,r.jsx)("div",{className:"aspect-[4/5] bg-gradient-to-br from-pink-100 via-rose-100 to-purple-100 relative",children:(0,r.jsx)(o.default,{src:v.images[s],alt:`${v.title} - صورة ${s+1}`,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, 80vw"})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{id:"gallery-title",className:"text-2xl font-bold text-gray-800",children:v.title}),(0,r.jsx)("span",{className:"bg-gradient-to-r from-pink-500 to-rose-500 text-white px-3 py-1 rounded-full text-sm font-medium",children:v.category})]}),(0,r.jsx)("div",{className:"flex justify-center mt-6 space-x-2 space-x-reverse",children:v.images.map((e,a)=>(0,r.jsx)("button",{onClick:()=>t(a),className:`w-3 h-3 rounded-full transition-colors duration-300 ${a===s?"bg-pink-500":"bg-gray-300"}`,"aria-label":`الانتقال إلى الصورة ${a+1}`},a))})]})]},s)]})})]})})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,507,146,814,765,233],()=>t(40074));module.exports=r})();