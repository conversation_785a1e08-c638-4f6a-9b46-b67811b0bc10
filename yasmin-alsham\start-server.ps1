# Ya<PERSON><PERSON> - Development Server Startup Script
# This script starts the development server with the most stable configuration

Write-Host "Starting Yasmin <PERSON> Tailoring Website..." -ForegroundColor Green
Write-Host "Project: Yasm<PERSON> Custom Dress Tailoring" -ForegroundColor Cyan

# Navigate to the project directory
Set-Location $PSScriptRoot
Write-Host "Working directory: $PWD" -ForegroundColor Yellow

# Check if Node.js is available
Write-Host ""
Write-Host "Checking Node.js installation..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "Node.js is not installed or not in PATH!" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Yellow
    pause
    exit 1
}

# Check if npm is available
try {
    $npmVersion = npm --version
    Write-Host "npm version: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "npm is not available!" -ForegroundColor Red
    pause
    exit 1
}

# Check if dependencies are installed
Write-Host ""
Write-Host "Checking dependencies..." -ForegroundColor Yellow
if (-not (Test-Path "node_modules")) {
    Write-Host "Dependencies not found. Installing..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to install dependencies!" -ForegroundColor Red
        pause
        exit 1
    }
    Write-Host "Dependencies installed successfully" -ForegroundColor Green
} else {
    Write-Host "Dependencies found" -ForegroundColor Green
}

# Clean cache for better stability
Write-Host ""
Write-Host "Cleaning development cache..." -ForegroundColor Yellow
Remove-Item -Recurse -Force .next -ErrorAction SilentlyContinue
Write-Host "Cache cleaned" -ForegroundColor Green

# Start the development server
Write-Host ""
Write-Host "Starting Next.js development server..." -ForegroundColor Yellow
Write-Host "Using stable configuration (without Turbopack)" -ForegroundColor Cyan
Write-Host "The server will automatically find an available port:" -ForegroundColor Cyan
Write-Host "   - Preferred: http://localhost:3000" -ForegroundColor White
Write-Host "   - Fallback: http://localhost:3001 (if 3000 is in use)" -ForegroundColor White
Write-Host ""
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Magenta
Write-Host "If you encounter any issues, this script will handle them automatically" -ForegroundColor Magenta
Write-Host ""

# Start the server with error handling
try {
    npm run dev:safe
} catch {
    Write-Host ""
    Write-Host "Error starting development server!" -ForegroundColor Red
    Write-Host "Trying alternative startup method..." -ForegroundColor Yellow

    # Alternative: Try with regular dev command
    Write-Host "Attempting with Turbopack..." -ForegroundColor Yellow
    try {
        npm run dev
    } catch {
        Write-Host "All startup methods failed!" -ForegroundColor Red
        Write-Host "Troubleshooting suggestions:" -ForegroundColor Yellow
        Write-Host "   1. Check if port 3000 and 3001 are available" -ForegroundColor White
        Write-Host "   2. Try running: npm install" -ForegroundColor White
        Write-Host "   3. Try running: npm run build" -ForegroundColor White
        Write-Host "   4. Check Node.js version (should be 18+)" -ForegroundColor White
        pause
        exit 1
    }
}

Write-Host ""
Write-Host "Server stopped. Thank you for using Yasmin Alsham!" -ForegroundColor Green
