"use strict";exports.id=306,exports.ids=[306],exports.modules={1733:(e,r,t)=>{t.d(r,{A:()=>h});var a=t(60687),s=t(43210),n=t(93613);let l=e=>/^\d*\.?\d*$/.test(e),i=e=>/^\d*$/.test(e),o=e=>/^(\+)?\d*$/.test(e),d=e=>e.replace(/[^\d.]/g,""),c=e=>e.replace(/[^\d]/g,""),u=e=>e.startsWith("+")?"+"+e.slice(1).replace(/[^\d]/g,""):e.replace(/[^\d]/g,""),p=e=>{let r=parseFloat(e);return!isNaN(r)&&r>0},m=e=>{let r=parseFloat(e);return!isNaN(r)&&r>0},g=(e,r="ar")=>{let t={ar:{measurement:"يرجى إدخال رقم صحيح للمقاس",price:"يرجى إدخال سعر صحيح",phone:"يرجى إدخال رقم هاتف صحيح",orderNumber:"يرجى إدخال رقم طلب صحيح",numeric:"يرجى إدخال أرقام فقط",positive:"يرجى إدخال رقم أكبر من الصفر"},en:{measurement:"Please enter a valid measurement",price:"Please enter a valid price",phone:"Please enter a valid phone number",orderNumber:"Please enter a valid order number",numeric:"Please enter numbers only",positive:"Please enter a number greater than zero"}};return t[r][e]||t[r].numeric},x=(e,r,t,a)=>{let s=e,n=!0,x=null;switch(r){case"measurement":p(s=d(e))||""===s||""===s||(x=g("measurement"));break;case"price":m(s=d(e))||""===s||""===s||(x=g("price"));break;case"phone":o(s=u(e))||(x=g("phone"));break;case"orderNumber":i(s=c(e))||(x=g("orderNumber"));break;case"integer":i(s=c(e))||(x=g("numeric"));break;case"decimal":l(s=d(e))||(x=g("numeric"))}t(s),a&&a(x)};function h({value:e,onChange:r,type:t,placeholder:l,className:i="",disabled:o=!1,required:d=!1,label:c,id:u}){let[p,m]=(0,s.useState)(null),g=`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors duration-200 ${p?"border-red-500 bg-red-50":"border-gray-300"} ${o?"bg-gray-100 cursor-not-allowed":""} ${i}`;return(0,a.jsxs)("div",{className:"space-y-2",children:[c&&(0,a.jsxs)("label",{htmlFor:u,className:"block text-sm font-medium text-gray-700",children:[c,d&&(0,a.jsx)("span",{className:"text-red-500 mr-1",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{id:u,type:"text",value:e,onChange:e=>{x(e.target.value,t,r,m)},placeholder:l,className:g,disabled:o,required:d,inputMode:"phone"===t?"tel":"numeric",autoComplete:"phone"===t?"tel":"off"}),p&&(0,a.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2",children:(0,a.jsx)(n.A,{className:"w-5 h-5 text-red-500"})})]}),p&&(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center space-x-1 space-x-reverse",children:[(0,a.jsx)(n.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:p})]})]})}},34270:(e,r,t)=>{t.d(r,{D:()=>l});var a=t(26787),s=t(59350);let n=()=>Date.now().toString(36)+Math.random().toString(36).substr(2),l=(0,a.v)()((0,s.Zr)((e,r)=>({appointments:[],orders:[],workers:[],isLoading:!1,error:null,addAppointment:r=>{let t={...r,id:n(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({appointments:[...e.appointments,t],error:null})),console.log("✅ تم إضافة موعد جديد:",t)},updateAppointment:(r,t)=>{e(e=>({appointments:e.appointments.map(e=>e.id===r?{...e,...t,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الموعد:",r)},deleteAppointment:r=>{e(e=>({appointments:e.appointments.filter(e=>e.id!==r),error:null})),console.log("✅ تم حذف الموعد:",r)},getAppointment:e=>r().appointments.find(r=>r.id===e),addOrder:r=>{let t={...r,id:n(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({orders:[...e.orders,t],error:null})),console.log("✅ تم إضافة طلب جديد:",t)},updateOrder:(r,t)=>{e(e=>({orders:e.orders.map(e=>e.id===r?{...e,...t,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الطلب:",r)},deleteOrder:r=>{e(e=>({orders:e.orders.filter(e=>e.id!==r),error:null})),console.log("✅ تم حذف الطلب:",r)},getOrder:e=>r().orders.find(r=>r.id===e),addWorker:r=>{let t={...r,id:n(),role:"worker",is_active:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({workers:[...e.workers,t],error:null})),console.log("✅ تم إضافة عامل جديد:",t)},updateWorker:(r,t)=>{e(e=>({workers:e.workers.map(e=>e.id===r?{...e,...t,updatedAt:new Date().toISOString()}:e),error:null})),t.email||t.password||t.full_name,console.log("✅ تم تحديث العامل:",r)},deleteWorker:r=>{e(e=>({workers:e.workers.filter(e=>e.id!==r),error:null})),console.log("✅ تم حذف العامل:",r)},getWorker:e=>r().workers.find(r=>r.id===e),startOrderWork:(r,t)=>{e(e=>({orders:e.orders.map(e=>e.id===r&&e.assignedWorker===t?{...e,status:"in_progress",updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم بدء العمل في الطلب:",r)},completeOrder:(r,t,a=[])=>{e(e=>({orders:e.orders.map(e=>e.id===r&&e.assignedWorker===t?{...e,status:"completed",completedImages:a.length>0?a:void 0,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم إنهاء الطلب:",r)},clearError:()=>{e({error:null})},loadData:()=>{e({isLoading:!0}),e({isLoading:!1})},getStats:()=>{let e=r();return{totalAppointments:e.appointments.length,totalOrders:e.orders.length,totalWorkers:e.workers.length,pendingAppointments:e.appointments.filter(e=>"pending"===e.status).length,activeOrders:e.orders.filter(e=>["pending","in_progress"].includes(e.status)).length,completedOrders:e.orders.filter(e=>"completed"===e.status).length,totalRevenue:e.orders.filter(e=>"completed"===e.status).reduce((e,r)=>e+r.price,0)}}}),{name:"yasmin-data-storage",partialize:e=>({appointments:e.appointments,orders:e.orders,workers:e.workers})}))},61465:(e,r,t)=>{t.d(r,{A:()=>p});var a=t(60687),s=t(43210),n=t(26001),l=t(73259),i=t(82719),o=t(36058),d=t(97840),c=t(88233),u=t(99182);function p({voiceNotes:e=[],onVoiceNotesChange:r,disabled:t=!1}){let[p,m]=(0,s.useState)(!1),[g,x]=(0,s.useState)(null),{t:h}=(0,u.B)(),[b,v]=(0,s.useState)(0),[y,f]=(0,s.useState)(null),[w,j]=(0,s.useState)(null),N=(0,s.useRef)(null),k=(0,s.useRef)(new Map),A=(0,s.useRef)(null),S=(0,s.useRef)([]),_=e=>{let r=atob(e.split(",")[1]),t=Array(r.length);for(let e=0;e<r.length;e++)t[e]=r.charCodeAt(e);return new Blob([new Uint8Array(t)],{type:"audio/webm"})},D=async()=>{try{j(null);let t=await navigator.mediaDevices.getUserMedia({audio:!0}),a=new MediaRecorder(t);N.current=a,S.current=[],a.ondataavailable=e=>{e.data.size>0&&S.current.push(e.data)},a.onstop=()=>{let a=new Blob(S.current,{type:"audio/webm"});f(a);let s=new FileReader;s.onloadend=()=>{let t=s.result,a={id:Date.now().toString(),data:t,timestamp:Date.now(),duration:b},n=[...e,a];r(n)},s.readAsDataURL(a),t.getTracks().forEach(e=>e.stop())},a.start(),m(!0),v(0),A.current=setInterval(()=>{v(e=>e+1)},1e3)}catch(e){console.error("خطأ في بدء التسجيل:",e),j(h("microphone_access_error"))}},O=e=>{let r=k.current;if(g&&g!==e.id){let e=r.get(g);e&&e.pause()}let t=r.get(e.id);if(!t){let a=_(e.data);(t=new Audio(URL.createObjectURL(a))).onended=()=>x(null),r.set(e.id,t)}g===e.id?(t.pause(),x(null)):(t.play(),x(e.id))},L=t=>{let a=k.current,s=a.get(t);s&&(s.pause(),a.delete(t)),g===t&&x(null),r(e.filter(e=>e.id!==t))},I=e=>{let r=Math.floor(e/60);return`${r}:${(e%60).toString().padStart(2,"0")}`},C=e=>new Date(e).toLocaleString("ar-SA",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return(0,a.jsxs)("div",{className:"space-y-4",children:[w&&(0,a.jsx)("div",{className:"p-3 bg-red-50 text-red-800 border border-red-200 rounded-lg text-sm",children:w}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 border border-gray-200",children:p?(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)(n.P.div,{animate:{scale:[1,1.1,1]},transition:{duration:1,repeat:1/0},className:"inline-flex items-center space-x-2 space-x-reverse mb-4",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-red-600 font-medium",children:"جاري التسجيل..."})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-800 mb-4",children:I(b)}),(0,a.jsxs)("button",{type:"button",onClick:()=>{N.current&&p&&(N.current.stop(),m(!1),A.current&&(clearInterval(A.current),A.current=null))},className:"inline-flex items-center space-x-2 space-x-reverse px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-300",children:[(0,a.jsx)(i.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:h("stop_recording")})]})]}):(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("button",{type:"button",onClick:D,disabled:t,className:"inline-flex items-center space-x-2 space-x-reverse px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)(l.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:h("start_recording")})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:h("click_to_record_voice_note")})]})}),e.length>0&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-700",children:[h("voice_notes")," (",e.length,")"]})}),e.map((e,r)=>(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*r},className:"bg-white rounded-lg p-4 border border-gray-200 shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,a.jsx)("button",{type:"button",onClick:()=>O(e),className:"p-2 bg-pink-600 text-white rounded-full hover:bg-pink-700 transition-colors duration-300",children:g===e.id?(0,a.jsx)(o.A,{className:"w-4 h-4"}):(0,a.jsx)(d.A,{className:"w-4 h-4"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-sm font-medium text-gray-800",children:[h("voice_note")," #",r+1]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[C(e.timestamp),e.duration&&` • ${I(e.duration)}`]})]})]}),(0,a.jsx)("button",{type:"button",onClick:()=>L(e.id),disabled:t,className:"p-2 text-red-600 hover:bg-red-50 rounded-full transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed",title:h("delete"),children:(0,a.jsx)(c.A,{className:"w-4 h-4"})})]})},e.id))]})]})}},65938:(e,r,t)=>{t.d(r,{A:()=>p});var a=t(60687),s=t(43210),n=t(88920),l=t(26001),i=t(9005),o=t(16023),d=t(11860),c=t(96474),u=t(99182);function p({images:e,onImagesChange:r,maxImages:t=10}){let[p,m]=(0,s.useState)(!1),g=(0,s.useRef)(null),{t:x}=(0,u.B)(),h=a=>{if(!a)return;let s=[],n=t-e.length;Array.from(a).slice(0,n).forEach(t=>{if(t.type.startsWith("image/")){let l=new FileReader;l.onload=t=>{let l=t.target?.result;l&&(s.push(l),s.length===Math.min(a.length,n)&&r([...e,...s]))},l.readAsDataURL(t)}})},b=t=>{r(e.filter((e,r)=>r!==t))},v=()=>{g.current?.click()};return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:`border-2 border-dashed rounded-lg p-6 text-center transition-all duration-300 cursor-pointer ${p?"border-pink-400 bg-pink-50":e.length>=t?"border-gray-200 bg-gray-50 cursor-not-allowed":"border-gray-300 hover:border-pink-400 hover:bg-pink-50"}`,onDrop:e=>{e.preventDefault(),m(!1),h(e.dataTransfer.files)},onDragOver:e=>{e.preventDefault(),m(!0)},onDragLeave:e=>{e.preventDefault(),m(!1)},onClick:e.length<t?v:void 0,children:[(0,a.jsx)("input",{ref:g,type:"file",accept:"image/*",multiple:!0,onChange:e=>h(e.target.files),className:"hidden",disabled:e.length>=t}),e.length>=t?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.A,{className:"w-12 h-12 text-gray-400 mx-auto"}),(0,a.jsxs)("p",{className:"text-gray-500",children:[x("max_images_reached")," (",t,")"]})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(o.A,{className:"w-12 h-12 text-gray-400 mx-auto"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-medium text-gray-700",children:x(p?"drop_images_here":"click_or_drag_images")}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[x("image_upload_format")," (",x("max_images_text")," ",t," ",x("images_text"),")"]}),(0,a.jsxs)("p",{className:"text-xs text-gray-400 mt-1",children:[e.length," ",x("of")," ",t," ",x("images_text")]})]})]})]}),e.length>0&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-700",children:"الصور المرفوعة:"}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4",children:(0,a.jsxs)(n.N,{children:[e.map((e,r)=>(0,a.jsxs)(l.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:"relative group",children:[(0,a.jsx)("div",{className:"aspect-square rounded-lg overflow-hidden border border-gray-200",children:(0,a.jsx)("img",{src:e,alt:`صورة ${r+1}`,className:"w-full h-full object-cover"})}),(0,a.jsx)("button",{onClick:()=>b(r),className:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-red-600",children:(0,a.jsx)(d.A,{className:"w-3 h-3"})}),(0,a.jsx)("div",{className:"absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded",children:r+1})]},r)),e.length<t&&(0,a.jsx)(l.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},className:"aspect-square border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center cursor-pointer hover:border-pink-400 hover:bg-pink-50 transition-all duration-300",onClick:v,children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(c.A,{className:"w-8 h-8 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:x("add_image")})]})})]})})]})]})}},99720:(e,r,t)=>{t.d(r,{n:()=>l});var a=t(26787),s=t(59350);let n=()=>[],l=(0,a.v)()((0,s.Zr)((e,r)=>({user:null,isLoading:!1,error:null,signIn:async(r,t)=>{e({isLoading:!0,error:null});try{console.log("\uD83D\uDD10 بدء عملية تسجيل الدخول...",{email:r}),await new Promise(e=>setTimeout(e,1500));let a=n().find(e=>e.email.toLowerCase()===r.toLowerCase()&&e.password===t);if(!a)return console.log("❌ بيانات تسجيل الدخول غير صحيحة"),e({error:"بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور.",isLoading:!1}),!1;{console.log("✅ تم العثور على المستخدم:",a.full_name);let r={id:a.id,email:a.email,full_name:a.full_name,role:a.role,is_active:a.is_active,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),token:`demo-token-${a.id}-${Date.now()}`};return e({user:r,isLoading:!1,error:null}),console.log("\uD83C\uDF89 تم تسجيل الدخول بنجاح!"),!0}}catch(r){return console.error("\uD83D\uDCA5 خطأ في تسجيل الدخول:",r),e({error:"حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.",isLoading:!1}),!1}},signOut:async()=>{e({isLoading:!0});try{await new Promise(e=>setTimeout(e,500)),e({user:null,isLoading:!1,error:null})}catch(r){console.error("خطأ في تسجيل الخروج:",r),e({isLoading:!1,error:"خطأ في تسجيل الخروج"})}},setUser:r=>{e({user:r})},clearError:()=>{e({error:null})},checkAuth:async()=>{e({isLoading:!0});try{e({user:null,isLoading:!1})}catch(r){console.error("خطأ في التحقق من المصادقة:",r),e({user:null,isLoading:!1})}},isAuthenticated:()=>{let e=r();return null!==e.user&&e.user.is_active}}),{name:"yasmin-auth-storage",partialize:e=>({user:e.user})}))}};