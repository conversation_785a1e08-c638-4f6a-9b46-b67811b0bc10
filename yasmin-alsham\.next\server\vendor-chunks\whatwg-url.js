"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/whatwg-url";
exports.ids = ["vendor-chunks/whatwg-url"];
exports.modules = {

/***/ "(ssr)/./node_modules/whatwg-url/lib/URL-impl.js":
/*!*************************************************!*\
  !*** ./node_modules/whatwg-url/lib/URL-impl.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nconst usm = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\");\n\nexports.implementation = class URLImpl {\n  constructor(constructorArgs) {\n    const url = constructorArgs[0];\n    const base = constructorArgs[1];\n\n    let parsedBase = null;\n    if (base !== undefined) {\n      parsedBase = usm.basicURLParse(base);\n      if (parsedBase === \"failure\") {\n        throw new TypeError(\"Invalid base URL\");\n      }\n    }\n\n    const parsedURL = usm.basicURLParse(url, { baseURL: parsedBase });\n    if (parsedURL === \"failure\") {\n      throw new TypeError(\"Invalid URL\");\n    }\n\n    this._url = parsedURL;\n\n    // TODO: query stuff\n  }\n\n  get href() {\n    return usm.serializeURL(this._url);\n  }\n\n  set href(v) {\n    const parsedURL = usm.basicURLParse(v);\n    if (parsedURL === \"failure\") {\n      throw new TypeError(\"Invalid URL\");\n    }\n\n    this._url = parsedURL;\n  }\n\n  get origin() {\n    return usm.serializeURLOrigin(this._url);\n  }\n\n  get protocol() {\n    return this._url.scheme + \":\";\n  }\n\n  set protocol(v) {\n    usm.basicURLParse(v + \":\", { url: this._url, stateOverride: \"scheme start\" });\n  }\n\n  get username() {\n    return this._url.username;\n  }\n\n  set username(v) {\n    if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n      return;\n    }\n\n    usm.setTheUsername(this._url, v);\n  }\n\n  get password() {\n    return this._url.password;\n  }\n\n  set password(v) {\n    if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n      return;\n    }\n\n    usm.setThePassword(this._url, v);\n  }\n\n  get host() {\n    const url = this._url;\n\n    if (url.host === null) {\n      return \"\";\n    }\n\n    if (url.port === null) {\n      return usm.serializeHost(url.host);\n    }\n\n    return usm.serializeHost(url.host) + \":\" + usm.serializeInteger(url.port);\n  }\n\n  set host(v) {\n    if (this._url.cannotBeABaseURL) {\n      return;\n    }\n\n    usm.basicURLParse(v, { url: this._url, stateOverride: \"host\" });\n  }\n\n  get hostname() {\n    if (this._url.host === null) {\n      return \"\";\n    }\n\n    return usm.serializeHost(this._url.host);\n  }\n\n  set hostname(v) {\n    if (this._url.cannotBeABaseURL) {\n      return;\n    }\n\n    usm.basicURLParse(v, { url: this._url, stateOverride: \"hostname\" });\n  }\n\n  get port() {\n    if (this._url.port === null) {\n      return \"\";\n    }\n\n    return usm.serializeInteger(this._url.port);\n  }\n\n  set port(v) {\n    if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n      return;\n    }\n\n    if (v === \"\") {\n      this._url.port = null;\n    } else {\n      usm.basicURLParse(v, { url: this._url, stateOverride: \"port\" });\n    }\n  }\n\n  get pathname() {\n    if (this._url.cannotBeABaseURL) {\n      return this._url.path[0];\n    }\n\n    if (this._url.path.length === 0) {\n      return \"\";\n    }\n\n    return \"/\" + this._url.path.join(\"/\");\n  }\n\n  set pathname(v) {\n    if (this._url.cannotBeABaseURL) {\n      return;\n    }\n\n    this._url.path = [];\n    usm.basicURLParse(v, { url: this._url, stateOverride: \"path start\" });\n  }\n\n  get search() {\n    if (this._url.query === null || this._url.query === \"\") {\n      return \"\";\n    }\n\n    return \"?\" + this._url.query;\n  }\n\n  set search(v) {\n    // TODO: query stuff\n\n    const url = this._url;\n\n    if (v === \"\") {\n      url.query = null;\n      return;\n    }\n\n    const input = v[0] === \"?\" ? v.substring(1) : v;\n    url.query = \"\";\n    usm.basicURLParse(input, { url, stateOverride: \"query\" });\n  }\n\n  get hash() {\n    if (this._url.fragment === null || this._url.fragment === \"\") {\n      return \"\";\n    }\n\n    return \"#\" + this._url.fragment;\n  }\n\n  set hash(v) {\n    if (v === \"\") {\n      this._url.fragment = null;\n      return;\n    }\n\n    const input = v[0] === \"#\" ? v.substring(1) : v;\n    this._url.fragment = \"\";\n    usm.basicURLParse(input, { url: this._url, stateOverride: \"fragment\" });\n  }\n\n  toJSON() {\n    return this.href;\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2hhdHdnLXVybC9saWIvVVJMLWltcGwuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYixZQUFZLG1CQUFPLENBQUMscUZBQXFCOztBQUV6QyxzQkFBc0I7QUFDdEI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLCtDQUErQyxxQkFBcUI7QUFDcEU7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsaUNBQWlDLCtDQUErQztBQUNoRjs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSwyQkFBMkIsdUNBQXVDO0FBQ2xFOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsMkJBQTJCLDJDQUEyQztBQUN0RTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxNQUFNO0FBQ04sNkJBQTZCLHVDQUF1QztBQUNwRTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsMkJBQTJCLDZDQUE2QztBQUN4RTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLCtCQUErQiw2QkFBNkI7QUFDNUQ7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSwrQkFBK0IsMkNBQTJDO0FBQzFFOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGtoYWxlXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFlBU01JTiBBTFNIQU1cXHlhc21pbi1hbHNoYW1cXG5vZGVfbW9kdWxlc1xcd2hhdHdnLXVybFxcbGliXFxVUkwtaW1wbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbmNvbnN0IHVzbSA9IHJlcXVpcmUoXCIuL3VybC1zdGF0ZS1tYWNoaW5lXCIpO1xuXG5leHBvcnRzLmltcGxlbWVudGF0aW9uID0gY2xhc3MgVVJMSW1wbCB7XG4gIGNvbnN0cnVjdG9yKGNvbnN0cnVjdG9yQXJncykge1xuICAgIGNvbnN0IHVybCA9IGNvbnN0cnVjdG9yQXJnc1swXTtcbiAgICBjb25zdCBiYXNlID0gY29uc3RydWN0b3JBcmdzWzFdO1xuXG4gICAgbGV0IHBhcnNlZEJhc2UgPSBudWxsO1xuICAgIGlmIChiYXNlICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIHBhcnNlZEJhc2UgPSB1c20uYmFzaWNVUkxQYXJzZShiYXNlKTtcbiAgICAgIGlmIChwYXJzZWRCYXNlID09PSBcImZhaWx1cmVcIikge1xuICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFwiSW52YWxpZCBiYXNlIFVSTFwiKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zdCBwYXJzZWRVUkwgPSB1c20uYmFzaWNVUkxQYXJzZSh1cmwsIHsgYmFzZVVSTDogcGFyc2VkQmFzZSB9KTtcbiAgICBpZiAocGFyc2VkVVJMID09PSBcImZhaWx1cmVcIikge1xuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcIkludmFsaWQgVVJMXCIpO1xuICAgIH1cblxuICAgIHRoaXMuX3VybCA9IHBhcnNlZFVSTDtcblxuICAgIC8vIFRPRE86IHF1ZXJ5IHN0dWZmXG4gIH1cblxuICBnZXQgaHJlZigpIHtcbiAgICByZXR1cm4gdXNtLnNlcmlhbGl6ZVVSTCh0aGlzLl91cmwpO1xuICB9XG5cbiAgc2V0IGhyZWYodikge1xuICAgIGNvbnN0IHBhcnNlZFVSTCA9IHVzbS5iYXNpY1VSTFBhcnNlKHYpO1xuICAgIGlmIChwYXJzZWRVUkwgPT09IFwiZmFpbHVyZVwiKSB7XG4gICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFwiSW52YWxpZCBVUkxcIik7XG4gICAgfVxuXG4gICAgdGhpcy5fdXJsID0gcGFyc2VkVVJMO1xuICB9XG5cbiAgZ2V0IG9yaWdpbigpIHtcbiAgICByZXR1cm4gdXNtLnNlcmlhbGl6ZVVSTE9yaWdpbih0aGlzLl91cmwpO1xuICB9XG5cbiAgZ2V0IHByb3RvY29sKCkge1xuICAgIHJldHVybiB0aGlzLl91cmwuc2NoZW1lICsgXCI6XCI7XG4gIH1cblxuICBzZXQgcHJvdG9jb2wodikge1xuICAgIHVzbS5iYXNpY1VSTFBhcnNlKHYgKyBcIjpcIiwgeyB1cmw6IHRoaXMuX3VybCwgc3RhdGVPdmVycmlkZTogXCJzY2hlbWUgc3RhcnRcIiB9KTtcbiAgfVxuXG4gIGdldCB1c2VybmFtZSgpIHtcbiAgICByZXR1cm4gdGhpcy5fdXJsLnVzZXJuYW1lO1xuICB9XG5cbiAgc2V0IHVzZXJuYW1lKHYpIHtcbiAgICBpZiAodXNtLmNhbm5vdEhhdmVBVXNlcm5hbWVQYXNzd29yZFBvcnQodGhpcy5fdXJsKSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHVzbS5zZXRUaGVVc2VybmFtZSh0aGlzLl91cmwsIHYpO1xuICB9XG5cbiAgZ2V0IHBhc3N3b3JkKCkge1xuICAgIHJldHVybiB0aGlzLl91cmwucGFzc3dvcmQ7XG4gIH1cblxuICBzZXQgcGFzc3dvcmQodikge1xuICAgIGlmICh1c20uY2Fubm90SGF2ZUFVc2VybmFtZVBhc3N3b3JkUG9ydCh0aGlzLl91cmwpKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdXNtLnNldFRoZVBhc3N3b3JkKHRoaXMuX3VybCwgdik7XG4gIH1cblxuICBnZXQgaG9zdCgpIHtcbiAgICBjb25zdCB1cmwgPSB0aGlzLl91cmw7XG5cbiAgICBpZiAodXJsLmhvc3QgPT09IG51bGwpIHtcbiAgICAgIHJldHVybiBcIlwiO1xuICAgIH1cblxuICAgIGlmICh1cmwucG9ydCA9PT0gbnVsbCkge1xuICAgICAgcmV0dXJuIHVzbS5zZXJpYWxpemVIb3N0KHVybC5ob3N0KTtcbiAgICB9XG5cbiAgICByZXR1cm4gdXNtLnNlcmlhbGl6ZUhvc3QodXJsLmhvc3QpICsgXCI6XCIgKyB1c20uc2VyaWFsaXplSW50ZWdlcih1cmwucG9ydCk7XG4gIH1cblxuICBzZXQgaG9zdCh2KSB7XG4gICAgaWYgKHRoaXMuX3VybC5jYW5ub3RCZUFCYXNlVVJMKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdXNtLmJhc2ljVVJMUGFyc2UodiwgeyB1cmw6IHRoaXMuX3VybCwgc3RhdGVPdmVycmlkZTogXCJob3N0XCIgfSk7XG4gIH1cblxuICBnZXQgaG9zdG5hbWUoKSB7XG4gICAgaWYgKHRoaXMuX3VybC5ob3N0ID09PSBudWxsKSB7XG4gICAgICByZXR1cm4gXCJcIjtcbiAgICB9XG5cbiAgICByZXR1cm4gdXNtLnNlcmlhbGl6ZUhvc3QodGhpcy5fdXJsLmhvc3QpO1xuICB9XG5cbiAgc2V0IGhvc3RuYW1lKHYpIHtcbiAgICBpZiAodGhpcy5fdXJsLmNhbm5vdEJlQUJhc2VVUkwpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB1c20uYmFzaWNVUkxQYXJzZSh2LCB7IHVybDogdGhpcy5fdXJsLCBzdGF0ZU92ZXJyaWRlOiBcImhvc3RuYW1lXCIgfSk7XG4gIH1cblxuICBnZXQgcG9ydCgpIHtcbiAgICBpZiAodGhpcy5fdXJsLnBvcnQgPT09IG51bGwpIHtcbiAgICAgIHJldHVybiBcIlwiO1xuICAgIH1cblxuICAgIHJldHVybiB1c20uc2VyaWFsaXplSW50ZWdlcih0aGlzLl91cmwucG9ydCk7XG4gIH1cblxuICBzZXQgcG9ydCh2KSB7XG4gICAgaWYgKHVzbS5jYW5ub3RIYXZlQVVzZXJuYW1lUGFzc3dvcmRQb3J0KHRoaXMuX3VybCkpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBpZiAodiA9PT0gXCJcIikge1xuICAgICAgdGhpcy5fdXJsLnBvcnQgPSBudWxsO1xuICAgIH0gZWxzZSB7XG4gICAgICB1c20uYmFzaWNVUkxQYXJzZSh2LCB7IHVybDogdGhpcy5fdXJsLCBzdGF0ZU92ZXJyaWRlOiBcInBvcnRcIiB9KTtcbiAgICB9XG4gIH1cblxuICBnZXQgcGF0aG5hbWUoKSB7XG4gICAgaWYgKHRoaXMuX3VybC5jYW5ub3RCZUFCYXNlVVJMKSB7XG4gICAgICByZXR1cm4gdGhpcy5fdXJsLnBhdGhbMF07XG4gICAgfVxuXG4gICAgaWYgKHRoaXMuX3VybC5wYXRoLmxlbmd0aCA9PT0gMCkge1xuICAgICAgcmV0dXJuIFwiXCI7XG4gICAgfVxuXG4gICAgcmV0dXJuIFwiL1wiICsgdGhpcy5fdXJsLnBhdGguam9pbihcIi9cIik7XG4gIH1cblxuICBzZXQgcGF0aG5hbWUodikge1xuICAgIGlmICh0aGlzLl91cmwuY2Fubm90QmVBQmFzZVVSTCkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRoaXMuX3VybC5wYXRoID0gW107XG4gICAgdXNtLmJhc2ljVVJMUGFyc2UodiwgeyB1cmw6IHRoaXMuX3VybCwgc3RhdGVPdmVycmlkZTogXCJwYXRoIHN0YXJ0XCIgfSk7XG4gIH1cblxuICBnZXQgc2VhcmNoKCkge1xuICAgIGlmICh0aGlzLl91cmwucXVlcnkgPT09IG51bGwgfHwgdGhpcy5fdXJsLnF1ZXJ5ID09PSBcIlwiKSB7XG4gICAgICByZXR1cm4gXCJcIjtcbiAgICB9XG5cbiAgICByZXR1cm4gXCI/XCIgKyB0aGlzLl91cmwucXVlcnk7XG4gIH1cblxuICBzZXQgc2VhcmNoKHYpIHtcbiAgICAvLyBUT0RPOiBxdWVyeSBzdHVmZlxuXG4gICAgY29uc3QgdXJsID0gdGhpcy5fdXJsO1xuXG4gICAgaWYgKHYgPT09IFwiXCIpIHtcbiAgICAgIHVybC5xdWVyeSA9IG51bGw7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc3QgaW5wdXQgPSB2WzBdID09PSBcIj9cIiA/IHYuc3Vic3RyaW5nKDEpIDogdjtcbiAgICB1cmwucXVlcnkgPSBcIlwiO1xuICAgIHVzbS5iYXNpY1VSTFBhcnNlKGlucHV0LCB7IHVybCwgc3RhdGVPdmVycmlkZTogXCJxdWVyeVwiIH0pO1xuICB9XG5cbiAgZ2V0IGhhc2goKSB7XG4gICAgaWYgKHRoaXMuX3VybC5mcmFnbWVudCA9PT0gbnVsbCB8fCB0aGlzLl91cmwuZnJhZ21lbnQgPT09IFwiXCIpIHtcbiAgICAgIHJldHVybiBcIlwiO1xuICAgIH1cblxuICAgIHJldHVybiBcIiNcIiArIHRoaXMuX3VybC5mcmFnbWVudDtcbiAgfVxuXG4gIHNldCBoYXNoKHYpIHtcbiAgICBpZiAodiA9PT0gXCJcIikge1xuICAgICAgdGhpcy5fdXJsLmZyYWdtZW50ID0gbnVsbDtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBjb25zdCBpbnB1dCA9IHZbMF0gPT09IFwiI1wiID8gdi5zdWJzdHJpbmcoMSkgOiB2O1xuICAgIHRoaXMuX3VybC5mcmFnbWVudCA9IFwiXCI7XG4gICAgdXNtLmJhc2ljVVJMUGFyc2UoaW5wdXQsIHsgdXJsOiB0aGlzLl91cmwsIHN0YXRlT3ZlcnJpZGU6IFwiZnJhZ21lbnRcIiB9KTtcbiAgfVxuXG4gIHRvSlNPTigpIHtcbiAgICByZXR1cm4gdGhpcy5ocmVmO1xuICB9XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/whatwg-url/lib/URL-impl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/whatwg-url/lib/URL.js":
/*!********************************************!*\
  !*** ./node_modules/whatwg-url/lib/URL.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst conversions = __webpack_require__(/*! webidl-conversions */ \"(ssr)/./node_modules/webidl-conversions/lib/index.js\");\nconst utils = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/whatwg-url/lib/utils.js\");\nconst Impl = __webpack_require__(/*! .//URL-impl.js */ \"(ssr)/./node_modules/whatwg-url/lib/URL-impl.js\");\n\nconst impl = utils.implSymbol;\n\nfunction URL(url) {\n  if (!this || this[impl] || !(this instanceof URL)) {\n    throw new TypeError(\"Failed to construct 'URL': Please use the 'new' operator, this DOM object constructor cannot be called as a function.\");\n  }\n  if (arguments.length < 1) {\n    throw new TypeError(\"Failed to construct 'URL': 1 argument required, but only \" + arguments.length + \" present.\");\n  }\n  const args = [];\n  for (let i = 0; i < arguments.length && i < 2; ++i) {\n    args[i] = arguments[i];\n  }\n  args[0] = conversions[\"USVString\"](args[0]);\n  if (args[1] !== undefined) {\n  args[1] = conversions[\"USVString\"](args[1]);\n  }\n\n  module.exports.setup(this, args);\n}\n\nURL.prototype.toJSON = function toJSON() {\n  if (!this || !module.exports.is(this)) {\n    throw new TypeError(\"Illegal invocation\");\n  }\n  const args = [];\n  for (let i = 0; i < arguments.length && i < 0; ++i) {\n    args[i] = arguments[i];\n  }\n  return this[impl].toJSON.apply(this[impl], args);\n};\nObject.defineProperty(URL.prototype, \"href\", {\n  get() {\n    return this[impl].href;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].href = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nURL.prototype.toString = function () {\n  if (!this || !module.exports.is(this)) {\n    throw new TypeError(\"Illegal invocation\");\n  }\n  return this.href;\n};\n\nObject.defineProperty(URL.prototype, \"origin\", {\n  get() {\n    return this[impl].origin;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"protocol\", {\n  get() {\n    return this[impl].protocol;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].protocol = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"username\", {\n  get() {\n    return this[impl].username;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].username = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"password\", {\n  get() {\n    return this[impl].password;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].password = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"host\", {\n  get() {\n    return this[impl].host;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].host = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"hostname\", {\n  get() {\n    return this[impl].hostname;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].hostname = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"port\", {\n  get() {\n    return this[impl].port;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].port = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"pathname\", {\n  get() {\n    return this[impl].pathname;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].pathname = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"search\", {\n  get() {\n    return this[impl].search;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].search = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"hash\", {\n  get() {\n    return this[impl].hash;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].hash = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\n\nmodule.exports = {\n  is(obj) {\n    return !!obj && obj[impl] instanceof Impl.implementation;\n  },\n  create(constructorArgs, privateData) {\n    let obj = Object.create(URL.prototype);\n    this.setup(obj, constructorArgs, privateData);\n    return obj;\n  },\n  setup(obj, constructorArgs, privateData) {\n    if (!privateData) privateData = {};\n    privateData.wrapper = obj;\n\n    obj[impl] = new Impl.implementation(constructorArgs, privateData);\n    obj[impl][utils.wrapperSymbol] = obj;\n  },\n  interface: URL,\n  expose: {\n    Window: { URL: URL },\n    Worker: { URL: URL }\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/whatwg-url/lib/URL.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/whatwg-url/lib/public-api.js":
/*!***************************************************!*\
  !*** ./node_modules/whatwg-url/lib/public-api.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nexports.URL = __webpack_require__(/*! ./URL */ \"(ssr)/./node_modules/whatwg-url/lib/URL.js\")[\"interface\"];\nexports.serializeURL = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeURL;\nexports.serializeURLOrigin = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeURLOrigin;\nexports.basicURLParse = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").basicURLParse;\nexports.setTheUsername = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").setTheUsername;\nexports.setThePassword = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").setThePassword;\nexports.serializeHost = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeHost;\nexports.serializeInteger = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeInteger;\nexports.parseURL = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").parseURL;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2hhdHdnLXVybC9saWIvcHVibGljLWFwaS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYix5R0FBd0M7QUFDeEMsOElBQWtFO0FBQ2xFLDBKQUE4RTtBQUM5RSxnSkFBb0U7QUFDcEUsa0pBQXNFO0FBQ3RFLGtKQUFzRTtBQUN0RSxnSkFBb0U7QUFDcEUsc0pBQTBFO0FBQzFFLHNJQUEwRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxraGFsZVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxZQVNNSU4gQUxTSEFNXFx5YXNtaW4tYWxzaGFtXFxub2RlX21vZHVsZXNcXHdoYXR3Zy11cmxcXGxpYlxccHVibGljLWFwaS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuZXhwb3J0cy5VUkwgPSByZXF1aXJlKFwiLi9VUkxcIikuaW50ZXJmYWNlO1xuZXhwb3J0cy5zZXJpYWxpemVVUkwgPSByZXF1aXJlKFwiLi91cmwtc3RhdGUtbWFjaGluZVwiKS5zZXJpYWxpemVVUkw7XG5leHBvcnRzLnNlcmlhbGl6ZVVSTE9yaWdpbiA9IHJlcXVpcmUoXCIuL3VybC1zdGF0ZS1tYWNoaW5lXCIpLnNlcmlhbGl6ZVVSTE9yaWdpbjtcbmV4cG9ydHMuYmFzaWNVUkxQYXJzZSA9IHJlcXVpcmUoXCIuL3VybC1zdGF0ZS1tYWNoaW5lXCIpLmJhc2ljVVJMUGFyc2U7XG5leHBvcnRzLnNldFRoZVVzZXJuYW1lID0gcmVxdWlyZShcIi4vdXJsLXN0YXRlLW1hY2hpbmVcIikuc2V0VGhlVXNlcm5hbWU7XG5leHBvcnRzLnNldFRoZVBhc3N3b3JkID0gcmVxdWlyZShcIi4vdXJsLXN0YXRlLW1hY2hpbmVcIikuc2V0VGhlUGFzc3dvcmQ7XG5leHBvcnRzLnNlcmlhbGl6ZUhvc3QgPSByZXF1aXJlKFwiLi91cmwtc3RhdGUtbWFjaGluZVwiKS5zZXJpYWxpemVIb3N0O1xuZXhwb3J0cy5zZXJpYWxpemVJbnRlZ2VyID0gcmVxdWlyZShcIi4vdXJsLXN0YXRlLW1hY2hpbmVcIikuc2VyaWFsaXplSW50ZWdlcjtcbmV4cG9ydHMucGFyc2VVUkwgPSByZXF1aXJlKFwiLi91cmwtc3RhdGUtbWFjaGluZVwiKS5wYXJzZVVSTDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/whatwg-url/lib/public-api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js":
/*!**********************************************************!*\
  !*** ./node_modules/whatwg-url/lib/url-state-machine.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\r\nconst punycode = __webpack_require__(/*! punycode */ \"punycode\");\r\nconst tr46 = __webpack_require__(/*! tr46 */ \"(ssr)/./node_modules/tr46/index.js\");\r\n\r\nconst specialSchemes = {\r\n  ftp: 21,\r\n  file: null,\r\n  gopher: 70,\r\n  http: 80,\r\n  https: 443,\r\n  ws: 80,\r\n  wss: 443\r\n};\r\n\r\nconst failure = Symbol(\"failure\");\r\n\r\nfunction countSymbols(str) {\r\n  return punycode.ucs2.decode(str).length;\r\n}\r\n\r\nfunction at(input, idx) {\r\n  const c = input[idx];\r\n  return isNaN(c) ? undefined : String.fromCodePoint(c);\r\n}\r\n\r\nfunction isASCIIDigit(c) {\r\n  return c >= 0x30 && c <= 0x39;\r\n}\r\n\r\nfunction isASCIIAlpha(c) {\r\n  return (c >= 0x41 && c <= 0x5A) || (c >= 0x61 && c <= 0x7A);\r\n}\r\n\r\nfunction isASCIIAlphanumeric(c) {\r\n  return isASCIIAlpha(c) || isASCIIDigit(c);\r\n}\r\n\r\nfunction isASCIIHex(c) {\r\n  return isASCIIDigit(c) || (c >= 0x41 && c <= 0x46) || (c >= 0x61 && c <= 0x66);\r\n}\r\n\r\nfunction isSingleDot(buffer) {\r\n  return buffer === \".\" || buffer.toLowerCase() === \"%2e\";\r\n}\r\n\r\nfunction isDoubleDot(buffer) {\r\n  buffer = buffer.toLowerCase();\r\n  return buffer === \"..\" || buffer === \"%2e.\" || buffer === \".%2e\" || buffer === \"%2e%2e\";\r\n}\r\n\r\nfunction isWindowsDriveLetterCodePoints(cp1, cp2) {\r\n  return isASCIIAlpha(cp1) && (cp2 === 58 || cp2 === 124);\r\n}\r\n\r\nfunction isWindowsDriveLetterString(string) {\r\n  return string.length === 2 && isASCIIAlpha(string.codePointAt(0)) && (string[1] === \":\" || string[1] === \"|\");\r\n}\r\n\r\nfunction isNormalizedWindowsDriveLetterString(string) {\r\n  return string.length === 2 && isASCIIAlpha(string.codePointAt(0)) && string[1] === \":\";\r\n}\r\n\r\nfunction containsForbiddenHostCodePoint(string) {\r\n  return string.search(/\\u0000|\\u0009|\\u000A|\\u000D|\\u0020|#|%|\\/|:|\\?|@|\\[|\\\\|\\]/) !== -1;\r\n}\r\n\r\nfunction containsForbiddenHostCodePointExcludingPercent(string) {\r\n  return string.search(/\\u0000|\\u0009|\\u000A|\\u000D|\\u0020|#|\\/|:|\\?|@|\\[|\\\\|\\]/) !== -1;\r\n}\r\n\r\nfunction isSpecialScheme(scheme) {\r\n  return specialSchemes[scheme] !== undefined;\r\n}\r\n\r\nfunction isSpecial(url) {\r\n  return isSpecialScheme(url.scheme);\r\n}\r\n\r\nfunction defaultPort(scheme) {\r\n  return specialSchemes[scheme];\r\n}\r\n\r\nfunction percentEncode(c) {\r\n  let hex = c.toString(16).toUpperCase();\r\n  if (hex.length === 1) {\r\n    hex = \"0\" + hex;\r\n  }\r\n\r\n  return \"%\" + hex;\r\n}\r\n\r\nfunction utf8PercentEncode(c) {\r\n  const buf = new Buffer(c);\r\n\r\n  let str = \"\";\r\n\r\n  for (let i = 0; i < buf.length; ++i) {\r\n    str += percentEncode(buf[i]);\r\n  }\r\n\r\n  return str;\r\n}\r\n\r\nfunction utf8PercentDecode(str) {\r\n  const input = new Buffer(str);\r\n  const output = [];\r\n  for (let i = 0; i < input.length; ++i) {\r\n    if (input[i] !== 37) {\r\n      output.push(input[i]);\r\n    } else if (input[i] === 37 && isASCIIHex(input[i + 1]) && isASCIIHex(input[i + 2])) {\r\n      output.push(parseInt(input.slice(i + 1, i + 3).toString(), 16));\r\n      i += 2;\r\n    } else {\r\n      output.push(input[i]);\r\n    }\r\n  }\r\n  return new Buffer(output).toString();\r\n}\r\n\r\nfunction isC0ControlPercentEncode(c) {\r\n  return c <= 0x1F || c > 0x7E;\r\n}\r\n\r\nconst extraPathPercentEncodeSet = new Set([32, 34, 35, 60, 62, 63, 96, 123, 125]);\r\nfunction isPathPercentEncode(c) {\r\n  return isC0ControlPercentEncode(c) || extraPathPercentEncodeSet.has(c);\r\n}\r\n\r\nconst extraUserinfoPercentEncodeSet =\r\n  new Set([47, 58, 59, 61, 64, 91, 92, 93, 94, 124]);\r\nfunction isUserinfoPercentEncode(c) {\r\n  return isPathPercentEncode(c) || extraUserinfoPercentEncodeSet.has(c);\r\n}\r\n\r\nfunction percentEncodeChar(c, encodeSetPredicate) {\r\n  const cStr = String.fromCodePoint(c);\r\n\r\n  if (encodeSetPredicate(c)) {\r\n    return utf8PercentEncode(cStr);\r\n  }\r\n\r\n  return cStr;\r\n}\r\n\r\nfunction parseIPv4Number(input) {\r\n  let R = 10;\r\n\r\n  if (input.length >= 2 && input.charAt(0) === \"0\" && input.charAt(1).toLowerCase() === \"x\") {\r\n    input = input.substring(2);\r\n    R = 16;\r\n  } else if (input.length >= 2 && input.charAt(0) === \"0\") {\r\n    input = input.substring(1);\r\n    R = 8;\r\n  }\r\n\r\n  if (input === \"\") {\r\n    return 0;\r\n  }\r\n\r\n  const regex = R === 10 ? /[^0-9]/ : (R === 16 ? /[^0-9A-Fa-f]/ : /[^0-7]/);\r\n  if (regex.test(input)) {\r\n    return failure;\r\n  }\r\n\r\n  return parseInt(input, R);\r\n}\r\n\r\nfunction parseIPv4(input) {\r\n  const parts = input.split(\".\");\r\n  if (parts[parts.length - 1] === \"\") {\r\n    if (parts.length > 1) {\r\n      parts.pop();\r\n    }\r\n  }\r\n\r\n  if (parts.length > 4) {\r\n    return input;\r\n  }\r\n\r\n  const numbers = [];\r\n  for (const part of parts) {\r\n    if (part === \"\") {\r\n      return input;\r\n    }\r\n    const n = parseIPv4Number(part);\r\n    if (n === failure) {\r\n      return input;\r\n    }\r\n\r\n    numbers.push(n);\r\n  }\r\n\r\n  for (let i = 0; i < numbers.length - 1; ++i) {\r\n    if (numbers[i] > 255) {\r\n      return failure;\r\n    }\r\n  }\r\n  if (numbers[numbers.length - 1] >= Math.pow(256, 5 - numbers.length)) {\r\n    return failure;\r\n  }\r\n\r\n  let ipv4 = numbers.pop();\r\n  let counter = 0;\r\n\r\n  for (const n of numbers) {\r\n    ipv4 += n * Math.pow(256, 3 - counter);\r\n    ++counter;\r\n  }\r\n\r\n  return ipv4;\r\n}\r\n\r\nfunction serializeIPv4(address) {\r\n  let output = \"\";\r\n  let n = address;\r\n\r\n  for (let i = 1; i <= 4; ++i) {\r\n    output = String(n % 256) + output;\r\n    if (i !== 4) {\r\n      output = \".\" + output;\r\n    }\r\n    n = Math.floor(n / 256);\r\n  }\r\n\r\n  return output;\r\n}\r\n\r\nfunction parseIPv6(input) {\r\n  const address = [0, 0, 0, 0, 0, 0, 0, 0];\r\n  let pieceIndex = 0;\r\n  let compress = null;\r\n  let pointer = 0;\r\n\r\n  input = punycode.ucs2.decode(input);\r\n\r\n  if (input[pointer] === 58) {\r\n    if (input[pointer + 1] !== 58) {\r\n      return failure;\r\n    }\r\n\r\n    pointer += 2;\r\n    ++pieceIndex;\r\n    compress = pieceIndex;\r\n  }\r\n\r\n  while (pointer < input.length) {\r\n    if (pieceIndex === 8) {\r\n      return failure;\r\n    }\r\n\r\n    if (input[pointer] === 58) {\r\n      if (compress !== null) {\r\n        return failure;\r\n      }\r\n      ++pointer;\r\n      ++pieceIndex;\r\n      compress = pieceIndex;\r\n      continue;\r\n    }\r\n\r\n    let value = 0;\r\n    let length = 0;\r\n\r\n    while (length < 4 && isASCIIHex(input[pointer])) {\r\n      value = value * 0x10 + parseInt(at(input, pointer), 16);\r\n      ++pointer;\r\n      ++length;\r\n    }\r\n\r\n    if (input[pointer] === 46) {\r\n      if (length === 0) {\r\n        return failure;\r\n      }\r\n\r\n      pointer -= length;\r\n\r\n      if (pieceIndex > 6) {\r\n        return failure;\r\n      }\r\n\r\n      let numbersSeen = 0;\r\n\r\n      while (input[pointer] !== undefined) {\r\n        let ipv4Piece = null;\r\n\r\n        if (numbersSeen > 0) {\r\n          if (input[pointer] === 46 && numbersSeen < 4) {\r\n            ++pointer;\r\n          } else {\r\n            return failure;\r\n          }\r\n        }\r\n\r\n        if (!isASCIIDigit(input[pointer])) {\r\n          return failure;\r\n        }\r\n\r\n        while (isASCIIDigit(input[pointer])) {\r\n          const number = parseInt(at(input, pointer));\r\n          if (ipv4Piece === null) {\r\n            ipv4Piece = number;\r\n          } else if (ipv4Piece === 0) {\r\n            return failure;\r\n          } else {\r\n            ipv4Piece = ipv4Piece * 10 + number;\r\n          }\r\n          if (ipv4Piece > 255) {\r\n            return failure;\r\n          }\r\n          ++pointer;\r\n        }\r\n\r\n        address[pieceIndex] = address[pieceIndex] * 0x100 + ipv4Piece;\r\n\r\n        ++numbersSeen;\r\n\r\n        if (numbersSeen === 2 || numbersSeen === 4) {\r\n          ++pieceIndex;\r\n        }\r\n      }\r\n\r\n      if (numbersSeen !== 4) {\r\n        return failure;\r\n      }\r\n\r\n      break;\r\n    } else if (input[pointer] === 58) {\r\n      ++pointer;\r\n      if (input[pointer] === undefined) {\r\n        return failure;\r\n      }\r\n    } else if (input[pointer] !== undefined) {\r\n      return failure;\r\n    }\r\n\r\n    address[pieceIndex] = value;\r\n    ++pieceIndex;\r\n  }\r\n\r\n  if (compress !== null) {\r\n    let swaps = pieceIndex - compress;\r\n    pieceIndex = 7;\r\n    while (pieceIndex !== 0 && swaps > 0) {\r\n      const temp = address[compress + swaps - 1];\r\n      address[compress + swaps - 1] = address[pieceIndex];\r\n      address[pieceIndex] = temp;\r\n      --pieceIndex;\r\n      --swaps;\r\n    }\r\n  } else if (compress === null && pieceIndex !== 8) {\r\n    return failure;\r\n  }\r\n\r\n  return address;\r\n}\r\n\r\nfunction serializeIPv6(address) {\r\n  let output = \"\";\r\n  const seqResult = findLongestZeroSequence(address);\r\n  const compress = seqResult.idx;\r\n  let ignore0 = false;\r\n\r\n  for (let pieceIndex = 0; pieceIndex <= 7; ++pieceIndex) {\r\n    if (ignore0 && address[pieceIndex] === 0) {\r\n      continue;\r\n    } else if (ignore0) {\r\n      ignore0 = false;\r\n    }\r\n\r\n    if (compress === pieceIndex) {\r\n      const separator = pieceIndex === 0 ? \"::\" : \":\";\r\n      output += separator;\r\n      ignore0 = true;\r\n      continue;\r\n    }\r\n\r\n    output += address[pieceIndex].toString(16);\r\n\r\n    if (pieceIndex !== 7) {\r\n      output += \":\";\r\n    }\r\n  }\r\n\r\n  return output;\r\n}\r\n\r\nfunction parseHost(input, isSpecialArg) {\r\n  if (input[0] === \"[\") {\r\n    if (input[input.length - 1] !== \"]\") {\r\n      return failure;\r\n    }\r\n\r\n    return parseIPv6(input.substring(1, input.length - 1));\r\n  }\r\n\r\n  if (!isSpecialArg) {\r\n    return parseOpaqueHost(input);\r\n  }\r\n\r\n  const domain = utf8PercentDecode(input);\r\n  const asciiDomain = tr46.toASCII(domain, false, tr46.PROCESSING_OPTIONS.NONTRANSITIONAL, false);\r\n  if (asciiDomain === null) {\r\n    return failure;\r\n  }\r\n\r\n  if (containsForbiddenHostCodePoint(asciiDomain)) {\r\n    return failure;\r\n  }\r\n\r\n  const ipv4Host = parseIPv4(asciiDomain);\r\n  if (typeof ipv4Host === \"number\" || ipv4Host === failure) {\r\n    return ipv4Host;\r\n  }\r\n\r\n  return asciiDomain;\r\n}\r\n\r\nfunction parseOpaqueHost(input) {\r\n  if (containsForbiddenHostCodePointExcludingPercent(input)) {\r\n    return failure;\r\n  }\r\n\r\n  let output = \"\";\r\n  const decoded = punycode.ucs2.decode(input);\r\n  for (let i = 0; i < decoded.length; ++i) {\r\n    output += percentEncodeChar(decoded[i], isC0ControlPercentEncode);\r\n  }\r\n  return output;\r\n}\r\n\r\nfunction findLongestZeroSequence(arr) {\r\n  let maxIdx = null;\r\n  let maxLen = 1; // only find elements > 1\r\n  let currStart = null;\r\n  let currLen = 0;\r\n\r\n  for (let i = 0; i < arr.length; ++i) {\r\n    if (arr[i] !== 0) {\r\n      if (currLen > maxLen) {\r\n        maxIdx = currStart;\r\n        maxLen = currLen;\r\n      }\r\n\r\n      currStart = null;\r\n      currLen = 0;\r\n    } else {\r\n      if (currStart === null) {\r\n        currStart = i;\r\n      }\r\n      ++currLen;\r\n    }\r\n  }\r\n\r\n  // if trailing zeros\r\n  if (currLen > maxLen) {\r\n    maxIdx = currStart;\r\n    maxLen = currLen;\r\n  }\r\n\r\n  return {\r\n    idx: maxIdx,\r\n    len: maxLen\r\n  };\r\n}\r\n\r\nfunction serializeHost(host) {\r\n  if (typeof host === \"number\") {\r\n    return serializeIPv4(host);\r\n  }\r\n\r\n  // IPv6 serializer\r\n  if (host instanceof Array) {\r\n    return \"[\" + serializeIPv6(host) + \"]\";\r\n  }\r\n\r\n  return host;\r\n}\r\n\r\nfunction trimControlChars(url) {\r\n  return url.replace(/^[\\u0000-\\u001F\\u0020]+|[\\u0000-\\u001F\\u0020]+$/g, \"\");\r\n}\r\n\r\nfunction trimTabAndNewline(url) {\r\n  return url.replace(/\\u0009|\\u000A|\\u000D/g, \"\");\r\n}\r\n\r\nfunction shortenPath(url) {\r\n  const path = url.path;\r\n  if (path.length === 0) {\r\n    return;\r\n  }\r\n  if (url.scheme === \"file\" && path.length === 1 && isNormalizedWindowsDriveLetter(path[0])) {\r\n    return;\r\n  }\r\n\r\n  path.pop();\r\n}\r\n\r\nfunction includesCredentials(url) {\r\n  return url.username !== \"\" || url.password !== \"\";\r\n}\r\n\r\nfunction cannotHaveAUsernamePasswordPort(url) {\r\n  return url.host === null || url.host === \"\" || url.cannotBeABaseURL || url.scheme === \"file\";\r\n}\r\n\r\nfunction isNormalizedWindowsDriveLetter(string) {\r\n  return /^[A-Za-z]:$/.test(string);\r\n}\r\n\r\nfunction URLStateMachine(input, base, encodingOverride, url, stateOverride) {\r\n  this.pointer = 0;\r\n  this.input = input;\r\n  this.base = base || null;\r\n  this.encodingOverride = encodingOverride || \"utf-8\";\r\n  this.stateOverride = stateOverride;\r\n  this.url = url;\r\n  this.failure = false;\r\n  this.parseError = false;\r\n\r\n  if (!this.url) {\r\n    this.url = {\r\n      scheme: \"\",\r\n      username: \"\",\r\n      password: \"\",\r\n      host: null,\r\n      port: null,\r\n      path: [],\r\n      query: null,\r\n      fragment: null,\r\n\r\n      cannotBeABaseURL: false\r\n    };\r\n\r\n    const res = trimControlChars(this.input);\r\n    if (res !== this.input) {\r\n      this.parseError = true;\r\n    }\r\n    this.input = res;\r\n  }\r\n\r\n  const res = trimTabAndNewline(this.input);\r\n  if (res !== this.input) {\r\n    this.parseError = true;\r\n  }\r\n  this.input = res;\r\n\r\n  this.state = stateOverride || \"scheme start\";\r\n\r\n  this.buffer = \"\";\r\n  this.atFlag = false;\r\n  this.arrFlag = false;\r\n  this.passwordTokenSeenFlag = false;\r\n\r\n  this.input = punycode.ucs2.decode(this.input);\r\n\r\n  for (; this.pointer <= this.input.length; ++this.pointer) {\r\n    const c = this.input[this.pointer];\r\n    const cStr = isNaN(c) ? undefined : String.fromCodePoint(c);\r\n\r\n    // exec state machine\r\n    const ret = this[\"parse \" + this.state](c, cStr);\r\n    if (!ret) {\r\n      break; // terminate algorithm\r\n    } else if (ret === failure) {\r\n      this.failure = true;\r\n      break;\r\n    }\r\n  }\r\n}\r\n\r\nURLStateMachine.prototype[\"parse scheme start\"] = function parseSchemeStart(c, cStr) {\r\n  if (isASCIIAlpha(c)) {\r\n    this.buffer += cStr.toLowerCase();\r\n    this.state = \"scheme\";\r\n  } else if (!this.stateOverride) {\r\n    this.state = \"no scheme\";\r\n    --this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n    return failure;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse scheme\"] = function parseScheme(c, cStr) {\r\n  if (isASCIIAlphanumeric(c) || c === 43 || c === 45 || c === 46) {\r\n    this.buffer += cStr.toLowerCase();\r\n  } else if (c === 58) {\r\n    if (this.stateOverride) {\r\n      if (isSpecial(this.url) && !isSpecialScheme(this.buffer)) {\r\n        return false;\r\n      }\r\n\r\n      if (!isSpecial(this.url) && isSpecialScheme(this.buffer)) {\r\n        return false;\r\n      }\r\n\r\n      if ((includesCredentials(this.url) || this.url.port !== null) && this.buffer === \"file\") {\r\n        return false;\r\n      }\r\n\r\n      if (this.url.scheme === \"file\" && (this.url.host === \"\" || this.url.host === null)) {\r\n        return false;\r\n      }\r\n    }\r\n    this.url.scheme = this.buffer;\r\n    this.buffer = \"\";\r\n    if (this.stateOverride) {\r\n      return false;\r\n    }\r\n    if (this.url.scheme === \"file\") {\r\n      if (this.input[this.pointer + 1] !== 47 || this.input[this.pointer + 2] !== 47) {\r\n        this.parseError = true;\r\n      }\r\n      this.state = \"file\";\r\n    } else if (isSpecial(this.url) && this.base !== null && this.base.scheme === this.url.scheme) {\r\n      this.state = \"special relative or authority\";\r\n    } else if (isSpecial(this.url)) {\r\n      this.state = \"special authority slashes\";\r\n    } else if (this.input[this.pointer + 1] === 47) {\r\n      this.state = \"path or authority\";\r\n      ++this.pointer;\r\n    } else {\r\n      this.url.cannotBeABaseURL = true;\r\n      this.url.path.push(\"\");\r\n      this.state = \"cannot-be-a-base-URL path\";\r\n    }\r\n  } else if (!this.stateOverride) {\r\n    this.buffer = \"\";\r\n    this.state = \"no scheme\";\r\n    this.pointer = -1;\r\n  } else {\r\n    this.parseError = true;\r\n    return failure;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse no scheme\"] = function parseNoScheme(c) {\r\n  if (this.base === null || (this.base.cannotBeABaseURL && c !== 35)) {\r\n    return failure;\r\n  } else if (this.base.cannotBeABaseURL && c === 35) {\r\n    this.url.scheme = this.base.scheme;\r\n    this.url.path = this.base.path.slice();\r\n    this.url.query = this.base.query;\r\n    this.url.fragment = \"\";\r\n    this.url.cannotBeABaseURL = true;\r\n    this.state = \"fragment\";\r\n  } else if (this.base.scheme === \"file\") {\r\n    this.state = \"file\";\r\n    --this.pointer;\r\n  } else {\r\n    this.state = \"relative\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse special relative or authority\"] = function parseSpecialRelativeOrAuthority(c) {\r\n  if (c === 47 && this.input[this.pointer + 1] === 47) {\r\n    this.state = \"special authority ignore slashes\";\r\n    ++this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n    this.state = \"relative\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse path or authority\"] = function parsePathOrAuthority(c) {\r\n  if (c === 47) {\r\n    this.state = \"authority\";\r\n  } else {\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse relative\"] = function parseRelative(c) {\r\n  this.url.scheme = this.base.scheme;\r\n  if (isNaN(c)) {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.url.path = this.base.path.slice();\r\n    this.url.query = this.base.query;\r\n  } else if (c === 47) {\r\n    this.state = \"relative slash\";\r\n  } else if (c === 63) {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.url.path = this.base.path.slice();\r\n    this.url.query = \"\";\r\n    this.state = \"query\";\r\n  } else if (c === 35) {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.url.path = this.base.path.slice();\r\n    this.url.query = this.base.query;\r\n    this.url.fragment = \"\";\r\n    this.state = \"fragment\";\r\n  } else if (isSpecial(this.url) && c === 92) {\r\n    this.parseError = true;\r\n    this.state = \"relative slash\";\r\n  } else {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.url.path = this.base.path.slice(0, this.base.path.length - 1);\r\n\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse relative slash\"] = function parseRelativeSlash(c) {\r\n  if (isSpecial(this.url) && (c === 47 || c === 92)) {\r\n    if (c === 92) {\r\n      this.parseError = true;\r\n    }\r\n    this.state = \"special authority ignore slashes\";\r\n  } else if (c === 47) {\r\n    this.state = \"authority\";\r\n  } else {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse special authority slashes\"] = function parseSpecialAuthoritySlashes(c) {\r\n  if (c === 47 && this.input[this.pointer + 1] === 47) {\r\n    this.state = \"special authority ignore slashes\";\r\n    ++this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n    this.state = \"special authority ignore slashes\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse special authority ignore slashes\"] = function parseSpecialAuthorityIgnoreSlashes(c) {\r\n  if (c !== 47 && c !== 92) {\r\n    this.state = \"authority\";\r\n    --this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse authority\"] = function parseAuthority(c, cStr) {\r\n  if (c === 64) {\r\n    this.parseError = true;\r\n    if (this.atFlag) {\r\n      this.buffer = \"%40\" + this.buffer;\r\n    }\r\n    this.atFlag = true;\r\n\r\n    // careful, this is based on buffer and has its own pointer (this.pointer != pointer) and inner chars\r\n    const len = countSymbols(this.buffer);\r\n    for (let pointer = 0; pointer < len; ++pointer) {\r\n      const codePoint = this.buffer.codePointAt(pointer);\r\n\r\n      if (codePoint === 58 && !this.passwordTokenSeenFlag) {\r\n        this.passwordTokenSeenFlag = true;\r\n        continue;\r\n      }\r\n      const encodedCodePoints = percentEncodeChar(codePoint, isUserinfoPercentEncode);\r\n      if (this.passwordTokenSeenFlag) {\r\n        this.url.password += encodedCodePoints;\r\n      } else {\r\n        this.url.username += encodedCodePoints;\r\n      }\r\n    }\r\n    this.buffer = \"\";\r\n  } else if (isNaN(c) || c === 47 || c === 63 || c === 35 ||\r\n             (isSpecial(this.url) && c === 92)) {\r\n    if (this.atFlag && this.buffer === \"\") {\r\n      this.parseError = true;\r\n      return failure;\r\n    }\r\n    this.pointer -= countSymbols(this.buffer) + 1;\r\n    this.buffer = \"\";\r\n    this.state = \"host\";\r\n  } else {\r\n    this.buffer += cStr;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse hostname\"] =\r\nURLStateMachine.prototype[\"parse host\"] = function parseHostName(c, cStr) {\r\n  if (this.stateOverride && this.url.scheme === \"file\") {\r\n    --this.pointer;\r\n    this.state = \"file host\";\r\n  } else if (c === 58 && !this.arrFlag) {\r\n    if (this.buffer === \"\") {\r\n      this.parseError = true;\r\n      return failure;\r\n    }\r\n\r\n    const host = parseHost(this.buffer, isSpecial(this.url));\r\n    if (host === failure) {\r\n      return failure;\r\n    }\r\n\r\n    this.url.host = host;\r\n    this.buffer = \"\";\r\n    this.state = \"port\";\r\n    if (this.stateOverride === \"hostname\") {\r\n      return false;\r\n    }\r\n  } else if (isNaN(c) || c === 47 || c === 63 || c === 35 ||\r\n             (isSpecial(this.url) && c === 92)) {\r\n    --this.pointer;\r\n    if (isSpecial(this.url) && this.buffer === \"\") {\r\n      this.parseError = true;\r\n      return failure;\r\n    } else if (this.stateOverride && this.buffer === \"\" &&\r\n               (includesCredentials(this.url) || this.url.port !== null)) {\r\n      this.parseError = true;\r\n      return false;\r\n    }\r\n\r\n    const host = parseHost(this.buffer, isSpecial(this.url));\r\n    if (host === failure) {\r\n      return failure;\r\n    }\r\n\r\n    this.url.host = host;\r\n    this.buffer = \"\";\r\n    this.state = \"path start\";\r\n    if (this.stateOverride) {\r\n      return false;\r\n    }\r\n  } else {\r\n    if (c === 91) {\r\n      this.arrFlag = true;\r\n    } else if (c === 93) {\r\n      this.arrFlag = false;\r\n    }\r\n    this.buffer += cStr;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse port\"] = function parsePort(c, cStr) {\r\n  if (isASCIIDigit(c)) {\r\n    this.buffer += cStr;\r\n  } else if (isNaN(c) || c === 47 || c === 63 || c === 35 ||\r\n             (isSpecial(this.url) && c === 92) ||\r\n             this.stateOverride) {\r\n    if (this.buffer !== \"\") {\r\n      const port = parseInt(this.buffer);\r\n      if (port > Math.pow(2, 16) - 1) {\r\n        this.parseError = true;\r\n        return failure;\r\n      }\r\n      this.url.port = port === defaultPort(this.url.scheme) ? null : port;\r\n      this.buffer = \"\";\r\n    }\r\n    if (this.stateOverride) {\r\n      return false;\r\n    }\r\n    this.state = \"path start\";\r\n    --this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n    return failure;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nconst fileOtherwiseCodePoints = new Set([47, 92, 63, 35]);\r\n\r\nURLStateMachine.prototype[\"parse file\"] = function parseFile(c) {\r\n  this.url.scheme = \"file\";\r\n\r\n  if (c === 47 || c === 92) {\r\n    if (c === 92) {\r\n      this.parseError = true;\r\n    }\r\n    this.state = \"file slash\";\r\n  } else if (this.base !== null && this.base.scheme === \"file\") {\r\n    if (isNaN(c)) {\r\n      this.url.host = this.base.host;\r\n      this.url.path = this.base.path.slice();\r\n      this.url.query = this.base.query;\r\n    } else if (c === 63) {\r\n      this.url.host = this.base.host;\r\n      this.url.path = this.base.path.slice();\r\n      this.url.query = \"\";\r\n      this.state = \"query\";\r\n    } else if (c === 35) {\r\n      this.url.host = this.base.host;\r\n      this.url.path = this.base.path.slice();\r\n      this.url.query = this.base.query;\r\n      this.url.fragment = \"\";\r\n      this.state = \"fragment\";\r\n    } else {\r\n      if (this.input.length - this.pointer - 1 === 0 || // remaining consists of 0 code points\r\n          !isWindowsDriveLetterCodePoints(c, this.input[this.pointer + 1]) ||\r\n          (this.input.length - this.pointer - 1 >= 2 && // remaining has at least 2 code points\r\n           !fileOtherwiseCodePoints.has(this.input[this.pointer + 2]))) {\r\n        this.url.host = this.base.host;\r\n        this.url.path = this.base.path.slice();\r\n        shortenPath(this.url);\r\n      } else {\r\n        this.parseError = true;\r\n      }\r\n\r\n      this.state = \"path\";\r\n      --this.pointer;\r\n    }\r\n  } else {\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse file slash\"] = function parseFileSlash(c) {\r\n  if (c === 47 || c === 92) {\r\n    if (c === 92) {\r\n      this.parseError = true;\r\n    }\r\n    this.state = \"file host\";\r\n  } else {\r\n    if (this.base !== null && this.base.scheme === \"file\") {\r\n      if (isNormalizedWindowsDriveLetterString(this.base.path[0])) {\r\n        this.url.path.push(this.base.path[0]);\r\n      } else {\r\n        this.url.host = this.base.host;\r\n      }\r\n    }\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse file host\"] = function parseFileHost(c, cStr) {\r\n  if (isNaN(c) || c === 47 || c === 92 || c === 63 || c === 35) {\r\n    --this.pointer;\r\n    if (!this.stateOverride && isWindowsDriveLetterString(this.buffer)) {\r\n      this.parseError = true;\r\n      this.state = \"path\";\r\n    } else if (this.buffer === \"\") {\r\n      this.url.host = \"\";\r\n      if (this.stateOverride) {\r\n        return false;\r\n      }\r\n      this.state = \"path start\";\r\n    } else {\r\n      let host = parseHost(this.buffer, isSpecial(this.url));\r\n      if (host === failure) {\r\n        return failure;\r\n      }\r\n      if (host === \"localhost\") {\r\n        host = \"\";\r\n      }\r\n      this.url.host = host;\r\n\r\n      if (this.stateOverride) {\r\n        return false;\r\n      }\r\n\r\n      this.buffer = \"\";\r\n      this.state = \"path start\";\r\n    }\r\n  } else {\r\n    this.buffer += cStr;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse path start\"] = function parsePathStart(c) {\r\n  if (isSpecial(this.url)) {\r\n    if (c === 92) {\r\n      this.parseError = true;\r\n    }\r\n    this.state = \"path\";\r\n\r\n    if (c !== 47 && c !== 92) {\r\n      --this.pointer;\r\n    }\r\n  } else if (!this.stateOverride && c === 63) {\r\n    this.url.query = \"\";\r\n    this.state = \"query\";\r\n  } else if (!this.stateOverride && c === 35) {\r\n    this.url.fragment = \"\";\r\n    this.state = \"fragment\";\r\n  } else if (c !== undefined) {\r\n    this.state = \"path\";\r\n    if (c !== 47) {\r\n      --this.pointer;\r\n    }\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse path\"] = function parsePath(c) {\r\n  if (isNaN(c) || c === 47 || (isSpecial(this.url) && c === 92) ||\r\n      (!this.stateOverride && (c === 63 || c === 35))) {\r\n    if (isSpecial(this.url) && c === 92) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    if (isDoubleDot(this.buffer)) {\r\n      shortenPath(this.url);\r\n      if (c !== 47 && !(isSpecial(this.url) && c === 92)) {\r\n        this.url.path.push(\"\");\r\n      }\r\n    } else if (isSingleDot(this.buffer) && c !== 47 &&\r\n               !(isSpecial(this.url) && c === 92)) {\r\n      this.url.path.push(\"\");\r\n    } else if (!isSingleDot(this.buffer)) {\r\n      if (this.url.scheme === \"file\" && this.url.path.length === 0 && isWindowsDriveLetterString(this.buffer)) {\r\n        if (this.url.host !== \"\" && this.url.host !== null) {\r\n          this.parseError = true;\r\n          this.url.host = \"\";\r\n        }\r\n        this.buffer = this.buffer[0] + \":\";\r\n      }\r\n      this.url.path.push(this.buffer);\r\n    }\r\n    this.buffer = \"\";\r\n    if (this.url.scheme === \"file\" && (c === undefined || c === 63 || c === 35)) {\r\n      while (this.url.path.length > 1 && this.url.path[0] === \"\") {\r\n        this.parseError = true;\r\n        this.url.path.shift();\r\n      }\r\n    }\r\n    if (c === 63) {\r\n      this.url.query = \"\";\r\n      this.state = \"query\";\r\n    }\r\n    if (c === 35) {\r\n      this.url.fragment = \"\";\r\n      this.state = \"fragment\";\r\n    }\r\n  } else {\r\n    // TODO: If c is not a URL code point and not \"%\", parse error.\r\n\r\n    if (c === 37 &&\r\n      (!isASCIIHex(this.input[this.pointer + 1]) ||\r\n        !isASCIIHex(this.input[this.pointer + 2]))) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    this.buffer += percentEncodeChar(c, isPathPercentEncode);\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse cannot-be-a-base-URL path\"] = function parseCannotBeABaseURLPath(c) {\r\n  if (c === 63) {\r\n    this.url.query = \"\";\r\n    this.state = \"query\";\r\n  } else if (c === 35) {\r\n    this.url.fragment = \"\";\r\n    this.state = \"fragment\";\r\n  } else {\r\n    // TODO: Add: not a URL code point\r\n    if (!isNaN(c) && c !== 37) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    if (c === 37 &&\r\n        (!isASCIIHex(this.input[this.pointer + 1]) ||\r\n         !isASCIIHex(this.input[this.pointer + 2]))) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    if (!isNaN(c)) {\r\n      this.url.path[0] = this.url.path[0] + percentEncodeChar(c, isC0ControlPercentEncode);\r\n    }\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse query\"] = function parseQuery(c, cStr) {\r\n  if (isNaN(c) || (!this.stateOverride && c === 35)) {\r\n    if (!isSpecial(this.url) || this.url.scheme === \"ws\" || this.url.scheme === \"wss\") {\r\n      this.encodingOverride = \"utf-8\";\r\n    }\r\n\r\n    const buffer = new Buffer(this.buffer); // TODO: Use encoding override instead\r\n    for (let i = 0; i < buffer.length; ++i) {\r\n      if (buffer[i] < 0x21 || buffer[i] > 0x7E || buffer[i] === 0x22 || buffer[i] === 0x23 ||\r\n          buffer[i] === 0x3C || buffer[i] === 0x3E) {\r\n        this.url.query += percentEncode(buffer[i]);\r\n      } else {\r\n        this.url.query += String.fromCodePoint(buffer[i]);\r\n      }\r\n    }\r\n\r\n    this.buffer = \"\";\r\n    if (c === 35) {\r\n      this.url.fragment = \"\";\r\n      this.state = \"fragment\";\r\n    }\r\n  } else {\r\n    // TODO: If c is not a URL code point and not \"%\", parse error.\r\n    if (c === 37 &&\r\n      (!isASCIIHex(this.input[this.pointer + 1]) ||\r\n        !isASCIIHex(this.input[this.pointer + 2]))) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    this.buffer += cStr;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse fragment\"] = function parseFragment(c) {\r\n  if (isNaN(c)) { // do nothing\r\n  } else if (c === 0x0) {\r\n    this.parseError = true;\r\n  } else {\r\n    // TODO: If c is not a URL code point and not \"%\", parse error.\r\n    if (c === 37 &&\r\n      (!isASCIIHex(this.input[this.pointer + 1]) ||\r\n        !isASCIIHex(this.input[this.pointer + 2]))) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    this.url.fragment += percentEncodeChar(c, isC0ControlPercentEncode);\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nfunction serializeURL(url, excludeFragment) {\r\n  let output = url.scheme + \":\";\r\n  if (url.host !== null) {\r\n    output += \"//\";\r\n\r\n    if (url.username !== \"\" || url.password !== \"\") {\r\n      output += url.username;\r\n      if (url.password !== \"\") {\r\n        output += \":\" + url.password;\r\n      }\r\n      output += \"@\";\r\n    }\r\n\r\n    output += serializeHost(url.host);\r\n\r\n    if (url.port !== null) {\r\n      output += \":\" + url.port;\r\n    }\r\n  } else if (url.host === null && url.scheme === \"file\") {\r\n    output += \"//\";\r\n  }\r\n\r\n  if (url.cannotBeABaseURL) {\r\n    output += url.path[0];\r\n  } else {\r\n    for (const string of url.path) {\r\n      output += \"/\" + string;\r\n    }\r\n  }\r\n\r\n  if (url.query !== null) {\r\n    output += \"?\" + url.query;\r\n  }\r\n\r\n  if (!excludeFragment && url.fragment !== null) {\r\n    output += \"#\" + url.fragment;\r\n  }\r\n\r\n  return output;\r\n}\r\n\r\nfunction serializeOrigin(tuple) {\r\n  let result = tuple.scheme + \"://\";\r\n  result += serializeHost(tuple.host);\r\n\r\n  if (tuple.port !== null) {\r\n    result += \":\" + tuple.port;\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\nmodule.exports.serializeURL = serializeURL;\r\n\r\nmodule.exports.serializeURLOrigin = function (url) {\r\n  // https://url.spec.whatwg.org/#concept-url-origin\r\n  switch (url.scheme) {\r\n    case \"blob\":\r\n      try {\r\n        return module.exports.serializeURLOrigin(module.exports.parseURL(url.path[0]));\r\n      } catch (e) {\r\n        // serializing an opaque origin returns \"null\"\r\n        return \"null\";\r\n      }\r\n    case \"ftp\":\r\n    case \"gopher\":\r\n    case \"http\":\r\n    case \"https\":\r\n    case \"ws\":\r\n    case \"wss\":\r\n      return serializeOrigin({\r\n        scheme: url.scheme,\r\n        host: url.host,\r\n        port: url.port\r\n      });\r\n    case \"file\":\r\n      // spec says \"exercise to the reader\", chrome says \"file://\"\r\n      return \"file://\";\r\n    default:\r\n      // serializing an opaque origin returns \"null\"\r\n      return \"null\";\r\n  }\r\n};\r\n\r\nmodule.exports.basicURLParse = function (input, options) {\r\n  if (options === undefined) {\r\n    options = {};\r\n  }\r\n\r\n  const usm = new URLStateMachine(input, options.baseURL, options.encodingOverride, options.url, options.stateOverride);\r\n  if (usm.failure) {\r\n    return \"failure\";\r\n  }\r\n\r\n  return usm.url;\r\n};\r\n\r\nmodule.exports.setTheUsername = function (url, username) {\r\n  url.username = \"\";\r\n  const decoded = punycode.ucs2.decode(username);\r\n  for (let i = 0; i < decoded.length; ++i) {\r\n    url.username += percentEncodeChar(decoded[i], isUserinfoPercentEncode);\r\n  }\r\n};\r\n\r\nmodule.exports.setThePassword = function (url, password) {\r\n  url.password = \"\";\r\n  const decoded = punycode.ucs2.decode(password);\r\n  for (let i = 0; i < decoded.length; ++i) {\r\n    url.password += percentEncodeChar(decoded[i], isUserinfoPercentEncode);\r\n  }\r\n};\r\n\r\nmodule.exports.serializeHost = serializeHost;\r\n\r\nmodule.exports.cannotHaveAUsernamePasswordPort = cannotHaveAUsernamePasswordPort;\r\n\r\nmodule.exports.serializeInteger = function (integer) {\r\n  return String(integer);\r\n};\r\n\r\nmodule.exports.parseURL = function (input, options) {\r\n  if (options === undefined) {\r\n    options = {};\r\n  }\r\n\r\n  // We don't handle blobs, so this just delegates:\r\n  return module.exports.basicURLParse(input, { baseURL: options.baseURL, encodingOverride: options.encodingOverride });\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2hhdHdnLXVybC9saWIvdXJsLXN0YXRlLW1hY2hpbmUuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYixpQkFBaUIsbUJBQU8sQ0FBQywwQkFBVTtBQUNuQyxhQUFhLG1CQUFPLENBQUMsZ0RBQU07QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLGdCQUFnQjtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0Isa0JBQWtCO0FBQ3BDO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLHdCQUF3QjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLFFBQVE7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsaUJBQWlCO0FBQzVDO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0Isb0JBQW9CO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixnQkFBZ0I7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyxtQ0FBbUM7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsZUFBZTtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QztBQUM1QyxvQkFBb0IsbUJBQW1CO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQixJQUFJO0FBQ0o7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQSxpQ0FBaUM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEI7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQSxrQkFBa0Isb0JBQW9CO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQSxrQkFBa0Isb0JBQW9CO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCO0FBQzVCO0FBQ0EsOENBQThDO0FBQzlDO0FBQ0EsK0JBQStCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QjtBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0NBQStDLHNFQUFzRTtBQUNySCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxraGFsZVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxZQVNNSU4gQUxTSEFNXFx5YXNtaW4tYWxzaGFtXFxub2RlX21vZHVsZXNcXHdoYXR3Zy11cmxcXGxpYlxcdXJsLXN0YXRlLW1hY2hpbmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XHJcbmNvbnN0IHB1bnljb2RlID0gcmVxdWlyZShcInB1bnljb2RlXCIpO1xyXG5jb25zdCB0cjQ2ID0gcmVxdWlyZShcInRyNDZcIik7XHJcblxyXG5jb25zdCBzcGVjaWFsU2NoZW1lcyA9IHtcclxuICBmdHA6IDIxLFxyXG4gIGZpbGU6IG51bGwsXHJcbiAgZ29waGVyOiA3MCxcclxuICBodHRwOiA4MCxcclxuICBodHRwczogNDQzLFxyXG4gIHdzOiA4MCxcclxuICB3c3M6IDQ0M1xyXG59O1xyXG5cclxuY29uc3QgZmFpbHVyZSA9IFN5bWJvbChcImZhaWx1cmVcIik7XHJcblxyXG5mdW5jdGlvbiBjb3VudFN5bWJvbHMoc3RyKSB7XHJcbiAgcmV0dXJuIHB1bnljb2RlLnVjczIuZGVjb2RlKHN0cikubGVuZ3RoO1xyXG59XHJcblxyXG5mdW5jdGlvbiBhdChpbnB1dCwgaWR4KSB7XHJcbiAgY29uc3QgYyA9IGlucHV0W2lkeF07XHJcbiAgcmV0dXJuIGlzTmFOKGMpID8gdW5kZWZpbmVkIDogU3RyaW5nLmZyb21Db2RlUG9pbnQoYyk7XHJcbn1cclxuXHJcbmZ1bmN0aW9uIGlzQVNDSUlEaWdpdChjKSB7XHJcbiAgcmV0dXJuIGMgPj0gMHgzMCAmJiBjIDw9IDB4Mzk7XHJcbn1cclxuXHJcbmZ1bmN0aW9uIGlzQVNDSUlBbHBoYShjKSB7XHJcbiAgcmV0dXJuIChjID49IDB4NDEgJiYgYyA8PSAweDVBKSB8fCAoYyA+PSAweDYxICYmIGMgPD0gMHg3QSk7XHJcbn1cclxuXHJcbmZ1bmN0aW9uIGlzQVNDSUlBbHBoYW51bWVyaWMoYykge1xyXG4gIHJldHVybiBpc0FTQ0lJQWxwaGEoYykgfHwgaXNBU0NJSURpZ2l0KGMpO1xyXG59XHJcblxyXG5mdW5jdGlvbiBpc0FTQ0lJSGV4KGMpIHtcclxuICByZXR1cm4gaXNBU0NJSURpZ2l0KGMpIHx8IChjID49IDB4NDEgJiYgYyA8PSAweDQ2KSB8fCAoYyA+PSAweDYxICYmIGMgPD0gMHg2Nik7XHJcbn1cclxuXHJcbmZ1bmN0aW9uIGlzU2luZ2xlRG90KGJ1ZmZlcikge1xyXG4gIHJldHVybiBidWZmZXIgPT09IFwiLlwiIHx8IGJ1ZmZlci50b0xvd2VyQ2FzZSgpID09PSBcIiUyZVwiO1xyXG59XHJcblxyXG5mdW5jdGlvbiBpc0RvdWJsZURvdChidWZmZXIpIHtcclxuICBidWZmZXIgPSBidWZmZXIudG9Mb3dlckNhc2UoKTtcclxuICByZXR1cm4gYnVmZmVyID09PSBcIi4uXCIgfHwgYnVmZmVyID09PSBcIiUyZS5cIiB8fCBidWZmZXIgPT09IFwiLiUyZVwiIHx8IGJ1ZmZlciA9PT0gXCIlMmUlMmVcIjtcclxufVxyXG5cclxuZnVuY3Rpb24gaXNXaW5kb3dzRHJpdmVMZXR0ZXJDb2RlUG9pbnRzKGNwMSwgY3AyKSB7XHJcbiAgcmV0dXJuIGlzQVNDSUlBbHBoYShjcDEpICYmIChjcDIgPT09IDU4IHx8IGNwMiA9PT0gMTI0KTtcclxufVxyXG5cclxuZnVuY3Rpb24gaXNXaW5kb3dzRHJpdmVMZXR0ZXJTdHJpbmcoc3RyaW5nKSB7XHJcbiAgcmV0dXJuIHN0cmluZy5sZW5ndGggPT09IDIgJiYgaXNBU0NJSUFscGhhKHN0cmluZy5jb2RlUG9pbnRBdCgwKSkgJiYgKHN0cmluZ1sxXSA9PT0gXCI6XCIgfHwgc3RyaW5nWzFdID09PSBcInxcIik7XHJcbn1cclxuXHJcbmZ1bmN0aW9uIGlzTm9ybWFsaXplZFdpbmRvd3NEcml2ZUxldHRlclN0cmluZyhzdHJpbmcpIHtcclxuICByZXR1cm4gc3RyaW5nLmxlbmd0aCA9PT0gMiAmJiBpc0FTQ0lJQWxwaGEoc3RyaW5nLmNvZGVQb2ludEF0KDApKSAmJiBzdHJpbmdbMV0gPT09IFwiOlwiO1xyXG59XHJcblxyXG5mdW5jdGlvbiBjb250YWluc0ZvcmJpZGRlbkhvc3RDb2RlUG9pbnQoc3RyaW5nKSB7XHJcbiAgcmV0dXJuIHN0cmluZy5zZWFyY2goL1xcdTAwMDB8XFx1MDAwOXxcXHUwMDBBfFxcdTAwMER8XFx1MDAyMHwjfCV8XFwvfDp8XFw/fEB8XFxbfFxcXFx8XFxdLykgIT09IC0xO1xyXG59XHJcblxyXG5mdW5jdGlvbiBjb250YWluc0ZvcmJpZGRlbkhvc3RDb2RlUG9pbnRFeGNsdWRpbmdQZXJjZW50KHN0cmluZykge1xyXG4gIHJldHVybiBzdHJpbmcuc2VhcmNoKC9cXHUwMDAwfFxcdTAwMDl8XFx1MDAwQXxcXHUwMDBEfFxcdTAwMjB8I3xcXC98OnxcXD98QHxcXFt8XFxcXHxcXF0vKSAhPT0gLTE7XHJcbn1cclxuXHJcbmZ1bmN0aW9uIGlzU3BlY2lhbFNjaGVtZShzY2hlbWUpIHtcclxuICByZXR1cm4gc3BlY2lhbFNjaGVtZXNbc2NoZW1lXSAhPT0gdW5kZWZpbmVkO1xyXG59XHJcblxyXG5mdW5jdGlvbiBpc1NwZWNpYWwodXJsKSB7XHJcbiAgcmV0dXJuIGlzU3BlY2lhbFNjaGVtZSh1cmwuc2NoZW1lKTtcclxufVxyXG5cclxuZnVuY3Rpb24gZGVmYXVsdFBvcnQoc2NoZW1lKSB7XHJcbiAgcmV0dXJuIHNwZWNpYWxTY2hlbWVzW3NjaGVtZV07XHJcbn1cclxuXHJcbmZ1bmN0aW9uIHBlcmNlbnRFbmNvZGUoYykge1xyXG4gIGxldCBoZXggPSBjLnRvU3RyaW5nKDE2KS50b1VwcGVyQ2FzZSgpO1xyXG4gIGlmIChoZXgubGVuZ3RoID09PSAxKSB7XHJcbiAgICBoZXggPSBcIjBcIiArIGhleDtcclxuICB9XHJcblxyXG4gIHJldHVybiBcIiVcIiArIGhleDtcclxufVxyXG5cclxuZnVuY3Rpb24gdXRmOFBlcmNlbnRFbmNvZGUoYykge1xyXG4gIGNvbnN0IGJ1ZiA9IG5ldyBCdWZmZXIoYyk7XHJcblxyXG4gIGxldCBzdHIgPSBcIlwiO1xyXG5cclxuICBmb3IgKGxldCBpID0gMDsgaSA8IGJ1Zi5sZW5ndGg7ICsraSkge1xyXG4gICAgc3RyICs9IHBlcmNlbnRFbmNvZGUoYnVmW2ldKTtcclxuICB9XHJcblxyXG4gIHJldHVybiBzdHI7XHJcbn1cclxuXHJcbmZ1bmN0aW9uIHV0ZjhQZXJjZW50RGVjb2RlKHN0cikge1xyXG4gIGNvbnN0IGlucHV0ID0gbmV3IEJ1ZmZlcihzdHIpO1xyXG4gIGNvbnN0IG91dHB1dCA9IFtdO1xyXG4gIGZvciAobGV0IGkgPSAwOyBpIDwgaW5wdXQubGVuZ3RoOyArK2kpIHtcclxuICAgIGlmIChpbnB1dFtpXSAhPT0gMzcpIHtcclxuICAgICAgb3V0cHV0LnB1c2goaW5wdXRbaV0pO1xyXG4gICAgfSBlbHNlIGlmIChpbnB1dFtpXSA9PT0gMzcgJiYgaXNBU0NJSUhleChpbnB1dFtpICsgMV0pICYmIGlzQVNDSUlIZXgoaW5wdXRbaSArIDJdKSkge1xyXG4gICAgICBvdXRwdXQucHVzaChwYXJzZUludChpbnB1dC5zbGljZShpICsgMSwgaSArIDMpLnRvU3RyaW5nKCksIDE2KSk7XHJcbiAgICAgIGkgKz0gMjtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIG91dHB1dC5wdXNoKGlucHV0W2ldKTtcclxuICAgIH1cclxuICB9XHJcbiAgcmV0dXJuIG5ldyBCdWZmZXIob3V0cHV0KS50b1N0cmluZygpO1xyXG59XHJcblxyXG5mdW5jdGlvbiBpc0MwQ29udHJvbFBlcmNlbnRFbmNvZGUoYykge1xyXG4gIHJldHVybiBjIDw9IDB4MUYgfHwgYyA+IDB4N0U7XHJcbn1cclxuXHJcbmNvbnN0IGV4dHJhUGF0aFBlcmNlbnRFbmNvZGVTZXQgPSBuZXcgU2V0KFszMiwgMzQsIDM1LCA2MCwgNjIsIDYzLCA5NiwgMTIzLCAxMjVdKTtcclxuZnVuY3Rpb24gaXNQYXRoUGVyY2VudEVuY29kZShjKSB7XHJcbiAgcmV0dXJuIGlzQzBDb250cm9sUGVyY2VudEVuY29kZShjKSB8fCBleHRyYVBhdGhQZXJjZW50RW5jb2RlU2V0LmhhcyhjKTtcclxufVxyXG5cclxuY29uc3QgZXh0cmFVc2VyaW5mb1BlcmNlbnRFbmNvZGVTZXQgPVxyXG4gIG5ldyBTZXQoWzQ3LCA1OCwgNTksIDYxLCA2NCwgOTEsIDkyLCA5MywgOTQsIDEyNF0pO1xyXG5mdW5jdGlvbiBpc1VzZXJpbmZvUGVyY2VudEVuY29kZShjKSB7XHJcbiAgcmV0dXJuIGlzUGF0aFBlcmNlbnRFbmNvZGUoYykgfHwgZXh0cmFVc2VyaW5mb1BlcmNlbnRFbmNvZGVTZXQuaGFzKGMpO1xyXG59XHJcblxyXG5mdW5jdGlvbiBwZXJjZW50RW5jb2RlQ2hhcihjLCBlbmNvZGVTZXRQcmVkaWNhdGUpIHtcclxuICBjb25zdCBjU3RyID0gU3RyaW5nLmZyb21Db2RlUG9pbnQoYyk7XHJcblxyXG4gIGlmIChlbmNvZGVTZXRQcmVkaWNhdGUoYykpIHtcclxuICAgIHJldHVybiB1dGY4UGVyY2VudEVuY29kZShjU3RyKTtcclxuICB9XHJcblxyXG4gIHJldHVybiBjU3RyO1xyXG59XHJcblxyXG5mdW5jdGlvbiBwYXJzZUlQdjROdW1iZXIoaW5wdXQpIHtcclxuICBsZXQgUiA9IDEwO1xyXG5cclxuICBpZiAoaW5wdXQubGVuZ3RoID49IDIgJiYgaW5wdXQuY2hhckF0KDApID09PSBcIjBcIiAmJiBpbnB1dC5jaGFyQXQoMSkudG9Mb3dlckNhc2UoKSA9PT0gXCJ4XCIpIHtcclxuICAgIGlucHV0ID0gaW5wdXQuc3Vic3RyaW5nKDIpO1xyXG4gICAgUiA9IDE2O1xyXG4gIH0gZWxzZSBpZiAoaW5wdXQubGVuZ3RoID49IDIgJiYgaW5wdXQuY2hhckF0KDApID09PSBcIjBcIikge1xyXG4gICAgaW5wdXQgPSBpbnB1dC5zdWJzdHJpbmcoMSk7XHJcbiAgICBSID0gODtcclxuICB9XHJcblxyXG4gIGlmIChpbnB1dCA9PT0gXCJcIikge1xyXG4gICAgcmV0dXJuIDA7XHJcbiAgfVxyXG5cclxuICBjb25zdCByZWdleCA9IFIgPT09IDEwID8gL1teMC05XS8gOiAoUiA9PT0gMTYgPyAvW14wLTlBLUZhLWZdLyA6IC9bXjAtN10vKTtcclxuICBpZiAocmVnZXgudGVzdChpbnB1dCkpIHtcclxuICAgIHJldHVybiBmYWlsdXJlO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIHBhcnNlSW50KGlucHV0LCBSKTtcclxufVxyXG5cclxuZnVuY3Rpb24gcGFyc2VJUHY0KGlucHV0KSB7XHJcbiAgY29uc3QgcGFydHMgPSBpbnB1dC5zcGxpdChcIi5cIik7XHJcbiAgaWYgKHBhcnRzW3BhcnRzLmxlbmd0aCAtIDFdID09PSBcIlwiKSB7XHJcbiAgICBpZiAocGFydHMubGVuZ3RoID4gMSkge1xyXG4gICAgICBwYXJ0cy5wb3AoKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGlmIChwYXJ0cy5sZW5ndGggPiA0KSB7XHJcbiAgICByZXR1cm4gaW5wdXQ7XHJcbiAgfVxyXG5cclxuICBjb25zdCBudW1iZXJzID0gW107XHJcbiAgZm9yIChjb25zdCBwYXJ0IG9mIHBhcnRzKSB7XHJcbiAgICBpZiAocGFydCA9PT0gXCJcIikge1xyXG4gICAgICByZXR1cm4gaW5wdXQ7XHJcbiAgICB9XHJcbiAgICBjb25zdCBuID0gcGFyc2VJUHY0TnVtYmVyKHBhcnQpO1xyXG4gICAgaWYgKG4gPT09IGZhaWx1cmUpIHtcclxuICAgICAgcmV0dXJuIGlucHV0O1xyXG4gICAgfVxyXG5cclxuICAgIG51bWJlcnMucHVzaChuKTtcclxuICB9XHJcblxyXG4gIGZvciAobGV0IGkgPSAwOyBpIDwgbnVtYmVycy5sZW5ndGggLSAxOyArK2kpIHtcclxuICAgIGlmIChudW1iZXJzW2ldID4gMjU1KSB7XHJcbiAgICAgIHJldHVybiBmYWlsdXJlO1xyXG4gICAgfVxyXG4gIH1cclxuICBpZiAobnVtYmVyc1tudW1iZXJzLmxlbmd0aCAtIDFdID49IE1hdGgucG93KDI1NiwgNSAtIG51bWJlcnMubGVuZ3RoKSkge1xyXG4gICAgcmV0dXJuIGZhaWx1cmU7XHJcbiAgfVxyXG5cclxuICBsZXQgaXB2NCA9IG51bWJlcnMucG9wKCk7XHJcbiAgbGV0IGNvdW50ZXIgPSAwO1xyXG5cclxuICBmb3IgKGNvbnN0IG4gb2YgbnVtYmVycykge1xyXG4gICAgaXB2NCArPSBuICogTWF0aC5wb3coMjU2LCAzIC0gY291bnRlcik7XHJcbiAgICArK2NvdW50ZXI7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gaXB2NDtcclxufVxyXG5cclxuZnVuY3Rpb24gc2VyaWFsaXplSVB2NChhZGRyZXNzKSB7XHJcbiAgbGV0IG91dHB1dCA9IFwiXCI7XHJcbiAgbGV0IG4gPSBhZGRyZXNzO1xyXG5cclxuICBmb3IgKGxldCBpID0gMTsgaSA8PSA0OyArK2kpIHtcclxuICAgIG91dHB1dCA9IFN0cmluZyhuICUgMjU2KSArIG91dHB1dDtcclxuICAgIGlmIChpICE9PSA0KSB7XHJcbiAgICAgIG91dHB1dCA9IFwiLlwiICsgb3V0cHV0O1xyXG4gICAgfVxyXG4gICAgbiA9IE1hdGguZmxvb3IobiAvIDI1Nik7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gb3V0cHV0O1xyXG59XHJcblxyXG5mdW5jdGlvbiBwYXJzZUlQdjYoaW5wdXQpIHtcclxuICBjb25zdCBhZGRyZXNzID0gWzAsIDAsIDAsIDAsIDAsIDAsIDAsIDBdO1xyXG4gIGxldCBwaWVjZUluZGV4ID0gMDtcclxuICBsZXQgY29tcHJlc3MgPSBudWxsO1xyXG4gIGxldCBwb2ludGVyID0gMDtcclxuXHJcbiAgaW5wdXQgPSBwdW55Y29kZS51Y3MyLmRlY29kZShpbnB1dCk7XHJcblxyXG4gIGlmIChpbnB1dFtwb2ludGVyXSA9PT0gNTgpIHtcclxuICAgIGlmIChpbnB1dFtwb2ludGVyICsgMV0gIT09IDU4KSB7XHJcbiAgICAgIHJldHVybiBmYWlsdXJlO1xyXG4gICAgfVxyXG5cclxuICAgIHBvaW50ZXIgKz0gMjtcclxuICAgICsrcGllY2VJbmRleDtcclxuICAgIGNvbXByZXNzID0gcGllY2VJbmRleDtcclxuICB9XHJcblxyXG4gIHdoaWxlIChwb2ludGVyIDwgaW5wdXQubGVuZ3RoKSB7XHJcbiAgICBpZiAocGllY2VJbmRleCA9PT0gOCkge1xyXG4gICAgICByZXR1cm4gZmFpbHVyZTtcclxuICAgIH1cclxuXHJcbiAgICBpZiAoaW5wdXRbcG9pbnRlcl0gPT09IDU4KSB7XHJcbiAgICAgIGlmIChjb21wcmVzcyAhPT0gbnVsbCkge1xyXG4gICAgICAgIHJldHVybiBmYWlsdXJlO1xyXG4gICAgICB9XHJcbiAgICAgICsrcG9pbnRlcjtcclxuICAgICAgKytwaWVjZUluZGV4O1xyXG4gICAgICBjb21wcmVzcyA9IHBpZWNlSW5kZXg7XHJcbiAgICAgIGNvbnRpbnVlO1xyXG4gICAgfVxyXG5cclxuICAgIGxldCB2YWx1ZSA9IDA7XHJcbiAgICBsZXQgbGVuZ3RoID0gMDtcclxuXHJcbiAgICB3aGlsZSAobGVuZ3RoIDwgNCAmJiBpc0FTQ0lJSGV4KGlucHV0W3BvaW50ZXJdKSkge1xyXG4gICAgICB2YWx1ZSA9IHZhbHVlICogMHgxMCArIHBhcnNlSW50KGF0KGlucHV0LCBwb2ludGVyKSwgMTYpO1xyXG4gICAgICArK3BvaW50ZXI7XHJcbiAgICAgICsrbGVuZ3RoO1xyXG4gICAgfVxyXG5cclxuICAgIGlmIChpbnB1dFtwb2ludGVyXSA9PT0gNDYpIHtcclxuICAgICAgaWYgKGxlbmd0aCA9PT0gMCkge1xyXG4gICAgICAgIHJldHVybiBmYWlsdXJlO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBwb2ludGVyIC09IGxlbmd0aDtcclxuXHJcbiAgICAgIGlmIChwaWVjZUluZGV4ID4gNikge1xyXG4gICAgICAgIHJldHVybiBmYWlsdXJlO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBsZXQgbnVtYmVyc1NlZW4gPSAwO1xyXG5cclxuICAgICAgd2hpbGUgKGlucHV0W3BvaW50ZXJdICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICBsZXQgaXB2NFBpZWNlID0gbnVsbDtcclxuXHJcbiAgICAgICAgaWYgKG51bWJlcnNTZWVuID4gMCkge1xyXG4gICAgICAgICAgaWYgKGlucHV0W3BvaW50ZXJdID09PSA0NiAmJiBudW1iZXJzU2VlbiA8IDQpIHtcclxuICAgICAgICAgICAgKytwb2ludGVyO1xyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgcmV0dXJuIGZhaWx1cmU7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBpZiAoIWlzQVNDSUlEaWdpdChpbnB1dFtwb2ludGVyXSkpIHtcclxuICAgICAgICAgIHJldHVybiBmYWlsdXJlO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgd2hpbGUgKGlzQVNDSUlEaWdpdChpbnB1dFtwb2ludGVyXSkpIHtcclxuICAgICAgICAgIGNvbnN0IG51bWJlciA9IHBhcnNlSW50KGF0KGlucHV0LCBwb2ludGVyKSk7XHJcbiAgICAgICAgICBpZiAoaXB2NFBpZWNlID09PSBudWxsKSB7XHJcbiAgICAgICAgICAgIGlwdjRQaWVjZSA9IG51bWJlcjtcclxuICAgICAgICAgIH0gZWxzZSBpZiAoaXB2NFBpZWNlID09PSAwKSB7XHJcbiAgICAgICAgICAgIHJldHVybiBmYWlsdXJlO1xyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgaXB2NFBpZWNlID0gaXB2NFBpZWNlICogMTAgKyBudW1iZXI7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBpZiAoaXB2NFBpZWNlID4gMjU1KSB7XHJcbiAgICAgICAgICAgIHJldHVybiBmYWlsdXJlO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgKytwb2ludGVyO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgYWRkcmVzc1twaWVjZUluZGV4XSA9IGFkZHJlc3NbcGllY2VJbmRleF0gKiAweDEwMCArIGlwdjRQaWVjZTtcclxuXHJcbiAgICAgICAgKytudW1iZXJzU2VlbjtcclxuXHJcbiAgICAgICAgaWYgKG51bWJlcnNTZWVuID09PSAyIHx8IG51bWJlcnNTZWVuID09PSA0KSB7XHJcbiAgICAgICAgICArK3BpZWNlSW5kZXg7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAobnVtYmVyc1NlZW4gIT09IDQpIHtcclxuICAgICAgICByZXR1cm4gZmFpbHVyZTtcclxuICAgICAgfVxyXG5cclxuICAgICAgYnJlYWs7XHJcbiAgICB9IGVsc2UgaWYgKGlucHV0W3BvaW50ZXJdID09PSA1OCkge1xyXG4gICAgICArK3BvaW50ZXI7XHJcbiAgICAgIGlmIChpbnB1dFtwb2ludGVyXSA9PT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgICAgcmV0dXJuIGZhaWx1cmU7XHJcbiAgICAgIH1cclxuICAgIH0gZWxzZSBpZiAoaW5wdXRbcG9pbnRlcl0gIT09IHVuZGVmaW5lZCkge1xyXG4gICAgICByZXR1cm4gZmFpbHVyZTtcclxuICAgIH1cclxuXHJcbiAgICBhZGRyZXNzW3BpZWNlSW5kZXhdID0gdmFsdWU7XHJcbiAgICArK3BpZWNlSW5kZXg7XHJcbiAgfVxyXG5cclxuICBpZiAoY29tcHJlc3MgIT09IG51bGwpIHtcclxuICAgIGxldCBzd2FwcyA9IHBpZWNlSW5kZXggLSBjb21wcmVzcztcclxuICAgIHBpZWNlSW5kZXggPSA3O1xyXG4gICAgd2hpbGUgKHBpZWNlSW5kZXggIT09IDAgJiYgc3dhcHMgPiAwKSB7XHJcbiAgICAgIGNvbnN0IHRlbXAgPSBhZGRyZXNzW2NvbXByZXNzICsgc3dhcHMgLSAxXTtcclxuICAgICAgYWRkcmVzc1tjb21wcmVzcyArIHN3YXBzIC0gMV0gPSBhZGRyZXNzW3BpZWNlSW5kZXhdO1xyXG4gICAgICBhZGRyZXNzW3BpZWNlSW5kZXhdID0gdGVtcDtcclxuICAgICAgLS1waWVjZUluZGV4O1xyXG4gICAgICAtLXN3YXBzO1xyXG4gICAgfVxyXG4gIH0gZWxzZSBpZiAoY29tcHJlc3MgPT09IG51bGwgJiYgcGllY2VJbmRleCAhPT0gOCkge1xyXG4gICAgcmV0dXJuIGZhaWx1cmU7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gYWRkcmVzcztcclxufVxyXG5cclxuZnVuY3Rpb24gc2VyaWFsaXplSVB2NihhZGRyZXNzKSB7XHJcbiAgbGV0IG91dHB1dCA9IFwiXCI7XHJcbiAgY29uc3Qgc2VxUmVzdWx0ID0gZmluZExvbmdlc3RaZXJvU2VxdWVuY2UoYWRkcmVzcyk7XHJcbiAgY29uc3QgY29tcHJlc3MgPSBzZXFSZXN1bHQuaWR4O1xyXG4gIGxldCBpZ25vcmUwID0gZmFsc2U7XHJcblxyXG4gIGZvciAobGV0IHBpZWNlSW5kZXggPSAwOyBwaWVjZUluZGV4IDw9IDc7ICsrcGllY2VJbmRleCkge1xyXG4gICAgaWYgKGlnbm9yZTAgJiYgYWRkcmVzc1twaWVjZUluZGV4XSA9PT0gMCkge1xyXG4gICAgICBjb250aW51ZTtcclxuICAgIH0gZWxzZSBpZiAoaWdub3JlMCkge1xyXG4gICAgICBpZ25vcmUwID0gZmFsc2U7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKGNvbXByZXNzID09PSBwaWVjZUluZGV4KSB7XHJcbiAgICAgIGNvbnN0IHNlcGFyYXRvciA9IHBpZWNlSW5kZXggPT09IDAgPyBcIjo6XCIgOiBcIjpcIjtcclxuICAgICAgb3V0cHV0ICs9IHNlcGFyYXRvcjtcclxuICAgICAgaWdub3JlMCA9IHRydWU7XHJcbiAgICAgIGNvbnRpbnVlO1xyXG4gICAgfVxyXG5cclxuICAgIG91dHB1dCArPSBhZGRyZXNzW3BpZWNlSW5kZXhdLnRvU3RyaW5nKDE2KTtcclxuXHJcbiAgICBpZiAocGllY2VJbmRleCAhPT0gNykge1xyXG4gICAgICBvdXRwdXQgKz0gXCI6XCI7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICByZXR1cm4gb3V0cHV0O1xyXG59XHJcblxyXG5mdW5jdGlvbiBwYXJzZUhvc3QoaW5wdXQsIGlzU3BlY2lhbEFyZykge1xyXG4gIGlmIChpbnB1dFswXSA9PT0gXCJbXCIpIHtcclxuICAgIGlmIChpbnB1dFtpbnB1dC5sZW5ndGggLSAxXSAhPT0gXCJdXCIpIHtcclxuICAgICAgcmV0dXJuIGZhaWx1cmU7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHBhcnNlSVB2NihpbnB1dC5zdWJzdHJpbmcoMSwgaW5wdXQubGVuZ3RoIC0gMSkpO1xyXG4gIH1cclxuXHJcbiAgaWYgKCFpc1NwZWNpYWxBcmcpIHtcclxuICAgIHJldHVybiBwYXJzZU9wYXF1ZUhvc3QoaW5wdXQpO1xyXG4gIH1cclxuXHJcbiAgY29uc3QgZG9tYWluID0gdXRmOFBlcmNlbnREZWNvZGUoaW5wdXQpO1xyXG4gIGNvbnN0IGFzY2lpRG9tYWluID0gdHI0Ni50b0FTQ0lJKGRvbWFpbiwgZmFsc2UsIHRyNDYuUFJPQ0VTU0lOR19PUFRJT05TLk5PTlRSQU5TSVRJT05BTCwgZmFsc2UpO1xyXG4gIGlmIChhc2NpaURvbWFpbiA9PT0gbnVsbCkge1xyXG4gICAgcmV0dXJuIGZhaWx1cmU7XHJcbiAgfVxyXG5cclxuICBpZiAoY29udGFpbnNGb3JiaWRkZW5Ib3N0Q29kZVBvaW50KGFzY2lpRG9tYWluKSkge1xyXG4gICAgcmV0dXJuIGZhaWx1cmU7XHJcbiAgfVxyXG5cclxuICBjb25zdCBpcHY0SG9zdCA9IHBhcnNlSVB2NChhc2NpaURvbWFpbik7XHJcbiAgaWYgKHR5cGVvZiBpcHY0SG9zdCA9PT0gXCJudW1iZXJcIiB8fCBpcHY0SG9zdCA9PT0gZmFpbHVyZSkge1xyXG4gICAgcmV0dXJuIGlwdjRIb3N0O1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIGFzY2lpRG9tYWluO1xyXG59XHJcblxyXG5mdW5jdGlvbiBwYXJzZU9wYXF1ZUhvc3QoaW5wdXQpIHtcclxuICBpZiAoY29udGFpbnNGb3JiaWRkZW5Ib3N0Q29kZVBvaW50RXhjbHVkaW5nUGVyY2VudChpbnB1dCkpIHtcclxuICAgIHJldHVybiBmYWlsdXJlO1xyXG4gIH1cclxuXHJcbiAgbGV0IG91dHB1dCA9IFwiXCI7XHJcbiAgY29uc3QgZGVjb2RlZCA9IHB1bnljb2RlLnVjczIuZGVjb2RlKGlucHV0KTtcclxuICBmb3IgKGxldCBpID0gMDsgaSA8IGRlY29kZWQubGVuZ3RoOyArK2kpIHtcclxuICAgIG91dHB1dCArPSBwZXJjZW50RW5jb2RlQ2hhcihkZWNvZGVkW2ldLCBpc0MwQ29udHJvbFBlcmNlbnRFbmNvZGUpO1xyXG4gIH1cclxuICByZXR1cm4gb3V0cHV0O1xyXG59XHJcblxyXG5mdW5jdGlvbiBmaW5kTG9uZ2VzdFplcm9TZXF1ZW5jZShhcnIpIHtcclxuICBsZXQgbWF4SWR4ID0gbnVsbDtcclxuICBsZXQgbWF4TGVuID0gMTsgLy8gb25seSBmaW5kIGVsZW1lbnRzID4gMVxyXG4gIGxldCBjdXJyU3RhcnQgPSBudWxsO1xyXG4gIGxldCBjdXJyTGVuID0gMDtcclxuXHJcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBhcnIubGVuZ3RoOyArK2kpIHtcclxuICAgIGlmIChhcnJbaV0gIT09IDApIHtcclxuICAgICAgaWYgKGN1cnJMZW4gPiBtYXhMZW4pIHtcclxuICAgICAgICBtYXhJZHggPSBjdXJyU3RhcnQ7XHJcbiAgICAgICAgbWF4TGVuID0gY3VyckxlbjtcclxuICAgICAgfVxyXG5cclxuICAgICAgY3VyclN0YXJ0ID0gbnVsbDtcclxuICAgICAgY3VyckxlbiA9IDA7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBpZiAoY3VyclN0YXJ0ID09PSBudWxsKSB7XHJcbiAgICAgICAgY3VyclN0YXJ0ID0gaTtcclxuICAgICAgfVxyXG4gICAgICArK2N1cnJMZW47XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyBpZiB0cmFpbGluZyB6ZXJvc1xyXG4gIGlmIChjdXJyTGVuID4gbWF4TGVuKSB7XHJcbiAgICBtYXhJZHggPSBjdXJyU3RhcnQ7XHJcbiAgICBtYXhMZW4gPSBjdXJyTGVuO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIHtcclxuICAgIGlkeDogbWF4SWR4LFxyXG4gICAgbGVuOiBtYXhMZW5cclxuICB9O1xyXG59XHJcblxyXG5mdW5jdGlvbiBzZXJpYWxpemVIb3N0KGhvc3QpIHtcclxuICBpZiAodHlwZW9mIGhvc3QgPT09IFwibnVtYmVyXCIpIHtcclxuICAgIHJldHVybiBzZXJpYWxpemVJUHY0KGhvc3QpO1xyXG4gIH1cclxuXHJcbiAgLy8gSVB2NiBzZXJpYWxpemVyXHJcbiAgaWYgKGhvc3QgaW5zdGFuY2VvZiBBcnJheSkge1xyXG4gICAgcmV0dXJuIFwiW1wiICsgc2VyaWFsaXplSVB2Nihob3N0KSArIFwiXVwiO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIGhvc3Q7XHJcbn1cclxuXHJcbmZ1bmN0aW9uIHRyaW1Db250cm9sQ2hhcnModXJsKSB7XHJcbiAgcmV0dXJuIHVybC5yZXBsYWNlKC9eW1xcdTAwMDAtXFx1MDAxRlxcdTAwMjBdK3xbXFx1MDAwMC1cXHUwMDFGXFx1MDAyMF0rJC9nLCBcIlwiKTtcclxufVxyXG5cclxuZnVuY3Rpb24gdHJpbVRhYkFuZE5ld2xpbmUodXJsKSB7XHJcbiAgcmV0dXJuIHVybC5yZXBsYWNlKC9cXHUwMDA5fFxcdTAwMEF8XFx1MDAwRC9nLCBcIlwiKTtcclxufVxyXG5cclxuZnVuY3Rpb24gc2hvcnRlblBhdGgodXJsKSB7XHJcbiAgY29uc3QgcGF0aCA9IHVybC5wYXRoO1xyXG4gIGlmIChwYXRoLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgcmV0dXJuO1xyXG4gIH1cclxuICBpZiAodXJsLnNjaGVtZSA9PT0gXCJmaWxlXCIgJiYgcGF0aC5sZW5ndGggPT09IDEgJiYgaXNOb3JtYWxpemVkV2luZG93c0RyaXZlTGV0dGVyKHBhdGhbMF0pKSB7XHJcbiAgICByZXR1cm47XHJcbiAgfVxyXG5cclxuICBwYXRoLnBvcCgpO1xyXG59XHJcblxyXG5mdW5jdGlvbiBpbmNsdWRlc0NyZWRlbnRpYWxzKHVybCkge1xyXG4gIHJldHVybiB1cmwudXNlcm5hbWUgIT09IFwiXCIgfHwgdXJsLnBhc3N3b3JkICE9PSBcIlwiO1xyXG59XHJcblxyXG5mdW5jdGlvbiBjYW5ub3RIYXZlQVVzZXJuYW1lUGFzc3dvcmRQb3J0KHVybCkge1xyXG4gIHJldHVybiB1cmwuaG9zdCA9PT0gbnVsbCB8fCB1cmwuaG9zdCA9PT0gXCJcIiB8fCB1cmwuY2Fubm90QmVBQmFzZVVSTCB8fCB1cmwuc2NoZW1lID09PSBcImZpbGVcIjtcclxufVxyXG5cclxuZnVuY3Rpb24gaXNOb3JtYWxpemVkV2luZG93c0RyaXZlTGV0dGVyKHN0cmluZykge1xyXG4gIHJldHVybiAvXltBLVphLXpdOiQvLnRlc3Qoc3RyaW5nKTtcclxufVxyXG5cclxuZnVuY3Rpb24gVVJMU3RhdGVNYWNoaW5lKGlucHV0LCBiYXNlLCBlbmNvZGluZ092ZXJyaWRlLCB1cmwsIHN0YXRlT3ZlcnJpZGUpIHtcclxuICB0aGlzLnBvaW50ZXIgPSAwO1xyXG4gIHRoaXMuaW5wdXQgPSBpbnB1dDtcclxuICB0aGlzLmJhc2UgPSBiYXNlIHx8IG51bGw7XHJcbiAgdGhpcy5lbmNvZGluZ092ZXJyaWRlID0gZW5jb2RpbmdPdmVycmlkZSB8fCBcInV0Zi04XCI7XHJcbiAgdGhpcy5zdGF0ZU92ZXJyaWRlID0gc3RhdGVPdmVycmlkZTtcclxuICB0aGlzLnVybCA9IHVybDtcclxuICB0aGlzLmZhaWx1cmUgPSBmYWxzZTtcclxuICB0aGlzLnBhcnNlRXJyb3IgPSBmYWxzZTtcclxuXHJcbiAgaWYgKCF0aGlzLnVybCkge1xyXG4gICAgdGhpcy51cmwgPSB7XHJcbiAgICAgIHNjaGVtZTogXCJcIixcclxuICAgICAgdXNlcm5hbWU6IFwiXCIsXHJcbiAgICAgIHBhc3N3b3JkOiBcIlwiLFxyXG4gICAgICBob3N0OiBudWxsLFxyXG4gICAgICBwb3J0OiBudWxsLFxyXG4gICAgICBwYXRoOiBbXSxcclxuICAgICAgcXVlcnk6IG51bGwsXHJcbiAgICAgIGZyYWdtZW50OiBudWxsLFxyXG5cclxuICAgICAgY2Fubm90QmVBQmFzZVVSTDogZmFsc2VcclxuICAgIH07XHJcblxyXG4gICAgY29uc3QgcmVzID0gdHJpbUNvbnRyb2xDaGFycyh0aGlzLmlucHV0KTtcclxuICAgIGlmIChyZXMgIT09IHRoaXMuaW5wdXQpIHtcclxuICAgICAgdGhpcy5wYXJzZUVycm9yID0gdHJ1ZTtcclxuICAgIH1cclxuICAgIHRoaXMuaW5wdXQgPSByZXM7XHJcbiAgfVxyXG5cclxuICBjb25zdCByZXMgPSB0cmltVGFiQW5kTmV3bGluZSh0aGlzLmlucHV0KTtcclxuICBpZiAocmVzICE9PSB0aGlzLmlucHV0KSB7XHJcbiAgICB0aGlzLnBhcnNlRXJyb3IgPSB0cnVlO1xyXG4gIH1cclxuICB0aGlzLmlucHV0ID0gcmVzO1xyXG5cclxuICB0aGlzLnN0YXRlID0gc3RhdGVPdmVycmlkZSB8fCBcInNjaGVtZSBzdGFydFwiO1xyXG5cclxuICB0aGlzLmJ1ZmZlciA9IFwiXCI7XHJcbiAgdGhpcy5hdEZsYWcgPSBmYWxzZTtcclxuICB0aGlzLmFyckZsYWcgPSBmYWxzZTtcclxuICB0aGlzLnBhc3N3b3JkVG9rZW5TZWVuRmxhZyA9IGZhbHNlO1xyXG5cclxuICB0aGlzLmlucHV0ID0gcHVueWNvZGUudWNzMi5kZWNvZGUodGhpcy5pbnB1dCk7XHJcblxyXG4gIGZvciAoOyB0aGlzLnBvaW50ZXIgPD0gdGhpcy5pbnB1dC5sZW5ndGg7ICsrdGhpcy5wb2ludGVyKSB7XHJcbiAgICBjb25zdCBjID0gdGhpcy5pbnB1dFt0aGlzLnBvaW50ZXJdO1xyXG4gICAgY29uc3QgY1N0ciA9IGlzTmFOKGMpID8gdW5kZWZpbmVkIDogU3RyaW5nLmZyb21Db2RlUG9pbnQoYyk7XHJcblxyXG4gICAgLy8gZXhlYyBzdGF0ZSBtYWNoaW5lXHJcbiAgICBjb25zdCByZXQgPSB0aGlzW1wicGFyc2UgXCIgKyB0aGlzLnN0YXRlXShjLCBjU3RyKTtcclxuICAgIGlmICghcmV0KSB7XHJcbiAgICAgIGJyZWFrOyAvLyB0ZXJtaW5hdGUgYWxnb3JpdGhtXHJcbiAgICB9IGVsc2UgaWYgKHJldCA9PT0gZmFpbHVyZSkge1xyXG4gICAgICB0aGlzLmZhaWx1cmUgPSB0cnVlO1xyXG4gICAgICBicmVhaztcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcblVSTFN0YXRlTWFjaGluZS5wcm90b3R5cGVbXCJwYXJzZSBzY2hlbWUgc3RhcnRcIl0gPSBmdW5jdGlvbiBwYXJzZVNjaGVtZVN0YXJ0KGMsIGNTdHIpIHtcclxuICBpZiAoaXNBU0NJSUFscGhhKGMpKSB7XHJcbiAgICB0aGlzLmJ1ZmZlciArPSBjU3RyLnRvTG93ZXJDYXNlKCk7XHJcbiAgICB0aGlzLnN0YXRlID0gXCJzY2hlbWVcIjtcclxuICB9IGVsc2UgaWYgKCF0aGlzLnN0YXRlT3ZlcnJpZGUpIHtcclxuICAgIHRoaXMuc3RhdGUgPSBcIm5vIHNjaGVtZVwiO1xyXG4gICAgLS10aGlzLnBvaW50ZXI7XHJcbiAgfSBlbHNlIHtcclxuICAgIHRoaXMucGFyc2VFcnJvciA9IHRydWU7XHJcbiAgICByZXR1cm4gZmFpbHVyZTtcclxuICB9XHJcblxyXG4gIHJldHVybiB0cnVlO1xyXG59O1xyXG5cclxuVVJMU3RhdGVNYWNoaW5lLnByb3RvdHlwZVtcInBhcnNlIHNjaGVtZVwiXSA9IGZ1bmN0aW9uIHBhcnNlU2NoZW1lKGMsIGNTdHIpIHtcclxuICBpZiAoaXNBU0NJSUFscGhhbnVtZXJpYyhjKSB8fCBjID09PSA0MyB8fCBjID09PSA0NSB8fCBjID09PSA0Nikge1xyXG4gICAgdGhpcy5idWZmZXIgKz0gY1N0ci50b0xvd2VyQ2FzZSgpO1xyXG4gIH0gZWxzZSBpZiAoYyA9PT0gNTgpIHtcclxuICAgIGlmICh0aGlzLnN0YXRlT3ZlcnJpZGUpIHtcclxuICAgICAgaWYgKGlzU3BlY2lhbCh0aGlzLnVybCkgJiYgIWlzU3BlY2lhbFNjaGVtZSh0aGlzLmJ1ZmZlcikpIHtcclxuICAgICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmICghaXNTcGVjaWFsKHRoaXMudXJsKSAmJiBpc1NwZWNpYWxTY2hlbWUodGhpcy5idWZmZXIpKSB7XHJcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAoKGluY2x1ZGVzQ3JlZGVudGlhbHModGhpcy51cmwpIHx8IHRoaXMudXJsLnBvcnQgIT09IG51bGwpICYmIHRoaXMuYnVmZmVyID09PSBcImZpbGVcIikge1xyXG4gICAgICAgIHJldHVybiBmYWxzZTtcclxuICAgICAgfVxyXG5cclxuICAgICAgaWYgKHRoaXMudXJsLnNjaGVtZSA9PT0gXCJmaWxlXCIgJiYgKHRoaXMudXJsLmhvc3QgPT09IFwiXCIgfHwgdGhpcy51cmwuaG9zdCA9PT0gbnVsbCkpIHtcclxuICAgICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICAgIHRoaXMudXJsLnNjaGVtZSA9IHRoaXMuYnVmZmVyO1xyXG4gICAgdGhpcy5idWZmZXIgPSBcIlwiO1xyXG4gICAgaWYgKHRoaXMuc3RhdGVPdmVycmlkZSkge1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcbiAgICBpZiAodGhpcy51cmwuc2NoZW1lID09PSBcImZpbGVcIikge1xyXG4gICAgICBpZiAodGhpcy5pbnB1dFt0aGlzLnBvaW50ZXIgKyAxXSAhPT0gNDcgfHwgdGhpcy5pbnB1dFt0aGlzLnBvaW50ZXIgKyAyXSAhPT0gNDcpIHtcclxuICAgICAgICB0aGlzLnBhcnNlRXJyb3IgPSB0cnVlO1xyXG4gICAgICB9XHJcbiAgICAgIHRoaXMuc3RhdGUgPSBcImZpbGVcIjtcclxuICAgIH0gZWxzZSBpZiAoaXNTcGVjaWFsKHRoaXMudXJsKSAmJiB0aGlzLmJhc2UgIT09IG51bGwgJiYgdGhpcy5iYXNlLnNjaGVtZSA9PT0gdGhpcy51cmwuc2NoZW1lKSB7XHJcbiAgICAgIHRoaXMuc3RhdGUgPSBcInNwZWNpYWwgcmVsYXRpdmUgb3IgYXV0aG9yaXR5XCI7XHJcbiAgICB9IGVsc2UgaWYgKGlzU3BlY2lhbCh0aGlzLnVybCkpIHtcclxuICAgICAgdGhpcy5zdGF0ZSA9IFwic3BlY2lhbCBhdXRob3JpdHkgc2xhc2hlc1wiO1xyXG4gICAgfSBlbHNlIGlmICh0aGlzLmlucHV0W3RoaXMucG9pbnRlciArIDFdID09PSA0Nykge1xyXG4gICAgICB0aGlzLnN0YXRlID0gXCJwYXRoIG9yIGF1dGhvcml0eVwiO1xyXG4gICAgICArK3RoaXMucG9pbnRlcjtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHRoaXMudXJsLmNhbm5vdEJlQUJhc2VVUkwgPSB0cnVlO1xyXG4gICAgICB0aGlzLnVybC5wYXRoLnB1c2goXCJcIik7XHJcbiAgICAgIHRoaXMuc3RhdGUgPSBcImNhbm5vdC1iZS1hLWJhc2UtVVJMIHBhdGhcIjtcclxuICAgIH1cclxuICB9IGVsc2UgaWYgKCF0aGlzLnN0YXRlT3ZlcnJpZGUpIHtcclxuICAgIHRoaXMuYnVmZmVyID0gXCJcIjtcclxuICAgIHRoaXMuc3RhdGUgPSBcIm5vIHNjaGVtZVwiO1xyXG4gICAgdGhpcy5wb2ludGVyID0gLTE7XHJcbiAgfSBlbHNlIHtcclxuICAgIHRoaXMucGFyc2VFcnJvciA9IHRydWU7XHJcbiAgICByZXR1cm4gZmFpbHVyZTtcclxuICB9XHJcblxyXG4gIHJldHVybiB0cnVlO1xyXG59O1xyXG5cclxuVVJMU3RhdGVNYWNoaW5lLnByb3RvdHlwZVtcInBhcnNlIG5vIHNjaGVtZVwiXSA9IGZ1bmN0aW9uIHBhcnNlTm9TY2hlbWUoYykge1xyXG4gIGlmICh0aGlzLmJhc2UgPT09IG51bGwgfHwgKHRoaXMuYmFzZS5jYW5ub3RCZUFCYXNlVVJMICYmIGMgIT09IDM1KSkge1xyXG4gICAgcmV0dXJuIGZhaWx1cmU7XHJcbiAgfSBlbHNlIGlmICh0aGlzLmJhc2UuY2Fubm90QmVBQmFzZVVSTCAmJiBjID09PSAzNSkge1xyXG4gICAgdGhpcy51cmwuc2NoZW1lID0gdGhpcy5iYXNlLnNjaGVtZTtcclxuICAgIHRoaXMudXJsLnBhdGggPSB0aGlzLmJhc2UucGF0aC5zbGljZSgpO1xyXG4gICAgdGhpcy51cmwucXVlcnkgPSB0aGlzLmJhc2UucXVlcnk7XHJcbiAgICB0aGlzLnVybC5mcmFnbWVudCA9IFwiXCI7XHJcbiAgICB0aGlzLnVybC5jYW5ub3RCZUFCYXNlVVJMID0gdHJ1ZTtcclxuICAgIHRoaXMuc3RhdGUgPSBcImZyYWdtZW50XCI7XHJcbiAgfSBlbHNlIGlmICh0aGlzLmJhc2Uuc2NoZW1lID09PSBcImZpbGVcIikge1xyXG4gICAgdGhpcy5zdGF0ZSA9IFwiZmlsZVwiO1xyXG4gICAgLS10aGlzLnBvaW50ZXI7XHJcbiAgfSBlbHNlIHtcclxuICAgIHRoaXMuc3RhdGUgPSBcInJlbGF0aXZlXCI7XHJcbiAgICAtLXRoaXMucG9pbnRlcjtcclxuICB9XHJcblxyXG4gIHJldHVybiB0cnVlO1xyXG59O1xyXG5cclxuVVJMU3RhdGVNYWNoaW5lLnByb3RvdHlwZVtcInBhcnNlIHNwZWNpYWwgcmVsYXRpdmUgb3IgYXV0aG9yaXR5XCJdID0gZnVuY3Rpb24gcGFyc2VTcGVjaWFsUmVsYXRpdmVPckF1dGhvcml0eShjKSB7XHJcbiAgaWYgKGMgPT09IDQ3ICYmIHRoaXMuaW5wdXRbdGhpcy5wb2ludGVyICsgMV0gPT09IDQ3KSB7XHJcbiAgICB0aGlzLnN0YXRlID0gXCJzcGVjaWFsIGF1dGhvcml0eSBpZ25vcmUgc2xhc2hlc1wiO1xyXG4gICAgKyt0aGlzLnBvaW50ZXI7XHJcbiAgfSBlbHNlIHtcclxuICAgIHRoaXMucGFyc2VFcnJvciA9IHRydWU7XHJcbiAgICB0aGlzLnN0YXRlID0gXCJyZWxhdGl2ZVwiO1xyXG4gICAgLS10aGlzLnBvaW50ZXI7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gdHJ1ZTtcclxufTtcclxuXHJcblVSTFN0YXRlTWFjaGluZS5wcm90b3R5cGVbXCJwYXJzZSBwYXRoIG9yIGF1dGhvcml0eVwiXSA9IGZ1bmN0aW9uIHBhcnNlUGF0aE9yQXV0aG9yaXR5KGMpIHtcclxuICBpZiAoYyA9PT0gNDcpIHtcclxuICAgIHRoaXMuc3RhdGUgPSBcImF1dGhvcml0eVwiO1xyXG4gIH0gZWxzZSB7XHJcbiAgICB0aGlzLnN0YXRlID0gXCJwYXRoXCI7XHJcbiAgICAtLXRoaXMucG9pbnRlcjtcclxuICB9XHJcblxyXG4gIHJldHVybiB0cnVlO1xyXG59O1xyXG5cclxuVVJMU3RhdGVNYWNoaW5lLnByb3RvdHlwZVtcInBhcnNlIHJlbGF0aXZlXCJdID0gZnVuY3Rpb24gcGFyc2VSZWxhdGl2ZShjKSB7XHJcbiAgdGhpcy51cmwuc2NoZW1lID0gdGhpcy5iYXNlLnNjaGVtZTtcclxuICBpZiAoaXNOYU4oYykpIHtcclxuICAgIHRoaXMudXJsLnVzZXJuYW1lID0gdGhpcy5iYXNlLnVzZXJuYW1lO1xyXG4gICAgdGhpcy51cmwucGFzc3dvcmQgPSB0aGlzLmJhc2UucGFzc3dvcmQ7XHJcbiAgICB0aGlzLnVybC5ob3N0ID0gdGhpcy5iYXNlLmhvc3Q7XHJcbiAgICB0aGlzLnVybC5wb3J0ID0gdGhpcy5iYXNlLnBvcnQ7XHJcbiAgICB0aGlzLnVybC5wYXRoID0gdGhpcy5iYXNlLnBhdGguc2xpY2UoKTtcclxuICAgIHRoaXMudXJsLnF1ZXJ5ID0gdGhpcy5iYXNlLnF1ZXJ5O1xyXG4gIH0gZWxzZSBpZiAoYyA9PT0gNDcpIHtcclxuICAgIHRoaXMuc3RhdGUgPSBcInJlbGF0aXZlIHNsYXNoXCI7XHJcbiAgfSBlbHNlIGlmIChjID09PSA2Mykge1xyXG4gICAgdGhpcy51cmwudXNlcm5hbWUgPSB0aGlzLmJhc2UudXNlcm5hbWU7XHJcbiAgICB0aGlzLnVybC5wYXNzd29yZCA9IHRoaXMuYmFzZS5wYXNzd29yZDtcclxuICAgIHRoaXMudXJsLmhvc3QgPSB0aGlzLmJhc2UuaG9zdDtcclxuICAgIHRoaXMudXJsLnBvcnQgPSB0aGlzLmJhc2UucG9ydDtcclxuICAgIHRoaXMudXJsLnBhdGggPSB0aGlzLmJhc2UucGF0aC5zbGljZSgpO1xyXG4gICAgdGhpcy51cmwucXVlcnkgPSBcIlwiO1xyXG4gICAgdGhpcy5zdGF0ZSA9IFwicXVlcnlcIjtcclxuICB9IGVsc2UgaWYgKGMgPT09IDM1KSB7XHJcbiAgICB0aGlzLnVybC51c2VybmFtZSA9IHRoaXMuYmFzZS51c2VybmFtZTtcclxuICAgIHRoaXMudXJsLnBhc3N3b3JkID0gdGhpcy5iYXNlLnBhc3N3b3JkO1xyXG4gICAgdGhpcy51cmwuaG9zdCA9IHRoaXMuYmFzZS5ob3N0O1xyXG4gICAgdGhpcy51cmwucG9ydCA9IHRoaXMuYmFzZS5wb3J0O1xyXG4gICAgdGhpcy51cmwucGF0aCA9IHRoaXMuYmFzZS5wYXRoLnNsaWNlKCk7XHJcbiAgICB0aGlzLnVybC5xdWVyeSA9IHRoaXMuYmFzZS5xdWVyeTtcclxuICAgIHRoaXMudXJsLmZyYWdtZW50ID0gXCJcIjtcclxuICAgIHRoaXMuc3RhdGUgPSBcImZyYWdtZW50XCI7XHJcbiAgfSBlbHNlIGlmIChpc1NwZWNpYWwodGhpcy51cmwpICYmIGMgPT09IDkyKSB7XHJcbiAgICB0aGlzLnBhcnNlRXJyb3IgPSB0cnVlO1xyXG4gICAgdGhpcy5zdGF0ZSA9IFwicmVsYXRpdmUgc2xhc2hcIjtcclxuICB9IGVsc2Uge1xyXG4gICAgdGhpcy51cmwudXNlcm5hbWUgPSB0aGlzLmJhc2UudXNlcm5hbWU7XHJcbiAgICB0aGlzLnVybC5wYXNzd29yZCA9IHRoaXMuYmFzZS5wYXNzd29yZDtcclxuICAgIHRoaXMudXJsLmhvc3QgPSB0aGlzLmJhc2UuaG9zdDtcclxuICAgIHRoaXMudXJsLnBvcnQgPSB0aGlzLmJhc2UucG9ydDtcclxuICAgIHRoaXMudXJsLnBhdGggPSB0aGlzLmJhc2UucGF0aC5zbGljZSgwLCB0aGlzLmJhc2UucGF0aC5sZW5ndGggLSAxKTtcclxuXHJcbiAgICB0aGlzLnN0YXRlID0gXCJwYXRoXCI7XHJcbiAgICAtLXRoaXMucG9pbnRlcjtcclxuICB9XHJcblxyXG4gIHJldHVybiB0cnVlO1xyXG59O1xyXG5cclxuVVJMU3RhdGVNYWNoaW5lLnByb3RvdHlwZVtcInBhcnNlIHJlbGF0aXZlIHNsYXNoXCJdID0gZnVuY3Rpb24gcGFyc2VSZWxhdGl2ZVNsYXNoKGMpIHtcclxuICBpZiAoaXNTcGVjaWFsKHRoaXMudXJsKSAmJiAoYyA9PT0gNDcgfHwgYyA9PT0gOTIpKSB7XHJcbiAgICBpZiAoYyA9PT0gOTIpIHtcclxuICAgICAgdGhpcy5wYXJzZUVycm9yID0gdHJ1ZTtcclxuICAgIH1cclxuICAgIHRoaXMuc3RhdGUgPSBcInNwZWNpYWwgYXV0aG9yaXR5IGlnbm9yZSBzbGFzaGVzXCI7XHJcbiAgfSBlbHNlIGlmIChjID09PSA0Nykge1xyXG4gICAgdGhpcy5zdGF0ZSA9IFwiYXV0aG9yaXR5XCI7XHJcbiAgfSBlbHNlIHtcclxuICAgIHRoaXMudXJsLnVzZXJuYW1lID0gdGhpcy5iYXNlLnVzZXJuYW1lO1xyXG4gICAgdGhpcy51cmwucGFzc3dvcmQgPSB0aGlzLmJhc2UucGFzc3dvcmQ7XHJcbiAgICB0aGlzLnVybC5ob3N0ID0gdGhpcy5iYXNlLmhvc3Q7XHJcbiAgICB0aGlzLnVybC5wb3J0ID0gdGhpcy5iYXNlLnBvcnQ7XHJcbiAgICB0aGlzLnN0YXRlID0gXCJwYXRoXCI7XHJcbiAgICAtLXRoaXMucG9pbnRlcjtcclxuICB9XHJcblxyXG4gIHJldHVybiB0cnVlO1xyXG59O1xyXG5cclxuVVJMU3RhdGVNYWNoaW5lLnByb3RvdHlwZVtcInBhcnNlIHNwZWNpYWwgYXV0aG9yaXR5IHNsYXNoZXNcIl0gPSBmdW5jdGlvbiBwYXJzZVNwZWNpYWxBdXRob3JpdHlTbGFzaGVzKGMpIHtcclxuICBpZiAoYyA9PT0gNDcgJiYgdGhpcy5pbnB1dFt0aGlzLnBvaW50ZXIgKyAxXSA9PT0gNDcpIHtcclxuICAgIHRoaXMuc3RhdGUgPSBcInNwZWNpYWwgYXV0aG9yaXR5IGlnbm9yZSBzbGFzaGVzXCI7XHJcbiAgICArK3RoaXMucG9pbnRlcjtcclxuICB9IGVsc2Uge1xyXG4gICAgdGhpcy5wYXJzZUVycm9yID0gdHJ1ZTtcclxuICAgIHRoaXMuc3RhdGUgPSBcInNwZWNpYWwgYXV0aG9yaXR5IGlnbm9yZSBzbGFzaGVzXCI7XHJcbiAgICAtLXRoaXMucG9pbnRlcjtcclxuICB9XHJcblxyXG4gIHJldHVybiB0cnVlO1xyXG59O1xyXG5cclxuVVJMU3RhdGVNYWNoaW5lLnByb3RvdHlwZVtcInBhcnNlIHNwZWNpYWwgYXV0aG9yaXR5IGlnbm9yZSBzbGFzaGVzXCJdID0gZnVuY3Rpb24gcGFyc2VTcGVjaWFsQXV0aG9yaXR5SWdub3JlU2xhc2hlcyhjKSB7XHJcbiAgaWYgKGMgIT09IDQ3ICYmIGMgIT09IDkyKSB7XHJcbiAgICB0aGlzLnN0YXRlID0gXCJhdXRob3JpdHlcIjtcclxuICAgIC0tdGhpcy5wb2ludGVyO1xyXG4gIH0gZWxzZSB7XHJcbiAgICB0aGlzLnBhcnNlRXJyb3IgPSB0cnVlO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIHRydWU7XHJcbn07XHJcblxyXG5VUkxTdGF0ZU1hY2hpbmUucHJvdG90eXBlW1wicGFyc2UgYXV0aG9yaXR5XCJdID0gZnVuY3Rpb24gcGFyc2VBdXRob3JpdHkoYywgY1N0cikge1xyXG4gIGlmIChjID09PSA2NCkge1xyXG4gICAgdGhpcy5wYXJzZUVycm9yID0gdHJ1ZTtcclxuICAgIGlmICh0aGlzLmF0RmxhZykge1xyXG4gICAgICB0aGlzLmJ1ZmZlciA9IFwiJTQwXCIgKyB0aGlzLmJ1ZmZlcjtcclxuICAgIH1cclxuICAgIHRoaXMuYXRGbGFnID0gdHJ1ZTtcclxuXHJcbiAgICAvLyBjYXJlZnVsLCB0aGlzIGlzIGJhc2VkIG9uIGJ1ZmZlciBhbmQgaGFzIGl0cyBvd24gcG9pbnRlciAodGhpcy5wb2ludGVyICE9IHBvaW50ZXIpIGFuZCBpbm5lciBjaGFyc1xyXG4gICAgY29uc3QgbGVuID0gY291bnRTeW1ib2xzKHRoaXMuYnVmZmVyKTtcclxuICAgIGZvciAobGV0IHBvaW50ZXIgPSAwOyBwb2ludGVyIDwgbGVuOyArK3BvaW50ZXIpIHtcclxuICAgICAgY29uc3QgY29kZVBvaW50ID0gdGhpcy5idWZmZXIuY29kZVBvaW50QXQocG9pbnRlcik7XHJcblxyXG4gICAgICBpZiAoY29kZVBvaW50ID09PSA1OCAmJiAhdGhpcy5wYXNzd29yZFRva2VuU2VlbkZsYWcpIHtcclxuICAgICAgICB0aGlzLnBhc3N3b3JkVG9rZW5TZWVuRmxhZyA9IHRydWU7XHJcbiAgICAgICAgY29udGludWU7XHJcbiAgICAgIH1cclxuICAgICAgY29uc3QgZW5jb2RlZENvZGVQb2ludHMgPSBwZXJjZW50RW5jb2RlQ2hhcihjb2RlUG9pbnQsIGlzVXNlcmluZm9QZXJjZW50RW5jb2RlKTtcclxuICAgICAgaWYgKHRoaXMucGFzc3dvcmRUb2tlblNlZW5GbGFnKSB7XHJcbiAgICAgICAgdGhpcy51cmwucGFzc3dvcmQgKz0gZW5jb2RlZENvZGVQb2ludHM7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgdGhpcy51cmwudXNlcm5hbWUgKz0gZW5jb2RlZENvZGVQb2ludHM7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICAgIHRoaXMuYnVmZmVyID0gXCJcIjtcclxuICB9IGVsc2UgaWYgKGlzTmFOKGMpIHx8IGMgPT09IDQ3IHx8IGMgPT09IDYzIHx8IGMgPT09IDM1IHx8XHJcbiAgICAgICAgICAgICAoaXNTcGVjaWFsKHRoaXMudXJsKSAmJiBjID09PSA5MikpIHtcclxuICAgIGlmICh0aGlzLmF0RmxhZyAmJiB0aGlzLmJ1ZmZlciA9PT0gXCJcIikge1xyXG4gICAgICB0aGlzLnBhcnNlRXJyb3IgPSB0cnVlO1xyXG4gICAgICByZXR1cm4gZmFpbHVyZTtcclxuICAgIH1cclxuICAgIHRoaXMucG9pbnRlciAtPSBjb3VudFN5bWJvbHModGhpcy5idWZmZXIpICsgMTtcclxuICAgIHRoaXMuYnVmZmVyID0gXCJcIjtcclxuICAgIHRoaXMuc3RhdGUgPSBcImhvc3RcIjtcclxuICB9IGVsc2Uge1xyXG4gICAgdGhpcy5idWZmZXIgKz0gY1N0cjtcclxuICB9XHJcblxyXG4gIHJldHVybiB0cnVlO1xyXG59O1xyXG5cclxuVVJMU3RhdGVNYWNoaW5lLnByb3RvdHlwZVtcInBhcnNlIGhvc3RuYW1lXCJdID1cclxuVVJMU3RhdGVNYWNoaW5lLnByb3RvdHlwZVtcInBhcnNlIGhvc3RcIl0gPSBmdW5jdGlvbiBwYXJzZUhvc3ROYW1lKGMsIGNTdHIpIHtcclxuICBpZiAodGhpcy5zdGF0ZU92ZXJyaWRlICYmIHRoaXMudXJsLnNjaGVtZSA9PT0gXCJmaWxlXCIpIHtcclxuICAgIC0tdGhpcy5wb2ludGVyO1xyXG4gICAgdGhpcy5zdGF0ZSA9IFwiZmlsZSBob3N0XCI7XHJcbiAgfSBlbHNlIGlmIChjID09PSA1OCAmJiAhdGhpcy5hcnJGbGFnKSB7XHJcbiAgICBpZiAodGhpcy5idWZmZXIgPT09IFwiXCIpIHtcclxuICAgICAgdGhpcy5wYXJzZUVycm9yID0gdHJ1ZTtcclxuICAgICAgcmV0dXJuIGZhaWx1cmU7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaG9zdCA9IHBhcnNlSG9zdCh0aGlzLmJ1ZmZlciwgaXNTcGVjaWFsKHRoaXMudXJsKSk7XHJcbiAgICBpZiAoaG9zdCA9PT0gZmFpbHVyZSkge1xyXG4gICAgICByZXR1cm4gZmFpbHVyZTtcclxuICAgIH1cclxuXHJcbiAgICB0aGlzLnVybC5ob3N0ID0gaG9zdDtcclxuICAgIHRoaXMuYnVmZmVyID0gXCJcIjtcclxuICAgIHRoaXMuc3RhdGUgPSBcInBvcnRcIjtcclxuICAgIGlmICh0aGlzLnN0YXRlT3ZlcnJpZGUgPT09IFwiaG9zdG5hbWVcIikge1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcbiAgfSBlbHNlIGlmIChpc05hTihjKSB8fCBjID09PSA0NyB8fCBjID09PSA2MyB8fCBjID09PSAzNSB8fFxyXG4gICAgICAgICAgICAgKGlzU3BlY2lhbCh0aGlzLnVybCkgJiYgYyA9PT0gOTIpKSB7XHJcbiAgICAtLXRoaXMucG9pbnRlcjtcclxuICAgIGlmIChpc1NwZWNpYWwodGhpcy51cmwpICYmIHRoaXMuYnVmZmVyID09PSBcIlwiKSB7XHJcbiAgICAgIHRoaXMucGFyc2VFcnJvciA9IHRydWU7XHJcbiAgICAgIHJldHVybiBmYWlsdXJlO1xyXG4gICAgfSBlbHNlIGlmICh0aGlzLnN0YXRlT3ZlcnJpZGUgJiYgdGhpcy5idWZmZXIgPT09IFwiXCIgJiZcclxuICAgICAgICAgICAgICAgKGluY2x1ZGVzQ3JlZGVudGlhbHModGhpcy51cmwpIHx8IHRoaXMudXJsLnBvcnQgIT09IG51bGwpKSB7XHJcbiAgICAgIHRoaXMucGFyc2VFcnJvciA9IHRydWU7XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBob3N0ID0gcGFyc2VIb3N0KHRoaXMuYnVmZmVyLCBpc1NwZWNpYWwodGhpcy51cmwpKTtcclxuICAgIGlmIChob3N0ID09PSBmYWlsdXJlKSB7XHJcbiAgICAgIHJldHVybiBmYWlsdXJlO1xyXG4gICAgfVxyXG5cclxuICAgIHRoaXMudXJsLmhvc3QgPSBob3N0O1xyXG4gICAgdGhpcy5idWZmZXIgPSBcIlwiO1xyXG4gICAgdGhpcy5zdGF0ZSA9IFwicGF0aCBzdGFydFwiO1xyXG4gICAgaWYgKHRoaXMuc3RhdGVPdmVycmlkZSkge1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcbiAgfSBlbHNlIHtcclxuICAgIGlmIChjID09PSA5MSkge1xyXG4gICAgICB0aGlzLmFyckZsYWcgPSB0cnVlO1xyXG4gICAgfSBlbHNlIGlmIChjID09PSA5Mykge1xyXG4gICAgICB0aGlzLmFyckZsYWcgPSBmYWxzZTtcclxuICAgIH1cclxuICAgIHRoaXMuYnVmZmVyICs9IGNTdHI7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gdHJ1ZTtcclxufTtcclxuXHJcblVSTFN0YXRlTWFjaGluZS5wcm90b3R5cGVbXCJwYXJzZSBwb3J0XCJdID0gZnVuY3Rpb24gcGFyc2VQb3J0KGMsIGNTdHIpIHtcclxuICBpZiAoaXNBU0NJSURpZ2l0KGMpKSB7XHJcbiAgICB0aGlzLmJ1ZmZlciArPSBjU3RyO1xyXG4gIH0gZWxzZSBpZiAoaXNOYU4oYykgfHwgYyA9PT0gNDcgfHwgYyA9PT0gNjMgfHwgYyA9PT0gMzUgfHxcclxuICAgICAgICAgICAgIChpc1NwZWNpYWwodGhpcy51cmwpICYmIGMgPT09IDkyKSB8fFxyXG4gICAgICAgICAgICAgdGhpcy5zdGF0ZU92ZXJyaWRlKSB7XHJcbiAgICBpZiAodGhpcy5idWZmZXIgIT09IFwiXCIpIHtcclxuICAgICAgY29uc3QgcG9ydCA9IHBhcnNlSW50KHRoaXMuYnVmZmVyKTtcclxuICAgICAgaWYgKHBvcnQgPiBNYXRoLnBvdygyLCAxNikgLSAxKSB7XHJcbiAgICAgICAgdGhpcy5wYXJzZUVycm9yID0gdHJ1ZTtcclxuICAgICAgICByZXR1cm4gZmFpbHVyZTtcclxuICAgICAgfVxyXG4gICAgICB0aGlzLnVybC5wb3J0ID0gcG9ydCA9PT0gZGVmYXVsdFBvcnQodGhpcy51cmwuc2NoZW1lKSA/IG51bGwgOiBwb3J0O1xyXG4gICAgICB0aGlzLmJ1ZmZlciA9IFwiXCI7XHJcbiAgICB9XHJcbiAgICBpZiAodGhpcy5zdGF0ZU92ZXJyaWRlKSB7XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH1cclxuICAgIHRoaXMuc3RhdGUgPSBcInBhdGggc3RhcnRcIjtcclxuICAgIC0tdGhpcy5wb2ludGVyO1xyXG4gIH0gZWxzZSB7XHJcbiAgICB0aGlzLnBhcnNlRXJyb3IgPSB0cnVlO1xyXG4gICAgcmV0dXJuIGZhaWx1cmU7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gdHJ1ZTtcclxufTtcclxuXHJcbmNvbnN0IGZpbGVPdGhlcndpc2VDb2RlUG9pbnRzID0gbmV3IFNldChbNDcsIDkyLCA2MywgMzVdKTtcclxuXHJcblVSTFN0YXRlTWFjaGluZS5wcm90b3R5cGVbXCJwYXJzZSBmaWxlXCJdID0gZnVuY3Rpb24gcGFyc2VGaWxlKGMpIHtcclxuICB0aGlzLnVybC5zY2hlbWUgPSBcImZpbGVcIjtcclxuXHJcbiAgaWYgKGMgPT09IDQ3IHx8IGMgPT09IDkyKSB7XHJcbiAgICBpZiAoYyA9PT0gOTIpIHtcclxuICAgICAgdGhpcy5wYXJzZUVycm9yID0gdHJ1ZTtcclxuICAgIH1cclxuICAgIHRoaXMuc3RhdGUgPSBcImZpbGUgc2xhc2hcIjtcclxuICB9IGVsc2UgaWYgKHRoaXMuYmFzZSAhPT0gbnVsbCAmJiB0aGlzLmJhc2Uuc2NoZW1lID09PSBcImZpbGVcIikge1xyXG4gICAgaWYgKGlzTmFOKGMpKSB7XHJcbiAgICAgIHRoaXMudXJsLmhvc3QgPSB0aGlzLmJhc2UuaG9zdDtcclxuICAgICAgdGhpcy51cmwucGF0aCA9IHRoaXMuYmFzZS5wYXRoLnNsaWNlKCk7XHJcbiAgICAgIHRoaXMudXJsLnF1ZXJ5ID0gdGhpcy5iYXNlLnF1ZXJ5O1xyXG4gICAgfSBlbHNlIGlmIChjID09PSA2Mykge1xyXG4gICAgICB0aGlzLnVybC5ob3N0ID0gdGhpcy5iYXNlLmhvc3Q7XHJcbiAgICAgIHRoaXMudXJsLnBhdGggPSB0aGlzLmJhc2UucGF0aC5zbGljZSgpO1xyXG4gICAgICB0aGlzLnVybC5xdWVyeSA9IFwiXCI7XHJcbiAgICAgIHRoaXMuc3RhdGUgPSBcInF1ZXJ5XCI7XHJcbiAgICB9IGVsc2UgaWYgKGMgPT09IDM1KSB7XHJcbiAgICAgIHRoaXMudXJsLmhvc3QgPSB0aGlzLmJhc2UuaG9zdDtcclxuICAgICAgdGhpcy51cmwucGF0aCA9IHRoaXMuYmFzZS5wYXRoLnNsaWNlKCk7XHJcbiAgICAgIHRoaXMudXJsLnF1ZXJ5ID0gdGhpcy5iYXNlLnF1ZXJ5O1xyXG4gICAgICB0aGlzLnVybC5mcmFnbWVudCA9IFwiXCI7XHJcbiAgICAgIHRoaXMuc3RhdGUgPSBcImZyYWdtZW50XCI7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBpZiAodGhpcy5pbnB1dC5sZW5ndGggLSB0aGlzLnBvaW50ZXIgLSAxID09PSAwIHx8IC8vIHJlbWFpbmluZyBjb25zaXN0cyBvZiAwIGNvZGUgcG9pbnRzXHJcbiAgICAgICAgICAhaXNXaW5kb3dzRHJpdmVMZXR0ZXJDb2RlUG9pbnRzKGMsIHRoaXMuaW5wdXRbdGhpcy5wb2ludGVyICsgMV0pIHx8XHJcbiAgICAgICAgICAodGhpcy5pbnB1dC5sZW5ndGggLSB0aGlzLnBvaW50ZXIgLSAxID49IDIgJiYgLy8gcmVtYWluaW5nIGhhcyBhdCBsZWFzdCAyIGNvZGUgcG9pbnRzXHJcbiAgICAgICAgICAgIWZpbGVPdGhlcndpc2VDb2RlUG9pbnRzLmhhcyh0aGlzLmlucHV0W3RoaXMucG9pbnRlciArIDJdKSkpIHtcclxuICAgICAgICB0aGlzLnVybC5ob3N0ID0gdGhpcy5iYXNlLmhvc3Q7XHJcbiAgICAgICAgdGhpcy51cmwucGF0aCA9IHRoaXMuYmFzZS5wYXRoLnNsaWNlKCk7XHJcbiAgICAgICAgc2hvcnRlblBhdGgodGhpcy51cmwpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHRoaXMucGFyc2VFcnJvciA9IHRydWU7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHRoaXMuc3RhdGUgPSBcInBhdGhcIjtcclxuICAgICAgLS10aGlzLnBvaW50ZXI7XHJcbiAgICB9XHJcbiAgfSBlbHNlIHtcclxuICAgIHRoaXMuc3RhdGUgPSBcInBhdGhcIjtcclxuICAgIC0tdGhpcy5wb2ludGVyO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIHRydWU7XHJcbn07XHJcblxyXG5VUkxTdGF0ZU1hY2hpbmUucHJvdG90eXBlW1wicGFyc2UgZmlsZSBzbGFzaFwiXSA9IGZ1bmN0aW9uIHBhcnNlRmlsZVNsYXNoKGMpIHtcclxuICBpZiAoYyA9PT0gNDcgfHwgYyA9PT0gOTIpIHtcclxuICAgIGlmIChjID09PSA5Mikge1xyXG4gICAgICB0aGlzLnBhcnNlRXJyb3IgPSB0cnVlO1xyXG4gICAgfVxyXG4gICAgdGhpcy5zdGF0ZSA9IFwiZmlsZSBob3N0XCI7XHJcbiAgfSBlbHNlIHtcclxuICAgIGlmICh0aGlzLmJhc2UgIT09IG51bGwgJiYgdGhpcy5iYXNlLnNjaGVtZSA9PT0gXCJmaWxlXCIpIHtcclxuICAgICAgaWYgKGlzTm9ybWFsaXplZFdpbmRvd3NEcml2ZUxldHRlclN0cmluZyh0aGlzLmJhc2UucGF0aFswXSkpIHtcclxuICAgICAgICB0aGlzLnVybC5wYXRoLnB1c2godGhpcy5iYXNlLnBhdGhbMF0pO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHRoaXMudXJsLmhvc3QgPSB0aGlzLmJhc2UuaG9zdDtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gICAgdGhpcy5zdGF0ZSA9IFwicGF0aFwiO1xyXG4gICAgLS10aGlzLnBvaW50ZXI7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gdHJ1ZTtcclxufTtcclxuXHJcblVSTFN0YXRlTWFjaGluZS5wcm90b3R5cGVbXCJwYXJzZSBmaWxlIGhvc3RcIl0gPSBmdW5jdGlvbiBwYXJzZUZpbGVIb3N0KGMsIGNTdHIpIHtcclxuICBpZiAoaXNOYU4oYykgfHwgYyA9PT0gNDcgfHwgYyA9PT0gOTIgfHwgYyA9PT0gNjMgfHwgYyA9PT0gMzUpIHtcclxuICAgIC0tdGhpcy5wb2ludGVyO1xyXG4gICAgaWYgKCF0aGlzLnN0YXRlT3ZlcnJpZGUgJiYgaXNXaW5kb3dzRHJpdmVMZXR0ZXJTdHJpbmcodGhpcy5idWZmZXIpKSB7XHJcbiAgICAgIHRoaXMucGFyc2VFcnJvciA9IHRydWU7XHJcbiAgICAgIHRoaXMuc3RhdGUgPSBcInBhdGhcIjtcclxuICAgIH0gZWxzZSBpZiAodGhpcy5idWZmZXIgPT09IFwiXCIpIHtcclxuICAgICAgdGhpcy51cmwuaG9zdCA9IFwiXCI7XHJcbiAgICAgIGlmICh0aGlzLnN0YXRlT3ZlcnJpZGUpIHtcclxuICAgICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICAgIH1cclxuICAgICAgdGhpcy5zdGF0ZSA9IFwicGF0aCBzdGFydFwiO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgbGV0IGhvc3QgPSBwYXJzZUhvc3QodGhpcy5idWZmZXIsIGlzU3BlY2lhbCh0aGlzLnVybCkpO1xyXG4gICAgICBpZiAoaG9zdCA9PT0gZmFpbHVyZSkge1xyXG4gICAgICAgIHJldHVybiBmYWlsdXJlO1xyXG4gICAgICB9XHJcbiAgICAgIGlmIChob3N0ID09PSBcImxvY2FsaG9zdFwiKSB7XHJcbiAgICAgICAgaG9zdCA9IFwiXCI7XHJcbiAgICAgIH1cclxuICAgICAgdGhpcy51cmwuaG9zdCA9IGhvc3Q7XHJcblxyXG4gICAgICBpZiAodGhpcy5zdGF0ZU92ZXJyaWRlKSB7XHJcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgICB9XHJcblxyXG4gICAgICB0aGlzLmJ1ZmZlciA9IFwiXCI7XHJcbiAgICAgIHRoaXMuc3RhdGUgPSBcInBhdGggc3RhcnRcIjtcclxuICAgIH1cclxuICB9IGVsc2Uge1xyXG4gICAgdGhpcy5idWZmZXIgKz0gY1N0cjtcclxuICB9XHJcblxyXG4gIHJldHVybiB0cnVlO1xyXG59O1xyXG5cclxuVVJMU3RhdGVNYWNoaW5lLnByb3RvdHlwZVtcInBhcnNlIHBhdGggc3RhcnRcIl0gPSBmdW5jdGlvbiBwYXJzZVBhdGhTdGFydChjKSB7XHJcbiAgaWYgKGlzU3BlY2lhbCh0aGlzLnVybCkpIHtcclxuICAgIGlmIChjID09PSA5Mikge1xyXG4gICAgICB0aGlzLnBhcnNlRXJyb3IgPSB0cnVlO1xyXG4gICAgfVxyXG4gICAgdGhpcy5zdGF0ZSA9IFwicGF0aFwiO1xyXG5cclxuICAgIGlmIChjICE9PSA0NyAmJiBjICE9PSA5Mikge1xyXG4gICAgICAtLXRoaXMucG9pbnRlcjtcclxuICAgIH1cclxuICB9IGVsc2UgaWYgKCF0aGlzLnN0YXRlT3ZlcnJpZGUgJiYgYyA9PT0gNjMpIHtcclxuICAgIHRoaXMudXJsLnF1ZXJ5ID0gXCJcIjtcclxuICAgIHRoaXMuc3RhdGUgPSBcInF1ZXJ5XCI7XHJcbiAgfSBlbHNlIGlmICghdGhpcy5zdGF0ZU92ZXJyaWRlICYmIGMgPT09IDM1KSB7XHJcbiAgICB0aGlzLnVybC5mcmFnbWVudCA9IFwiXCI7XHJcbiAgICB0aGlzLnN0YXRlID0gXCJmcmFnbWVudFwiO1xyXG4gIH0gZWxzZSBpZiAoYyAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICB0aGlzLnN0YXRlID0gXCJwYXRoXCI7XHJcbiAgICBpZiAoYyAhPT0gNDcpIHtcclxuICAgICAgLS10aGlzLnBvaW50ZXI7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICByZXR1cm4gdHJ1ZTtcclxufTtcclxuXHJcblVSTFN0YXRlTWFjaGluZS5wcm90b3R5cGVbXCJwYXJzZSBwYXRoXCJdID0gZnVuY3Rpb24gcGFyc2VQYXRoKGMpIHtcclxuICBpZiAoaXNOYU4oYykgfHwgYyA9PT0gNDcgfHwgKGlzU3BlY2lhbCh0aGlzLnVybCkgJiYgYyA9PT0gOTIpIHx8XHJcbiAgICAgICghdGhpcy5zdGF0ZU92ZXJyaWRlICYmIChjID09PSA2MyB8fCBjID09PSAzNSkpKSB7XHJcbiAgICBpZiAoaXNTcGVjaWFsKHRoaXMudXJsKSAmJiBjID09PSA5Mikge1xyXG4gICAgICB0aGlzLnBhcnNlRXJyb3IgPSB0cnVlO1xyXG4gICAgfVxyXG5cclxuICAgIGlmIChpc0RvdWJsZURvdCh0aGlzLmJ1ZmZlcikpIHtcclxuICAgICAgc2hvcnRlblBhdGgodGhpcy51cmwpO1xyXG4gICAgICBpZiAoYyAhPT0gNDcgJiYgIShpc1NwZWNpYWwodGhpcy51cmwpICYmIGMgPT09IDkyKSkge1xyXG4gICAgICAgIHRoaXMudXJsLnBhdGgucHVzaChcIlwiKTtcclxuICAgICAgfVxyXG4gICAgfSBlbHNlIGlmIChpc1NpbmdsZURvdCh0aGlzLmJ1ZmZlcikgJiYgYyAhPT0gNDcgJiZcclxuICAgICAgICAgICAgICAgIShpc1NwZWNpYWwodGhpcy51cmwpICYmIGMgPT09IDkyKSkge1xyXG4gICAgICB0aGlzLnVybC5wYXRoLnB1c2goXCJcIik7XHJcbiAgICB9IGVsc2UgaWYgKCFpc1NpbmdsZURvdCh0aGlzLmJ1ZmZlcikpIHtcclxuICAgICAgaWYgKHRoaXMudXJsLnNjaGVtZSA9PT0gXCJmaWxlXCIgJiYgdGhpcy51cmwucGF0aC5sZW5ndGggPT09IDAgJiYgaXNXaW5kb3dzRHJpdmVMZXR0ZXJTdHJpbmcodGhpcy5idWZmZXIpKSB7XHJcbiAgICAgICAgaWYgKHRoaXMudXJsLmhvc3QgIT09IFwiXCIgJiYgdGhpcy51cmwuaG9zdCAhPT0gbnVsbCkge1xyXG4gICAgICAgICAgdGhpcy5wYXJzZUVycm9yID0gdHJ1ZTtcclxuICAgICAgICAgIHRoaXMudXJsLmhvc3QgPSBcIlwiO1xyXG4gICAgICAgIH1cclxuICAgICAgICB0aGlzLmJ1ZmZlciA9IHRoaXMuYnVmZmVyWzBdICsgXCI6XCI7XHJcbiAgICAgIH1cclxuICAgICAgdGhpcy51cmwucGF0aC5wdXNoKHRoaXMuYnVmZmVyKTtcclxuICAgIH1cclxuICAgIHRoaXMuYnVmZmVyID0gXCJcIjtcclxuICAgIGlmICh0aGlzLnVybC5zY2hlbWUgPT09IFwiZmlsZVwiICYmIChjID09PSB1bmRlZmluZWQgfHwgYyA9PT0gNjMgfHwgYyA9PT0gMzUpKSB7XHJcbiAgICAgIHdoaWxlICh0aGlzLnVybC5wYXRoLmxlbmd0aCA+IDEgJiYgdGhpcy51cmwucGF0aFswXSA9PT0gXCJcIikge1xyXG4gICAgICAgIHRoaXMucGFyc2VFcnJvciA9IHRydWU7XHJcbiAgICAgICAgdGhpcy51cmwucGF0aC5zaGlmdCgpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICBpZiAoYyA9PT0gNjMpIHtcclxuICAgICAgdGhpcy51cmwucXVlcnkgPSBcIlwiO1xyXG4gICAgICB0aGlzLnN0YXRlID0gXCJxdWVyeVwiO1xyXG4gICAgfVxyXG4gICAgaWYgKGMgPT09IDM1KSB7XHJcbiAgICAgIHRoaXMudXJsLmZyYWdtZW50ID0gXCJcIjtcclxuICAgICAgdGhpcy5zdGF0ZSA9IFwiZnJhZ21lbnRcIjtcclxuICAgIH1cclxuICB9IGVsc2Uge1xyXG4gICAgLy8gVE9ETzogSWYgYyBpcyBub3QgYSBVUkwgY29kZSBwb2ludCBhbmQgbm90IFwiJVwiLCBwYXJzZSBlcnJvci5cclxuXHJcbiAgICBpZiAoYyA9PT0gMzcgJiZcclxuICAgICAgKCFpc0FTQ0lJSGV4KHRoaXMuaW5wdXRbdGhpcy5wb2ludGVyICsgMV0pIHx8XHJcbiAgICAgICAgIWlzQVNDSUlIZXgodGhpcy5pbnB1dFt0aGlzLnBvaW50ZXIgKyAyXSkpKSB7XHJcbiAgICAgIHRoaXMucGFyc2VFcnJvciA9IHRydWU7XHJcbiAgICB9XHJcblxyXG4gICAgdGhpcy5idWZmZXIgKz0gcGVyY2VudEVuY29kZUNoYXIoYywgaXNQYXRoUGVyY2VudEVuY29kZSk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gdHJ1ZTtcclxufTtcclxuXHJcblVSTFN0YXRlTWFjaGluZS5wcm90b3R5cGVbXCJwYXJzZSBjYW5ub3QtYmUtYS1iYXNlLVVSTCBwYXRoXCJdID0gZnVuY3Rpb24gcGFyc2VDYW5ub3RCZUFCYXNlVVJMUGF0aChjKSB7XHJcbiAgaWYgKGMgPT09IDYzKSB7XHJcbiAgICB0aGlzLnVybC5xdWVyeSA9IFwiXCI7XHJcbiAgICB0aGlzLnN0YXRlID0gXCJxdWVyeVwiO1xyXG4gIH0gZWxzZSBpZiAoYyA9PT0gMzUpIHtcclxuICAgIHRoaXMudXJsLmZyYWdtZW50ID0gXCJcIjtcclxuICAgIHRoaXMuc3RhdGUgPSBcImZyYWdtZW50XCI7XHJcbiAgfSBlbHNlIHtcclxuICAgIC8vIFRPRE86IEFkZDogbm90IGEgVVJMIGNvZGUgcG9pbnRcclxuICAgIGlmICghaXNOYU4oYykgJiYgYyAhPT0gMzcpIHtcclxuICAgICAgdGhpcy5wYXJzZUVycm9yID0gdHJ1ZTtcclxuICAgIH1cclxuXHJcbiAgICBpZiAoYyA9PT0gMzcgJiZcclxuICAgICAgICAoIWlzQVNDSUlIZXgodGhpcy5pbnB1dFt0aGlzLnBvaW50ZXIgKyAxXSkgfHxcclxuICAgICAgICAgIWlzQVNDSUlIZXgodGhpcy5pbnB1dFt0aGlzLnBvaW50ZXIgKyAyXSkpKSB7XHJcbiAgICAgIHRoaXMucGFyc2VFcnJvciA9IHRydWU7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKCFpc05hTihjKSkge1xyXG4gICAgICB0aGlzLnVybC5wYXRoWzBdID0gdGhpcy51cmwucGF0aFswXSArIHBlcmNlbnRFbmNvZGVDaGFyKGMsIGlzQzBDb250cm9sUGVyY2VudEVuY29kZSk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICByZXR1cm4gdHJ1ZTtcclxufTtcclxuXHJcblVSTFN0YXRlTWFjaGluZS5wcm90b3R5cGVbXCJwYXJzZSBxdWVyeVwiXSA9IGZ1bmN0aW9uIHBhcnNlUXVlcnkoYywgY1N0cikge1xyXG4gIGlmIChpc05hTihjKSB8fCAoIXRoaXMuc3RhdGVPdmVycmlkZSAmJiBjID09PSAzNSkpIHtcclxuICAgIGlmICghaXNTcGVjaWFsKHRoaXMudXJsKSB8fCB0aGlzLnVybC5zY2hlbWUgPT09IFwid3NcIiB8fCB0aGlzLnVybC5zY2hlbWUgPT09IFwid3NzXCIpIHtcclxuICAgICAgdGhpcy5lbmNvZGluZ092ZXJyaWRlID0gXCJ1dGYtOFwiO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGJ1ZmZlciA9IG5ldyBCdWZmZXIodGhpcy5idWZmZXIpOyAvLyBUT0RPOiBVc2UgZW5jb2Rpbmcgb3ZlcnJpZGUgaW5zdGVhZFxyXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBidWZmZXIubGVuZ3RoOyArK2kpIHtcclxuICAgICAgaWYgKGJ1ZmZlcltpXSA8IDB4MjEgfHwgYnVmZmVyW2ldID4gMHg3RSB8fCBidWZmZXJbaV0gPT09IDB4MjIgfHwgYnVmZmVyW2ldID09PSAweDIzIHx8XHJcbiAgICAgICAgICBidWZmZXJbaV0gPT09IDB4M0MgfHwgYnVmZmVyW2ldID09PSAweDNFKSB7XHJcbiAgICAgICAgdGhpcy51cmwucXVlcnkgKz0gcGVyY2VudEVuY29kZShidWZmZXJbaV0pO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHRoaXMudXJsLnF1ZXJ5ICs9IFN0cmluZy5mcm9tQ29kZVBvaW50KGJ1ZmZlcltpXSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICB0aGlzLmJ1ZmZlciA9IFwiXCI7XHJcbiAgICBpZiAoYyA9PT0gMzUpIHtcclxuICAgICAgdGhpcy51cmwuZnJhZ21lbnQgPSBcIlwiO1xyXG4gICAgICB0aGlzLnN0YXRlID0gXCJmcmFnbWVudFwiO1xyXG4gICAgfVxyXG4gIH0gZWxzZSB7XHJcbiAgICAvLyBUT0RPOiBJZiBjIGlzIG5vdCBhIFVSTCBjb2RlIHBvaW50IGFuZCBub3QgXCIlXCIsIHBhcnNlIGVycm9yLlxyXG4gICAgaWYgKGMgPT09IDM3ICYmXHJcbiAgICAgICghaXNBU0NJSUhleCh0aGlzLmlucHV0W3RoaXMucG9pbnRlciArIDFdKSB8fFxyXG4gICAgICAgICFpc0FTQ0lJSGV4KHRoaXMuaW5wdXRbdGhpcy5wb2ludGVyICsgMl0pKSkge1xyXG4gICAgICB0aGlzLnBhcnNlRXJyb3IgPSB0cnVlO1xyXG4gICAgfVxyXG5cclxuICAgIHRoaXMuYnVmZmVyICs9IGNTdHI7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gdHJ1ZTtcclxufTtcclxuXHJcblVSTFN0YXRlTWFjaGluZS5wcm90b3R5cGVbXCJwYXJzZSBmcmFnbWVudFwiXSA9IGZ1bmN0aW9uIHBhcnNlRnJhZ21lbnQoYykge1xyXG4gIGlmIChpc05hTihjKSkgeyAvLyBkbyBub3RoaW5nXHJcbiAgfSBlbHNlIGlmIChjID09PSAweDApIHtcclxuICAgIHRoaXMucGFyc2VFcnJvciA9IHRydWU7XHJcbiAgfSBlbHNlIHtcclxuICAgIC8vIFRPRE86IElmIGMgaXMgbm90IGEgVVJMIGNvZGUgcG9pbnQgYW5kIG5vdCBcIiVcIiwgcGFyc2UgZXJyb3IuXHJcbiAgICBpZiAoYyA9PT0gMzcgJiZcclxuICAgICAgKCFpc0FTQ0lJSGV4KHRoaXMuaW5wdXRbdGhpcy5wb2ludGVyICsgMV0pIHx8XHJcbiAgICAgICAgIWlzQVNDSUlIZXgodGhpcy5pbnB1dFt0aGlzLnBvaW50ZXIgKyAyXSkpKSB7XHJcbiAgICAgIHRoaXMucGFyc2VFcnJvciA9IHRydWU7XHJcbiAgICB9XHJcblxyXG4gICAgdGhpcy51cmwuZnJhZ21lbnQgKz0gcGVyY2VudEVuY29kZUNoYXIoYywgaXNDMENvbnRyb2xQZXJjZW50RW5jb2RlKTtcclxuICB9XHJcblxyXG4gIHJldHVybiB0cnVlO1xyXG59O1xyXG5cclxuZnVuY3Rpb24gc2VyaWFsaXplVVJMKHVybCwgZXhjbHVkZUZyYWdtZW50KSB7XHJcbiAgbGV0IG91dHB1dCA9IHVybC5zY2hlbWUgKyBcIjpcIjtcclxuICBpZiAodXJsLmhvc3QgIT09IG51bGwpIHtcclxuICAgIG91dHB1dCArPSBcIi8vXCI7XHJcblxyXG4gICAgaWYgKHVybC51c2VybmFtZSAhPT0gXCJcIiB8fCB1cmwucGFzc3dvcmQgIT09IFwiXCIpIHtcclxuICAgICAgb3V0cHV0ICs9IHVybC51c2VybmFtZTtcclxuICAgICAgaWYgKHVybC5wYXNzd29yZCAhPT0gXCJcIikge1xyXG4gICAgICAgIG91dHB1dCArPSBcIjpcIiArIHVybC5wYXNzd29yZDtcclxuICAgICAgfVxyXG4gICAgICBvdXRwdXQgKz0gXCJAXCI7XHJcbiAgICB9XHJcblxyXG4gICAgb3V0cHV0ICs9IHNlcmlhbGl6ZUhvc3QodXJsLmhvc3QpO1xyXG5cclxuICAgIGlmICh1cmwucG9ydCAhPT0gbnVsbCkge1xyXG4gICAgICBvdXRwdXQgKz0gXCI6XCIgKyB1cmwucG9ydDtcclxuICAgIH1cclxuICB9IGVsc2UgaWYgKHVybC5ob3N0ID09PSBudWxsICYmIHVybC5zY2hlbWUgPT09IFwiZmlsZVwiKSB7XHJcbiAgICBvdXRwdXQgKz0gXCIvL1wiO1xyXG4gIH1cclxuXHJcbiAgaWYgKHVybC5jYW5ub3RCZUFCYXNlVVJMKSB7XHJcbiAgICBvdXRwdXQgKz0gdXJsLnBhdGhbMF07XHJcbiAgfSBlbHNlIHtcclxuICAgIGZvciAoY29uc3Qgc3RyaW5nIG9mIHVybC5wYXRoKSB7XHJcbiAgICAgIG91dHB1dCArPSBcIi9cIiArIHN0cmluZztcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGlmICh1cmwucXVlcnkgIT09IG51bGwpIHtcclxuICAgIG91dHB1dCArPSBcIj9cIiArIHVybC5xdWVyeTtcclxuICB9XHJcblxyXG4gIGlmICghZXhjbHVkZUZyYWdtZW50ICYmIHVybC5mcmFnbWVudCAhPT0gbnVsbCkge1xyXG4gICAgb3V0cHV0ICs9IFwiI1wiICsgdXJsLmZyYWdtZW50O1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIG91dHB1dDtcclxufVxyXG5cclxuZnVuY3Rpb24gc2VyaWFsaXplT3JpZ2luKHR1cGxlKSB7XHJcbiAgbGV0IHJlc3VsdCA9IHR1cGxlLnNjaGVtZSArIFwiOi8vXCI7XHJcbiAgcmVzdWx0ICs9IHNlcmlhbGl6ZUhvc3QodHVwbGUuaG9zdCk7XHJcblxyXG4gIGlmICh0dXBsZS5wb3J0ICE9PSBudWxsKSB7XHJcbiAgICByZXN1bHQgKz0gXCI6XCIgKyB0dXBsZS5wb3J0O1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIHJlc3VsdDtcclxufVxyXG5cclxubW9kdWxlLmV4cG9ydHMuc2VyaWFsaXplVVJMID0gc2VyaWFsaXplVVJMO1xyXG5cclxubW9kdWxlLmV4cG9ydHMuc2VyaWFsaXplVVJMT3JpZ2luID0gZnVuY3Rpb24gKHVybCkge1xyXG4gIC8vIGh0dHBzOi8vdXJsLnNwZWMud2hhdHdnLm9yZy8jY29uY2VwdC11cmwtb3JpZ2luXHJcbiAgc3dpdGNoICh1cmwuc2NoZW1lKSB7XHJcbiAgICBjYXNlIFwiYmxvYlwiOlxyXG4gICAgICB0cnkge1xyXG4gICAgICAgIHJldHVybiBtb2R1bGUuZXhwb3J0cy5zZXJpYWxpemVVUkxPcmlnaW4obW9kdWxlLmV4cG9ydHMucGFyc2VVUkwodXJsLnBhdGhbMF0pKTtcclxuICAgICAgfSBjYXRjaCAoZSkge1xyXG4gICAgICAgIC8vIHNlcmlhbGl6aW5nIGFuIG9wYXF1ZSBvcmlnaW4gcmV0dXJucyBcIm51bGxcIlxyXG4gICAgICAgIHJldHVybiBcIm51bGxcIjtcclxuICAgICAgfVxyXG4gICAgY2FzZSBcImZ0cFwiOlxyXG4gICAgY2FzZSBcImdvcGhlclwiOlxyXG4gICAgY2FzZSBcImh0dHBcIjpcclxuICAgIGNhc2UgXCJodHRwc1wiOlxyXG4gICAgY2FzZSBcIndzXCI6XHJcbiAgICBjYXNlIFwid3NzXCI6XHJcbiAgICAgIHJldHVybiBzZXJpYWxpemVPcmlnaW4oe1xyXG4gICAgICAgIHNjaGVtZTogdXJsLnNjaGVtZSxcclxuICAgICAgICBob3N0OiB1cmwuaG9zdCxcclxuICAgICAgICBwb3J0OiB1cmwucG9ydFxyXG4gICAgICB9KTtcclxuICAgIGNhc2UgXCJmaWxlXCI6XHJcbiAgICAgIC8vIHNwZWMgc2F5cyBcImV4ZXJjaXNlIHRvIHRoZSByZWFkZXJcIiwgY2hyb21lIHNheXMgXCJmaWxlOi8vXCJcclxuICAgICAgcmV0dXJuIFwiZmlsZTovL1wiO1xyXG4gICAgZGVmYXVsdDpcclxuICAgICAgLy8gc2VyaWFsaXppbmcgYW4gb3BhcXVlIG9yaWdpbiByZXR1cm5zIFwibnVsbFwiXHJcbiAgICAgIHJldHVybiBcIm51bGxcIjtcclxuICB9XHJcbn07XHJcblxyXG5tb2R1bGUuZXhwb3J0cy5iYXNpY1VSTFBhcnNlID0gZnVuY3Rpb24gKGlucHV0LCBvcHRpb25zKSB7XHJcbiAgaWYgKG9wdGlvbnMgPT09IHVuZGVmaW5lZCkge1xyXG4gICAgb3B0aW9ucyA9IHt9O1xyXG4gIH1cclxuXHJcbiAgY29uc3QgdXNtID0gbmV3IFVSTFN0YXRlTWFjaGluZShpbnB1dCwgb3B0aW9ucy5iYXNlVVJMLCBvcHRpb25zLmVuY29kaW5nT3ZlcnJpZGUsIG9wdGlvbnMudXJsLCBvcHRpb25zLnN0YXRlT3ZlcnJpZGUpO1xyXG4gIGlmICh1c20uZmFpbHVyZSkge1xyXG4gICAgcmV0dXJuIFwiZmFpbHVyZVwiO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIHVzbS51cmw7XHJcbn07XHJcblxyXG5tb2R1bGUuZXhwb3J0cy5zZXRUaGVVc2VybmFtZSA9IGZ1bmN0aW9uICh1cmwsIHVzZXJuYW1lKSB7XHJcbiAgdXJsLnVzZXJuYW1lID0gXCJcIjtcclxuICBjb25zdCBkZWNvZGVkID0gcHVueWNvZGUudWNzMi5kZWNvZGUodXNlcm5hbWUpO1xyXG4gIGZvciAobGV0IGkgPSAwOyBpIDwgZGVjb2RlZC5sZW5ndGg7ICsraSkge1xyXG4gICAgdXJsLnVzZXJuYW1lICs9IHBlcmNlbnRFbmNvZGVDaGFyKGRlY29kZWRbaV0sIGlzVXNlcmluZm9QZXJjZW50RW5jb2RlKTtcclxuICB9XHJcbn07XHJcblxyXG5tb2R1bGUuZXhwb3J0cy5zZXRUaGVQYXNzd29yZCA9IGZ1bmN0aW9uICh1cmwsIHBhc3N3b3JkKSB7XHJcbiAgdXJsLnBhc3N3b3JkID0gXCJcIjtcclxuICBjb25zdCBkZWNvZGVkID0gcHVueWNvZGUudWNzMi5kZWNvZGUocGFzc3dvcmQpO1xyXG4gIGZvciAobGV0IGkgPSAwOyBpIDwgZGVjb2RlZC5sZW5ndGg7ICsraSkge1xyXG4gICAgdXJsLnBhc3N3b3JkICs9IHBlcmNlbnRFbmNvZGVDaGFyKGRlY29kZWRbaV0sIGlzVXNlcmluZm9QZXJjZW50RW5jb2RlKTtcclxuICB9XHJcbn07XHJcblxyXG5tb2R1bGUuZXhwb3J0cy5zZXJpYWxpemVIb3N0ID0gc2VyaWFsaXplSG9zdDtcclxuXHJcbm1vZHVsZS5leHBvcnRzLmNhbm5vdEhhdmVBVXNlcm5hbWVQYXNzd29yZFBvcnQgPSBjYW5ub3RIYXZlQVVzZXJuYW1lUGFzc3dvcmRQb3J0O1xyXG5cclxubW9kdWxlLmV4cG9ydHMuc2VyaWFsaXplSW50ZWdlciA9IGZ1bmN0aW9uIChpbnRlZ2VyKSB7XHJcbiAgcmV0dXJuIFN0cmluZyhpbnRlZ2VyKTtcclxufTtcclxuXHJcbm1vZHVsZS5leHBvcnRzLnBhcnNlVVJMID0gZnVuY3Rpb24gKGlucHV0LCBvcHRpb25zKSB7XHJcbiAgaWYgKG9wdGlvbnMgPT09IHVuZGVmaW5lZCkge1xyXG4gICAgb3B0aW9ucyA9IHt9O1xyXG4gIH1cclxuXHJcbiAgLy8gV2UgZG9uJ3QgaGFuZGxlIGJsb2JzLCBzbyB0aGlzIGp1c3QgZGVsZWdhdGVzOlxyXG4gIHJldHVybiBtb2R1bGUuZXhwb3J0cy5iYXNpY1VSTFBhcnNlKGlucHV0LCB7IGJhc2VVUkw6IG9wdGlvbnMuYmFzZVVSTCwgZW5jb2RpbmdPdmVycmlkZTogb3B0aW9ucy5lbmNvZGluZ092ZXJyaWRlIH0pO1xyXG59O1xyXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/whatwg-url/lib/utils.js":
/*!**********************************************!*\
  !*** ./node_modules/whatwg-url/lib/utils.js ***!
  \**********************************************/
/***/ ((module) => {

eval("\n\nmodule.exports.mixin = function mixin(target, source) {\n  const keys = Object.getOwnPropertyNames(source);\n  for (let i = 0; i < keys.length; ++i) {\n    Object.defineProperty(target, keys[i], Object.getOwnPropertyDescriptor(source, keys[i]));\n  }\n};\n\nmodule.exports.wrapperSymbol = Symbol(\"wrapper\");\nmodule.exports.implSymbol = Symbol(\"impl\");\n\nmodule.exports.wrapperForImpl = function (impl) {\n  return impl[module.exports.wrapperSymbol];\n};\n\nmodule.exports.implForWrapper = function (wrapper) {\n  return wrapper[module.exports.implSymbol];\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2hhdHdnLXVybC9saWIvdXRpbHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsb0JBQW9CO0FBQ3BCO0FBQ0Esa0JBQWtCLGlCQUFpQjtBQUNuQztBQUNBO0FBQ0E7O0FBRUEsNEJBQTRCO0FBQzVCLHlCQUF5Qjs7QUFFekIsNkJBQTZCO0FBQzdCO0FBQ0E7O0FBRUEsNkJBQTZCO0FBQzdCO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca2hhbGVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcWUFTTUlOIEFMU0hBTVxceWFzbWluLWFsc2hhbVxcbm9kZV9tb2R1bGVzXFx3aGF0d2ctdXJsXFxsaWJcXHV0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5tb2R1bGUuZXhwb3J0cy5taXhpbiA9IGZ1bmN0aW9uIG1peGluKHRhcmdldCwgc291cmNlKSB7XG4gIGNvbnN0IGtleXMgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlOYW1lcyhzb3VyY2UpO1xuICBmb3IgKGxldCBpID0gMDsgaSA8IGtleXMubGVuZ3RoOyArK2kpIHtcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGFyZ2V0LCBrZXlzW2ldLCBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKHNvdXJjZSwga2V5c1tpXSkpO1xuICB9XG59O1xuXG5tb2R1bGUuZXhwb3J0cy53cmFwcGVyU3ltYm9sID0gU3ltYm9sKFwid3JhcHBlclwiKTtcbm1vZHVsZS5leHBvcnRzLmltcGxTeW1ib2wgPSBTeW1ib2woXCJpbXBsXCIpO1xuXG5tb2R1bGUuZXhwb3J0cy53cmFwcGVyRm9ySW1wbCA9IGZ1bmN0aW9uIChpbXBsKSB7XG4gIHJldHVybiBpbXBsW21vZHVsZS5leHBvcnRzLndyYXBwZXJTeW1ib2xdO1xufTtcblxubW9kdWxlLmV4cG9ydHMuaW1wbEZvcldyYXBwZXIgPSBmdW5jdGlvbiAod3JhcHBlcikge1xuICByZXR1cm4gd3JhcHBlclttb2R1bGUuZXhwb3J0cy5pbXBsU3ltYm9sXTtcbn07XG5cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/whatwg-url/lib/utils.js\n");

/***/ })

};
;