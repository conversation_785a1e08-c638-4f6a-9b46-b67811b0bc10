(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{931:(e,a,s)=>{Promise.resolve().then(s.bind(s,9690))},3294:(e,a,s)=>{"use strict";s.d(a,{n:()=>i});var t=s(5453),r=s(6786);let l=()=>{let e=localStorage.getItem("yasmin-users");if(e)return JSON.parse(e);let a=[{id:"1",email:"<EMAIL>",password:"admin123",full_name:"مدير النظام",role:"admin",is_active:!0}];return localStorage.setItem("yasmin-users",JSON.stringify(a)),a},i=(0,t.v)()((0,r.Zr)((e,a)=>({user:null,isLoading:!1,error:null,signIn:async(a,s)=>{e({isLoading:!0,error:null});try{console.log("\uD83D\uDD10 بدء عملية تسجيل الدخول...",{email:a}),await new Promise(e=>setTimeout(e,1500));let t=l().find(e=>e.email.toLowerCase()===a.toLowerCase()&&e.password===s);if(!t)return console.log("❌ بيانات تسجيل الدخول غير صحيحة"),e({error:"بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور.",isLoading:!1}),!1;{console.log("✅ تم العثور على المستخدم:",t.full_name);let a={id:t.id,email:t.email,full_name:t.full_name,role:t.role,is_active:t.is_active,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),token:"demo-token-".concat(t.id,"-").concat(Date.now())};return localStorage.setItem("yasmin-auth-user",JSON.stringify(a)),console.log("\uD83D\uDCBE تم حفظ المستخدم في localStorage"),e({user:a,isLoading:!1,error:null}),console.log("\uD83C\uDF89 تم تسجيل الدخول بنجاح!"),!0}}catch(a){return console.error("\uD83D\uDCA5 خطأ في تسجيل الدخول:",a),e({error:"حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.",isLoading:!1}),!1}},signOut:async()=>{e({isLoading:!0});try{await new Promise(e=>setTimeout(e,500)),localStorage.removeItem("yasmin-auth-user"),e({user:null,isLoading:!1,error:null})}catch(a){console.error("خطأ في تسجيل الخروج:",a),e({isLoading:!1,error:"خطأ في تسجيل الخروج"})}},setUser:a=>{e({user:a}),a?localStorage.setItem("yasmin-auth-user",JSON.stringify(a)):localStorage.removeItem("yasmin-auth-user")},clearError:()=>{e({error:null})},checkAuth:async()=>{e({isLoading:!0});try{{let a=localStorage.getItem("yasmin-auth-user");if(a){let s=JSON.parse(a);e({user:s,isLoading:!1});return}}e({user:null,isLoading:!1})}catch(a){console.error("خطأ في التحقق من المصادقة:",a),e({user:null,isLoading:!1})}},isAuthenticated:()=>{let e=a();return null!==e.user&&e.user.is_active}}),{name:"yasmin-auth-storage",partialize:e=>({user:e.user})}))},9690:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>b});var t=s(5155),r=s(2115),l=s(6408),i=s(306),n=s(1284),o=s(9074),c=s(5339),d=s(1007),m=s(2919),u=s(8749),x=s(2657),p=s(3294),h=s(5695),g=s(6874),y=s.n(g);function b(){let[e,a]=(0,r.useState)(""),[s,g]=(0,r.useState)(""),[b,f]=(0,r.useState)(!1),[j,v]=(0,r.useState)(null),{signIn:N,isLoading:w,user:S}=(0,p.n)(),k=(0,h.useRouter)();(0,r.useEffect)(()=>{S&&S.is_active&&(console.log("\uD83D\uDC64 المستخدم مسجل دخول بالفعل، توجيه إلى لوحة التحكم..."),k.push("/dashboard"))},[S,k]);let _=async a=>{if(a.preventDefault(),!e||!s)return void v("يرجى ملء جميع الحقول");console.log("\uD83D\uDCDD بدء عملية تسجيل الدخول من النموذج...",{email:e}),v(null);try{let a=await N(e,s);if(console.log("\uD83D\uDCCA نتيجة تسجيل الدخول:",a),a)console.log("\uD83D\uDE80 تسجيل الدخول نجح، جاري التوجيه إلى لوحة التحكم..."),await new Promise(e=>setTimeout(e,100)),k.push("/dashboard"),console.log("✅ تم إرسال طلب التوجيه إلى /dashboard");else{console.log("❌ فشل تسجيل الدخول");let e=p.n.getState();e.error?v(e.error):v("فشل في تسجيل الدخول")}}catch(e){console.error("\uD83D\uDCA5 خطأ في تسجيل الدخول:",e),v("حدث خطأ غير متوقع أثناء تسجيل الدخول")}};return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20",children:(0,t.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,t.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,t.jsxs)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,t.jsx)(i.A,{className:"w-10 h-10 text-white"})}),(0,t.jsx)("h1",{className:"text-3xl font-bold mb-4",children:(0,t.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent",children:"تسجيل الدخول"})}),(0,t.jsx)("p",{className:"text-gray-600",children:"أدخل بياناتك للوصول إلى لوحة التحكم"})]}),(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},className:"mb-8",children:(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3 space-x-reverse",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(n.A,{className:"w-6 h-6 text-blue-600"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-lg font-bold text-blue-800 mb-2",children:"ملاحظة للزبائن"}),(0,t.jsx)("p",{className:"text-blue-700 mb-4 leading-relaxed",children:"هذه الصفحة مخصصة للمدير والعمال فقط. الزبائن يمكنهم حجز موعد دون تسجيل دخول"}),(0,t.jsxs)(y(),{href:"/book-appointment",className:"inline-flex items-center space-x-2 space-x-reverse bg-gradient-to-r from-pink-500 to-purple-500 text-white px-4 py-2 rounded-lg font-medium hover:shadow-lg transition-all duration-300",children:[(0,t.jsx)(o.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"احجزي موعدك الآن"})]})]})]})})}),(0,t.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100 shadow-xl",children:[j&&(0,t.jsxs)(l.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-4 bg-red-50 text-red-800 border border-red-200 rounded-lg flex items-center space-x-3 space-x-reverse",children:[(0,t.jsx)(c.A,{className:"w-5 h-5 text-red-600"}),(0,t.jsx)("span",{children:j})]}),(0,t.jsxs)("form",{onSubmit:_,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"البريد الإلكتروني"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"email",value:e,onChange:e=>a(e.target.value),className:"w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:"أدخل البريد الإلكتروني",required:!0,disabled:w}),(0,t.jsx)(d.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"كلمة المرور"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:b?"text":"password",value:s,onChange:e=>g(e.target.value),className:"w-full px-4 py-3 pl-12 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:"أدخل كلمة المرور",required:!0,disabled:w}),(0,t.jsx)(m.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,t.jsx)("button",{type:"button",onClick:()=>f(!b),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-300",children:b?(0,t.jsx)(u.A,{className:"w-5 h-5"}):(0,t.jsx)(x.A,{className:"w-5 h-5"})})]})]}),(0,t.jsx)("button",{type:"submit",disabled:w,className:"w-full btn-primary py-3 text-lg disabled:opacity-50 disabled:cursor-not-allowed",children:w?(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[(0,t.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,t.jsx)("span",{children:"جاري تسجيل الدخول..."})]}):(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[(0,t.jsx)(i.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"تسجيل الدخول"})]})})]})]}),(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"text-center mt-8",children:(0,t.jsx)("a",{href:"/",className:"text-pink-600 hover:text-pink-700 transition-colors duration-300 text-sm font-medium",children:"العودة إلى الصفحة الرئيسية"})})]})})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[165,874,695,441,684,358],()=>a(931)),_N_E=e.O()}]);