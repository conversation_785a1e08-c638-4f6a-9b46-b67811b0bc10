[{"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\book-appointment\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\add-order\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\appointments\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\orders\\page.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\page.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\reports\\page.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\workers\\page.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\designs\\page.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\designs\\[designId]\\page.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\fabrics\\page-original.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\fabrics\\page.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\fabrics\\[id]\\page.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\layout.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\login\\page.tsx": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\page.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\privacy\\page.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\terms\\page.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\testimonials\\page.tsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\track-order\\page.tsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\About.tsx": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\CompletedWorkUpload.tsx": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\DeleteOrderModal.tsx": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\EditOrderModal.tsx": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\FAQ.tsx": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\Footer.tsx": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\Header.tsx": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\Hero.tsx": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\ImageUpload.tsx": "29", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\NumericInput.tsx": "30", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\OrderModal.tsx": "31", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\ProtectedRoute.tsx": "32", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\ReadyDesigns.tsx": "33", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\ScrollToTop.tsx": "34", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\Services.tsx": "35", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\Testimonials.tsx": "36", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\VoiceNotes.tsx": "37", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\data\\designs.ts": "38", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\hooks\\useTranslation.ts": "39", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\lib\\appointments.ts": "40", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\lib\\supabase.ts": "41", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\store\\authStore.ts": "42", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\store\\dataStore.ts": "43", "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\utils\\inputValidation.ts": "44"}, {"size": 11642, "mtime": 1751044187558, "results": "45", "hashOfConfig": "46"}, {"size": 19380, "mtime": 1751749099246, "results": "47", "hashOfConfig": "46"}, {"size": 25331, "mtime": 1751749164957, "results": "48", "hashOfConfig": "46"}, {"size": 16490, "mtime": 1751677034169, "results": "49", "hashOfConfig": "46"}, {"size": 26682, "mtime": 1751749236235, "results": "50", "hashOfConfig": "46"}, {"size": 32434, "mtime": 1751750100502, "results": "51", "hashOfConfig": "46"}, {"size": 15841, "mtime": 1751750210101, "results": "52", "hashOfConfig": "46"}, {"size": 26501, "mtime": 1751750289874, "results": "53", "hashOfConfig": "46"}, {"size": 14924, "mtime": 1751572302566, "results": "54", "hashOfConfig": "46"}, {"size": 13748, "mtime": 1751389919338, "results": "55", "hashOfConfig": "46"}, {"size": 13471, "mtime": 1751377300083, "results": "56", "hashOfConfig": "46"}, {"size": 8884, "mtime": 1751748950846, "results": "57", "hashOfConfig": "46"}, {"size": 18124, "mtime": 1751377179900, "results": "58", "hashOfConfig": "46"}, {"size": 1479, "mtime": 1750532501209, "results": "59", "hashOfConfig": "46"}, {"size": 9834, "mtime": 1751748983110, "results": "60", "hashOfConfig": "46"}, {"size": 620, "mtime": 1751380855235, "results": "61", "hashOfConfig": "46"}, {"size": 9781, "mtime": 1750684783880, "results": "62", "hashOfConfig": "46"}, {"size": 10973, "mtime": 1750684753378, "results": "63", "hashOfConfig": "46"}, {"size": 11072, "mtime": 1751646578640, "results": "64", "hashOfConfig": "46"}, {"size": 22582, "mtime": 1751750452631, "results": "65", "hashOfConfig": "46"}, {"size": 10986, "mtime": 1751381480008, "results": "66", "hashOfConfig": "46"}, {"size": 6284, "mtime": 1750623890208, "results": "67", "hashOfConfig": "46"}, {"size": 8294, "mtime": 1751725404323, "results": "68", "hashOfConfig": "46"}, {"size": 24239, "mtime": 1751730682029, "results": "69", "hashOfConfig": "46"}, {"size": 9324, "mtime": 1751646167878, "results": "70", "hashOfConfig": "46"}, {"size": 12955, "mtime": 1751710978015, "results": "71", "hashOfConfig": "46"}, {"size": 5804, "mtime": 1751710616207, "results": "72", "hashOfConfig": "46"}, {"size": 12439, "mtime": 1751710472834, "results": "73", "hashOfConfig": "46"}, {"size": 6240, "mtime": 1751712282294, "results": "74", "hashOfConfig": "46"}, {"size": 2284, "mtime": 1751729877059, "results": "75", "hashOfConfig": "46"}, {"size": 28029, "mtime": 1751729327384, "results": "76", "hashOfConfig": "46"}, {"size": 5463, "mtime": 1750536486883, "results": "77", "hashOfConfig": "46"}, {"size": 13871, "mtime": 1751572258188, "results": "78", "hashOfConfig": "46"}, {"size": 4454, "mtime": 1751572133299, "results": "79", "hashOfConfig": "46"}, {"size": 11069, "mtime": 1751379262675, "results": "80", "hashOfConfig": "46"}, {"size": 9184, "mtime": 1750532771888, "results": "81", "hashOfConfig": "46"}, {"size": 9794, "mtime": 1751748721672, "results": "82", "hashOfConfig": "46"}, {"size": 11862, "mtime": 1751572523346, "results": "83", "hashOfConfig": "46"}, {"size": 36225, "mtime": 1751795229857, "results": "84", "hashOfConfig": "46"}, {"size": 7075, "mtime": 1750535581891, "results": "85", "hashOfConfig": "46"}, {"size": 2961, "mtime": 1750536636887, "results": "86", "hashOfConfig": "46"}, {"size": 5686, "mtime": 1750611578670, "results": "87", "hashOfConfig": "46"}, {"size": 11682, "mtime": 1751729545636, "results": "88", "hashOfConfig": "46"}, {"size": 4548, "mtime": 1751729847747, "results": "89", "hashOfConfig": "46"}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1d3jevv", {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\about\\page.tsx", ["222", "223"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\book-appointment\\page.tsx", ["224", "225"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\add-order\\page.tsx", ["226", "227", "228", "229", "230", "231", "232", "233"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\appointments\\page.tsx", ["234", "235", "236"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\orders\\page.tsx", ["237", "238", "239", "240", "241", "242", "243", "244", "245", "246", "247"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\page.tsx", ["248", "249", "250", "251"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\reports\\page.tsx", ["252", "253"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\workers\\page.tsx", ["254", "255", "256", "257", "258", "259", "260", "261"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\designs\\page.tsx", ["262"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\designs\\[designId]\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\fabrics\\page-original.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\fabrics\\page.tsx", ["263", "264", "265", "266", "267", "268"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\fabrics\\[id]\\page.tsx", ["269", "270"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\login\\page.tsx", ["271", "272", "273", "274", "275", "276", "277", "278"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\privacy\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\terms\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\testimonials\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\track-order\\page.tsx", ["279", "280", "281", "282"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\About.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\CompletedWorkUpload.tsx", ["283"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\DeleteOrderModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\EditOrderModal.tsx", ["284", "285", "286", "287", "288", "289", "290"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\FAQ.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\Footer.tsx", ["291"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\ImageUpload.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\NumericInput.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\OrderModal.tsx", ["292", "293", "294"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\ReadyDesigns.tsx", ["295", "296", "297"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\ScrollToTop.tsx", ["298"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\Services.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\Testimonials.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\components\\VoiceNotes.tsx", ["299", "300", "301"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\data\\designs.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\hooks\\useTranslation.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\lib\\appointments.ts", ["302", "303"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\lib\\supabase.ts", ["304"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\store\\authStore.ts", ["305", "306", "307", "308", "309", "310", "311", "312"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\store\\dataStore.ts", ["313", "314", "315", "316", "317", "318", "319", "320", "321", "322", "323", "324", "325"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\utils\\inputValidation.ts", [], [], {"ruleId": "326", "severity": 1, "message": "327", "line": 4, "column": 38, "nodeType": null, "messageId": "328", "endLine": 4, "endColumn": 42}, {"ruleId": "326", "severity": 1, "message": "329", "line": 4, "column": 44, "nodeType": null, "messageId": "328", "endLine": 4, "endColumn": 52}, {"ruleId": "326", "severity": 1, "message": "330", "line": 124, "column": 9, "nodeType": null, "messageId": "328", "endLine": 124, "endColumn": 29}, {"ruleId": "331", "severity": 1, "message": "332", "line": 171, "column": 7, "nodeType": "333", "messageId": "334", "endLine": 171, "endColumn": 20, "suggestions": "335"}, {"ruleId": "326", "severity": 1, "message": "336", "line": 3, "column": 20, "nodeType": null, "messageId": "328", "endLine": 3, "endColumn": 29}, {"ruleId": "326", "severity": 1, "message": "337", "line": 16, "column": 3, "nodeType": null, "messageId": "328", "endLine": 16, "endColumn": 9}, {"ruleId": "326", "severity": 1, "message": "338", "line": 19, "column": 3, "nodeType": null, "messageId": "328", "endLine": 19, "endColumn": 11}, {"ruleId": "326", "severity": 1, "message": "339", "line": 20, "column": 3, "nodeType": null, "messageId": "328", "endLine": 20, "endColumn": 11}, {"ruleId": "326", "severity": 1, "message": "340", "line": 29, "column": 11, "nodeType": null, "messageId": "328", "endLine": 29, "endColumn": 15}, {"ruleId": "326", "severity": 1, "message": "341", "line": 31, "column": 14, "nodeType": null, "messageId": "328", "endLine": 31, "endColumn": 22}, {"ruleId": "342", "severity": 1, "message": "343", "line": 80, "column": 52, "nodeType": "344", "messageId": "345", "endLine": 80, "endColumn": 55, "suggestions": "346"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 166, "column": 7, "nodeType": "333", "messageId": "334", "endLine": 166, "endColumn": 20, "suggestions": "347"}, {"ruleId": "326", "severity": 1, "message": "348", "line": 17, "column": 3, "nodeType": null, "messageId": "328", "endLine": 17, "endColumn": 7}, {"ruleId": "326", "severity": 1, "message": "349", "line": 20, "column": 3, "nodeType": null, "messageId": "328", "endLine": 20, "endColumn": 14}, {"ruleId": "326", "severity": 1, "message": "350", "line": 26, "column": 44, "nodeType": null, "messageId": "328", "endLine": 26, "endColumn": 61}, {"ruleId": "326", "severity": 1, "message": "351", "line": 29, "column": 3, "nodeType": null, "messageId": "328", "endLine": 29, "endColumn": 12}, {"ruleId": "326", "severity": 1, "message": "352", "line": 36, "column": 14, "nodeType": null, "messageId": "328", "endLine": 36, "endColumn": 22}, {"ruleId": "326", "severity": 1, "message": "353", "line": 36, "column": 24, "nodeType": null, "messageId": "328", "endLine": 36, "endColumn": 38}, {"ruleId": "326", "severity": 1, "message": "341", "line": 36, "column": 40, "nodeType": null, "messageId": "328", "endLine": 36, "endColumn": 48}, {"ruleId": "342", "severity": 1, "message": "343", "line": 49, "column": 54, "nodeType": "344", "messageId": "345", "endLine": 49, "endColumn": 57, "suggestions": "354"}, {"ruleId": "342", "severity": 1, "message": "343", "line": 58, "column": 54, "nodeType": "344", "messageId": "345", "endLine": 58, "endColumn": 57, "suggestions": "355"}, {"ruleId": "342", "severity": 1, "message": "343", "line": 80, "column": 35, "nodeType": "344", "messageId": "345", "endLine": 80, "endColumn": 38, "suggestions": "356"}, {"ruleId": "342", "severity": 1, "message": "343", "line": 86, "column": 35, "nodeType": "344", "messageId": "345", "endLine": 86, "endColumn": 38, "suggestions": "357"}, {"ruleId": "342", "severity": 1, "message": "343", "line": 92, "column": 54, "nodeType": "344", "messageId": "345", "endLine": 92, "endColumn": 57, "suggestions": "358"}, {"ruleId": "342", "severity": 1, "message": "343", "line": 108, "column": 37, "nodeType": "344", "messageId": "345", "endLine": 108, "endColumn": 40, "suggestions": "359"}, {"ruleId": "342", "severity": 1, "message": "343", "line": 148, "column": 43, "nodeType": "344", "messageId": "345", "endLine": 148, "endColumn": 46, "suggestions": "360"}, {"ruleId": "326", "severity": 1, "message": "336", "line": 3, "column": 10, "nodeType": null, "messageId": "328", "endLine": 3, "endColumn": 19}, {"ruleId": "326", "severity": 1, "message": "361", "line": 3, "column": 21, "nodeType": null, "messageId": "328", "endLine": 3, "endColumn": 29}, {"ruleId": "326", "severity": 1, "message": "351", "line": 23, "column": 3, "nodeType": null, "messageId": "328", "endLine": 23, "endColumn": 12}, {"ruleId": "326", "severity": 1, "message": "341", "line": 29, "column": 40, "nodeType": null, "messageId": "328", "endLine": 29, "endColumn": 48}, {"ruleId": "326", "severity": 1, "message": "339", "line": 18, "column": 3, "nodeType": null, "messageId": "328", "endLine": 18, "endColumn": 11}, {"ruleId": "326", "severity": 1, "message": "362", "line": 25, "column": 28, "nodeType": null, "messageId": "328", "endLine": 25, "endColumn": 40}, {"ruleId": "326", "severity": 1, "message": "327", "line": 17, "column": 3, "nodeType": null, "messageId": "328", "endLine": 17, "endColumn": 7}, {"ruleId": "326", "severity": 1, "message": "363", "line": 18, "column": 3, "nodeType": null, "messageId": "328", "endLine": 18, "endColumn": 10}, {"ruleId": "326", "severity": 1, "message": "341", "line": 29, "column": 14, "nodeType": null, "messageId": "328", "endLine": 29, "endColumn": 22}, {"ruleId": "342", "severity": 1, "message": "343", "line": 48, "column": 54, "nodeType": "344", "messageId": "345", "endLine": 48, "endColumn": 57, "suggestions": "364"}, {"ruleId": "326", "severity": 1, "message": "365", "line": 87, "column": 14, "nodeType": null, "messageId": "328", "endLine": 87, "endColumn": 19}, {"ruleId": "342", "severity": 1, "message": "343", "line": 95, "column": 37, "nodeType": "344", "messageId": "345", "endLine": 95, "endColumn": 40, "suggestions": "366"}, {"ruleId": "342", "severity": 1, "message": "343", "line": 118, "column": 22, "nodeType": "344", "messageId": "345", "endLine": 118, "endColumn": 25, "suggestions": "367"}, {"ruleId": "326", "severity": 1, "message": "365", "line": 137, "column": 14, "nodeType": null, "messageId": "328", "endLine": 137, "endColumn": 19}, {"ruleId": "368", "severity": 1, "message": "369", "line": 115, "column": 6, "nodeType": "370", "endLine": 115, "endColumn": 41, "suggestions": "371"}, {"ruleId": "326", "severity": 1, "message": "372", "line": 10, "column": 11, "nodeType": null, "messageId": "328", "endLine": 10, "endColumn": 12}, {"ruleId": "326", "severity": 1, "message": "373", "line": 13, "column": 28, "nodeType": null, "messageId": "328", "endLine": 13, "endColumn": 47}, {"ruleId": "326", "severity": 1, "message": "374", "line": 14, "column": 10, "nodeType": null, "messageId": "328", "endLine": 14, "endColumn": 19}, {"ruleId": "326", "severity": 1, "message": "375", "line": 158, "column": 9, "nodeType": null, "messageId": "328", "endLine": 158, "endColumn": 19}, {"ruleId": "326", "severity": 1, "message": "376", "line": 200, "column": 9, "nodeType": null, "messageId": "328", "endLine": 200, "endColumn": 23}, {"ruleId": "326", "severity": 1, "message": "377", "line": 208, "column": 9, "nodeType": null, "messageId": "328", "endLine": 208, "endColumn": 24}, {"ruleId": "326", "severity": 1, "message": "378", "line": 7, "column": 37, "nodeType": null, "messageId": "328", "endLine": 7, "endColumn": 42}, {"ruleId": "326", "severity": 1, "message": "339", "line": 7, "column": 44, "nodeType": null, "messageId": "328", "endLine": 7, "endColumn": 52}, {"ruleId": "326", "severity": 1, "message": "379", "line": 5, "column": 55, "nodeType": null, "messageId": "328", "endLine": 5, "endColumn": 66}, {"ruleId": "331", "severity": 1, "message": "332", "line": 22, "column": 7, "nodeType": "333", "messageId": "334", "endLine": 22, "endColumn": 18, "suggestions": "380"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 35, "column": 5, "nodeType": "333", "messageId": "334", "endLine": 35, "endColumn": 16, "suggestions": "381"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 41, "column": 7, "nodeType": "333", "messageId": "334", "endLine": 41, "endColumn": 18, "suggestions": "382"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 44, "column": 9, "nodeType": "333", "messageId": "334", "endLine": 44, "endColumn": 20, "suggestions": "383"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 52, "column": 9, "nodeType": "333", "messageId": "334", "endLine": 52, "endColumn": 20, "suggestions": "384"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 54, "column": 9, "nodeType": "333", "messageId": "334", "endLine": 54, "endColumn": 20, "suggestions": "385"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 64, "column": 7, "nodeType": "333", "messageId": "334", "endLine": 64, "endColumn": 20, "suggestions": "386"}, {"ruleId": "342", "severity": 1, "message": "343", "line": 13, "column": 46, "nodeType": "344", "messageId": "345", "endLine": 13, "endColumn": 49, "suggestions": "387"}, {"ruleId": "326", "severity": 1, "message": "388", "line": 17, "column": 19, "nodeType": null, "messageId": "328", "endLine": 17, "endColumn": 26}, {"ruleId": "342", "severity": 1, "message": "343", "line": 36, "column": 23, "nodeType": "344", "messageId": "345", "endLine": 36, "endColumn": 26, "suggestions": "389"}, {"ruleId": "326", "severity": 1, "message": "365", "line": 74, "column": 14, "nodeType": null, "messageId": "328", "endLine": 74, "endColumn": 19}, {"ruleId": "326", "severity": 1, "message": "337", "line": 5, "column": 10, "nodeType": null, "messageId": "328", "endLine": 5, "endColumn": 16}, {"ruleId": "326", "severity": 1, "message": "390", "line": 9, "column": 3, "nodeType": null, "messageId": "328", "endLine": 9, "endColumn": 8}, {"ruleId": "326", "severity": 1, "message": "391", "line": 12, "column": 3, "nodeType": null, "messageId": "328", "endLine": 12, "endColumn": 13}, {"ruleId": "326", "severity": 1, "message": "339", "line": 15, "column": 3, "nodeType": null, "messageId": "328", "endLine": 15, "endColumn": 11}, {"ruleId": "342", "severity": 1, "message": "343", "line": 59, "column": 52, "nodeType": "344", "messageId": "345", "endLine": 59, "endColumn": 55, "suggestions": "392"}, {"ruleId": "342", "severity": 1, "message": "343", "line": 95, "column": 49, "nodeType": "344", "messageId": "345", "endLine": 95, "endColumn": 52, "suggestions": "393"}, {"ruleId": "342", "severity": 1, "message": "343", "line": 98, "column": 16, "nodeType": "344", "messageId": "345", "endLine": 98, "endColumn": 19, "suggestions": "394"}, {"ruleId": "326", "severity": 1, "message": "365", "line": 114, "column": 14, "nodeType": null, "messageId": "328", "endLine": 114, "endColumn": 19}, {"ruleId": "326", "severity": 1, "message": "327", "line": 15, "column": 3, "nodeType": null, "messageId": "328", "endLine": 15, "endColumn": 7}, {"ruleId": "326", "severity": 1, "message": "361", "line": 3, "column": 10, "nodeType": null, "messageId": "328", "endLine": 3, "endColumn": 18}, {"ruleId": "326", "severity": 1, "message": "390", "line": 8, "column": 3, "nodeType": null, "messageId": "328", "endLine": 8, "endColumn": 8}, {"ruleId": "326", "severity": 1, "message": "391", "line": 12, "column": 3, "nodeType": null, "messageId": "328", "endLine": 12, "endColumn": 13}, {"ruleId": "326", "severity": 1, "message": "327", "line": 6, "column": 22, "nodeType": null, "messageId": "328", "endLine": 6, "endColumn": 26}, {"ruleId": "326", "severity": 1, "message": "395", "line": 6, "column": 28, "nodeType": null, "messageId": "328", "endLine": 6, "endColumn": 35}, {"ruleId": "368", "severity": 1, "message": "369", "line": 138, "column": 6, "nodeType": "370", "endLine": 138, "endColumn": 41, "suggestions": "396"}, {"ruleId": "326", "severity": 1, "message": "397", "line": 5, "column": 10, "nodeType": null, "messageId": "328", "endLine": 5, "endColumn": 19}, {"ruleId": "326", "severity": 1, "message": "398", "line": 26, "column": 10, "nodeType": null, "messageId": "328", "endLine": 26, "endColumn": 26}, {"ruleId": "331", "severity": 1, "message": "332", "line": 95, "column": 7, "nodeType": "333", "messageId": "334", "endLine": 95, "endColumn": 20, "suggestions": "399"}, {"ruleId": "368", "severity": 1, "message": "400", "line": 185, "column": 38, "nodeType": "401", "endLine": 185, "endColumn": 45}, {"ruleId": "326", "severity": 1, "message": "365", "line": 141, "column": 14, "nodeType": null, "messageId": "328", "endLine": 141, "endColumn": 19}, {"ruleId": "326", "severity": 1, "message": "365", "line": 201, "column": 14, "nodeType": null, "messageId": "328", "endLine": 201, "endColumn": 19}, {"ruleId": "342", "severity": 1, "message": "343", "line": 91, "column": 18, "nodeType": "344", "messageId": "345", "endLine": 91, "endColumn": 21, "suggestions": "402"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 66, "column": 11, "nodeType": "333", "messageId": "334", "endLine": 66, "endColumn": 22, "suggestions": "403"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 78, "column": 13, "nodeType": "333", "messageId": "334", "endLine": 78, "endColumn": 24, "suggestions": "404"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 94, "column": 15, "nodeType": "333", "messageId": "334", "endLine": 94, "endColumn": 26, "suggestions": "405"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 99, "column": 13, "nodeType": "333", "messageId": "334", "endLine": 99, "endColumn": 24, "suggestions": "406"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 103, "column": 13, "nodeType": "333", "messageId": "334", "endLine": 103, "endColumn": 24, "suggestions": "407"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 111, "column": 11, "nodeType": "333", "messageId": "334", "endLine": 111, "endColumn": 24, "suggestions": "408"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 131, "column": 11, "nodeType": "333", "messageId": "334", "endLine": 131, "endColumn": 24, "suggestions": "409"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 169, "column": 11, "nodeType": "333", "messageId": "334", "endLine": 169, "endColumn": 24, "suggestions": "410"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 159, "column": 9, "nodeType": "333", "messageId": "334", "endLine": 159, "endColumn": 20, "suggestions": "411"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 172, "column": 9, "nodeType": "333", "messageId": "334", "endLine": 172, "endColumn": 20, "suggestions": "412"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 181, "column": 9, "nodeType": "333", "messageId": "334", "endLine": 181, "endColumn": 20, "suggestions": "413"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 204, "column": 9, "nodeType": "333", "messageId": "334", "endLine": 204, "endColumn": 20, "suggestions": "414"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 217, "column": 9, "nodeType": "333", "messageId": "334", "endLine": 217, "endColumn": 20, "suggestions": "415"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 226, "column": 9, "nodeType": "333", "messageId": "334", "endLine": 226, "endColumn": 20, "suggestions": "416"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 264, "column": 9, "nodeType": "333", "messageId": "334", "endLine": 264, "endColumn": 20, "suggestions": "417"}, {"ruleId": "342", "severity": 1, "message": "343", "line": 281, "column": 54, "nodeType": "344", "messageId": "345", "endLine": 281, "endColumn": 57, "suggestions": "418"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 291, "column": 9, "nodeType": "333", "messageId": "334", "endLine": 291, "endColumn": 20, "suggestions": "419"}, {"ruleId": "342", "severity": 1, "message": "343", "line": 303, "column": 53, "nodeType": "344", "messageId": "345", "endLine": 303, "endColumn": 56, "suggestions": "420"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 307, "column": 9, "nodeType": "333", "messageId": "334", "endLine": 307, "endColumn": 20, "suggestions": "421"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 326, "column": 9, "nodeType": "333", "messageId": "334", "endLine": 326, "endColumn": 20, "suggestions": "422"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 344, "column": 9, "nodeType": "333", "messageId": "334", "endLine": 344, "endColumn": 20, "suggestions": "423"}, "@typescript-eslint/no-unused-vars", "'Star' is defined but never used.", "unusedVar", "'Scissors' is defined but never used.", "'formatTimeForDisplay' is assigned a value but never used.", "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["424"], "'useEffect' is defined but never used.", "'Upload' is defined but never used.", "'FileText' is defined but never used.", "'Calendar' is defined but never used.", "'user' is assigned a value but never used.", "'isArabic' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["425", "426"], ["427"], "'User' is defined but never used.", "'AlertCircle' is defined but never used.", "'deleteAppointment' is assigned a value but never used.", "'Languages' is defined but never used.", "'language' is assigned a value but never used.", "'changeLanguage' is assigned a value but never used.", ["428", "429"], ["430", "431"], ["432", "433"], ["434", "435"], ["436", "437"], ["438", "439"], ["440", "441"], "'useState' is defined but never used.", "'appointments' is assigned a value but never used.", "'Package' is defined but never used.", ["442", "443"], "'error' is defined but never used.", ["444", "445"], ["446", "447"], "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'nextImage' and 'prevImage'. Either include them or remove the dependency array.", "ArrayExpression", ["448"], "'t' is assigned a value but never used.", "'setSelectedCategory' is assigned a value but never used.", "'favorites' is assigned a value but never used.", "'categories' is assigned a value but never used.", "'toggleFavorite' is assigned a value but never used.", "'filteredFabrics' is assigned a value but never used.", "'Heart' is defined but never used.", "'CheckCircle' is defined but never used.", ["449"], ["450"], ["451"], ["452"], ["453"], ["454"], ["455"], ["456", "457"], "'workers' is assigned a value but never used.", ["458", "459"], "'Phone' is defined but never used.", "'DollarSign' is defined but never used.", ["460", "461"], ["462", "463"], ["464", "465"], "'Palette' is defined but never used.", ["466"], "'ChevronUp' is defined but never used.", "'currentAudioBlob' is assigned a value but never used.", ["467"], "The ref value 'audioRefsRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'audioRefsRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "Identifier", ["468", "469"], ["470"], ["471"], ["472"], ["473"], ["474"], ["475"], ["476"], ["477"], ["478"], ["479"], ["480"], ["481"], ["482"], ["483"], ["484"], ["485", "486"], ["487"], ["488", "489"], ["490"], ["491"], ["492"], {"fix": "493", "messageId": "494", "data": "495", "desc": "496"}, {"messageId": "497", "fix": "498", "desc": "499"}, {"messageId": "500", "fix": "501", "desc": "502"}, {"fix": "503", "messageId": "494", "data": "504", "desc": "496"}, {"messageId": "497", "fix": "505", "desc": "499"}, {"messageId": "500", "fix": "506", "desc": "502"}, {"messageId": "497", "fix": "507", "desc": "499"}, {"messageId": "500", "fix": "508", "desc": "502"}, {"messageId": "497", "fix": "509", "desc": "499"}, {"messageId": "500", "fix": "510", "desc": "502"}, {"messageId": "497", "fix": "511", "desc": "499"}, {"messageId": "500", "fix": "512", "desc": "502"}, {"messageId": "497", "fix": "513", "desc": "499"}, {"messageId": "500", "fix": "514", "desc": "502"}, {"messageId": "497", "fix": "515", "desc": "499"}, {"messageId": "500", "fix": "516", "desc": "502"}, {"messageId": "497", "fix": "517", "desc": "499"}, {"messageId": "500", "fix": "518", "desc": "502"}, {"messageId": "497", "fix": "519", "desc": "499"}, {"messageId": "500", "fix": "520", "desc": "502"}, {"messageId": "497", "fix": "521", "desc": "499"}, {"messageId": "500", "fix": "522", "desc": "502"}, {"messageId": "497", "fix": "523", "desc": "499"}, {"messageId": "500", "fix": "524", "desc": "502"}, {"desc": "525", "fix": "526"}, {"fix": "527", "messageId": "494", "data": "528", "desc": "529"}, {"fix": "530", "messageId": "494", "data": "531", "desc": "529"}, {"fix": "532", "messageId": "494", "data": "533", "desc": "529"}, {"fix": "534", "messageId": "494", "data": "535", "desc": "529"}, {"fix": "536", "messageId": "494", "data": "537", "desc": "529"}, {"fix": "538", "messageId": "494", "data": "539", "desc": "529"}, {"fix": "540", "messageId": "494", "data": "541", "desc": "496"}, {"messageId": "497", "fix": "542", "desc": "499"}, {"messageId": "500", "fix": "543", "desc": "502"}, {"messageId": "497", "fix": "544", "desc": "499"}, {"messageId": "500", "fix": "545", "desc": "502"}, {"messageId": "497", "fix": "546", "desc": "499"}, {"messageId": "500", "fix": "547", "desc": "502"}, {"messageId": "497", "fix": "548", "desc": "499"}, {"messageId": "500", "fix": "549", "desc": "502"}, {"messageId": "497", "fix": "550", "desc": "499"}, {"messageId": "500", "fix": "551", "desc": "502"}, {"desc": "525", "fix": "552"}, {"fix": "553", "messageId": "494", "data": "554", "desc": "496"}, {"messageId": "497", "fix": "555", "desc": "499"}, {"messageId": "500", "fix": "556", "desc": "502"}, {"fix": "557", "messageId": "494", "data": "558", "desc": "529"}, {"fix": "559", "messageId": "494", "data": "560", "desc": "529"}, {"fix": "561", "messageId": "494", "data": "562", "desc": "529"}, {"fix": "563", "messageId": "494", "data": "564", "desc": "529"}, {"fix": "565", "messageId": "494", "data": "566", "desc": "529"}, {"fix": "567", "messageId": "494", "data": "568", "desc": "496"}, {"fix": "569", "messageId": "494", "data": "570", "desc": "496"}, {"fix": "571", "messageId": "494", "data": "572", "desc": "496"}, {"fix": "573", "messageId": "494", "data": "574", "desc": "529"}, {"fix": "575", "messageId": "494", "data": "576", "desc": "529"}, {"fix": "577", "messageId": "494", "data": "578", "desc": "529"}, {"fix": "579", "messageId": "494", "data": "580", "desc": "529"}, {"fix": "581", "messageId": "494", "data": "582", "desc": "529"}, {"fix": "583", "messageId": "494", "data": "584", "desc": "529"}, {"fix": "585", "messageId": "494", "data": "586", "desc": "529"}, {"messageId": "497", "fix": "587", "desc": "499"}, {"messageId": "500", "fix": "588", "desc": "502"}, {"fix": "589", "messageId": "494", "data": "590", "desc": "529"}, {"messageId": "497", "fix": "591", "desc": "499"}, {"messageId": "500", "fix": "592", "desc": "502"}, {"fix": "593", "messageId": "494", "data": "594", "desc": "529"}, {"fix": "595", "messageId": "494", "data": "596", "desc": "529"}, {"fix": "597", "messageId": "494", "data": "598", "desc": "529"}, {"range": "599", "text": "600"}, "removeConsole", {"propertyName": "601"}, "Remove the console.error().", "suggestUnknown", {"range": "602", "text": "603"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "604", "text": "605"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "606", "text": "600"}, {"propertyName": "601"}, {"range": "607", "text": "603"}, {"range": "608", "text": "605"}, {"range": "609", "text": "603"}, {"range": "610", "text": "605"}, {"range": "611", "text": "603"}, {"range": "612", "text": "605"}, {"range": "613", "text": "603"}, {"range": "614", "text": "605"}, {"range": "615", "text": "603"}, {"range": "616", "text": "605"}, {"range": "617", "text": "603"}, {"range": "618", "text": "605"}, {"range": "619", "text": "603"}, {"range": "620", "text": "605"}, {"range": "621", "text": "603"}, {"range": "622", "text": "605"}, {"range": "623", "text": "603"}, {"range": "624", "text": "605"}, {"range": "625", "text": "603"}, {"range": "626", "text": "605"}, "Update the dependencies array to be: [isGalleryOpen, nextImage, prevImage, selectedImageIndex]", {"range": "627", "text": "628"}, {"range": "629", "text": "600"}, {"propertyName": "630"}, "Remove the console.log().", {"range": "631", "text": "600"}, {"propertyName": "630"}, {"range": "632", "text": "600"}, {"propertyName": "630"}, {"range": "633", "text": "600"}, {"propertyName": "630"}, {"range": "634", "text": "600"}, {"propertyName": "630"}, {"range": "635", "text": "600"}, {"propertyName": "630"}, {"range": "636", "text": "600"}, {"propertyName": "601"}, {"range": "637", "text": "603"}, {"range": "638", "text": "605"}, {"range": "639", "text": "603"}, {"range": "640", "text": "605"}, {"range": "641", "text": "603"}, {"range": "642", "text": "605"}, {"range": "643", "text": "603"}, {"range": "644", "text": "605"}, {"range": "645", "text": "603"}, {"range": "646", "text": "605"}, {"range": "647", "text": "628"}, {"range": "648", "text": "600"}, {"propertyName": "601"}, {"range": "649", "text": "603"}, {"range": "650", "text": "605"}, {"range": "651", "text": "600"}, {"propertyName": "630"}, {"range": "652", "text": "600"}, {"propertyName": "630"}, {"range": "653", "text": "600"}, {"propertyName": "630"}, {"range": "654", "text": "600"}, {"propertyName": "630"}, {"range": "655", "text": "600"}, {"propertyName": "630"}, {"range": "656", "text": "600"}, {"propertyName": "601"}, {"range": "657", "text": "600"}, {"propertyName": "601"}, {"range": "658", "text": "600"}, {"propertyName": "601"}, {"range": "659", "text": "600"}, {"propertyName": "630"}, {"range": "660", "text": "600"}, {"propertyName": "630"}, {"range": "661", "text": "600"}, {"propertyName": "630"}, {"range": "662", "text": "600"}, {"propertyName": "630"}, {"range": "663", "text": "600"}, {"propertyName": "630"}, {"range": "664", "text": "600"}, {"propertyName": "630"}, {"range": "665", "text": "600"}, {"propertyName": "630"}, {"range": "666", "text": "603"}, {"range": "667", "text": "605"}, {"range": "668", "text": "600"}, {"propertyName": "630"}, {"range": "669", "text": "603"}, {"range": "670", "text": "605"}, {"range": "671", "text": "600"}, {"propertyName": "630"}, {"range": "672", "text": "600"}, {"propertyName": "630"}, {"range": "673", "text": "600"}, {"propertyName": "630"}, [5202, 5244], "", "error", [1923, 1926], "unknown", [1923, 1926], "never", [5250, 5293], [1430, 1433], [1430, 1433], [1903, 1906], [1903, 1906], [2951, 2954], [2951, 2954], [3078, 3081], [3078, 3081], [3220, 3223], [3220, 3223], [3587, 3590], [3587, 3590], [4513, 4516], [4513, 4516], [1163, 1166], [1163, 1166], [2491, 2494], [2491, 2494], [3080, 3083], [3080, 3083], [3226, 3261], "[isGalleryOpen, nextImage, prevImage, selectedImageIndex]", [764, 833], "log", [1067, 1132], [1222, 1268], [1299, 1366], [1568, 1620], [1644, 1677], [1948, 1995], [553, 556], [553, 556], [1150, 1153], [1150, 1153], [1646, 1649], [1646, 1649], [2598, 2601], [2598, 2601], [2713, 2716], [2713, 2716], [3816, 3851], [3011, 3054], [2023, 2026], [2023, 2026], [1479, 1533], [1919, 1980], [2581, 2630], [2748, 2788], [2845, 2891], [3131, 3178], [3748, 3792], [4812, 4862], [4114, 4163], [4508, 4545], [4749, 4784], [5327, 5369], [5672, 5708], [5882, 5916], [6947, 6991], [7614, 7617], [7614, 7617], [8030, 8067], [8448, 8451], [8448, 8451], [8569, 8604], [9122, 9170], [9717, 9758]]