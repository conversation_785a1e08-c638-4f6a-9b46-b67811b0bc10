(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[161],{3717:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4535:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>w});var a=r(5155),t=r(2115),l=r(6408),i=r(5695),n=r(6874),d=r.n(n),c=r(3294),o=r(1364),x=r(9137),m=r(2138),p=r(4616),u=r(7924),h=r(646),b=r(4861),g=r(7580),y=r(1007),f=r(8883),v=r(9420),j=r(3717),N=r(2525);function w(){let{user:e}=(0,c.n)(),{workers:s,addWorker:r,updateWorker:n,deleteWorker:w,orders:k}=(0,o.D)(),{t:_,isArabic:A}=(0,x.B)(),C=(0,i.useRouter)();(0,t.useEffect)(()=>{e&&"admin"===e.role||C.push("/dashboard")},[e,C]);let[q,P]=(0,t.useState)(""),[S,L]=(0,t.useState)(!1),[M,z]=(0,t.useState)({email:"",password:"",full_name:"",phone:"",specialty:""}),[D,E]=(0,t.useState)(null),[H,O]=(0,t.useState)(!1),[T,B]=(0,t.useState)(!1),[R,U]=(0,t.useState)(null),W=async e=>{if(e.preventDefault(),!M.email||!M.password||!M.full_name||!M.phone||!M.specialty)return void U({type:"error",text:_("fill_required_fields")});B(!0),U(null);try{await new Promise(e=>setTimeout(e,1e3)),r({email:M.email,password:M.password,full_name:M.full_name,phone:M.phone,specialty:M.specialty,is_active:!0}),U({type:"success",text:_("worker_added_success")}),z({email:"",password:"",full_name:"",phone:"",specialty:""}),L(!1)}catch(e){U({type:"error",text:_("error_adding_worker")})}finally{B(!1)}},F=e=>{E({...e,password:""}),O(!0)},G=async e=>{if(e.preventDefault(),!D||!D.email||!D.full_name||!D.phone||!D.specialty)return void U({type:"error",text:_("fill_required_fields")});B(!0),U(null);try{await new Promise(e=>setTimeout(e,1e3));let e={email:D.email,full_name:D.full_name,phone:D.phone,specialty:D.specialty,is_active:D.is_active};D.password&&(e.password=D.password),n(D.id,e),U({type:"success",text:_("worker_updated_success")}),O(!1),E(null)}catch(e){U({type:"error",text:_("error_updating_worker")})}finally{B(!1)}},I=e=>{confirm(_("confirm_delete_worker"))&&(w(e),U({type:"success",text:_("worker_deleted_success")}))},J=(e,s)=>{n(e,{is_active:!s}),U({type:"success",text:s?_("worker_deactivated"):_("worker_activated")})},K=e=>k.filter(s=>s.assignedWorker===e&&"completed"===s.status).length,Q=e=>new Date(e).toLocaleDateString("ar-US",{year:"numeric",month:"long",day:"numeric"}),V=s.filter(e=>e.full_name.toLowerCase().includes(q.toLowerCase())||e.email.toLowerCase().includes(q.toLowerCase())||e.specialty.toLowerCase().includes(q.toLowerCase()));return e&&"admin"===e.role?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-8",children:(0,a.jsxs)(d(),{href:"/dashboard",className:"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:_("back_to_dashboard")})]})}),(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold mb-2",children:(0,a.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent",children:_("workers_management")})}),(0,a.jsx)("p",{className:"text-lg text-gray-600",children:_("view_manage_team")})]}),(0,a.jsxs)("button",{onClick:()=>L(!0),className:"btn-primary inline-flex items-center justify-center space-x-2 space-x-reverse px-6 py-3 group",children:[(0,a.jsx)(p.A,{className:"w-5 h-5 group-hover:scale-110 transition-transform duration-300"}),(0,a.jsx)("span",{children:_("add_new_worker")})]})]}),(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 mb-8",children:(0,a.jsxs)("div",{className:"relative max-w-md",children:[(0,a.jsx)(u.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",value:q,onChange:e=>P(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:_("search_workers_placeholder")})]})}),R&&(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-4 rounded-lg flex items-center space-x-3 space-x-reverse ".concat("success"===R.type?"bg-green-50 text-green-800 border border-green-200":"bg-red-50 text-red-800 border border-red-200"),children:["success"===R.type?(0,a.jsx)(h.A,{className:"w-5 h-5 text-green-600"}):(0,a.jsx)(b.A,{className:"w-5 h-5 text-red-600"}),(0,a.jsx)("span",{children:R.text})]}),S&&(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:_("add_new_worker_form")}),(0,a.jsx)("button",{onClick:()=>L(!1),className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)(b.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("form",{onSubmit:W,className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("full_name_required")}),(0,a.jsx)("input",{type:"text",value:M.full_name,onChange:e=>z(s=>({...s,full_name:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("enter_full_name"),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("email_required")}),(0,a.jsx)("input",{type:"email",value:M.email,onChange:e=>z(s=>({...s,email:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("enter_email"),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("password_required")}),(0,a.jsx)("input",{type:"password",value:M.password,onChange:e=>z(s=>({...s,password:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("enter_password"),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("phone_required")}),(0,a.jsx)("input",{type:"tel",value:M.phone,onChange:e=>z(s=>({...s,phone:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("enter_phone"),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("specialty_required")}),(0,a.jsx)("input",{type:"text",value:M.specialty,onChange:e=>z(s=>({...s,specialty:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("specialty_example"),required:!0})]}),(0,a.jsxs)("div",{className:"md:col-span-2 flex gap-4 pt-4",children:[(0,a.jsx)("button",{type:"submit",disabled:T,className:"btn-primary py-3 px-6 disabled:opacity-50 disabled:cursor-not-allowed",children:T?_("adding"):_("add_worker")}),(0,a.jsx)("button",{type:"button",onClick:()=>L(!1),className:"btn-secondary py-3 px-6",children:_("cancel")})]})]})]}),H&&D&&(0,a.jsxs)(l.P.div,{initial:{opacity:0},animate:{opacity:1},className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm",onClick:()=>O(!1)}),(0,a.jsxs)(l.P.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},className:"relative bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)("div",{className:"sticky top-0 bg-white border-b border-gray-200 p-6 rounded-t-2xl",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:_("edit_worker")}),(0,a.jsx)("button",{onClick:()=>O(!1),className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(b.A,{className:"w-6 h-6"})})]})}),(0,a.jsxs)("form",{onSubmit:G,className:"p-6 space-y-4",children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("full_name_required")}),(0,a.jsx)("input",{type:"text",value:D.full_name,onChange:e=>E(s=>({...s,full_name:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("email_required")}),(0,a.jsx)("input",{type:"email",value:D.email,onChange:e=>E(s=>({...s,email:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("new_password")}),(0,a.jsx)("input",{type:"password",value:D.password,onChange:e=>E(s=>({...s,password:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("leave_empty_no_change")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("phone_required")}),(0,a.jsx)("input",{type:"tel",value:D.phone,onChange:e=>E(s=>({...s,phone:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("specialty_required")}),(0,a.jsx)("input",{type:"text",value:D.specialty,onChange:e=>E(s=>({...s,specialty:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("status")}),(0,a.jsxs)("select",{value:D.is_active?"active":"inactive",onChange:e=>E(s=>({...s,is_active:"active"===e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"active",children:_("active")}),(0,a.jsx)("option",{value:"inactive",children:_("inactive")})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-4 pt-4",children:[(0,a.jsx)("button",{type:"submit",disabled:T,className:"btn-primary py-3 px-6 disabled:opacity-50 disabled:cursor-not-allowed",children:T?_("saving"):_("save_changes")}),(0,a.jsx)("button",{type:"button",onClick:()=>O(!1),className:"btn-secondary py-3 px-6",children:_("cancel")})]})]})]})]}),(0,a.jsx)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"grid lg:grid-cols-2 gap-6 mb-12",children:0===V.length?(0,a.jsxs)("div",{className:"lg:col-span-2 text-center py-12 bg-white/80 backdrop-blur-sm rounded-2xl border border-pink-100",children:[(0,a.jsx)(g.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-medium text-gray-600 mb-2",children:_("no_workers")}),(0,a.jsx)("p",{className:"text-gray-500",children:_("no_workers_found")})]}):V.map((e,s)=>(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*s},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border transition-all duration-300 hover:shadow-lg ".concat(e.is_active?"border-pink-100":"border-gray-200 opacity-75"),children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full flex items-center justify-center",children:(0,a.jsx)(y.A,{className:"w-6 h-6 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-800",children:e.full_name}),(0,a.jsx)("p",{className:"text-sm text-pink-600 font-medium",children:e.specialty})]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2 space-x-reverse",children:e.is_active?(0,a.jsxs)("span",{className:"px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium flex items-center space-x-1 space-x-reverse",children:[(0,a.jsx)(h.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:_("active")})]}):(0,a.jsxs)("span",{className:"px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium flex items-center space-x-1 space-x-reverse",children:[(0,a.jsx)(b.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:_("inactive")})]})})]}),(0,a.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse text-sm text-gray-600",children:[(0,a.jsx)(f.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.email})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse text-sm text-gray-600",children:[(0,a.jsx)(v.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.phone})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 gap-4 mb-6",children:(0,a.jsxs)("div",{className:"text-center p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-green-600",children:K(e.id)}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:_("completed_orders")})]})}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mb-4",children:[_("joined_on")," ",Q(e.createdAt)]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{onClick:()=>F(e),className:"flex-1 btn-secondary py-2 text-sm inline-flex items-center justify-center space-x-1 space-x-reverse",children:[(0,a.jsx)(j.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:_("edit")})]}),(0,a.jsx)("button",{onClick:()=>J(e.id,e.is_active),className:"px-3 py-2 text-sm rounded-lg transition-all duration-300 ".concat(e.is_active?"bg-red-100 text-red-700 hover:bg-red-200":"bg-green-100 text-green-700 hover:bg-green-200"),children:e.is_active?(0,a.jsx)(b.A,{className:"w-4 h-4"}):(0,a.jsx)(h.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>I(e.id),className:"px-3 py-2 text-red-600 hover:text-red-700 border border-red-200 rounded-lg hover:bg-red-50 transition-all duration-300",children:(0,a.jsx)(N.A,{className:"w-4 h-4"})})]})]},e.id))}),(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"grid grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:s.length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:_("total_workers")})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-1",children:s.filter(e=>e.is_active).length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:_("active_workers")})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600 mb-1",children:s.reduce((e,s)=>e+K(s.id),0)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:_("total_completed_orders")})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600 mb-1",children:s.filter(e=>e.is_active).length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:_("active_workers")})]})]})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:_("checking_permissions")})]})})}},4861:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5977:(e,s,r)=>{Promise.resolve().then(r.bind(r,4535))},7580:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7924:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8883:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9420:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[165,874,69,764,441,684,358],()=>s(5977)),_N_E=e.O()}]);