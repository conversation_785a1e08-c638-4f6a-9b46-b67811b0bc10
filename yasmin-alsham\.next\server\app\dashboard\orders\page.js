(()=>{var e={};e.id=611,e.ids=[611],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19080:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19796:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>G});var a=r(60687),t=r(43210),l=r(26001),n=r(16189),i=r(85814),d=r.n(i),c=r(99720),o=r(34270),x=r(99182),m=r(88920),p=r(11860),h=r(58869),g=r(19080),u=r(40228),b=r(48730),j=r(93508),y=r(33800),v=r(9005),N=r(58887),f=r(5336),k=r(61465);function w({order:e,workers:s,isOpen:r,onClose:t}){let{user:n}=(0,c.n)(),{t:i,isArabic:d}=(0,x.B)();if(!e)return null;let o=e=>{let s={pending:{label:i("pending"),color:"text-yellow-600",bgColor:"bg-yellow-100"},in_progress:{label:i("in_progress"),color:"text-blue-600",bgColor:"bg-blue-100"},completed:{label:i("completed"),color:"text-green-600",bgColor:"bg-green-100"},delivered:{label:i("delivered"),color:"text-purple-600",bgColor:"bg-purple-100"},cancelled:{label:i("cancelled"),color:"text-red-600",bgColor:"bg-red-100"}};return s[e]||s.pending},w=e=>new Date(e).toLocaleDateString("ar-US",{year:"numeric",month:"long",day:"numeric"});return(0,a.jsx)(m.N,{children:r&&(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,a.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-black/50 backdrop-blur-sm",onClick:t}),(0,a.jsxs)(l.P.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},className:"relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)("div",{className:"sticky top-0 bg-white border-b border-gray-200 p-6 rounded-t-2xl",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:i("order_details")}),(0,a.jsx)("div",{className:"flex items-center space-x-3 space-x-reverse",children:(0,a.jsx)("button",{onClick:t,className:"p-2 text-gray-400 hover:text-gray-600 transition-colors duration-300",children:(0,a.jsx)(p.A,{className:"w-6 h-6"})})})]})}),(0,a.jsxs)("div",{className:"p-6 space-y-8",children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(h.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsxs)("span",{children:[i("customer_information"),d&&(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Customer Information)"})]})]}),(0,a.jsx)("div",{className:"space-y-3 bg-gray-50 p-4 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 text-gray-600"}),(0,a.jsxs)("span",{className:"font-medium",children:[i("name"),d&&(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Name)"})]}),(0,a.jsx)("span",{children:e.clientName})]})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(g.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsxs)("span",{children:[i("order_details"),d&&(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Order Details)"})]})]}),(0,a.jsxs)("div",{className:"space-y-3 bg-gray-50 p-4 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"font-medium",children:[i("description"),d&&(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Description)"})]}),(0,a.jsx)("p",{className:"mt-1",children:e.description})]}),e.fabric&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"font-medium",children:[i("fabric_type"),d&&(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Fabric Type)"})]}),(0,a.jsx)("p",{className:"mt-1",children:e.fabric})]})]})]})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("h4",{className:"font-bold text-gray-800",children:[i("status"),d&&(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Status)"})]}),(0,a.jsx)("span",{className:`px-3 py-2 rounded-full text-sm font-medium ${o(e.status).bgColor} ${o(e.status).color}`,children:o(e.status).label})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("h4",{className:"font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(u.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[i("order_date"),d&&(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Order Date)"})]})]}),(0,a.jsx)("p",{children:w(e.createdAt)})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("h4",{className:"font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(b.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[i("delivery_date"),d&&(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Delivery Date)"})]})]}),(0,a.jsx)("p",{children:w((e=>{let s=new Date(e);return s.setDate(s.getDate()-2),s.toISOString()})(e.dueDate))})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(j.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsxs)("span",{children:[i("assigned_worker"),d&&(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Assigned Worker)"})]})]}),(0,a.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg",children:(0,a.jsx)("p",{className:"text-lg",children:(e=>{if(!e)return i("not_specified");let r=s.find(s=>s.id===e);return r?r.full_name:i("not_specified")})(e.assignedWorker)})})]}),Object.values(e.measurements).some(e=>void 0!==e)&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(y.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsxs)("span",{children:[i("measurements_cm"),d&&(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Measurements)"})]})]}),(e.measurements.shoulder||e.measurements.shoulderCircumference||e.measurements.chest||e.measurements.waist||e.measurements.hips)&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("h4",{className:"text-md font-semibold text-gray-700 border-b border-pink-200 pb-1",children:[i("basic_measurements"),d&&(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Basic Measurements)"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[e.measurements.shoulder&&(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("shoulder"),d&&(0,a.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Shoulder)"})]}),(0,a.jsx)("p",{className:"text-lg font-bold",children:e.measurements.shoulder})]}),e.measurements.shoulderCircumference&&(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("shoulder_circumference"),d&&(0,a.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Shoulder Circumference)"})]}),(0,a.jsx)("p",{className:"text-lg font-bold",children:e.measurements.shoulderCircumference})]}),e.measurements.chest&&(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("chest_bust"),d&&(0,a.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Chest/Bust)"})]}),(0,a.jsx)("p",{className:"text-lg font-bold",children:e.measurements.chest})]}),e.measurements.waist&&(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("waist"),d&&(0,a.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Waist)"})]}),(0,a.jsx)("p",{className:"text-lg font-bold",children:e.measurements.waist})]}),e.measurements.hips&&(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("hips"),d&&(0,a.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Hips)"})]}),(0,a.jsx)("p",{className:"text-lg font-bold",children:e.measurements.hips})]})]})]}),(e.measurements.dartLength||e.measurements.bodiceLength||e.measurements.neckline||e.measurements.armpit)&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("h4",{className:"text-md font-semibold text-gray-700 border-b border-pink-200 pb-1",children:[i("advanced_tailoring_measurements"),d&&(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Advanced Tailoring)"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[e.measurements.dartLength&&(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("dart_length"),d&&(0,a.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Dart Length)"})]}),(0,a.jsx)("p",{className:"text-lg font-bold",children:e.measurements.dartLength})]}),e.measurements.bodiceLength&&(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("bodice_length"),d&&(0,a.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Bodice Length)"})]}),(0,a.jsx)("p",{className:"text-lg font-bold",children:e.measurements.bodiceLength})]}),e.measurements.neckline&&(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("neckline"),d&&(0,a.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Neckline)"})]}),(0,a.jsx)("p",{className:"text-lg font-bold",children:e.measurements.neckline})]}),e.measurements.armpit&&(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("armpit"),d&&(0,a.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Armpit)"})]}),(0,a.jsx)("p",{className:"text-lg font-bold",children:e.measurements.armpit})]})]})]}),(e.measurements.sleeveLength||e.measurements.forearm||e.measurements.cuff)&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("h4",{className:"text-md font-semibold text-gray-700 border-b border-pink-200 pb-1",children:[i("sleeve_measurements"),d&&(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Sleeve Measurements)"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[e.measurements.sleeveLength&&(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("sleeve_length"),d&&(0,a.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Sleeve Length)"})]}),(0,a.jsx)("p",{className:"text-lg font-bold",children:e.measurements.sleeveLength})]}),e.measurements.forearm&&(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("forearm"),d&&(0,a.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Forearm)"})]}),(0,a.jsx)("p",{className:"text-lg font-bold",children:e.measurements.forearm})]}),e.measurements.cuff&&(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("cuff"),d&&(0,a.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Cuff)"})]}),(0,a.jsx)("p",{className:"text-lg font-bold",children:e.measurements.cuff})]})]})]}),(e.measurements.frontLength||e.measurements.backLength)&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("h4",{className:"text-md font-semibold text-gray-700 border-b border-pink-200 pb-1",children:[i("length_measurements"),d&&(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Length Measurements)"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[e.measurements.frontLength&&(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("front_length"),d&&(0,a.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Front Length)"})]}),(0,a.jsx)("p",{className:"text-lg font-bold",children:e.measurements.frontLength})]}),e.measurements.backLength&&(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("back_length"),d&&(0,a.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Back Length)"})]}),(0,a.jsx)("p",{className:"text-lg font-bold",children:e.measurements.backLength})]})]})]}),(e.measurements.length||e.measurements.shoulders||e.measurements.sleeves)&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("h4",{className:"text-md font-semibold text-gray-700 border-b border-gray-300 pb-1 text-gray-500",children:[i("additional_measurements"),d&&(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Additional)"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[e.measurements.length&&(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("dress_length"),d&&(0,a.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Dress Length)"})]}),(0,a.jsx)("p",{className:"text-lg font-bold",children:e.measurements.length})]}),e.measurements.shoulders&&(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("shoulder_width"),d&&(0,a.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Shoulder Width)"})]}),(0,a.jsx)("p",{className:"text-lg font-bold",children:e.measurements.shoulders})]}),e.measurements.sleeves&&(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("sleeve_length_old"),d&&(0,a.jsx)("span",{className:"text-xs text-gray-400 block",children:"(Sleeve Length)"})]}),(0,a.jsx)("p",{className:"text-lg font-bold",children:e.measurements.sleeves})]})]})]})]}),e.images&&e.images.length>0&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(v.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsxs)("span",{children:[i("design_images"),d&&(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Design Images)"})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:e.images.map((e,s)=>(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"aspect-square rounded-lg overflow-hidden border border-gray-200",children:(0,a.jsx)("img",{src:e,alt:`${i("design_image_alt")} ${s+1}`,className:"w-full h-full object-cover cursor-pointer hover:scale-105 transition-transform duration-300",onClick:()=>window.open(e,"_blank")})}),(0,a.jsx)("div",{className:"absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded",children:s+1})]},s))})]}),e.notes&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(N.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsxs)("span",{children:[i("notes"),d&&(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Notes)"})]})]}),(0,a.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg",children:(0,a.jsx)("p",{children:e.notes})})]}),e.voiceNotes&&e.voiceNotes.length>0&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(N.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsxs)("span",{children:[i("voice_notes"),d&&(0,a.jsx)("span",{className:"text-sm text-gray-500 mr-2",children:"(Voice Notes)"})]})]}),(0,a.jsx)(k.A,{voiceNotes:e.voiceNotes,onVoiceNotesChange:()=>{},disabled:!0})]}),n?.role==="admin"&&e.completedImages&&e.completedImages.length>0&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(f.A,{className:"w-5 h-5 text-green-600"}),(0,a.jsx)("span",{children:i("completed_work_images")})]}),(0,a.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg border border-green-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse mb-3",children:[(0,a.jsx)(f.A,{className:"w-4 h-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-800",children:i("completed_work_description")})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:e.completedImages.map((e,s)=>(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"aspect-square rounded-lg overflow-hidden border border-green-300",children:(0,a.jsx)("img",{src:e,alt:`${i("completed_work_image_alt")} ${s+1}`,className:"w-full h-full object-cover cursor-pointer hover:scale-105 transition-transform duration-300",onClick:()=>window.open(e,"_blank")})}),(0,a.jsx)("div",{className:"absolute bottom-2 left-2 bg-green-600/80 text-white text-xs px-2 py-1 rounded",children:s+1})]},s))})]})]})]}),(0,a.jsx)("div",{className:"sticky bottom-0 bg-white border-t border-gray-200 p-6 rounded-b-2xl",children:(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)("button",{onClick:t,className:"btn-secondary px-6 py-2",children:i("close")})})})]})]})})}var _=r(93613),A=r(8819),C=r(65938),S=r(1733);function L({order:e,workers:s,isOpen:r,onClose:n,onSave:i}){let{t:d}=(0,x.B)(),[c,o]=(0,t.useState)({}),[u,b]=(0,t.useState)(!1),[w,L]=(0,t.useState)(null),P=(e,s)=>{o(r=>({...r,[e]:s}))},D=(e,s)=>{o(r=>({...r,measurements:{...r.measurements,[e]:s}}))},M=async s=>{if(s.preventDefault(),e){if(!c.clientName||!c.clientPhone||!c.description||!c.price||!c.dueDate)return void L({type:"error",text:d("fill_required_fields")});b(!0),L(null);try{await new Promise(e=>setTimeout(e,1e3));let s=Object.keys(c.measurements||{}).reduce((e,s)=>{let r=c.measurements?.[s];return e[s]=r&&""!==r?Number(r):void 0,e},{});i(e.id,{...c,price:Number(c.price),measurements:s,updatedAt:new Date().toISOString()}),L({type:"success",text:d("order_updated_success")}),setTimeout(()=>{n(),L(null)},1500)}catch(e){L({type:"error",text:d("order_update_error")})}finally{b(!1)}}};return e?(0,a.jsx)(m.N,{children:r&&(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,a.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-black/50 backdrop-blur-sm",onClick:n}),(0,a.jsxs)(l.P.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},className:"relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)("div",{className:"sticky top-0 bg-white border-b border-gray-200 p-6 rounded-t-2xl",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:d("edit_order")}),(0,a.jsx)("button",{onClick:n,className:"p-2 text-gray-400 hover:text-gray-600 transition-colors duration-300",children:(0,a.jsx)(p.A,{className:"w-6 h-6"})})]})}),w&&(0,a.jsxs)("div",{className:`mx-6 mt-4 p-4 rounded-lg flex items-center space-x-3 space-x-reverse ${"success"===w.type?"bg-green-50 text-green-800 border border-green-200":"bg-red-50 text-red-800 border border-red-200"}`,children:["success"===w.type?(0,a.jsx)(f.A,{className:"w-5 h-5 text-green-600"}):(0,a.jsx)(_.A,{className:"w-5 h-5 text-red-600"}),(0,a.jsx)("span",{children:w.text})]}),(0,a.jsxs)("form",{onSubmit:M,className:"p-6 space-y-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(h.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsx)("span",{children:d("customer_information")})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:d("client_name_required")}),(0,a.jsx)("input",{type:"text",value:c.clientName||"",onChange:e=>P("clientName",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,a.jsx)("div",{children:(0,a.jsx)(S.A,{value:c.clientPhone||"",onChange:e=>P("clientPhone",e),type:"phone",label:d("phone_required"),required:!0,disabled:u})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(g.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsx)("span",{children:d("order_details")})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:d("order_description_required")}),(0,a.jsx)("input",{type:"text",value:c.description||"",onChange:e=>P("description",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:d("fabric_type_optional")}),(0,a.jsx)("input",{type:"text",value:c.fabric||"",onChange:e=>P("fabric",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"})]}),(0,a.jsx)("div",{children:(0,a.jsx)(S.A,{value:c.price?.toString()||"",onChange:e=>P("price",e?Number(e):""),type:"price",label:d("price_sar_required"),required:!0,disabled:u})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:d("delivery_date_required")}),(0,a.jsx)("input",{type:"date",value:c.dueDate||"",onChange:e=>P("dueDate",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(j.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsx)("span",{children:d("status_and_worker")})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:d("order_status")}),(0,a.jsxs)("select",{value:c.status||"",onChange:e=>P("status",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"pending",children:d("status_pending")}),(0,a.jsx)("option",{value:"in_progress",children:d("status_in_progress")}),(0,a.jsx)("option",{value:"completed",children:d("status_completed")}),(0,a.jsx)("option",{value:"delivered",children:d("status_delivered")}),(0,a.jsx)("option",{value:"cancelled",children:d("status_cancelled")})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:d("responsible_worker")}),(0,a.jsxs)("select",{value:c.assignedWorker||"",onChange:e=>P("assignedWorker",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:d("choose_worker")}),s.filter(e=>e.is_active).map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.full_name," - ",e.specialty]},e.id))]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(v.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsx)("span",{children:d("design_images")})]}),(0,a.jsx)(C.A,{images:c.images||[],onImagesChange:e=>P("images",e),maxImages:5})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(y.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsx)("span",{children:d("measurements_cm")})]}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2",children:d("basic_measurements")}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,a.jsx)("div",{children:(0,a.jsx)(S.A,{value:c.measurements?.shoulder?.toString()||"",onChange:e=>D("shoulder",e),type:"measurement",label:d("shoulder"),placeholder:d("cm_placeholder"),disabled:u})}),(0,a.jsx)("div",{children:(0,a.jsx)(S.A,{value:c.measurements?.shoulderCircumference?.toString()||"",onChange:e=>D("shoulderCircumference",e),type:"measurement",label:d("shoulder_circumference"),placeholder:d("cm_placeholder"),disabled:u})}),(0,a.jsx)("div",{children:(0,a.jsx)(S.A,{value:c.measurements?.chest?.toString()||"",onChange:e=>D("chest",e),type:"measurement",label:d("chest"),placeholder:d("cm_placeholder"),disabled:u})}),(0,a.jsx)("div",{children:(0,a.jsx)(S.A,{value:c.measurements?.waist?.toString()||"",onChange:e=>D("waist",e),type:"measurement",label:d("waist"),placeholder:d("cm_placeholder"),disabled:u})}),(0,a.jsx)("div",{children:(0,a.jsx)(S.A,{value:c.measurements?.hips?.toString()||"",onChange:e=>D("hips",e),type:"measurement",label:d("hips"),placeholder:d("cm_placeholder"),disabled:u})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2",children:d("advanced_measurements")}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,a.jsx)("div",{children:(0,a.jsx)(S.A,{value:c.measurements?.dartLength?.toString()||"",onChange:e=>D("dartLength",e),type:"measurement",label:d("dart_length"),placeholder:d("cm_placeholder"),disabled:u})}),(0,a.jsx)("div",{children:(0,a.jsx)(S.A,{value:c.measurements?.bodiceLength?.toString()||"",onChange:e=>D("bodiceLength",e),type:"measurement",label:d("bodice_length"),placeholder:d("cm_placeholder"),disabled:u})}),(0,a.jsx)("div",{children:(0,a.jsx)(S.A,{value:c.measurements?.neckline?.toString()||"",onChange:e=>D("neckline",e),type:"measurement",label:d("neckline"),placeholder:d("cm_placeholder"),disabled:u})}),(0,a.jsx)("div",{children:(0,a.jsx)(S.A,{value:c.measurements?.armpit?.toString()||"",onChange:e=>D("armpit",e),type:"measurement",label:d("armpit"),placeholder:d("cm_placeholder"),disabled:u})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2",children:d("sleeve_measurements")}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,a.jsx)("div",{children:(0,a.jsx)(S.A,{value:c.measurements?.sleeveLength?.toString()||"",onChange:e=>D("sleeveLength",e),type:"measurement",label:d("sleeve_length"),placeholder:d("cm_placeholder"),disabled:u})}),(0,a.jsx)("div",{children:(0,a.jsx)(S.A,{value:c.measurements?.forearm?.toString()||"",onChange:e=>D("forearm",e),type:"measurement",label:d("forearm"),placeholder:d("cm_placeholder"),disabled:u})}),(0,a.jsx)("div",{children:(0,a.jsx)(S.A,{value:c.measurements?.cuff?.toString()||"",onChange:e=>D("cuff",e),type:"measurement",label:d("cuff"),placeholder:d("cm_placeholder"),disabled:u})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2",children:d("length_measurements")}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,a.jsx)("div",{children:(0,a.jsx)(S.A,{value:c.measurements?.frontLength?.toString()||"",onChange:e=>D("frontLength",e),type:"measurement",label:d("front_length"),placeholder:d("cm_placeholder"),disabled:u})}),(0,a.jsx)("div",{children:(0,a.jsx)(S.A,{value:c.measurements?.backLength?.toString()||"",onChange:e=>D("backLength",e),type:"measurement",label:d("back_length"),placeholder:d("cm_placeholder"),disabled:u})})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(N.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsx)("span",{children:d("notes_section")})]}),(0,a.jsx)("textarea",{value:c.notes||"",onChange:e=>P("notes",e.target.value),rows:4,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:d("additional_notes_placeholder")}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)(k.A,{voiceNotes:c.voiceNotes||[],onVoiceNotesChange:e=>P("voiceNotes",e),disabled:u})})]})]}),(0,a.jsx)("div",{className:"sticky bottom-0 bg-white border-t border-gray-200 p-6 rounded-b-2xl",children:(0,a.jsxs)("div",{className:"flex gap-4 justify-end",children:[(0,a.jsx)("button",{type:"button",onClick:n,className:"btn-secondary px-6 py-2",disabled:u,children:d("cancel")}),(0,a.jsx)("button",{onClick:M,disabled:u,className:"btn-primary px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 space-x-reverse",children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,a.jsx)("span",{children:d("saving")})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(A.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:d("save_changes")})]})})]})})]})]})}):null}let P=(0,r(62688).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);function D({onImagesChange:e,maxImages:s=3,disabled:r=!1}){let[n,i]=(0,t.useState)([]),[d,c]=(0,t.useState)(!1),o=(0,t.useRef)(null),x=a=>{if(!a||r)return;let t=[],l=s-n.length;Array.from(a).slice(0,l).forEach(s=>{if(s.type.startsWith("image/")){let r=new FileReader;r.onload=s=>{let r=s.target?.result;if(t.push(r),t.length===Math.min(a.length,l)){let s=[...n,...t];i(s),e(s)}},r.readAsDataURL(s)}})},m=s=>{if(r)return;let a=n.filter((e,r)=>r!==s);i(a),e(a)};return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"صور العمل المكتمل"}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[n.length,"/",s," صور"]})]}),(0,a.jsxs)("div",{onDrop:e=>{e.preventDefault(),c(!1),x(e.dataTransfer.files)},onDragOver:e=>{e.preventDefault(),r||c(!0)},onDragLeave:e=>{e.preventDefault(),c(!1)},onClick:()=>{!r&&o.current&&o.current.click()},className:`
          relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-300
          ${d?"border-pink-400 bg-pink-50":"border-gray-300 hover:border-pink-400 hover:bg-pink-50"}
          ${r?"opacity-50 cursor-not-allowed":""}
          ${n.length>=s?"opacity-50 cursor-not-allowed":""}
        `,children:[(0,a.jsx)("input",{ref:o,type:"file",multiple:!0,accept:"image/*",onChange:e=>x(e.target.files),className:"hidden",disabled:r||n.length>=s}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(P,{className:"w-12 h-12 text-gray-400 mx-auto"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700",children:n.length>=s?"تم الوصول للحد الأقصى من الصور":"اضغط لرفع صور العمل المكتمل"}),n.length<s&&(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"أو اسحب الصور هنا • JPG, PNG, GIF"})]})]})]}),n.length>0&&(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:n.map((e,s)=>(0,a.jsxs)(l.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:"relative group",children:[(0,a.jsx)("div",{className:"aspect-square rounded-lg overflow-hidden bg-gray-100",children:(0,a.jsx)("img",{src:e,alt:`صورة العمل ${s+1}`,className:"w-full h-full object-cover"})}),!r&&(0,a.jsx)("button",{onClick:e=>{e.stopPropagation(),m(s)},className:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-red-600",children:(0,a.jsx)(p.A,{className:"w-3 h-3"})}),(0,a.jsx)("div",{className:"absolute bottom-2 left-2 right-2",children:(0,a.jsxs)("div",{className:"bg-black/50 text-white text-xs px-2 py-1 rounded backdrop-blur-sm",children:["صورة ",s+1]})})]},s))}),n.length>0&&(0,a.jsx)("div",{className:"text-xs text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200",children:(0,a.jsxs)("div",{className:"flex items-start space-x-2 space-x-reverse",children:[(0,a.jsx)(v.A,{className:"w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-blue-800 mb-1",children:"ملاحظة مهمة:"}),(0,a.jsx)("p",{className:"text-blue-700",children:"هذه الصور ستكون مرئية للمدير فقط ولن تظهر للعملاء في صفحة تتبع الطلبات. تُستخدم لتوثيق جودة العمل المكتمل."})]})]})})]})}var M=r(43649),q=r(88233),I=r(12597),W=r(13861);function $({isOpen:e,onClose:s,onConfirm:r,orderInfo:n}){let{t:i}=(0,x.B)(),{user:d}=(0,c.n)(),[o,h]=(0,t.useState)(""),[g,u]=(0,t.useState)(""),[b,j]=(0,t.useState)(!1),[y,v]=(0,t.useState)(!1),[N,f]=(0,t.useState)(""),k=async e=>{if(e.preventDefault(),f(""),v(!0),!o||!g){f(i("please_fill_all_fields")),v(!1);return}if(o!==d?.email){f(i("email_does_not_match")),v(!1);return}if("admin123"!==g){f(i("incorrect_password")),v(!1);return}await new Promise(e=>setTimeout(e,1e3)),v(!1),r(),w()},w=()=>{h(""),u(""),f(""),j(!1),s()};return(0,a.jsx)(m.N,{children:e&&(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,a.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-black/50 backdrop-blur-sm",onClick:w}),(0,a.jsxs)(l.P.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},className:"relative bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,a.jsx)("div",{className:"p-2 bg-red-100 rounded-full",children:(0,a.jsx)(M.A,{className:"w-6 h-6 text-red-600"})}),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:i("confirm_delete_order")})]}),(0,a.jsx)("button",{onClick:w,className:"p-2 hover:bg-gray-100 rounded-full transition-colors",children:(0,a.jsx)(p.A,{className:"w-5 h-5 text-gray-500"})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3 space-x-reverse",children:[(0,a.jsx)(q.A,{className:"w-5 h-5 text-red-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-red-800 mb-2",children:i("warning_delete_order")}),(0,a.jsx)("p",{className:"text-sm text-red-700",children:i("delete_order_warning_message")})]})]})}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:i("order_details")}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-gray-600",children:[(0,a.jsxs)("p",{children:[(0,a.jsxs)("span",{className:"font-medium",children:[i("order_id"),":"]})," #",n.id]}),(0,a.jsxs)("p",{children:[(0,a.jsxs)("span",{className:"font-medium",children:[i("client_name"),":"]})," ",n.clientName]}),(0,a.jsxs)("p",{children:[(0,a.jsxs)("span",{className:"font-medium",children:[i("description"),":"]})," ",n.description]})]})]}),(0,a.jsxs)("form",{onSubmit:k,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:i("admin_email")}),(0,a.jsx)("input",{type:"email",value:o,onChange:e=>h(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:i("enter_admin_email"),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:i("admin_password")}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:b?"text":"password",value:g,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:i("enter_admin_password"),required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>j(!b),className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:b?(0,a.jsx)(I.A,{className:"w-4 h-4"}):(0,a.jsx)(W.A,{className:"w-4 h-4"})})]})]}),N&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,a.jsx)("p",{className:"text-sm text-red-700",children:N})}),(0,a.jsxs)("div",{className:"flex space-x-3 space-x-reverse pt-4",children:[(0,a.jsx)("button",{type:"button",onClick:w,className:"flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors",children:i("cancel")}),(0,a.jsx)("button",{type:"submit",disabled:y,className:"flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 space-x-reverse",children:y?(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(q.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:i("confirm_delete")})]})})]})]})]})]})]})})}var z=r(70334),F=r(96474),O=r(99270),H=r(80462),T=r(63143);function G(){let{user:e}=(0,c.n)(),{orders:s,workers:r,updateOrder:i,deleteOrder:m,startOrderWork:j,completeOrder:y}=(0,o.D)(),{t:v,language:N,changeLanguage:k,isArabic:A}=(0,x.B)();(0,n.useRouter)();let[C,P]=(0,t.useState)(""),[M,I]=(0,t.useState)("text"),[G,B]=(0,t.useState)("all"),[U,R]=(0,t.useState)(null),[E,V]=(0,t.useState)(!1),[Y,J]=(0,t.useState)(!1),[K,X]=(0,t.useState)(!1),[Q,Z]=(0,t.useState)([]),[ee,es]=(0,t.useState)(!1),[er,ea]=(0,t.useState)(!1),[et,el]=(0,t.useState)(null),[en,ei]=(0,t.useState)(!1),ed=e=>{let s={pending:{label:v("pending"),color:"text-yellow-600",bgColor:"bg-yellow-100",icon:b.A},in_progress:{label:v("in_progress"),color:"text-blue-600",bgColor:"bg-blue-100",icon:g.A},completed:{label:v("completed"),color:"text-green-600",bgColor:"bg-green-100",icon:f.A},delivered:{label:v("delivered"),color:"text-purple-600",bgColor:"bg-purple-100",icon:f.A},cancelled:{label:v("cancelled"),color:"text-red-600",bgColor:"bg-red-100",icon:_.A}};return s[e]||s.pending},ec=e=>{if(!e)return null;let s=r.find(s=>s.id===e);return s?s.full_name:null},eo=e=>{R(e),V(!0)},ex=e=>{R(e),J(!0)},em=()=>{V(!1),J(!1),X(!1),R(null),Z([])},ep=e=>{el(e),ea(!0)},eh=async s=>{if(e&&"worker"===e.role){es(!0);try{await new Promise(e=>setTimeout(e,500)),j(s,e.id)}finally{es(!1)}}},eg=e=>{R(e),X(!0)},eu=async()=>{if(U&&e&&"worker"===e.role){es(!0);try{await new Promise(e=>setTimeout(e,1e3)),y(U.id,e.id,Q),X(!1),R(null),Z([])}finally{es(!1)}}},eb=e=>new Date(e).toLocaleDateString("ar-US",{year:"numeric",month:"short",day:"numeric",calendar:"gregory"}),ej=s.filter(s=>{let r=e?.role==="admin"||s.assignedWorker===e?.id,a="orderNumber"===M?s.id.toLowerCase().includes(C.toLowerCase()):s.clientName.toLowerCase().includes(C.toLowerCase())||s.id.toLowerCase().includes(C.toLowerCase())||s.description.toLowerCase().includes(C.toLowerCase()),t="all"===G||s.status===G;return r&&a&&t});return e?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-8",children:(0,a.jsxs)(d(),{href:"/dashboard",className:"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300",children:[(0,a.jsx)(z.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[v("back_to_dashboard")," ",v("dashboard")]})]})}),(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold mb-2",children:(0,a.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent",children:v("orders")})}),(0,a.jsx)("p",{className:"text-lg text-gray-600",children:v("view_manage_orders")})]}),(0,a.jsx)("div",{className:"flex items-center gap-3",children:"admin"===e.role&&(0,a.jsxs)(d(),{href:"/dashboard/add-order",className:"btn-primary inline-flex items-center justify-center space-x-2 space-x-reverse px-6 py-3 group",children:[(0,a.jsx)(F.A,{className:"w-5 h-5 group-hover:scale-110 transition-transform duration-300"}),(0,a.jsx)("span",{children:v("add_new_order")})]})})]}),(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 mb-8",children:(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex space-x-2 space-x-reverse",children:[(0,a.jsx)("button",{onClick:()=>{I("text"),P("")},className:`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${"text"===M?"bg-pink-500 text-white":"bg-gray-100 text-gray-600 hover:bg-gray-200"}`,children:v("search_by_text")}),(0,a.jsx)("button",{onClick:()=>{I("orderNumber"),P("")},className:`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${"orderNumber"===M?"bg-pink-500 text-white":"bg-gray-100 text-gray-600 hover:bg-gray-200"}`,children:v("search_by_order_number")})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(O.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),"orderNumber"===M?(0,a.jsx)(S.A,{value:C,onChange:P,type:"orderNumber",placeholder:v("enter_order_number"),className:"pr-10"}):(0,a.jsx)("input",{type:"text",value:C,onChange:e=>P(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:v("search_placeholder")})]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(H.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsxs)("select",{value:G,onChange:e=>B(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",children:[(0,a.jsx)("option",{value:"all",children:v("all_orders")}),(0,a.jsx)("option",{value:"pending",children:v("pending")}),(0,a.jsx)("option",{value:"in_progress",children:v("in_progress")}),(0,a.jsx)("option",{value:"completed",children:v("completed")}),(0,a.jsx)("option",{value:"delivered",children:v("delivered")})]})]})]})}),(0,a.jsx)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"space-y-6",children:0===ej.length?(0,a.jsxs)("div",{className:"text-center py-12 bg-white/80 backdrop-blur-sm rounded-2xl border border-pink-100",children:[(0,a.jsx)(g.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-medium text-gray-600 mb-2",children:"worker"===e.role?v("no_orders_assigned"):v("no_orders_found")}),(0,a.jsx)("p",{className:"text-gray-500",children:"worker"===e.role?v("no_orders_assigned_desc"):v("no_orders_found_desc")})]}):ej.map((s,r)=>(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*r},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 hover:shadow-lg transition-all duration-300",children:(0,a.jsxs)("div",{className:"grid lg:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-1",children:s.clientName}),(0,a.jsx)("p",{className:"text-pink-600 font-medium",children:s.description}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["#",s.id]})]}),(0,a.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${ed(s.status).bgColor} ${ed(s.status).color}`,children:ed(s.status).label})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(u.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[v("order_date_label"),": ",eb(s.createdAt)]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(b.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[v("delivery_date_label"),": ",eb(s.dueDate)]})]}),s.assignedWorker&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(h.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[v("worker_label"),": ",ec(s.assignedWorker)]})]}),s.fabric&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(g.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[v("fabric_label")," ",s.fabric]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:v("price_label")}),(0,a.jsxs)("p",{className:"text-lg font-bold text-green-600",children:[s.price," ",v("sar")]})]}),s.notes&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-1",children:v("notes_label")}),(0,a.jsx)("p",{className:"text-sm text-gray-600 bg-gray-50 p-2 rounded",children:s.notes})]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsxs)("button",{onClick:()=>eo(s),className:"btn-secondary py-2 px-4 text-sm inline-flex items-center justify-center space-x-1 space-x-reverse",children:[(0,a.jsx)(W.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:v("view")})]}),"admin"===e.role&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("button",{onClick:()=>ex(s),className:"btn-primary py-2 px-4 text-sm inline-flex items-center justify-center space-x-1 space-x-reverse",children:[(0,a.jsx)(T.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:v("edit")})]}),(0,a.jsxs)("button",{onClick:()=>ep(s),className:"bg-red-500 hover:bg-red-600 text-white py-2 px-4 text-sm inline-flex items-center justify-center space-x-1 space-x-reverse rounded-lg transition-colors duration-200",children:[(0,a.jsx)(q.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:v("delete")})]})]}),"worker"===e.role&&s.assignedWorker===e.id&&(0,a.jsxs)(a.Fragment,{children:["pending"===s.status&&(0,a.jsxs)("button",{onClick:()=>eh(s.id),disabled:ee,className:"bg-blue-600 text-white py-2 px-4 text-sm rounded-lg hover:bg-blue-700 transition-colors duration-300 inline-flex items-center justify-center space-x-1 space-x-reverse disabled:opacity-50",children:[(0,a.jsx)(g.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:v("start_work")})]}),"in_progress"===s.status&&(0,a.jsxs)("button",{onClick:()=>eg(s),disabled:ee,className:"bg-green-600 text-white py-2 px-4 text-sm rounded-lg hover:bg-green-700 transition-colors duration-300 inline-flex items-center justify-center space-x-1 space-x-reverse disabled:opacity-50",children:[(0,a.jsx)(f.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:v("complete_order")})]})]})]})]})},s.id))}),(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"mt-12 grid grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600 mb-1",children:s.filter(s=>("admin"===e.role||s.assignedWorker===e.id)&&"pending"===s.status).length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:v("pending")})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:s.filter(s=>("admin"===e.role||s.assignedWorker===e.id)&&"in_progress"===s.status).length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:v("in_progress")})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-1",children:s.filter(s=>("admin"===e.role||s.assignedWorker===e.id)&&"completed"===s.status).length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:v("completed")})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600 mb-1",children:s.filter(s=>("admin"===e.role||s.assignedWorker===e.id)&&"delivered"===s.status).length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:v("delivered")})]})]}),(0,a.jsx)(w,{order:U,workers:r,isOpen:E,onClose:em}),(0,a.jsx)(L,{order:U,workers:r,isOpen:Y,onClose:em,onSave:(e,s)=>{i(e,s),J(!1),R(null)}}),K&&U&&(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm",onClick:em}),(0,a.jsxs)(l.P.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},className:"relative bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)("div",{className:"sticky top-0 bg-white border-b border-gray-200 p-6 rounded-t-2xl",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:v("complete_order_modal_title")}),(0,a.jsx)("button",{onClick:em,className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(p.A,{className:"w-6 h-6"})})]})}),(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("h4",{className:"text-lg font-medium text-gray-800 mb-2",children:[v("order_label")," ",U.description]}),(0,a.jsxs)("p",{className:"text-gray-600",children:[v("for_client")," ",U.clientName]})]}),(0,a.jsx)(D,{onImagesChange:Z,maxImages:3,disabled:ee}),(0,a.jsx)("div",{className:"bg-yellow-50 p-4 rounded-lg border border-yellow-200",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3 space-x-reverse",children:[(0,a.jsx)(_.A,{className:"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-yellow-800 mb-1",children:v("important_warning")}),(0,a.jsx)("p",{className:"text-yellow-700 text-sm",children:v("complete_order_warning")})]})]})}),(0,a.jsxs)("div",{className:"flex gap-4 justify-end",children:[(0,a.jsx)("button",{onClick:em,disabled:ee,className:"btn-secondary px-6 py-2",children:v("cancel")}),(0,a.jsx)("button",{onClick:eu,disabled:ee,className:"bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 space-x-reverse",children:ee?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,a.jsx)("span",{children:v("completing")})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:v("complete_order")})]})})]})]})]})]}),(0,a.jsx)($,{isOpen:er,onClose:()=>{ea(!1),el(null)},onConfirm:()=>{et&&(m(et.id),ea(!1),el(null),ei(!0),setTimeout(()=>{ei(!1)},3e3))},orderInfo:et}),en&&(0,a.jsx)("div",{className:"fixed top-4 right-4 z-50",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,x:100},animate:{opacity:1,x:0},exit:{opacity:0,x:100},className:"bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(f.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:v("order_deleted_successfully")})]})})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:v("loading")})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33682:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var a=r(65239),t=r(48088),l=r(88170),n=r.n(l),i=r(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(s,d);let c={children:["",{children:["dashboard",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,71731)),"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\orders\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\orders\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/dashboard/orders/page",pathname:"/dashboard/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},33873:e=>{"use strict";e.exports=require("path")},40228:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},43649:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},48730:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},70631:(e,s,r)=>{Promise.resolve().then(r.bind(r,71731))},71731:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\orders\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\orders\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},80462:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},84543:(e,s,r)=>{Promise.resolve().then(r.bind(r,19796))},93508:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},99270:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[447,507,146,814,267,238,985,424],()=>r(33682));module.exports=a})();