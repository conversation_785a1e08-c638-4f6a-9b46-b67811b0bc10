/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/appointments/page";
exports.ids = ["app/dashboard/appointments/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fappointments%2Fpage&page=%2Fdashboard%2Fappointments%2Fpage&appPaths=%2Fdashboard%2Fappointments%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fappointments%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fappointments%2Fpage&page=%2Fdashboard%2Fappointments%2Fpage&appPaths=%2Fdashboard%2Fappointments%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fappointments%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/appointments/page.tsx */ \"(rsc)/./src/app/dashboard/appointments/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'appointments',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/appointments/page\",\n        pathname: \"/dashboard/appointments\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fappointments%2Fpage&page=%2Fdashboard%2Fappointments%2Fpage&appPaths=%2Fdashboard%2Fappointments%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fappointments%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Kufi_Arabic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-kufi%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoKufi%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Kufi_Arabic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-kufi%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoKufi%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cappointments%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cappointments%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/appointments/page.tsx */ \"(rsc)/./src/app/dashboard/appointments/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2toYWxlJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1lBU01JTiUyMEFMU0hBTSU1QyU1Q3lhc21pbi1hbHNoYW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNhcHBvaW50bWVudHMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOExBQTZKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxraGFsZVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxZQVNNSU4gQUxTSEFNXFxcXHlhc21pbi1hbHNoYW1cXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcYXBwb2ludG1lbnRzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cappointments%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca2hhbGVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcWUFTTUlOIEFMU0hBTVxceWFzbWluLWFsc2hhbVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/appointments/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/dashboard/appointments/page.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\appointments\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"bbd4ce015cec\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGtoYWxlXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFlBU01JTiBBTFNIQU1cXHlhc21pbi1hbHNoYW1cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImJiZDRjZTAxNWNlY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_variable_font_cairo_subsets_arabic_latin_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"variable\":\"--font-cairo\",\"subsets\":[\"arabic\",\"latin\"],\"display\":\"swap\"}],\"variableName\":\"cairo\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Cairo\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-cairo\\\",\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"cairo\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_variable_font_cairo_subsets_arabic_latin_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_variable_font_cairo_subsets_arabic_latin_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Kufi_Arabic_arguments_variable_font_noto_kufi_subsets_arabic_display_swap_variableName_notoKufi___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Noto_Kufi_Arabic\",\"arguments\":[{\"variable\":\"--font-noto-kufi\",\"subsets\":[\"arabic\"],\"display\":\"swap\"}],\"variableName\":\"notoKufi\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Noto_Kufi_Arabic\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-noto-kufi\\\",\\\"subsets\\\":[\\\"arabic\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoKufi\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Kufi_Arabic_arguments_variable_font_noto_kufi_subsets_arabic_display_swap_variableName_notoKufi___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Kufi_Arabic_arguments_variable_font_noto_kufi_subsets_arabic_display_swap_variableName_notoKufi___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"ياسمين الشام - تفصيل فساتين حسب الطلب\",\n    description: \"محل ياسمين الشام لتفصيل الفساتين النسائية بأناقة دمشقية. حجز مواعيد، استعلام عن الطلبات، وأفضل الأقمشة.\",\n    keywords: \"تفصيل فساتين، خياطة نسائية، ياسمين الشام، فساتين دمشقية، حجز موعد\",\n    authors: [\n        {\n            name: \"ياسمين الشام\"\n        }\n    ],\n    openGraph: {\n        title: \"ياسمين الشام - تفصيل فساتين حسب الطلب\",\n        description: \"محل ياسمين الشام لتفصيل الفساتين النسائية بأناقة دمشقية\",\n        type: \"website\",\n        locale: \"ar_SA\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_variable_font_cairo_subsets_arabic_latin_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Kufi_Arabic_arguments_variable_font_noto_kufi_subsets_arabic_display_swap_variableName_notoKufi___WEBPACK_IMPORTED_MODULE_3___default().variable)} font-cairo antialiased bg-gradient-to-br from-rose-50 to-pink-50 min-h-screen`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Kufi_Arabic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-kufi%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoKufi%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Kufi_Arabic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-kufi%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoKufi%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cappointments%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cappointments%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/appointments/page.tsx */ \"(ssr)/./src/app/dashboard/appointments/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2toYWxlJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1lBU01JTiUyMEFMU0hBTSU1QyU1Q3lhc21pbi1hbHNoYW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNhcHBvaW50bWVudHMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOExBQTZKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxraGFsZVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxZQVNNSU4gQUxTSEFNXFxcXHlhc21pbi1hbHNoYW1cXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcYXBwb2ludG1lbnRzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cappointments%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/appointments/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/dashboard/appointments/page.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppointmentsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/authStore */ \"(ssr)/./src/store/authStore.ts\");\n/* harmony import */ var _store_dataStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/dataStore */ \"(ssr)/./src/store/dataStore.ts\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(ssr)/./src/hooks/useTranslation.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,CheckCircle,Clock,Filter,Phone,Plus,Search,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,CheckCircle,Clock,Filter,Phone,Plus,Search,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,CheckCircle,Clock,Filter,Phone,Plus,Search,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,CheckCircle,Clock,Filter,Phone,Plus,Search,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,CheckCircle,Clock,Filter,Phone,Plus,Search,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,CheckCircle,Clock,Filter,Phone,Plus,Search,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,CheckCircle,Clock,Filter,Phone,Plus,Search,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,CheckCircle,Clock,Filter,Phone,Plus,Search,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,CheckCircle,Clock,Filter,Phone,Plus,Search,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction AppointmentsPage() {\n    const { user } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_4__.useAuthStore)();\n    const { appointments, updateAppointment, deleteAppointment } = (0,_store_dataStore__WEBPACK_IMPORTED_MODULE_5__.useDataStore)();\n    const { t, isArabic } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // التحقق من الصلاحيات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentsPage.useEffect\": ()=>{\n            if (!user) {\n                router.push('/login');\n            } else if (user.role !== 'admin') {\n                router.push('/dashboard');\n            }\n        }\n    }[\"AppointmentsPage.useEffect\"], [\n        user,\n        router\n    ]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [dateFilter, setDateFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const getStatusInfo = (status)=>{\n        const statusMap = {\n            pending: {\n                label: t('pending'),\n                color: 'text-blue-600',\n                bgColor: 'bg-blue-100',\n                icon: _barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n            },\n            confirmed: {\n                label: t('confirmed'),\n                color: 'text-green-600',\n                bgColor: 'bg-green-100',\n                icon: _barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n            },\n            completed: {\n                label: t('completed'),\n                color: 'text-purple-600',\n                bgColor: 'bg-purple-100',\n                icon: _barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n            },\n            cancelled: {\n                label: t('cancelled'),\n                color: 'text-red-600',\n                bgColor: 'bg-red-100',\n                icon: _barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n            }\n        };\n        return statusMap[status] || statusMap.pending;\n    };\n    // وظائف إدارة المواعيد\n    const handleConfirmAppointment = (id)=>{\n        updateAppointment(id, {\n            status: 'confirmed'\n        });\n    };\n    const handleCompleteAppointment = (id)=>{\n        updateAppointment(id, {\n            status: 'completed'\n        });\n    };\n    const handleCancelAppointment = (id)=>{\n        updateAppointment(id, {\n            status: 'cancelled'\n        });\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleDateString('ar-US', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n    const formatTime = (timeString)=>{\n        const [hours, minutes] = timeString.split(':');\n        const hour = parseInt(hours);\n        const ampm = hour >= 12 ? t('pm') : t('am');\n        const displayHour = hour > 12 ? hour - 12 : hour === 0 ? 12 : hour;\n        return `${displayHour}:${minutes} ${ampm}`;\n    };\n    const isToday = (dateString)=>{\n        const today = new Date().toISOString().split('T')[0];\n        return dateString === today;\n    };\n    const isTomorrow = (dateString)=>{\n        const tomorrow = new Date();\n        tomorrow.setDate(tomorrow.getDate() + 1);\n        return dateString === tomorrow.toISOString().split('T')[0];\n    };\n    const filteredAppointments = appointments.filter((appointment)=>{\n        const matchesSearch = appointment.clientName.toLowerCase().includes(searchTerm.toLowerCase()) || appointment.clientPhone.includes(searchTerm) || appointment.id.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesStatus = statusFilter === 'all' || appointment.status === statusFilter;\n        let matchesDate = true;\n        if (dateFilter === 'today') {\n            matchesDate = isToday(appointment.appointmentDate);\n        } else if (dateFilter === 'tomorrow') {\n            matchesDate = isTomorrow(appointment.appointmentDate);\n        } else if (dateFilter === 'week') {\n            const appointmentDate = new Date(appointment.appointmentDate);\n            const today = new Date();\n            const weekFromNow = new Date();\n            weekFromNow.setDate(today.getDate() + 7);\n            matchesDate = appointmentDate >= today && appointmentDate <= weekFromNow;\n        }\n        return matchesSearch && matchesStatus && matchesDate;\n    });\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: t('loading')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                lineNumber: 121,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/dashboard\",\n                        className: \"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: t('back_to_dashboard')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl sm:text-4xl font-bold mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                                        children: t('appointments_management')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600\",\n                                    children: t('view_manage_appointments')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/book-appointment\",\n                            className: \"btn-primary inline-flex items-center justify-center space-x-2 space-x-reverse px-6 py-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-5 h-5 group-hover:scale-110 transition-transform duration-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t('book_new_appointment')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.2\n                    },\n                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300\",\n                                        placeholder: t('search_appointments_placeholder')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: statusFilter,\n                                        onChange: (e)=>setStatusFilter(e.target.value),\n                                        className: \"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: t('all_statuses')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pending\",\n                                                children: t('pending')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"confirmed\",\n                                                children: t('confirmed')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"completed\",\n                                                children: t('completed')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"cancelled\",\n                                                children: t('cancelled')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: dateFilter,\n                                        onChange: (e)=>setDateFilter(e.target.value),\n                                        className: \"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: t('all_dates')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"today\",\n                                                children: t('today')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"tomorrow\",\n                                                children: t('tomorrow')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"week\",\n                                                children: t('this_week')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.4\n                    },\n                    className: \"space-y-6\",\n                    children: filteredAppointments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12 bg-white/80 backdrop-blur-sm rounded-2xl border border-pink-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-medium text-gray-600 mb-2\",\n                                children: t('no_appointments')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: t('no_appointments_found')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 13\n                    }, this) : filteredAppointments.map((appointment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: index * 0.1\n                            },\n                            className: `bg-white/80 backdrop-blur-sm rounded-2xl p-6 border transition-all duration-300 hover:shadow-lg ${isToday(appointment.appointmentDate) ? 'border-pink-300 bg-pink-50/50' : 'border-pink-100'}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold text-gray-800 mb-1\",\n                                                                children: appointment.clientName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1 text-sm text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2 space-x-reverse\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                                                lineNumber: 264,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: appointment.clientPhone\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                                                lineNumber: 265,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                                        lineNumber: 263,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"#\",\n                                                                            appointment.id\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                                        lineNumber: 267,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 space-x-reverse\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `px-3 py-1 rounded-full text-sm font-medium ${getStatusInfo(appointment.status).bgColor} ${getStatusInfo(appointment.status).color}`,\n                                                                children: getStatusInfo(appointment.status).label\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            isToday(appointment.appointmentDate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs font-medium\",\n                                                                children: t('today')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 21\n                                            }, this),\n                                            appointment.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 rounded-lg p-3 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-700\",\n                                                    children: appointment.notes\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-1\",\n                                                        children: t('date_time')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 space-x-reverse text-sm text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatDate(appointment.appointmentDate)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 space-x-reverse text-sm text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_CheckCircle_Clock_Filter_Phone_Plus_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatTime(appointment.appointmentTime)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        t('created_on'),\n                                                        \" \",\n                                                        new Date(appointment.createdAt).toLocaleDateString(isArabic ? 'ar-US' : 'en-US')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-2\",\n                                        children: [\n                                            appointment.status === 'pending' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleConfirmAppointment(appointment.id),\n                                                className: \"btn-primary py-2 px-4 text-sm\",\n                                                children: t('confirm_appointment')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 23\n                                            }, this),\n                                            appointment.status === 'confirmed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleCompleteAppointment(appointment.id),\n                                                className: \"btn-secondary py-2 px-4 text-sm\",\n                                                children: t('mark_attended')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 23\n                                            }, this),\n                                            appointment.status !== 'cancelled' && appointment.status !== 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleCancelAppointment(appointment.id),\n                                                className: \"text-red-600 hover:text-red-700 py-2 px-4 text-sm border border-red-200 rounded-lg hover:bg-red-50 transition-all duration-300\",\n                                                children: t('cancel_appointment')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 17\n                            }, this)\n                        }, appointment.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.6\n                    },\n                    className: \"mt-12 grid grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-600 mb-1\",\n                                    children: appointments.filter((a)=>a.status === 'pending').length\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('pending')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-green-600 mb-1\",\n                                    children: appointments.filter((a)=>a.status === 'confirmed').length\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('confirmed')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-orange-600 mb-1\",\n                                    children: appointments.filter((a)=>isToday(a.appointmentDate)).length\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('today')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-purple-600 mb-1\",\n                                    children: appointments.filter((a)=>a.status === 'completed').length\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: t('completed')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/appointments/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useTranslation.ts":
/*!*************************************!*\
  !*** ./src/hooks/useTranslation.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useTranslation auto */ \n// الترجمات العربية\nconst arTranslations = {\n    // التنقل والعناوين الرئيسية\n    'dashboard': 'لوحة التحكم',\n    'orders': 'الطلبات',\n    'appointments': 'المواعيد',\n    'settings': 'الإعدادات',\n    'workers': 'العمال',\n    'reports': 'التقارير',\n    'logout': 'تسجيل الخروج',\n    'welcome': 'مرحباً',\n    'welcome_back': 'مرحباً بعودتك',\n    // الأزرار والإجراءات\n    'add_new_order': 'إضافة طلب جديد',\n    'book_appointment': 'حجز موعد',\n    'view_details': 'عرض التفاصيل',\n    'edit': 'تعديل',\n    'delete': 'حذف',\n    'save': 'حفظ',\n    'cancel': 'إلغاء',\n    'submit': 'إرسال',\n    'search': 'بحث',\n    'filter': 'تصفية',\n    'export': 'تصدير',\n    'print': 'طباعة',\n    'back': 'رجوع',\n    'next': 'التالي',\n    'previous': 'السابق',\n    'close': 'إغلاق',\n    'confirm': 'تأكيد',\n    'loading': 'جاري التحميل...',\n    'saving': 'جاري الحفظ...',\n    // حالات الطلبات\n    'pending': 'في الانتظار',\n    'in_progress': 'قيد التنفيذ',\n    'completed': 'مكتمل',\n    'delivered': 'تم التسليم',\n    'cancelled': 'ملغي',\n    // نصوص عامة\n    'name': 'الاسم',\n    'email': 'البريد الإلكتروني',\n    'phone': 'رقم الهاتف',\n    'address': 'العنوان',\n    'date': 'التاريخ',\n    'time': 'الوقت',\n    'status': 'الحالة',\n    'price': 'السعر',\n    'total': 'المجموع',\n    'description': 'الوصف',\n    'notes': 'ملاحظات',\n    'client_name': 'اسم الزبونة',\n    'client_phone': 'رقم هاتف الزبونة',\n    // رسائل النجاح والخطأ\n    'success': 'نجح',\n    'error': 'خطأ',\n    'warning': 'تحذير',\n    'info': 'معلومات',\n    'order_added_success': 'تم إضافة الطلب بنجاح',\n    'order_updated_success': 'تم تحديث الطلب بنجاح',\n    'order_deleted_success': 'تم حذف الطلب بنجاح',\n    'fill_required_fields': 'يرجى ملء جميع الحقول المطلوبة',\n    // نصوص إضافية مطلوبة\n    'admin_dashboard': 'لوحة تحكم المدير',\n    'worker_dashboard': 'لوحة تحكم العامل',\n    'admin': 'مدير',\n    'worker': 'عامل',\n    'change_language': 'تغيير اللغة',\n    'my_active_orders': 'طلباتي النشطة',\n    'completed_orders': 'الطلبات المكتملة',\n    'total_orders': 'إجمالي الطلبات',\n    'total_revenue': 'إجمالي الإيرادات',\n    'recent_orders': 'الطلبات الحديثة',\n    'quick_actions': 'إجراءات سريعة',\n    'view_all_orders': 'عرض جميع الطلبات',\n    'add_order': 'إضافة طلب',\n    'manage_workers': 'إدارة العمال',\n    'view_reports': 'عرض التقارير',\n    'client_name_required': 'اسم الزبونة *',\n    'phone_required': 'رقم الهاتف *',\n    'order_description_required': 'وصف الطلب *',\n    'delivery_date_required': 'موعد التسليم *',\n    'price_sar': 'السعر (ريال سعودي)',\n    'measurements_cm': 'المقاسات (بالسنتيمتر)',\n    'additional_notes': 'ملاحظات إضافية',\n    'voice_notes_optional': 'ملاحظات صوتية (اختيارية)',\n    'design_images': 'صور التصميم',\n    'fabric_type': 'نوع القماش',\n    'fabric_type_optional': 'نوع القماش',\n    'responsible_worker': 'العامل المسؤول',\n    'choose_worker': 'اختر العامل المسؤول',\n    'status_and_worker': 'الحالة والعامل',\n    'order_status': 'حالة الطلب',\n    'additional_notes_placeholder': 'أي ملاحظات أو تفاصيل إضافية...',\n    'not_specified': 'غير محدد',\n    'back_to_dashboard': 'العودة إلى لوحة التحكم',\n    'overview_today': 'نظرة عامة على أنشطة اليوم',\n    'welcome_worker': 'مرحباً بك في مساحة العمل',\n    // مفاتيح مفقودة من لوحة التحكم\n    'homepage': 'الصفحة الرئيسية',\n    'my_completed_orders': 'طلباتي المكتملة',\n    'my_total_orders': 'إجمالي طلباتي',\n    'active_orders': 'الطلبات النشطة',\n    'today_appointments': 'مواعيد اليوم',\n    'statistics': 'الإحصائيات',\n    'no_orders_found': 'لا توجد طلبات',\n    'view_all': 'عرض الكل',\n    'worker_management': 'إدارة العمال',\n    'reminder': 'تذكير',\n    'you_have': 'لديك',\n    'today_appointments_reminder': 'موعد اليوم',\n    'and': 'و',\n    'orders_need_follow': 'طلبات تحتاج متابعة',\n    'detailed_reports': 'تقارير مفصلة',\n    'worker_description': 'يمكنك هنا متابعة طلباتك المخصصة لك وتحديث حالتها',\n    // مفاتيح صفحة إضافة الطلبات\n    'order_add_error': 'حدث خطأ أثناء إضافة الطلب',\n    'cm_placeholder': 'سم',\n    'shoulder': 'الكتف',\n    'shoulder_circumference': 'محيط الكتف',\n    'chest': 'الصدر',\n    'waist': 'الخصر',\n    'hips': 'الأرداف',\n    'dart_length': 'طول الخياطة',\n    'bodice_length': 'طول الجسم',\n    'neckline': 'خط الرقبة',\n    'armpit': 'الإبط',\n    'sleeve_length': 'طول الكم',\n    'forearm': 'الساعد',\n    'cuff': 'الكم',\n    'front_length': 'الطول الأمامي',\n    'back_length': 'الطول الخلفي',\n    // مفاتيح صفحة الطلبات\n    'enter_order_number': 'أدخل رقم الطلب',\n    'search_placeholder': 'البحث بالاسم أو رقم الطلب أو الوصف...',\n    'search_by_text': 'البحث بالنص',\n    'search_by_order_number': 'البحث برقم الطلب',\n    'all_orders': 'جميع الطلبات',\n    'no_orders_assigned': 'لا توجد طلبات مخصصة لك',\n    'no_orders_assigned_desc': 'لم يتم تخصيص أي طلبات لك بعد',\n    'no_orders_found_desc': 'لا توجد طلبات مطابقة لمعايير البحث',\n    'price_label': 'السعر',\n    'sar': 'ريال',\n    'view': 'عرض',\n    'completing': 'جاري الإنهاء...',\n    'start_work': 'بدء العمل',\n    'complete_order': 'إنهاء الطلب',\n    'complete_order_modal_title': 'إنهاء الطلب وتحميل صور العمل المكتمل',\n    'important_warning': 'تحذير مهم',\n    'complete_order_warning': 'بمجرد إنهاء الطلب، لن تتمكن من تعديل حالته مرة أخرى. تأكد من تحميل جميع صور العمل المكتمل قبل المتابعة.',\n    'order_deleted_successfully': 'تم حذف الطلب بنجاح',\n    // مفاتيح مكون حذف الطلب\n    'confirm_delete_order': 'تأكيد حذف الطلب',\n    'warning_delete_order': 'تحذير: حذف الطلب',\n    'delete_order_warning_message': 'لا يمكن التراجع عن هذا الإجراء. سيتم حذف الطلب وجميع البيانات المرتبطة به نهائياً.',\n    'admin_email': 'بريد المدير الإلكتروني',\n    'admin_password': 'كلمة مرور المدير',\n    'enter_admin_email': 'أدخل بريد المدير الإلكتروني',\n    'enter_admin_password': 'أدخل كلمة مرور المدير',\n    'please_fill_all_fields': 'يرجى ملء جميع الحقول',\n    'email_does_not_match': 'البريد الإلكتروني لا يطابق بريد المدير المسجل',\n    'incorrect_password': 'كلمة المرور غير صحيحة',\n    'confirm_delete': 'تأكيد الحذف',\n    // مفاتيح مكون الملاحظات الصوتية\n    'start_recording': 'بدء التسجيل',\n    'stop_recording': 'إيقاف التسجيل',\n    'click_to_record_voice_note': 'انقر لتسجيل ملاحظة صوتية',\n    'voice_notes': 'الملاحظات الصوتية',\n    'voice_note': 'ملاحظة صوتية',\n    'microphone_access_error': 'خطأ في الوصول للميكروفون',\n    // مفاتيح صفحة العمال\n    'worker_added_success': 'تم إضافة العامل بنجاح',\n    'error_adding_worker': 'حدث خطأ أثناء إضافة العامل',\n    'worker_updated_success': 'تم تحديث العامل بنجاح',\n    'error_updating_worker': 'حدث خطأ أثناء تحديث العامل',\n    'worker_deleted_success': 'تم حذف العامل بنجاح',\n    'worker_deactivated': 'تم إلغاء تفعيل العامل',\n    'worker_activated': 'تم تفعيل العامل',\n    'adding': 'جاري الإضافة...',\n    'add_worker': 'إضافة عامل',\n    'active': 'نشط',\n    'inactive': 'غير نشط',\n    'save_changes': 'حفظ التغييرات',\n    'search_workers_placeholder': 'البحث عن العمال...',\n    'workers_management': 'إدارة العمال',\n    'view_manage_team': 'عرض وإدارة فريق العمل في ورشة التفصيل',\n    'add_new_worker': 'إضافة عامل جديد',\n    'add_new_worker_form': 'إضافة عامل جديد',\n    'full_name_required': 'الاسم الكامل *',\n    'email_required': 'البريد الإلكتروني *',\n    'password_required': 'كلمة المرور *',\n    'phone_required_worker': 'رقم الهاتف *',\n    'enter_full_name': 'أدخل الاسم الكامل',\n    'enter_email': 'أدخل البريد الإلكتروني',\n    'enter_password': 'أدخل كلمة المرور',\n    'enter_phone': 'أدخل رقم الهاتف',\n    'specialty_required': 'التخصص *',\n    'specialty_example': 'مثال: خياطة فساتين السهرة',\n    'edit_worker': 'تعديل العامل',\n    'new_password': 'كلمة المرور الجديدة',\n    'leave_empty_no_change': 'اتركه فارغاً إذا لم ترد تغييره',\n    'no_workers': 'لا يوجد عمال',\n    'no_workers_found': 'لا يوجد عمال مطابقين لمعايير البحث',\n    'joined_on': 'انضم في',\n    'total_workers': 'إجمالي العمال',\n    'active_workers': 'العمال النشطون',\n    'total_completed_orders': 'إجمالي الطلبات المكتملة',\n    'confirm_delete_worker': 'هل أنت متأكد من حذف هذا العامل؟',\n    // مفاتيح صفحة التقارير\n    'engagement_dress': 'فستان خطوبة',\n    'casual_dress': 'فستان يومي',\n    'other': 'أخرى',\n    'this_week': 'هذا الأسبوع',\n    'this_month': 'هذا الشهر',\n    'this_quarter': 'هذا الربع',\n    'this_year': 'هذا العام',\n    'reports_analytics': 'التقارير والتحليلات',\n    'monthly_trend': 'الاتجاه الشهري',\n    'revenue_label': 'الإيرادات',\n    // مفاتيح صفحة المواعيد\n    'confirmed': 'مؤكد',\n    'pm': 'مساءً',\n    'am': 'صباحاً',\n    'all_statuses': 'جميع الحالات',\n    'all_dates': 'جميع التواريخ',\n    'today': 'اليوم',\n    'tomorrow': 'غداً',\n    'appointments_management': 'إدارة المواعيد',\n    'view_manage_appointments': 'عرض وإدارة جميع مواعيد التفصيل',\n    'book_new_appointment': 'حجز موعد جديد',\n    'search_appointments_placeholder': 'البحث في المواعيد...',\n    'no_appointments': 'لا توجد مواعيد',\n    'no_appointments_found': 'لا توجد مواعيد مطابقة لمعايير البحث',\n    'created_on': 'تم الإنشاء في',\n    'confirm_appointment': 'تأكيد الموعد',\n    'cancel_appointment': 'إلغاء الموعد',\n    // مفاتيح مكونات إضافية\n    'of': 'من',\n    'images_text': 'صور',\n    // مفاتيح مكونات الصور والتحميل\n    'max_images_reached': 'تم الوصول للحد الأقصى من الصور',\n    'drop_images_here': 'اسقط الصور هنا',\n    'click_or_drag_images': 'انقر أو اسحب الصور هنا',\n    'image_upload_format': 'PNG, JPG, JPEG حتى 5MB',\n    'max_images_text': 'الحد الأقصى',\n    'order_label': 'الطلب',\n    'for_client': 'للزبونة',\n    // مفاتيح مكون تعديل الطلب\n    'order_update_error': 'حدث خطأ أثناء تحديث الطلب',\n    'price_sar_required': 'السعر (ريال سعودي) *',\n    'status_pending': 'في الانتظار',\n    'status_in_progress': 'قيد التنفيذ',\n    'status_completed': 'مكتمل',\n    'status_delivered': 'تم التسليم',\n    'status_cancelled': 'ملغي',\n    // نصوص الفوتر\n    'home': 'الرئيسية',\n    'track_order': 'استعلام عن الطلب',\n    'fabrics': 'الأقمشة',\n    'contact_us': 'تواصلي معنا',\n    'yasmin_alsham': 'ياسمين الشام',\n    'custom_dress_tailoring': 'تفصيل فساتين حسب الطلب'\n};\n// الترجمات الإنجليزية\nconst enTranslations = {\n    // التنقل والعناوين الرئيسية\n    'dashboard': 'Dashboard',\n    'orders': 'Orders',\n    'appointments': 'Appointments',\n    'settings': 'Settings',\n    'workers': 'Workers',\n    'reports': 'Reports',\n    'logout': 'Logout',\n    'welcome': 'Welcome',\n    'welcome_back': 'Welcome Back',\n    // الأزرار والإجراءات\n    'add_new_order': 'Add New Order',\n    'book_appointment': 'Book Appointment',\n    'view_details': 'View Details',\n    'edit': 'Edit',\n    'delete': 'Delete',\n    'save': 'Save',\n    'cancel': 'Cancel',\n    'submit': 'Submit',\n    'search': 'Search',\n    'filter': 'Filter',\n    'export': 'Export',\n    'print': 'Print',\n    'back': 'Back',\n    'next': 'Next',\n    'previous': 'Previous',\n    'close': 'Close',\n    'confirm': 'Confirm',\n    'loading': 'Loading...',\n    'saving': 'Saving...',\n    // حالات الطلبات\n    'pending': 'Pending',\n    'in_progress': 'In Progress',\n    'completed': 'Completed',\n    'delivered': 'Delivered',\n    'cancelled': 'Cancelled',\n    // نصوص عامة\n    'name': 'Name',\n    'email': 'Email',\n    'phone': 'Phone',\n    'address': 'Address',\n    'date': 'Date',\n    'time': 'Time',\n    'status': 'Status',\n    'price': 'Price',\n    'total': 'Total',\n    'description': 'Description',\n    'notes': 'Notes',\n    'client_name': 'Client Name',\n    'client_phone': 'Client Phone',\n    // رسائل النجاح والخطأ\n    'success': 'Success',\n    'error': 'Error',\n    'warning': 'Warning',\n    'info': 'Info',\n    'order_added_success': 'Order added successfully',\n    'order_updated_success': 'Order updated successfully',\n    'order_deleted_success': 'Order deleted successfully',\n    'fill_required_fields': 'Please fill all required fields',\n    // نصوص إضافية مطلوبة\n    'admin_dashboard': 'Admin Dashboard',\n    'worker_dashboard': 'Worker Dashboard',\n    'admin': 'Admin',\n    'worker': 'Worker',\n    'change_language': 'Change Language',\n    'my_active_orders': 'My Active Orders',\n    'completed_orders': 'Completed Orders',\n    'total_orders': 'Total Orders',\n    'total_revenue': 'Total Revenue',\n    'recent_orders': 'Recent Orders',\n    'quick_actions': 'Quick Actions',\n    'view_all_orders': 'View All Orders',\n    'add_order': 'Add Order',\n    'manage_workers': 'Manage Workers',\n    'view_reports': 'View Reports',\n    'client_name_required': 'Client Name *',\n    'phone_required': 'Phone Number *',\n    'order_description_required': 'Order Description *',\n    'delivery_date_required': 'Delivery Date *',\n    'price_sar': 'Price (SAR)',\n    'measurements_cm': 'Measurements (cm)',\n    'additional_notes': 'Additional Notes',\n    'voice_notes_optional': 'Voice Notes (Optional)',\n    'design_images': 'Design Images',\n    'fabric_type': 'Fabric Type',\n    'fabric_type_optional': 'Fabric Type',\n    'responsible_worker': 'Responsible Worker',\n    'choose_worker': 'Choose Responsible Worker',\n    'status_and_worker': 'Status and Worker',\n    'order_status': 'Order Status',\n    'additional_notes_placeholder': 'Any additional notes or details...',\n    'not_specified': 'Not Specified',\n    'back_to_dashboard': 'Back to Dashboard',\n    'overview_today': 'Overview of today\\'s activities',\n    'welcome_worker': 'Welcome to your workspace',\n    // مفاتيح مفقودة من لوحة التحكم\n    'homepage': 'Homepage',\n    'my_completed_orders': 'My Completed Orders',\n    'my_total_orders': 'My Total Orders',\n    'active_orders': 'Active Orders',\n    'today_appointments': 'Today\\'s Appointments',\n    'statistics': 'Statistics',\n    'no_orders_found': 'No orders found',\n    'view_all': 'View All',\n    'worker_management': 'Worker Management',\n    'reminder': 'Reminder',\n    'you_have': 'You have',\n    'today_appointments_reminder': 'appointments today',\n    'and': 'and',\n    'orders_need_follow': 'orders that need follow-up',\n    'detailed_reports': 'Detailed Reports',\n    'worker_description': 'Here you can track your assigned orders and update their status',\n    // مفاتيح صفحة إضافة الطلبات\n    'order_add_error': 'An error occurred while adding the order',\n    'cm_placeholder': 'cm',\n    'shoulder': 'Shoulder',\n    'shoulder_circumference': 'Shoulder Circumference',\n    'chest': 'Chest',\n    'waist': 'Waist',\n    'hips': 'Hips',\n    'dart_length': 'Dart Length',\n    'bodice_length': 'Bodice Length',\n    'neckline': 'Neckline',\n    'armpit': 'Armpit',\n    'sleeve_length': 'Sleeve Length',\n    'forearm': 'Forearm',\n    'cuff': 'Cuff',\n    'front_length': 'Front Length',\n    'back_length': 'Back Length',\n    // مفاتيح صفحة الطلبات\n    'enter_order_number': 'Enter order number',\n    'search_placeholder': 'Search by name, order number, or description...',\n    'search_by_text': 'Search by Text',\n    'search_by_order_number': 'Search by Order Number',\n    'all_orders': 'All Orders',\n    'no_orders_assigned': 'No orders assigned to you',\n    'no_orders_assigned_desc': 'No orders have been assigned to you yet',\n    'no_orders_found_desc': 'No orders found matching the search criteria',\n    'price_label': 'Price',\n    'sar': 'SAR',\n    'view': 'View',\n    'completing': 'Completing...',\n    'start_work': 'Start Work',\n    'complete_order': 'Complete Order',\n    'complete_order_modal_title': 'Complete Order and Upload Finished Work Images',\n    'important_warning': 'Important Warning',\n    'complete_order_warning': 'Once you complete the order, you will not be able to modify its status again. Make sure to upload all finished work images before proceeding.',\n    'order_deleted_successfully': 'Order deleted successfully',\n    // مفاتيح مكون حذف الطلب\n    'confirm_delete_order': 'Confirm Delete Order',\n    'warning_delete_order': 'Warning: Delete Order',\n    'delete_order_warning_message': 'This action cannot be undone. The order and all associated data will be permanently deleted.',\n    'admin_email': 'Admin Email',\n    'admin_password': 'Admin Password',\n    'enter_admin_email': 'Enter admin email',\n    'enter_admin_password': 'Enter admin password',\n    'please_fill_all_fields': 'Please fill all fields',\n    'email_does_not_match': 'Email does not match the registered admin email',\n    'incorrect_password': 'Incorrect password',\n    'confirm_delete': 'Confirm Delete',\n    // مفاتيح مكون الملاحظات الصوتية\n    'start_recording': 'Start Recording',\n    'stop_recording': 'Stop Recording',\n    'click_to_record_voice_note': 'Click to record a voice note',\n    'voice_notes': 'Voice Notes',\n    'voice_note': 'Voice Note',\n    'microphone_access_error': 'Microphone access error',\n    // مفاتيح صفحة العمال\n    'worker_added_success': 'Worker added successfully',\n    'error_adding_worker': 'Error adding worker',\n    'worker_updated_success': 'Worker updated successfully',\n    'error_updating_worker': 'Error updating worker',\n    'worker_deleted_success': 'Worker deleted successfully',\n    'worker_deactivated': 'Worker deactivated',\n    'worker_activated': 'Worker activated',\n    'adding': 'Adding...',\n    'add_worker': 'Add Worker',\n    'active': 'Active',\n    'inactive': 'Inactive',\n    'save_changes': 'Save Changes',\n    'search_workers_placeholder': 'Search workers...',\n    'workers_management': 'Workers Management',\n    'view_manage_team': 'View and manage the tailoring workshop team',\n    'add_new_worker': 'Add New Worker',\n    'add_new_worker_form': 'Add New Worker',\n    'full_name_required': 'Full Name *',\n    'email_required': 'Email *',\n    'password_required': 'Password *',\n    'phone_required_worker': 'Phone Number *',\n    'enter_full_name': 'Enter full name',\n    'enter_email': 'Enter email',\n    'enter_password': 'Enter password',\n    'enter_phone': 'Enter phone number',\n    'specialty_required': 'Specialty *',\n    'specialty_example': 'Example: Evening dress tailoring',\n    'edit_worker': 'Edit Worker',\n    'new_password': 'New Password',\n    'leave_empty_no_change': 'Leave empty if you don\\'t want to change it',\n    'no_workers': 'No workers',\n    'no_workers_found': 'No workers found matching the search criteria',\n    'joined_on': 'Joined on',\n    'total_workers': 'Total Workers',\n    'active_workers': 'Active Workers',\n    'total_completed_orders': 'Total Completed Orders',\n    'confirm_delete_worker': 'Are you sure you want to delete this worker?',\n    // مفاتيح صفحة التقارير\n    'engagement_dress': 'Engagement Dress',\n    'casual_dress': 'Casual Dress',\n    'other': 'Other',\n    'this_week': 'This Week',\n    'this_month': 'This Month',\n    'this_quarter': 'This Quarter',\n    'this_year': 'This Year',\n    'reports_analytics': 'Reports & Analytics',\n    'monthly_trend': 'Monthly Trend',\n    'revenue_label': 'Revenue',\n    // مفاتيح صفحة المواعيد\n    'confirmed': 'Confirmed',\n    'pm': 'PM',\n    'am': 'AM',\n    'all_statuses': 'All Statuses',\n    'all_dates': 'All Dates',\n    'today': 'Today',\n    'tomorrow': 'Tomorrow',\n    'appointments_management': 'Appointments Management',\n    'view_manage_appointments': 'View and manage all tailoring appointments',\n    'book_new_appointment': 'Book New Appointment',\n    'search_appointments_placeholder': 'Search appointments...',\n    'no_appointments': 'No appointments',\n    'no_appointments_found': 'No appointments found matching the search criteria',\n    'created_on': 'Created on',\n    'confirm_appointment': 'Confirm Appointment',\n    'cancel_appointment': 'Cancel Appointment',\n    // مفاتيح مكونات إضافية\n    'of': 'of',\n    'images_text': 'images',\n    // مفاتيح مكونات الصور والتحميل\n    'max_images_reached': 'Maximum images reached',\n    'drop_images_here': 'Drop images here',\n    'click_or_drag_images': 'Click or drag images here',\n    'image_upload_format': 'PNG, JPG, JPEG up to 5MB',\n    'max_images_text': 'Maximum',\n    'order_label': 'Order',\n    'for_client': 'For client',\n    // مفاتيح مكون تعديل الطلب\n    'order_update_error': 'Error updating order',\n    'price_sar_required': 'Price (SAR) *',\n    'status_pending': 'Pending',\n    'status_in_progress': 'In Progress',\n    'status_completed': 'Completed',\n    'status_delivered': 'Delivered',\n    'status_cancelled': 'Cancelled',\n    // نصوص الفوتر\n    'home': 'Home',\n    'track_order': 'Track Order',\n    'fabrics': 'Fabrics',\n    'contact_us': 'Contact Us',\n    'yasmin_alsham': 'Yasmin Alsham',\n    'custom_dress_tailoring': 'Custom Dress Tailoring'\n};\n// Hook للترجمة\nfunction useTranslation() {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('ar');\n    // تحميل اللغة المحفوظة عند بدء التطبيق\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTranslation.useEffect\": ()=>{\n            const savedLanguage = localStorage.getItem('dashboard-language');\n            if (savedLanguage) {\n                setLanguage(savedLanguage);\n            }\n        }\n    }[\"useTranslation.useEffect\"], []);\n    // حفظ اللغة عند تغييرها\n    const changeLanguage = (newLanguage)=>{\n        setLanguage(newLanguage);\n        localStorage.setItem('dashboard-language', newLanguage);\n    };\n    // دالة الترجمة\n    const t = (key)=>{\n        const translations = language === 'ar' ? arTranslations : enTranslations;\n        const translation = translations[key];\n        if (typeof translation === 'string') {\n            return translation;\n        }\n        // إذا لم توجد الترجمة، أرجع المفتاح نفسه\n        return key;\n    };\n    // التحقق من اللغة الحالية\n    const isArabic = language === 'ar';\n    const isEnglish = language === 'en';\n    return {\n        language,\n        changeLanguage,\n        t,\n        isArabic,\n        isEnglish\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlVHJhbnNsYXRpb24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O29FQUUyQztBQU8zQyxtQkFBbUI7QUFDbkIsTUFBTUUsaUJBQStCO0lBQ25DLDRCQUE0QjtJQUM1QixhQUFhO0lBQ2IsVUFBVTtJQUNWLGdCQUFnQjtJQUNoQixZQUFZO0lBQ1osV0FBVztJQUNYLFdBQVc7SUFDWCxVQUFVO0lBQ1YsV0FBVztJQUNYLGdCQUFnQjtJQUVoQixxQkFBcUI7SUFDckIsaUJBQWlCO0lBQ2pCLG9CQUFvQjtJQUNwQixnQkFBZ0I7SUFDaEIsUUFBUTtJQUNSLFVBQVU7SUFDVixRQUFRO0lBQ1IsVUFBVTtJQUNWLFVBQVU7SUFDVixVQUFVO0lBQ1YsVUFBVTtJQUNWLFVBQVU7SUFDVixTQUFTO0lBQ1QsUUFBUTtJQUNSLFFBQVE7SUFDUixZQUFZO0lBQ1osU0FBUztJQUNULFdBQVc7SUFDWCxXQUFXO0lBQ1gsVUFBVTtJQUVWLGdCQUFnQjtJQUNoQixXQUFXO0lBQ1gsZUFBZTtJQUNmLGFBQWE7SUFDYixhQUFhO0lBQ2IsYUFBYTtJQUViLFlBQVk7SUFDWixRQUFRO0lBQ1IsU0FBUztJQUNULFNBQVM7SUFDVCxXQUFXO0lBQ1gsUUFBUTtJQUNSLFFBQVE7SUFDUixVQUFVO0lBQ1YsU0FBUztJQUNULFNBQVM7SUFDVCxlQUFlO0lBQ2YsU0FBUztJQUNULGVBQWU7SUFDZixnQkFBZ0I7SUFFaEIsc0JBQXNCO0lBQ3RCLFdBQVc7SUFDWCxTQUFTO0lBQ1QsV0FBVztJQUNYLFFBQVE7SUFDUix1QkFBdUI7SUFDdkIseUJBQXlCO0lBQ3pCLHlCQUF5QjtJQUN6Qix3QkFBd0I7SUFFeEIscUJBQXFCO0lBQ3JCLG1CQUFtQjtJQUNuQixvQkFBb0I7SUFDcEIsU0FBUztJQUNULFVBQVU7SUFDVixtQkFBbUI7SUFDbkIsb0JBQW9CO0lBQ3BCLG9CQUFvQjtJQUNwQixnQkFBZ0I7SUFDaEIsaUJBQWlCO0lBQ2pCLGlCQUFpQjtJQUNqQixpQkFBaUI7SUFDakIsbUJBQW1CO0lBQ25CLGFBQWE7SUFDYixrQkFBa0I7SUFDbEIsZ0JBQWdCO0lBQ2hCLHdCQUF3QjtJQUN4QixrQkFBa0I7SUFDbEIsOEJBQThCO0lBQzlCLDBCQUEwQjtJQUMxQixhQUFhO0lBQ2IsbUJBQW1CO0lBQ25CLG9CQUFvQjtJQUNwQix3QkFBd0I7SUFDeEIsaUJBQWlCO0lBQ2pCLGVBQWU7SUFDZix3QkFBd0I7SUFDeEIsc0JBQXNCO0lBQ3RCLGlCQUFpQjtJQUNqQixxQkFBcUI7SUFDckIsZ0JBQWdCO0lBQ2hCLGdDQUFnQztJQUNoQyxpQkFBaUI7SUFDakIscUJBQXFCO0lBQ3JCLGtCQUFrQjtJQUNsQixrQkFBa0I7SUFFbEIsK0JBQStCO0lBQy9CLFlBQVk7SUFDWix1QkFBdUI7SUFDdkIsbUJBQW1CO0lBQ25CLGlCQUFpQjtJQUNqQixzQkFBc0I7SUFDdEIsY0FBYztJQUNkLG1CQUFtQjtJQUNuQixZQUFZO0lBQ1oscUJBQXFCO0lBQ3JCLFlBQVk7SUFDWixZQUFZO0lBQ1osK0JBQStCO0lBQy9CLE9BQU87SUFDUCxzQkFBc0I7SUFDdEIsb0JBQW9CO0lBQ3BCLHNCQUFzQjtJQUV0Qiw0QkFBNEI7SUFDNUIsbUJBQW1CO0lBQ25CLGtCQUFrQjtJQUNsQixZQUFZO0lBQ1osMEJBQTBCO0lBQzFCLFNBQVM7SUFDVCxTQUFTO0lBQ1QsUUFBUTtJQUNSLGVBQWU7SUFDZixpQkFBaUI7SUFDakIsWUFBWTtJQUNaLFVBQVU7SUFDVixpQkFBaUI7SUFDakIsV0FBVztJQUNYLFFBQVE7SUFDUixnQkFBZ0I7SUFDaEIsZUFBZTtJQUVmLHNCQUFzQjtJQUN0QixzQkFBc0I7SUFDdEIsc0JBQXNCO0lBQ3RCLGtCQUFrQjtJQUNsQiwwQkFBMEI7SUFDMUIsY0FBYztJQUNkLHNCQUFzQjtJQUN0QiwyQkFBMkI7SUFDM0Isd0JBQXdCO0lBQ3hCLGVBQWU7SUFDZixPQUFPO0lBQ1AsUUFBUTtJQUNSLGNBQWM7SUFDZCxjQUFjO0lBQ2Qsa0JBQWtCO0lBQ2xCLDhCQUE4QjtJQUM5QixxQkFBcUI7SUFDckIsMEJBQTBCO0lBQzFCLDhCQUE4QjtJQUU5Qix3QkFBd0I7SUFDeEIsd0JBQXdCO0lBQ3hCLHdCQUF3QjtJQUN4QixnQ0FBZ0M7SUFDaEMsZUFBZTtJQUNmLGtCQUFrQjtJQUNsQixxQkFBcUI7SUFDckIsd0JBQXdCO0lBQ3hCLDBCQUEwQjtJQUMxQix3QkFBd0I7SUFDeEIsc0JBQXNCO0lBQ3RCLGtCQUFrQjtJQUVsQixnQ0FBZ0M7SUFDaEMsbUJBQW1CO0lBQ25CLGtCQUFrQjtJQUNsQiw4QkFBOEI7SUFDOUIsZUFBZTtJQUNmLGNBQWM7SUFDZCwyQkFBMkI7SUFFM0IscUJBQXFCO0lBQ3JCLHdCQUF3QjtJQUN4Qix1QkFBdUI7SUFDdkIsMEJBQTBCO0lBQzFCLHlCQUF5QjtJQUN6QiwwQkFBMEI7SUFDMUIsc0JBQXNCO0lBQ3RCLG9CQUFvQjtJQUNwQixVQUFVO0lBQ1YsY0FBYztJQUNkLFVBQVU7SUFDVixZQUFZO0lBQ1osZ0JBQWdCO0lBQ2hCLDhCQUE4QjtJQUM5QixzQkFBc0I7SUFDdEIsb0JBQW9CO0lBQ3BCLGtCQUFrQjtJQUNsQix1QkFBdUI7SUFDdkIsc0JBQXNCO0lBQ3RCLGtCQUFrQjtJQUNsQixxQkFBcUI7SUFDckIseUJBQXlCO0lBQ3pCLG1CQUFtQjtJQUNuQixlQUFlO0lBQ2Ysa0JBQWtCO0lBQ2xCLGVBQWU7SUFDZixzQkFBc0I7SUFDdEIscUJBQXFCO0lBQ3JCLGVBQWU7SUFDZixnQkFBZ0I7SUFDaEIseUJBQXlCO0lBQ3pCLGNBQWM7SUFDZCxvQkFBb0I7SUFDcEIsYUFBYTtJQUNiLGlCQUFpQjtJQUNqQixrQkFBa0I7SUFDbEIsMEJBQTBCO0lBQzFCLHlCQUF5QjtJQUV6Qix1QkFBdUI7SUFDdkIsb0JBQW9CO0lBQ3BCLGdCQUFnQjtJQUNoQixTQUFTO0lBQ1QsYUFBYTtJQUNiLGNBQWM7SUFDZCxnQkFBZ0I7SUFDaEIsYUFBYTtJQUNiLHFCQUFxQjtJQUNyQixpQkFBaUI7SUFDakIsaUJBQWlCO0lBRWpCLHVCQUF1QjtJQUN2QixhQUFhO0lBQ2IsTUFBTTtJQUNOLE1BQU07SUFDTixnQkFBZ0I7SUFDaEIsYUFBYTtJQUNiLFNBQVM7SUFDVCxZQUFZO0lBQ1osMkJBQTJCO0lBQzNCLDRCQUE0QjtJQUM1Qix3QkFBd0I7SUFDeEIsbUNBQW1DO0lBQ25DLG1CQUFtQjtJQUNuQix5QkFBeUI7SUFDekIsY0FBYztJQUNkLHVCQUF1QjtJQUN2QixzQkFBc0I7SUFFdEIsdUJBQXVCO0lBQ3ZCLE1BQU07SUFDTixlQUFlO0lBRWYsK0JBQStCO0lBQy9CLHNCQUFzQjtJQUN0QixvQkFBb0I7SUFDcEIsd0JBQXdCO0lBQ3hCLHVCQUF1QjtJQUN2QixtQkFBbUI7SUFDbkIsZUFBZTtJQUNmLGNBQWM7SUFFZCwwQkFBMEI7SUFDMUIsc0JBQXNCO0lBQ3RCLHNCQUFzQjtJQUN0QixrQkFBa0I7SUFDbEIsc0JBQXNCO0lBQ3RCLG9CQUFvQjtJQUNwQixvQkFBb0I7SUFDcEIsb0JBQW9CO0lBRXBCLGNBQWM7SUFDZCxRQUFRO0lBQ1IsZUFBZTtJQUNmLFdBQVc7SUFDWCxjQUFjO0lBQ2QsaUJBQWlCO0lBQ2pCLDBCQUEwQjtBQUM1QjtBQUVBLHNCQUFzQjtBQUN0QixNQUFNQyxpQkFBK0I7SUFDbkMsNEJBQTRCO0lBQzVCLGFBQWE7SUFDYixVQUFVO0lBQ1YsZ0JBQWdCO0lBQ2hCLFlBQVk7SUFDWixXQUFXO0lBQ1gsV0FBVztJQUNYLFVBQVU7SUFDVixXQUFXO0lBQ1gsZ0JBQWdCO0lBRWhCLHFCQUFxQjtJQUNyQixpQkFBaUI7SUFDakIsb0JBQW9CO0lBQ3BCLGdCQUFnQjtJQUNoQixRQUFRO0lBQ1IsVUFBVTtJQUNWLFFBQVE7SUFDUixVQUFVO0lBQ1YsVUFBVTtJQUNWLFVBQVU7SUFDVixVQUFVO0lBQ1YsVUFBVTtJQUNWLFNBQVM7SUFDVCxRQUFRO0lBQ1IsUUFBUTtJQUNSLFlBQVk7SUFDWixTQUFTO0lBQ1QsV0FBVztJQUNYLFdBQVc7SUFDWCxVQUFVO0lBRVYsZ0JBQWdCO0lBQ2hCLFdBQVc7SUFDWCxlQUFlO0lBQ2YsYUFBYTtJQUNiLGFBQWE7SUFDYixhQUFhO0lBRWIsWUFBWTtJQUNaLFFBQVE7SUFDUixTQUFTO0lBQ1QsU0FBUztJQUNULFdBQVc7SUFDWCxRQUFRO0lBQ1IsUUFBUTtJQUNSLFVBQVU7SUFDVixTQUFTO0lBQ1QsU0FBUztJQUNULGVBQWU7SUFDZixTQUFTO0lBQ1QsZUFBZTtJQUNmLGdCQUFnQjtJQUVoQixzQkFBc0I7SUFDdEIsV0FBVztJQUNYLFNBQVM7SUFDVCxXQUFXO0lBQ1gsUUFBUTtJQUNSLHVCQUF1QjtJQUN2Qix5QkFBeUI7SUFDekIseUJBQXlCO0lBQ3pCLHdCQUF3QjtJQUV4QixxQkFBcUI7SUFDckIsbUJBQW1CO0lBQ25CLG9CQUFvQjtJQUNwQixTQUFTO0lBQ1QsVUFBVTtJQUNWLG1CQUFtQjtJQUNuQixvQkFBb0I7SUFDcEIsb0JBQW9CO0lBQ3BCLGdCQUFnQjtJQUNoQixpQkFBaUI7SUFDakIsaUJBQWlCO0lBQ2pCLGlCQUFpQjtJQUNqQixtQkFBbUI7SUFDbkIsYUFBYTtJQUNiLGtCQUFrQjtJQUNsQixnQkFBZ0I7SUFDaEIsd0JBQXdCO0lBQ3hCLGtCQUFrQjtJQUNsQiw4QkFBOEI7SUFDOUIsMEJBQTBCO0lBQzFCLGFBQWE7SUFDYixtQkFBbUI7SUFDbkIsb0JBQW9CO0lBQ3BCLHdCQUF3QjtJQUN4QixpQkFBaUI7SUFDakIsZUFBZTtJQUNmLHdCQUF3QjtJQUN4QixzQkFBc0I7SUFDdEIsaUJBQWlCO0lBQ2pCLHFCQUFxQjtJQUNyQixnQkFBZ0I7SUFDaEIsZ0NBQWdDO0lBQ2hDLGlCQUFpQjtJQUNqQixxQkFBcUI7SUFDckIsa0JBQWtCO0lBQ2xCLGtCQUFrQjtJQUVsQiwrQkFBK0I7SUFDL0IsWUFBWTtJQUNaLHVCQUF1QjtJQUN2QixtQkFBbUI7SUFDbkIsaUJBQWlCO0lBQ2pCLHNCQUFzQjtJQUN0QixjQUFjO0lBQ2QsbUJBQW1CO0lBQ25CLFlBQVk7SUFDWixxQkFBcUI7SUFDckIsWUFBWTtJQUNaLFlBQVk7SUFDWiwrQkFBK0I7SUFDL0IsT0FBTztJQUNQLHNCQUFzQjtJQUN0QixvQkFBb0I7SUFDcEIsc0JBQXNCO0lBRXRCLDRCQUE0QjtJQUM1QixtQkFBbUI7SUFDbkIsa0JBQWtCO0lBQ2xCLFlBQVk7SUFDWiwwQkFBMEI7SUFDMUIsU0FBUztJQUNULFNBQVM7SUFDVCxRQUFRO0lBQ1IsZUFBZTtJQUNmLGlCQUFpQjtJQUNqQixZQUFZO0lBQ1osVUFBVTtJQUNWLGlCQUFpQjtJQUNqQixXQUFXO0lBQ1gsUUFBUTtJQUNSLGdCQUFnQjtJQUNoQixlQUFlO0lBRWYsc0JBQXNCO0lBQ3RCLHNCQUFzQjtJQUN0QixzQkFBc0I7SUFDdEIsa0JBQWtCO0lBQ2xCLDBCQUEwQjtJQUMxQixjQUFjO0lBQ2Qsc0JBQXNCO0lBQ3RCLDJCQUEyQjtJQUMzQix3QkFBd0I7SUFDeEIsZUFBZTtJQUNmLE9BQU87SUFDUCxRQUFRO0lBQ1IsY0FBYztJQUNkLGNBQWM7SUFDZCxrQkFBa0I7SUFDbEIsOEJBQThCO0lBQzlCLHFCQUFxQjtJQUNyQiwwQkFBMEI7SUFDMUIsOEJBQThCO0lBRTlCLHdCQUF3QjtJQUN4Qix3QkFBd0I7SUFDeEIsd0JBQXdCO0lBQ3hCLGdDQUFnQztJQUNoQyxlQUFlO0lBQ2Ysa0JBQWtCO0lBQ2xCLHFCQUFxQjtJQUNyQix3QkFBd0I7SUFDeEIsMEJBQTBCO0lBQzFCLHdCQUF3QjtJQUN4QixzQkFBc0I7SUFDdEIsa0JBQWtCO0lBRWxCLGdDQUFnQztJQUNoQyxtQkFBbUI7SUFDbkIsa0JBQWtCO0lBQ2xCLDhCQUE4QjtJQUM5QixlQUFlO0lBQ2YsY0FBYztJQUNkLDJCQUEyQjtJQUUzQixxQkFBcUI7SUFDckIsd0JBQXdCO0lBQ3hCLHVCQUF1QjtJQUN2QiwwQkFBMEI7SUFDMUIseUJBQXlCO0lBQ3pCLDBCQUEwQjtJQUMxQixzQkFBc0I7SUFDdEIsb0JBQW9CO0lBQ3BCLFVBQVU7SUFDVixjQUFjO0lBQ2QsVUFBVTtJQUNWLFlBQVk7SUFDWixnQkFBZ0I7SUFDaEIsOEJBQThCO0lBQzlCLHNCQUFzQjtJQUN0QixvQkFBb0I7SUFDcEIsa0JBQWtCO0lBQ2xCLHVCQUF1QjtJQUN2QixzQkFBc0I7SUFDdEIsa0JBQWtCO0lBQ2xCLHFCQUFxQjtJQUNyQix5QkFBeUI7SUFDekIsbUJBQW1CO0lBQ25CLGVBQWU7SUFDZixrQkFBa0I7SUFDbEIsZUFBZTtJQUNmLHNCQUFzQjtJQUN0QixxQkFBcUI7SUFDckIsZUFBZTtJQUNmLGdCQUFnQjtJQUNoQix5QkFBeUI7SUFDekIsY0FBYztJQUNkLG9CQUFvQjtJQUNwQixhQUFhO0lBQ2IsaUJBQWlCO0lBQ2pCLGtCQUFrQjtJQUNsQiwwQkFBMEI7SUFDMUIseUJBQXlCO0lBRXpCLHVCQUF1QjtJQUN2QixvQkFBb0I7SUFDcEIsZ0JBQWdCO0lBQ2hCLFNBQVM7SUFDVCxhQUFhO0lBQ2IsY0FBYztJQUNkLGdCQUFnQjtJQUNoQixhQUFhO0lBQ2IscUJBQXFCO0lBQ3JCLGlCQUFpQjtJQUNqQixpQkFBaUI7SUFFakIsdUJBQXVCO0lBQ3ZCLGFBQWE7SUFDYixNQUFNO0lBQ04sTUFBTTtJQUNOLGdCQUFnQjtJQUNoQixhQUFhO0lBQ2IsU0FBUztJQUNULFlBQVk7SUFDWiwyQkFBMkI7SUFDM0IsNEJBQTRCO0lBQzVCLHdCQUF3QjtJQUN4QixtQ0FBbUM7SUFDbkMsbUJBQW1CO0lBQ25CLHlCQUF5QjtJQUN6QixjQUFjO0lBQ2QsdUJBQXVCO0lBQ3ZCLHNCQUFzQjtJQUV0Qix1QkFBdUI7SUFDdkIsTUFBTTtJQUNOLGVBQWU7SUFFZiwrQkFBK0I7SUFDL0Isc0JBQXNCO0lBQ3RCLG9CQUFvQjtJQUNwQix3QkFBd0I7SUFDeEIsdUJBQXVCO0lBQ3ZCLG1CQUFtQjtJQUNuQixlQUFlO0lBQ2YsY0FBYztJQUVkLDBCQUEwQjtJQUMxQixzQkFBc0I7SUFDdEIsc0JBQXNCO0lBQ3RCLGtCQUFrQjtJQUNsQixzQkFBc0I7SUFDdEIsb0JBQW9CO0lBQ3BCLG9CQUFvQjtJQUNwQixvQkFBb0I7SUFFcEIsY0FBYztJQUNkLFFBQVE7SUFDUixlQUFlO0lBQ2YsV0FBVztJQUNYLGNBQWM7SUFDZCxpQkFBaUI7SUFDakIsMEJBQTBCO0FBQzVCO0FBRUEsZUFBZTtBQUNSLFNBQVNDO0lBQ2QsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdOLCtDQUFRQSxDQUFjO0lBRXRELHVDQUF1QztJQUN2Q0MsZ0RBQVNBO29DQUFDO1lBQ1IsTUFBTU0sZ0JBQWdCQyxhQUFhQyxPQUFPLENBQUM7WUFDM0MsSUFBSUYsZUFBZTtnQkFDakJELFlBQVlDO1lBQ2Q7UUFDRjttQ0FBRyxFQUFFO0lBRUwsd0JBQXdCO0lBQ3hCLE1BQU1HLGlCQUFpQixDQUFDQztRQUN0QkwsWUFBWUs7UUFDWkgsYUFBYUksT0FBTyxDQUFDLHNCQUFzQkQ7SUFDN0M7SUFFQSxlQUFlO0lBQ2YsTUFBTUUsSUFBSSxDQUFDQztRQUNULE1BQU1DLGVBQWVWLGFBQWEsT0FBT0gsaUJBQWlCQztRQUMxRCxNQUFNYSxjQUFjRCxZQUFZLENBQUNELElBQUk7UUFFckMsSUFBSSxPQUFPRSxnQkFBZ0IsVUFBVTtZQUNuQyxPQUFPQTtRQUNUO1FBRUEseUNBQXlDO1FBQ3pDLE9BQU9GO0lBQ1Q7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTUcsV0FBV1osYUFBYTtJQUM5QixNQUFNYSxZQUFZYixhQUFhO0lBRS9CLE9BQU87UUFDTEE7UUFDQUs7UUFDQUc7UUFDQUk7UUFDQUM7SUFDRjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGtoYWxlXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFlBU01JTiBBTFNIQU1cXHlhc21pbi1hbHNoYW1cXHNyY1xcaG9va3NcXHVzZVRyYW5zbGF0aW9uLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xyXG5cclxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xyXG5cclxuLy8g2YbZiNi5INin2YTYqNmK2KfZhtin2Kog2YTZhNiq2LHYrNmF2KfYqlxyXG50eXBlIFRyYW5zbGF0aW9uS2V5ID0gc3RyaW5nXHJcbnR5cGUgVHJhbnNsYXRpb25WYWx1ZSA9IHN0cmluZyB8IHsgW2tleTogc3RyaW5nXTogc3RyaW5nIH1cclxudHlwZSBUcmFuc2xhdGlvbnMgPSB7IFtrZXk6IHN0cmluZ106IFRyYW5zbGF0aW9uVmFsdWUgfVxyXG5cclxuLy8g2KfZhNiq2LHYrNmF2KfYqiDYp9mE2LnYsdio2YrYqVxyXG5jb25zdCBhclRyYW5zbGF0aW9uczogVHJhbnNsYXRpb25zID0ge1xyXG4gIC8vINin2YTYqtmG2YLZhCDZiNin2YTYudmG2KfZiNmK2YYg2KfZhNix2KbZitiz2YrYqVxyXG4gICdkYXNoYm9hcmQnOiAn2YTZiNit2Kkg2KfZhNiq2K3Zg9mFJyxcclxuICAnb3JkZXJzJzogJ9in2YTYt9mE2KjYp9iqJyxcclxuICAnYXBwb2ludG1lbnRzJzogJ9in2YTZhdmI2KfYudmK2K8nLFxyXG4gICdzZXR0aW5ncyc6ICfYp9mE2KXYudiv2KfYr9in2KonLFxyXG4gICd3b3JrZXJzJzogJ9in2YTYudmF2KfZhCcsXHJcbiAgJ3JlcG9ydHMnOiAn2KfZhNiq2YLYp9ix2YrYsScsXHJcbiAgJ2xvZ291dCc6ICfYqtiz2KzZitmEINin2YTYrtix2YjYrCcsXHJcbiAgJ3dlbGNvbWUnOiAn2YXYsdit2KjYp9mLJyxcclxuICAnd2VsY29tZV9iYWNrJzogJ9mF2LHYrdio2KfZiyDYqNi52YjYr9iq2YMnLFxyXG4gIFxyXG4gIC8vINin2YTYo9iy2LHYp9ixINmI2KfZhNil2KzYsdin2KHYp9iqXHJcbiAgJ2FkZF9uZXdfb3JkZXInOiAn2KXYttin2YHYqSDYt9mE2Kgg2KzYr9mK2K8nLFxyXG4gICdib29rX2FwcG9pbnRtZW50JzogJ9it2KzYsiDZhdmI2LnYrycsXHJcbiAgJ3ZpZXdfZGV0YWlscyc6ICfYudix2LYg2KfZhNiq2YHYp9i12YrZhCcsXHJcbiAgJ2VkaXQnOiAn2KrYudiv2YrZhCcsXHJcbiAgJ2RlbGV0ZSc6ICfYrdiw2YEnLFxyXG4gICdzYXZlJzogJ9it2YHYuCcsXHJcbiAgJ2NhbmNlbCc6ICfYpdmE2LrYp9ihJyxcclxuICAnc3VibWl0JzogJ9il2LHYs9in2YQnLFxyXG4gICdzZWFyY2gnOiAn2KjYrdirJyxcclxuICAnZmlsdGVyJzogJ9iq2LXZgdmK2KknLFxyXG4gICdleHBvcnQnOiAn2KrYtdiv2YrYsScsXHJcbiAgJ3ByaW50JzogJ9i32KjYp9i52KknLFxyXG4gICdiYWNrJzogJ9ix2KzZiNi5JyxcclxuICAnbmV4dCc6ICfYp9mE2KrYp9mE2YonLFxyXG4gICdwcmV2aW91cyc6ICfYp9mE2LPYp9io2YInLFxyXG4gICdjbG9zZSc6ICfYpdi62YTYp9mCJyxcclxuICAnY29uZmlybSc6ICfYqtij2YPZitivJyxcclxuICAnbG9hZGluZyc6ICfYrNin2LHZiiDYp9mE2KrYrdmF2YrZhC4uLicsXHJcbiAgJ3NhdmluZyc6ICfYrNin2LHZiiDYp9mE2K3Zgdi4Li4uJyxcclxuICBcclxuICAvLyDYrdin2YTYp9iqINin2YTYt9mE2KjYp9iqXHJcbiAgJ3BlbmRpbmcnOiAn2YHZiiDYp9mE2KfZhtiq2LjYp9ixJyxcclxuICAnaW5fcHJvZ3Jlc3MnOiAn2YLZitivINin2YTYqtmG2YHZitiwJyxcclxuICAnY29tcGxldGVkJzogJ9mF2YPYqtmF2YQnLFxyXG4gICdkZWxpdmVyZWQnOiAn2KrZhSDYp9mE2KrYs9mE2YrZhScsXHJcbiAgJ2NhbmNlbGxlZCc6ICfZhdmE2LrZiicsXHJcbiAgXHJcbiAgLy8g2YbYtdmI2LUg2LnYp9mF2KlcclxuICAnbmFtZSc6ICfYp9mE2KfYs9mFJyxcclxuICAnZW1haWwnOiAn2KfZhNio2LHZitivINin2YTYpdmE2YPYqtix2YjZhtmKJyxcclxuICAncGhvbmUnOiAn2LHZgtmFINin2YTZh9in2KrZgScsXHJcbiAgJ2FkZHJlc3MnOiAn2KfZhNi52YbZiNin2YYnLFxyXG4gICdkYXRlJzogJ9in2YTYqtin2LHZitiuJyxcclxuICAndGltZSc6ICfYp9mE2YjZgtiqJyxcclxuICAnc3RhdHVzJzogJ9in2YTYrdin2YTYqScsXHJcbiAgJ3ByaWNlJzogJ9in2YTYs9i52LEnLFxyXG4gICd0b3RhbCc6ICfYp9mE2YXYrNmF2YjYuScsXHJcbiAgJ2Rlc2NyaXB0aW9uJzogJ9in2YTZiNi12YEnLFxyXG4gICdub3Rlcyc6ICfZhdmE2KfYrdi42KfYqicsXHJcbiAgJ2NsaWVudF9uYW1lJzogJ9in2LPZhSDYp9mE2LLYqNmI2YbYqScsXHJcbiAgJ2NsaWVudF9waG9uZSc6ICfYsdmC2YUg2YfYp9iq2YEg2KfZhNiy2KjZiNmG2KknLFxyXG4gIFxyXG4gIC8vINix2LPYp9im2YQg2KfZhNmG2KzYp9itINmI2KfZhNiu2LfYo1xyXG4gICdzdWNjZXNzJzogJ9mG2KzYrScsXHJcbiAgJ2Vycm9yJzogJ9iu2LfYoycsXHJcbiAgJ3dhcm5pbmcnOiAn2KrYrdiw2YrYsScsXHJcbiAgJ2luZm8nOiAn2YXYudmE2YjZhdin2KonLFxyXG4gICdvcmRlcl9hZGRlZF9zdWNjZXNzJzogJ9iq2YUg2KXYttin2YHYqSDYp9mE2LfZhNioINio2YbYrNin2K0nLFxyXG4gICdvcmRlcl91cGRhdGVkX3N1Y2Nlc3MnOiAn2KrZhSDYqtit2K/ZitirINin2YTYt9mE2Kgg2KjZhtis2KfYrScsXHJcbiAgJ29yZGVyX2RlbGV0ZWRfc3VjY2Vzcyc6ICfYqtmFINit2LDZgSDYp9mE2LfZhNioINio2YbYrNin2K0nLFxyXG4gICdmaWxsX3JlcXVpcmVkX2ZpZWxkcyc6ICfZitix2KzZiSDZhdmE2KEg2KzZhdmK2Lkg2KfZhNit2YLZiNmEINin2YTZhdi32YTZiNio2KknLFxyXG5cclxuICAvLyDZhti12YjYtSDYpdi22KfZgdmK2Kkg2YXYt9mE2YjYqNipXHJcbiAgJ2FkbWluX2Rhc2hib2FyZCc6ICfZhNmI2K3YqSDYqtit2YPZhSDYp9mE2YXYr9mK2LEnLFxyXG4gICd3b3JrZXJfZGFzaGJvYXJkJzogJ9mE2YjYrdipINiq2K3Zg9mFINin2YTYudin2YXZhCcsXHJcbiAgJ2FkbWluJzogJ9mF2K/ZitixJyxcclxuICAnd29ya2VyJzogJ9i52KfZhdmEJyxcclxuICAnY2hhbmdlX2xhbmd1YWdlJzogJ9iq2LrZitmK2LEg2KfZhNmE2LrYqScsXHJcbiAgJ215X2FjdGl2ZV9vcmRlcnMnOiAn2LfZhNio2KfYqtmKINin2YTZhti02LfYqScsXHJcbiAgJ2NvbXBsZXRlZF9vcmRlcnMnOiAn2KfZhNi32YTYqNin2Kog2KfZhNmF2YPYqtmF2YTYqScsXHJcbiAgJ3RvdGFsX29yZGVycyc6ICfYpdis2YXYp9mE2Yog2KfZhNi32YTYqNin2KonLFxyXG4gICd0b3RhbF9yZXZlbnVlJzogJ9il2KzZhdin2YTZiiDYp9mE2KXZitix2KfYr9in2KonLFxyXG4gICdyZWNlbnRfb3JkZXJzJzogJ9in2YTYt9mE2KjYp9iqINin2YTYrdiv2YrYq9ipJyxcclxuICAncXVpY2tfYWN0aW9ucyc6ICfYpdis2LHYp9ih2KfYqiDYs9ix2YrYudipJyxcclxuICAndmlld19hbGxfb3JkZXJzJzogJ9i52LHYtiDYrNmF2YrYuSDYp9mE2LfZhNio2KfYqicsXHJcbiAgJ2FkZF9vcmRlcic6ICfYpdi22KfZgdipINi32YTYqCcsXHJcbiAgJ21hbmFnZV93b3JrZXJzJzogJ9il2K/Yp9ix2Kkg2KfZhNi52YXYp9mEJyxcclxuICAndmlld19yZXBvcnRzJzogJ9i52LHYtiDYp9mE2KrZgtin2LHZitixJyxcclxuICAnY2xpZW50X25hbWVfcmVxdWlyZWQnOiAn2KfYs9mFINin2YTYstio2YjZhtipIConLFxyXG4gICdwaG9uZV9yZXF1aXJlZCc6ICfYsdmC2YUg2KfZhNmH2KfYqtmBIConLFxyXG4gICdvcmRlcl9kZXNjcmlwdGlvbl9yZXF1aXJlZCc6ICfZiNi12YEg2KfZhNi32YTYqCAqJyxcclxuICAnZGVsaXZlcnlfZGF0ZV9yZXF1aXJlZCc6ICfZhdmI2LnYryDYp9mE2KrYs9mE2YrZhSAqJyxcclxuICAncHJpY2Vfc2FyJzogJ9in2YTYs9i52LEgKNix2YrYp9mEINiz2LnZiNiv2YopJyxcclxuICAnbWVhc3VyZW1lbnRzX2NtJzogJ9in2YTZhdmC2KfYs9in2KogKNio2KfZhNiz2YbYqtmK2YXYqtixKScsXHJcbiAgJ2FkZGl0aW9uYWxfbm90ZXMnOiAn2YXZhNin2K3YuNin2Kog2KXYttin2YHZitipJyxcclxuICAndm9pY2Vfbm90ZXNfb3B0aW9uYWwnOiAn2YXZhNin2K3YuNin2Kog2LXZiNiq2YrYqSAo2KfYrtiq2YrYp9ix2YrYqSknLFxyXG4gICdkZXNpZ25faW1hZ2VzJzogJ9i12YjYsSDYp9mE2KrYtdmF2YrZhScsXHJcbiAgJ2ZhYnJpY190eXBlJzogJ9mG2YjYuSDYp9mE2YLZhdin2LQnLFxyXG4gICdmYWJyaWNfdHlwZV9vcHRpb25hbCc6ICfZhtmI2Lkg2KfZhNmC2YXYp9i0JyxcclxuICAncmVzcG9uc2libGVfd29ya2VyJzogJ9in2YTYudin2YXZhCDYp9mE2YXYs9ik2YjZhCcsXHJcbiAgJ2Nob29zZV93b3JrZXInOiAn2KfYrtiq2LEg2KfZhNi52KfZhdmEINin2YTZhdiz2KTZiNmEJyxcclxuICAnc3RhdHVzX2FuZF93b3JrZXInOiAn2KfZhNit2KfZhNipINmI2KfZhNi52KfZhdmEJyxcclxuICAnb3JkZXJfc3RhdHVzJzogJ9it2KfZhNipINin2YTYt9mE2KgnLFxyXG4gICdhZGRpdGlvbmFsX25vdGVzX3BsYWNlaG9sZGVyJzogJ9ij2Yog2YXZhNin2K3YuNin2Kog2KPZiCDYqtmB2KfYtdmK2YQg2KXYttin2YHZitipLi4uJyxcclxuICAnbm90X3NwZWNpZmllZCc6ICfYutmK2LEg2YXYrdiv2K8nLFxyXG4gICdiYWNrX3RvX2Rhc2hib2FyZCc6ICfYp9mE2LnZiNiv2Kkg2KXZhNmJINmE2YjYrdipINin2YTYqtit2YPZhScsXHJcbiAgJ292ZXJ2aWV3X3RvZGF5JzogJ9mG2LjYsdipINi52KfZhdipINi52YTZiSDYo9mG2LTYt9ipINin2YTZitmI2YUnLFxyXG4gICd3ZWxjb21lX3dvcmtlcic6ICfZhdix2K3YqNin2Ysg2KjZgyDZgdmKINmF2LPYp9it2Kkg2KfZhNi52YXZhCcsXHJcblxyXG4gIC8vINmF2YHYp9iq2YrYrSDZhdmB2YLZiNiv2Kkg2YXZhiDZhNmI2K3YqSDYp9mE2KrYrdmD2YVcclxuICAnaG9tZXBhZ2UnOiAn2KfZhNi12YHYrdipINin2YTYsdim2YrYs9mK2KknLFxyXG4gICdteV9jb21wbGV0ZWRfb3JkZXJzJzogJ9i32YTYqNin2KrZiiDYp9mE2YXZg9iq2YXZhNipJyxcclxuICAnbXlfdG90YWxfb3JkZXJzJzogJ9il2KzZhdin2YTZiiDYt9mE2KjYp9iq2YonLFxyXG4gICdhY3RpdmVfb3JkZXJzJzogJ9in2YTYt9mE2KjYp9iqINin2YTZhti02LfYqScsXHJcbiAgJ3RvZGF5X2FwcG9pbnRtZW50cyc6ICfZhdmI2KfYudmK2K8g2KfZhNmK2YjZhScsXHJcbiAgJ3N0YXRpc3RpY3MnOiAn2KfZhNil2K3Ytdin2KbZitin2KonLFxyXG4gICdub19vcmRlcnNfZm91bmQnOiAn2YTYpyDYqtmI2KzYryDYt9mE2KjYp9iqJyxcclxuICAndmlld19hbGwnOiAn2LnYsdi2INin2YTZg9mEJyxcclxuICAnd29ya2VyX21hbmFnZW1lbnQnOiAn2KXYr9in2LHYqSDYp9mE2LnZhdin2YQnLFxyXG4gICdyZW1pbmRlcic6ICfYqtiw2YPZitixJyxcclxuICAneW91X2hhdmUnOiAn2YTYr9mK2YMnLFxyXG4gICd0b2RheV9hcHBvaW50bWVudHNfcmVtaW5kZXInOiAn2YXZiNi52K8g2KfZhNmK2YjZhScsXHJcbiAgJ2FuZCc6ICfZiCcsXHJcbiAgJ29yZGVyc19uZWVkX2ZvbGxvdyc6ICfYt9mE2KjYp9iqINiq2K3Yqtin2Kwg2YXYqtin2KjYudipJyxcclxuICAnZGV0YWlsZWRfcmVwb3J0cyc6ICfYqtmC2KfYsdmK2LEg2YXZgdi12YTYqScsXHJcbiAgJ3dvcmtlcl9kZXNjcmlwdGlvbic6ICfZitmF2YPZhtmDINmH2YbYpyDZhdiq2KfYqNi52Kkg2LfZhNio2KfYqtmDINin2YTZhdiu2LXYtdipINmE2YMg2YjYqtit2K/ZitirINit2KfZhNiq2YfYpycsXHJcblxyXG4gIC8vINmF2YHYp9iq2YrYrSDYtdmB2K3YqSDYpdi22KfZgdipINin2YTYt9mE2KjYp9iqXHJcbiAgJ29yZGVyX2FkZF9lcnJvcic6ICfYrdiv2Ksg2K7Yt9ijINij2KvZhtin2KEg2KXYttin2YHYqSDYp9mE2LfZhNioJyxcclxuICAnY21fcGxhY2Vob2xkZXInOiAn2LPZhScsXHJcbiAgJ3Nob3VsZGVyJzogJ9in2YTZg9iq2YEnLFxyXG4gICdzaG91bGRlcl9jaXJjdW1mZXJlbmNlJzogJ9mF2K3Ziti3INin2YTZg9iq2YEnLFxyXG4gICdjaGVzdCc6ICfYp9mE2LXYr9ixJyxcclxuICAnd2Fpc3QnOiAn2KfZhNiu2LXYsScsXHJcbiAgJ2hpcHMnOiAn2KfZhNij2LHYr9in2YEnLFxyXG4gICdkYXJ0X2xlbmd0aCc6ICfYt9mI2YQg2KfZhNiu2YrYp9i32KknLFxyXG4gICdib2RpY2VfbGVuZ3RoJzogJ9i32YjZhCDYp9mE2KzYs9mFJyxcclxuICAnbmVja2xpbmUnOiAn2K7YtyDYp9mE2LHZgtio2KknLFxyXG4gICdhcm1waXQnOiAn2KfZhNil2KjYtycsXHJcbiAgJ3NsZWV2ZV9sZW5ndGgnOiAn2LfZiNmEINin2YTZg9mFJyxcclxuICAnZm9yZWFybSc6ICfYp9mE2LPYp9i52K8nLFxyXG4gICdjdWZmJzogJ9in2YTZg9mFJyxcclxuICAnZnJvbnRfbGVuZ3RoJzogJ9in2YTYt9mI2YQg2KfZhNij2YXYp9mF2YonLFxyXG4gICdiYWNrX2xlbmd0aCc6ICfYp9mE2LfZiNmEINin2YTYrtmE2YHZiicsXHJcblxyXG4gIC8vINmF2YHYp9iq2YrYrSDYtdmB2K3YqSDYp9mE2LfZhNio2KfYqlxyXG4gICdlbnRlcl9vcmRlcl9udW1iZXInOiAn2KPYr9iu2YQg2LHZgtmFINin2YTYt9mE2KgnLFxyXG4gICdzZWFyY2hfcGxhY2Vob2xkZXInOiAn2KfZhNio2K3YqyDYqNin2YTYp9iz2YUg2KPZiCDYsdmC2YUg2KfZhNi32YTYqCDYo9mIINin2YTZiNi12YEuLi4nLFxyXG4gICdzZWFyY2hfYnlfdGV4dCc6ICfYp9mE2KjYrdirINio2KfZhNmG2LUnLFxyXG4gICdzZWFyY2hfYnlfb3JkZXJfbnVtYmVyJzogJ9in2YTYqNit2Ksg2KjYsdmC2YUg2KfZhNi32YTYqCcsXHJcbiAgJ2FsbF9vcmRlcnMnOiAn2KzZhdmK2Lkg2KfZhNi32YTYqNin2KonLFxyXG4gICdub19vcmRlcnNfYXNzaWduZWQnOiAn2YTYpyDYqtmI2KzYryDYt9mE2KjYp9iqINmF2K7Ytdi12Kkg2YTZgycsXHJcbiAgJ25vX29yZGVyc19hc3NpZ25lZF9kZXNjJzogJ9mE2YUg2YrYqtmFINiq2K7YtdmK2LUg2KPZiiDYt9mE2KjYp9iqINmE2YMg2KjYudivJyxcclxuICAnbm9fb3JkZXJzX2ZvdW5kX2Rlc2MnOiAn2YTYpyDYqtmI2KzYryDYt9mE2KjYp9iqINmF2LfYp9io2YLYqSDZhNmF2LnYp9mK2YrYsSDYp9mE2KjYrdirJyxcclxuICAncHJpY2VfbGFiZWwnOiAn2KfZhNiz2LnYsScsXHJcbiAgJ3Nhcic6ICfYsdmK2KfZhCcsXHJcbiAgJ3ZpZXcnOiAn2LnYsdi2JyxcclxuICAnY29tcGxldGluZyc6ICfYrNin2LHZiiDYp9mE2KXZhtmH2KfYoS4uLicsXHJcbiAgJ3N0YXJ0X3dvcmsnOiAn2KjYr9ihINin2YTYudmF2YQnLFxyXG4gICdjb21wbGV0ZV9vcmRlcic6ICfYpdmG2YfYp9ihINin2YTYt9mE2KgnLFxyXG4gICdjb21wbGV0ZV9vcmRlcl9tb2RhbF90aXRsZSc6ICfYpdmG2YfYp9ihINin2YTYt9mE2Kgg2YjYqtit2YXZitmEINi12YjYsSDYp9mE2LnZhdmEINin2YTZhdmD2KrZhdmEJyxcclxuICAnaW1wb3J0YW50X3dhcm5pbmcnOiAn2KrYrdiw2YrYsSDZhdmH2YUnLFxyXG4gICdjb21wbGV0ZV9vcmRlcl93YXJuaW5nJzogJ9io2YXYrNix2K8g2KXZhtmH2KfYoSDYp9mE2LfZhNio2Iwg2YTZhiDYqtiq2YXZg9mGINmF2YYg2KrYudiv2YrZhCDYrdin2YTYqtmHINmF2LHYqSDYo9iu2LHZiS4g2KrYo9mD2K8g2YXZhiDYqtit2YXZitmEINis2YXZiti5INi12YjYsSDYp9mE2LnZhdmEINin2YTZhdmD2KrZhdmEINmC2KjZhCDYp9mE2YXYqtin2KjYudipLicsXHJcbiAgJ29yZGVyX2RlbGV0ZWRfc3VjY2Vzc2Z1bGx5JzogJ9iq2YUg2K3YsNmBINin2YTYt9mE2Kgg2KjZhtis2KfYrScsXHJcblxyXG4gIC8vINmF2YHYp9iq2YrYrSDZhdmD2YjZhiDYrdiw2YEg2KfZhNi32YTYqFxyXG4gICdjb25maXJtX2RlbGV0ZV9vcmRlcic6ICfYqtij2YPZitivINit2LDZgSDYp9mE2LfZhNioJyxcclxuICAnd2FybmluZ19kZWxldGVfb3JkZXInOiAn2KrYrdiw2YrYsTog2K3YsNmBINin2YTYt9mE2KgnLFxyXG4gICdkZWxldGVfb3JkZXJfd2FybmluZ19tZXNzYWdlJzogJ9mE2Kcg2YrZhdmD2YYg2KfZhNiq2LHYp9is2Lkg2LnZhiDZh9iw2Kcg2KfZhNil2KzYsdin2KEuINiz2YrYqtmFINit2LDZgSDYp9mE2LfZhNioINmI2KzZhdmK2Lkg2KfZhNio2YrYp9mG2KfYqiDYp9mE2YXYsdiq2KjYt9ipINio2Ycg2YbZh9in2KbZitin2YsuJyxcclxuICAnYWRtaW5fZW1haWwnOiAn2KjYsdmK2K8g2KfZhNmF2K/ZitixINin2YTYpdmE2YPYqtix2YjZhtmKJyxcclxuICAnYWRtaW5fcGFzc3dvcmQnOiAn2YPZhNmF2Kkg2YXYsdmI2LEg2KfZhNmF2K/ZitixJyxcclxuICAnZW50ZXJfYWRtaW5fZW1haWwnOiAn2KPYr9iu2YQg2KjYsdmK2K8g2KfZhNmF2K/ZitixINin2YTYpdmE2YPYqtix2YjZhtmKJyxcclxuICAnZW50ZXJfYWRtaW5fcGFzc3dvcmQnOiAn2KPYr9iu2YQg2YPZhNmF2Kkg2YXYsdmI2LEg2KfZhNmF2K/ZitixJyxcclxuICAncGxlYXNlX2ZpbGxfYWxsX2ZpZWxkcyc6ICfZitix2KzZiSDZhdmE2KEg2KzZhdmK2Lkg2KfZhNit2YLZiNmEJyxcclxuICAnZW1haWxfZG9lc19ub3RfbWF0Y2gnOiAn2KfZhNio2LHZitivINin2YTYpdmE2YPYqtix2YjZhtmKINmE2Kcg2YrYt9in2KjZgiDYqNix2YrYryDYp9mE2YXYr9mK2LEg2KfZhNmF2LPYrNmEJyxcclxuICAnaW5jb3JyZWN0X3Bhc3N3b3JkJzogJ9mD2YTZhdipINin2YTZhdix2YjYsSDYutmK2LEg2LXYrdmK2K3YqScsXHJcbiAgJ2NvbmZpcm1fZGVsZXRlJzogJ9iq2KPZg9mK2K8g2KfZhNit2LDZgScsXHJcblxyXG4gIC8vINmF2YHYp9iq2YrYrSDZhdmD2YjZhiDYp9mE2YXZhNin2K3YuNin2Kog2KfZhNi12YjYqtmK2KlcclxuICAnc3RhcnRfcmVjb3JkaW5nJzogJ9io2K/YoSDYp9mE2KrYs9is2YrZhCcsXHJcbiAgJ3N0b3BfcmVjb3JkaW5nJzogJ9il2YrZgtin2YEg2KfZhNiq2LPYrNmK2YQnLFxyXG4gICdjbGlja190b19yZWNvcmRfdm9pY2Vfbm90ZSc6ICfYp9mG2YLYsSDZhNiq2LPYrNmK2YQg2YXZhNin2K3YuNipINi12YjYqtmK2KknLFxyXG4gICd2b2ljZV9ub3Rlcyc6ICfYp9mE2YXZhNin2K3YuNin2Kog2KfZhNi12YjYqtmK2KknLFxyXG4gICd2b2ljZV9ub3RlJzogJ9mF2YTYp9it2LjYqSDYtdmI2KrZitipJyxcclxuICAnbWljcm9waG9uZV9hY2Nlc3NfZXJyb3InOiAn2K7Yt9ijINmB2Yog2KfZhNmI2LXZiNmEINmE2YTZhdmK2YPYsdmI2YHZiNmGJyxcclxuXHJcbiAgLy8g2YXZgdin2KrZititINi12YHYrdipINin2YTYudmF2KfZhFxyXG4gICd3b3JrZXJfYWRkZWRfc3VjY2Vzcyc6ICfYqtmFINil2LbYp9mB2Kkg2KfZhNi52KfZhdmEINio2YbYrNin2K0nLFxyXG4gICdlcnJvcl9hZGRpbmdfd29ya2VyJzogJ9it2K/YqyDYrti32KMg2KPYq9mG2KfYoSDYpdi22KfZgdipINin2YTYudin2YXZhCcsXHJcbiAgJ3dvcmtlcl91cGRhdGVkX3N1Y2Nlc3MnOiAn2KrZhSDYqtit2K/ZitirINin2YTYudin2YXZhCDYqNmG2KzYp9itJyxcclxuICAnZXJyb3JfdXBkYXRpbmdfd29ya2VyJzogJ9it2K/YqyDYrti32KMg2KPYq9mG2KfYoSDYqtit2K/ZitirINin2YTYudin2YXZhCcsXHJcbiAgJ3dvcmtlcl9kZWxldGVkX3N1Y2Nlc3MnOiAn2KrZhSDYrdiw2YEg2KfZhNi52KfZhdmEINio2YbYrNin2K0nLFxyXG4gICd3b3JrZXJfZGVhY3RpdmF0ZWQnOiAn2KrZhSDYpdmE2LrYp9ihINiq2YHYudmK2YQg2KfZhNi52KfZhdmEJyxcclxuICAnd29ya2VyX2FjdGl2YXRlZCc6ICfYqtmFINiq2YHYudmK2YQg2KfZhNi52KfZhdmEJyxcclxuICAnYWRkaW5nJzogJ9is2KfYsdmKINin2YTYpdi22KfZgdipLi4uJyxcclxuICAnYWRkX3dvcmtlcic6ICfYpdi22KfZgdipINi52KfZhdmEJyxcclxuICAnYWN0aXZlJzogJ9mG2LTYtycsXHJcbiAgJ2luYWN0aXZlJzogJ9i62YrYsSDZhti02LcnLFxyXG4gICdzYXZlX2NoYW5nZXMnOiAn2K3Zgdi4INin2YTYqti62YrZitix2KfYqicsXHJcbiAgJ3NlYXJjaF93b3JrZXJzX3BsYWNlaG9sZGVyJzogJ9in2YTYqNit2Ksg2LnZhiDYp9mE2LnZhdin2YQuLi4nLFxyXG4gICd3b3JrZXJzX21hbmFnZW1lbnQnOiAn2KXYr9in2LHYqSDYp9mE2LnZhdin2YQnLFxyXG4gICd2aWV3X21hbmFnZV90ZWFtJzogJ9i52LHYtiDZiNil2K/Yp9ix2Kkg2YHYsdmK2YIg2KfZhNi52YXZhCDZgdmKINmI2LHYtNipINin2YTYqtmB2LXZitmEJyxcclxuICAnYWRkX25ld193b3JrZXInOiAn2KXYttin2YHYqSDYudin2YXZhCDYrNiv2YrYrycsXHJcbiAgJ2FkZF9uZXdfd29ya2VyX2Zvcm0nOiAn2KXYttin2YHYqSDYudin2YXZhCDYrNiv2YrYrycsXHJcbiAgJ2Z1bGxfbmFtZV9yZXF1aXJlZCc6ICfYp9mE2KfYs9mFINin2YTZg9in2YXZhCAqJyxcclxuICAnZW1haWxfcmVxdWlyZWQnOiAn2KfZhNio2LHZitivINin2YTYpdmE2YPYqtix2YjZhtmKIConLFxyXG4gICdwYXNzd29yZF9yZXF1aXJlZCc6ICfZg9mE2YXYqSDYp9mE2YXYsdmI2LEgKicsXHJcbiAgJ3Bob25lX3JlcXVpcmVkX3dvcmtlcic6ICfYsdmC2YUg2KfZhNmH2KfYqtmBIConLFxyXG4gICdlbnRlcl9mdWxsX25hbWUnOiAn2KPYr9iu2YQg2KfZhNin2LPZhSDYp9mE2YPYp9mF2YQnLFxyXG4gICdlbnRlcl9lbWFpbCc6ICfYo9iv2K7ZhCDYp9mE2KjYsdmK2K8g2KfZhNil2YTZg9iq2LHZiNmG2YonLFxyXG4gICdlbnRlcl9wYXNzd29yZCc6ICfYo9iv2K7ZhCDZg9mE2YXYqSDYp9mE2YXYsdmI2LEnLFxyXG4gICdlbnRlcl9waG9uZSc6ICfYo9iv2K7ZhCDYsdmC2YUg2KfZhNmH2KfYqtmBJyxcclxuICAnc3BlY2lhbHR5X3JlcXVpcmVkJzogJ9in2YTYqtiu2LXYtSAqJyxcclxuICAnc3BlY2lhbHR5X2V4YW1wbGUnOiAn2YXYq9in2YQ6INiu2YrYp9i32Kkg2YHYs9in2KrZitmGINin2YTYs9mH2LHYqScsXHJcbiAgJ2VkaXRfd29ya2VyJzogJ9iq2LnYr9mK2YQg2KfZhNi52KfZhdmEJyxcclxuICAnbmV3X3Bhc3N3b3JkJzogJ9mD2YTZhdipINin2YTZhdix2YjYsSDYp9mE2KzYr9mK2K/YqScsXHJcbiAgJ2xlYXZlX2VtcHR5X25vX2NoYW5nZSc6ICfYp9iq2LHZg9mHINmB2KfYsdi62KfZiyDYpdiw2Kcg2YTZhSDYqtix2K8g2KrYutmK2YrYsdmHJyxcclxuICAnbm9fd29ya2Vycyc6ICfZhNinINmK2YjYrNivINi52YXYp9mEJyxcclxuICAnbm9fd29ya2Vyc19mb3VuZCc6ICfZhNinINmK2YjYrNivINi52YXYp9mEINmF2LfYp9io2YLZitmGINmE2YXYudin2YrZitixINin2YTYqNit2KsnLFxyXG4gICdqb2luZWRfb24nOiAn2KfZhti22YUg2YHZiicsXHJcbiAgJ3RvdGFsX3dvcmtlcnMnOiAn2KXYrNmF2KfZhNmKINin2YTYudmF2KfZhCcsXHJcbiAgJ2FjdGl2ZV93b3JrZXJzJzogJ9in2YTYudmF2KfZhCDYp9mE2YbYtNi32YjZhicsXHJcbiAgJ3RvdGFsX2NvbXBsZXRlZF9vcmRlcnMnOiAn2KXYrNmF2KfZhNmKINin2YTYt9mE2KjYp9iqINin2YTZhdmD2KrZhdmE2KknLFxyXG4gICdjb25maXJtX2RlbGV0ZV93b3JrZXInOiAn2YfZhCDYo9mG2Kog2YXYqtij2YPYryDZhdmGINit2LDZgSDZh9iw2Kcg2KfZhNi52KfZhdmE2J8nLFxyXG5cclxuICAvLyDZhdmB2KfYqtmK2K0g2LXZgdit2Kkg2KfZhNiq2YLYp9ix2YrYsVxyXG4gICdlbmdhZ2VtZW50X2RyZXNzJzogJ9mB2LPYqtin2YYg2K7Yt9mI2KjYqScsXHJcbiAgJ2Nhc3VhbF9kcmVzcyc6ICfZgdiz2KrYp9mGINmK2YjZhdmKJyxcclxuICAnb3RoZXInOiAn2KPYrtix2YknLFxyXG4gICd0aGlzX3dlZWsnOiAn2YfYsNinINin2YTYo9iz2KjZiNi5JyxcclxuICAndGhpc19tb250aCc6ICfZh9iw2Kcg2KfZhNi02YfYsScsXHJcbiAgJ3RoaXNfcXVhcnRlcic6ICfZh9iw2Kcg2KfZhNix2KjYuScsXHJcbiAgJ3RoaXNfeWVhcic6ICfZh9iw2Kcg2KfZhNi52KfZhScsXHJcbiAgJ3JlcG9ydHNfYW5hbHl0aWNzJzogJ9in2YTYqtmC2KfYsdmK2LEg2YjYp9mE2KrYrdmE2YrZhNin2KonLFxyXG4gICdtb250aGx5X3RyZW5kJzogJ9in2YTYp9iq2KzYp9mHINin2YTYtNmH2LHZiicsXHJcbiAgJ3JldmVudWVfbGFiZWwnOiAn2KfZhNil2YrYsdin2K/Yp9iqJyxcclxuXHJcbiAgLy8g2YXZgdin2KrZititINi12YHYrdipINin2YTZhdmI2KfYudmK2K9cclxuICAnY29uZmlybWVkJzogJ9mF2KTZg9ivJyxcclxuICAncG0nOiAn2YXYs9in2KHZiycsXHJcbiAgJ2FtJzogJ9i12KjYp9it2KfZiycsXHJcbiAgJ2FsbF9zdGF0dXNlcyc6ICfYrNmF2YrYuSDYp9mE2K3Yp9mE2KfYqicsXHJcbiAgJ2FsbF9kYXRlcyc6ICfYrNmF2YrYuSDYp9mE2KrZiNin2LHZitiuJyxcclxuICAndG9kYXknOiAn2KfZhNmK2YjZhScsXHJcbiAgJ3RvbW9ycm93JzogJ9i62K/Yp9mLJyxcclxuICAnYXBwb2ludG1lbnRzX21hbmFnZW1lbnQnOiAn2KXYr9in2LHYqSDYp9mE2YXZiNin2LnZitivJyxcclxuICAndmlld19tYW5hZ2VfYXBwb2ludG1lbnRzJzogJ9i52LHYtiDZiNil2K/Yp9ix2Kkg2KzZhdmK2Lkg2YXZiNin2LnZitivINin2YTYqtmB2LXZitmEJyxcclxuICAnYm9va19uZXdfYXBwb2ludG1lbnQnOiAn2K3YrNiyINmF2YjYudivINis2K/ZitivJyxcclxuICAnc2VhcmNoX2FwcG9pbnRtZW50c19wbGFjZWhvbGRlcic6ICfYp9mE2KjYrdirINmB2Yog2KfZhNmF2YjYp9i52YrYry4uLicsXHJcbiAgJ25vX2FwcG9pbnRtZW50cyc6ICfZhNinINiq2YjYrNivINmF2YjYp9i52YrYrycsXHJcbiAgJ25vX2FwcG9pbnRtZW50c19mb3VuZCc6ICfZhNinINiq2YjYrNivINmF2YjYp9i52YrYryDZhdi32KfYqNmC2Kkg2YTZhdi52KfZitmK2LEg2KfZhNio2K3YqycsXHJcbiAgJ2NyZWF0ZWRfb24nOiAn2KrZhSDYp9mE2KXZhti02KfYoSDZgdmKJyxcclxuICAnY29uZmlybV9hcHBvaW50bWVudCc6ICfYqtij2YPZitivINin2YTZhdmI2LnYrycsXHJcbiAgJ2NhbmNlbF9hcHBvaW50bWVudCc6ICfYpdmE2LrYp9ihINin2YTZhdmI2LnYrycsXHJcblxyXG4gIC8vINmF2YHYp9iq2YrYrSDZhdmD2YjZhtin2Kog2KXYttin2YHZitipXHJcbiAgJ29mJzogJ9mF2YYnLFxyXG4gICdpbWFnZXNfdGV4dCc6ICfYtdmI2LEnLFxyXG5cclxuICAvLyDZhdmB2KfYqtmK2K0g2YXZg9mI2YbYp9iqINin2YTYtdmI2LEg2YjYp9mE2KrYrdmF2YrZhFxyXG4gICdtYXhfaW1hZ2VzX3JlYWNoZWQnOiAn2KrZhSDYp9mE2YjYtdmI2YQg2YTZhNit2K8g2KfZhNij2YLYtdmJINmF2YYg2KfZhNi12YjYsScsXHJcbiAgJ2Ryb3BfaW1hZ2VzX2hlcmUnOiAn2KfYs9mC2Lcg2KfZhNi12YjYsSDZh9mG2KcnLFxyXG4gICdjbGlja19vcl9kcmFnX2ltYWdlcyc6ICfYp9mG2YLYsSDYo9mIINin2LPYrdioINin2YTYtdmI2LEg2YfZhtinJyxcclxuICAnaW1hZ2VfdXBsb2FkX2Zvcm1hdCc6ICdQTkcsIEpQRywgSlBFRyDYrdiq2YkgNU1CJyxcclxuICAnbWF4X2ltYWdlc190ZXh0JzogJ9in2YTYrdivINin2YTYo9mC2LXZiScsXHJcbiAgJ29yZGVyX2xhYmVsJzogJ9in2YTYt9mE2KgnLFxyXG4gICdmb3JfY2xpZW50JzogJ9mE2YTYstio2YjZhtipJyxcclxuXHJcbiAgLy8g2YXZgdin2KrZititINmF2YPZiNmGINiq2LnYr9mK2YQg2KfZhNi32YTYqFxyXG4gICdvcmRlcl91cGRhdGVfZXJyb3InOiAn2K3Yr9irINiu2LfYoyDYo9ir2YbYp9ihINiq2K3Yr9mK2Ksg2KfZhNi32YTYqCcsXHJcbiAgJ3ByaWNlX3Nhcl9yZXF1aXJlZCc6ICfYp9mE2LPYudixICjYsdmK2KfZhCDYs9i52YjYr9mKKSAqJyxcclxuICAnc3RhdHVzX3BlbmRpbmcnOiAn2YHZiiDYp9mE2KfZhtiq2LjYp9ixJyxcclxuICAnc3RhdHVzX2luX3Byb2dyZXNzJzogJ9mC2YrYryDYp9mE2KrZhtmB2YrYsCcsXHJcbiAgJ3N0YXR1c19jb21wbGV0ZWQnOiAn2YXZg9iq2YXZhCcsXHJcbiAgJ3N0YXR1c19kZWxpdmVyZWQnOiAn2KrZhSDYp9mE2KrYs9mE2YrZhScsXHJcbiAgJ3N0YXR1c19jYW5jZWxsZWQnOiAn2YXZhNi62YonLFxyXG5cclxuICAvLyDZhti12YjYtSDYp9mE2YHZiNiq2LFcclxuICAnaG9tZSc6ICfYp9mE2LHYptmK2LPZitipJyxcclxuICAndHJhY2tfb3JkZXInOiAn2KfYs9iq2LnZhNin2YUg2LnZhiDYp9mE2LfZhNioJyxcclxuICAnZmFicmljcyc6ICfYp9mE2KPZgtmF2LTYqScsXHJcbiAgJ2NvbnRhY3RfdXMnOiAn2KrZiNin2LXZhNmKINmF2LnZhtinJyxcclxuICAneWFzbWluX2Fsc2hhbSc6ICfZitin2LPZhdmK2YYg2KfZhNi02KfZhScsXHJcbiAgJ2N1c3RvbV9kcmVzc190YWlsb3JpbmcnOiAn2KrZgdi12YrZhCDZgdiz2KfYqtmK2YYg2K3Ys9ioINin2YTYt9mE2KgnXHJcbn1cclxuXHJcbi8vINin2YTYqtix2KzZhdin2Kog2KfZhNil2YbYrNmE2YrYstmK2KlcclxuY29uc3QgZW5UcmFuc2xhdGlvbnM6IFRyYW5zbGF0aW9ucyA9IHtcclxuICAvLyDYp9mE2KrZhtmC2YQg2YjYp9mE2LnZhtin2YjZitmGINin2YTYsdim2YrYs9mK2KlcclxuICAnZGFzaGJvYXJkJzogJ0Rhc2hib2FyZCcsXHJcbiAgJ29yZGVycyc6ICdPcmRlcnMnLFxyXG4gICdhcHBvaW50bWVudHMnOiAnQXBwb2ludG1lbnRzJyxcclxuICAnc2V0dGluZ3MnOiAnU2V0dGluZ3MnLFxyXG4gICd3b3JrZXJzJzogJ1dvcmtlcnMnLFxyXG4gICdyZXBvcnRzJzogJ1JlcG9ydHMnLFxyXG4gICdsb2dvdXQnOiAnTG9nb3V0JyxcclxuICAnd2VsY29tZSc6ICdXZWxjb21lJyxcclxuICAnd2VsY29tZV9iYWNrJzogJ1dlbGNvbWUgQmFjaycsXHJcbiAgXHJcbiAgLy8g2KfZhNij2LLYsdin2LEg2YjYp9mE2KXYrNix2KfYodin2KpcclxuICAnYWRkX25ld19vcmRlcic6ICdBZGQgTmV3IE9yZGVyJyxcclxuICAnYm9va19hcHBvaW50bWVudCc6ICdCb29rIEFwcG9pbnRtZW50JyxcclxuICAndmlld19kZXRhaWxzJzogJ1ZpZXcgRGV0YWlscycsXHJcbiAgJ2VkaXQnOiAnRWRpdCcsXHJcbiAgJ2RlbGV0ZSc6ICdEZWxldGUnLFxyXG4gICdzYXZlJzogJ1NhdmUnLFxyXG4gICdjYW5jZWwnOiAnQ2FuY2VsJyxcclxuICAnc3VibWl0JzogJ1N1Ym1pdCcsXHJcbiAgJ3NlYXJjaCc6ICdTZWFyY2gnLFxyXG4gICdmaWx0ZXInOiAnRmlsdGVyJyxcclxuICAnZXhwb3J0JzogJ0V4cG9ydCcsXHJcbiAgJ3ByaW50JzogJ1ByaW50JyxcclxuICAnYmFjayc6ICdCYWNrJyxcclxuICAnbmV4dCc6ICdOZXh0JyxcclxuICAncHJldmlvdXMnOiAnUHJldmlvdXMnLFxyXG4gICdjbG9zZSc6ICdDbG9zZScsXHJcbiAgJ2NvbmZpcm0nOiAnQ29uZmlybScsXHJcbiAgJ2xvYWRpbmcnOiAnTG9hZGluZy4uLicsXHJcbiAgJ3NhdmluZyc6ICdTYXZpbmcuLi4nLFxyXG4gIFxyXG4gIC8vINit2KfZhNin2Kog2KfZhNi32YTYqNin2KpcclxuICAncGVuZGluZyc6ICdQZW5kaW5nJyxcclxuICAnaW5fcHJvZ3Jlc3MnOiAnSW4gUHJvZ3Jlc3MnLFxyXG4gICdjb21wbGV0ZWQnOiAnQ29tcGxldGVkJyxcclxuICAnZGVsaXZlcmVkJzogJ0RlbGl2ZXJlZCcsXHJcbiAgJ2NhbmNlbGxlZCc6ICdDYW5jZWxsZWQnLFxyXG4gIFxyXG4gIC8vINmG2LXZiNi1INi52KfZhdipXHJcbiAgJ25hbWUnOiAnTmFtZScsXHJcbiAgJ2VtYWlsJzogJ0VtYWlsJyxcclxuICAncGhvbmUnOiAnUGhvbmUnLFxyXG4gICdhZGRyZXNzJzogJ0FkZHJlc3MnLFxyXG4gICdkYXRlJzogJ0RhdGUnLFxyXG4gICd0aW1lJzogJ1RpbWUnLFxyXG4gICdzdGF0dXMnOiAnU3RhdHVzJyxcclxuICAncHJpY2UnOiAnUHJpY2UnLFxyXG4gICd0b3RhbCc6ICdUb3RhbCcsXHJcbiAgJ2Rlc2NyaXB0aW9uJzogJ0Rlc2NyaXB0aW9uJyxcclxuICAnbm90ZXMnOiAnTm90ZXMnLFxyXG4gICdjbGllbnRfbmFtZSc6ICdDbGllbnQgTmFtZScsXHJcbiAgJ2NsaWVudF9waG9uZSc6ICdDbGllbnQgUGhvbmUnLFxyXG4gIFxyXG4gIC8vINix2LPYp9im2YQg2KfZhNmG2KzYp9itINmI2KfZhNiu2LfYo1xyXG4gICdzdWNjZXNzJzogJ1N1Y2Nlc3MnLFxyXG4gICdlcnJvcic6ICdFcnJvcicsXHJcbiAgJ3dhcm5pbmcnOiAnV2FybmluZycsXHJcbiAgJ2luZm8nOiAnSW5mbycsXHJcbiAgJ29yZGVyX2FkZGVkX3N1Y2Nlc3MnOiAnT3JkZXIgYWRkZWQgc3VjY2Vzc2Z1bGx5JyxcclxuICAnb3JkZXJfdXBkYXRlZF9zdWNjZXNzJzogJ09yZGVyIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5JyxcclxuICAnb3JkZXJfZGVsZXRlZF9zdWNjZXNzJzogJ09yZGVyIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5JyxcclxuICAnZmlsbF9yZXF1aXJlZF9maWVsZHMnOiAnUGxlYXNlIGZpbGwgYWxsIHJlcXVpcmVkIGZpZWxkcycsXHJcblxyXG4gIC8vINmG2LXZiNi1INil2LbYp9mB2YrYqSDZhdi32YTZiNio2KlcclxuICAnYWRtaW5fZGFzaGJvYXJkJzogJ0FkbWluIERhc2hib2FyZCcsXHJcbiAgJ3dvcmtlcl9kYXNoYm9hcmQnOiAnV29ya2VyIERhc2hib2FyZCcsXHJcbiAgJ2FkbWluJzogJ0FkbWluJyxcclxuICAnd29ya2VyJzogJ1dvcmtlcicsXHJcbiAgJ2NoYW5nZV9sYW5ndWFnZSc6ICdDaGFuZ2UgTGFuZ3VhZ2UnLFxyXG4gICdteV9hY3RpdmVfb3JkZXJzJzogJ015IEFjdGl2ZSBPcmRlcnMnLFxyXG4gICdjb21wbGV0ZWRfb3JkZXJzJzogJ0NvbXBsZXRlZCBPcmRlcnMnLFxyXG4gICd0b3RhbF9vcmRlcnMnOiAnVG90YWwgT3JkZXJzJyxcclxuICAndG90YWxfcmV2ZW51ZSc6ICdUb3RhbCBSZXZlbnVlJyxcclxuICAncmVjZW50X29yZGVycyc6ICdSZWNlbnQgT3JkZXJzJyxcclxuICAncXVpY2tfYWN0aW9ucyc6ICdRdWljayBBY3Rpb25zJyxcclxuICAndmlld19hbGxfb3JkZXJzJzogJ1ZpZXcgQWxsIE9yZGVycycsXHJcbiAgJ2FkZF9vcmRlcic6ICdBZGQgT3JkZXInLFxyXG4gICdtYW5hZ2Vfd29ya2Vycyc6ICdNYW5hZ2UgV29ya2VycycsXHJcbiAgJ3ZpZXdfcmVwb3J0cyc6ICdWaWV3IFJlcG9ydHMnLFxyXG4gICdjbGllbnRfbmFtZV9yZXF1aXJlZCc6ICdDbGllbnQgTmFtZSAqJyxcclxuICAncGhvbmVfcmVxdWlyZWQnOiAnUGhvbmUgTnVtYmVyIConLFxyXG4gICdvcmRlcl9kZXNjcmlwdGlvbl9yZXF1aXJlZCc6ICdPcmRlciBEZXNjcmlwdGlvbiAqJyxcclxuICAnZGVsaXZlcnlfZGF0ZV9yZXF1aXJlZCc6ICdEZWxpdmVyeSBEYXRlIConLFxyXG4gICdwcmljZV9zYXInOiAnUHJpY2UgKFNBUiknLFxyXG4gICdtZWFzdXJlbWVudHNfY20nOiAnTWVhc3VyZW1lbnRzIChjbSknLFxyXG4gICdhZGRpdGlvbmFsX25vdGVzJzogJ0FkZGl0aW9uYWwgTm90ZXMnLFxyXG4gICd2b2ljZV9ub3Rlc19vcHRpb25hbCc6ICdWb2ljZSBOb3RlcyAoT3B0aW9uYWwpJyxcclxuICAnZGVzaWduX2ltYWdlcyc6ICdEZXNpZ24gSW1hZ2VzJyxcclxuICAnZmFicmljX3R5cGUnOiAnRmFicmljIFR5cGUnLFxyXG4gICdmYWJyaWNfdHlwZV9vcHRpb25hbCc6ICdGYWJyaWMgVHlwZScsXHJcbiAgJ3Jlc3BvbnNpYmxlX3dvcmtlcic6ICdSZXNwb25zaWJsZSBXb3JrZXInLFxyXG4gICdjaG9vc2Vfd29ya2VyJzogJ0Nob29zZSBSZXNwb25zaWJsZSBXb3JrZXInLFxyXG4gICdzdGF0dXNfYW5kX3dvcmtlcic6ICdTdGF0dXMgYW5kIFdvcmtlcicsXHJcbiAgJ29yZGVyX3N0YXR1cyc6ICdPcmRlciBTdGF0dXMnLFxyXG4gICdhZGRpdGlvbmFsX25vdGVzX3BsYWNlaG9sZGVyJzogJ0FueSBhZGRpdGlvbmFsIG5vdGVzIG9yIGRldGFpbHMuLi4nLFxyXG4gICdub3Rfc3BlY2lmaWVkJzogJ05vdCBTcGVjaWZpZWQnLFxyXG4gICdiYWNrX3RvX2Rhc2hib2FyZCc6ICdCYWNrIHRvIERhc2hib2FyZCcsXHJcbiAgJ292ZXJ2aWV3X3RvZGF5JzogJ092ZXJ2aWV3IG9mIHRvZGF5XFwncyBhY3Rpdml0aWVzJyxcclxuICAnd2VsY29tZV93b3JrZXInOiAnV2VsY29tZSB0byB5b3VyIHdvcmtzcGFjZScsXHJcblxyXG4gIC8vINmF2YHYp9iq2YrYrSDZhdmB2YLZiNiv2Kkg2YXZhiDZhNmI2K3YqSDYp9mE2KrYrdmD2YVcclxuICAnaG9tZXBhZ2UnOiAnSG9tZXBhZ2UnLFxyXG4gICdteV9jb21wbGV0ZWRfb3JkZXJzJzogJ015IENvbXBsZXRlZCBPcmRlcnMnLFxyXG4gICdteV90b3RhbF9vcmRlcnMnOiAnTXkgVG90YWwgT3JkZXJzJyxcclxuICAnYWN0aXZlX29yZGVycyc6ICdBY3RpdmUgT3JkZXJzJyxcclxuICAndG9kYXlfYXBwb2ludG1lbnRzJzogJ1RvZGF5XFwncyBBcHBvaW50bWVudHMnLFxyXG4gICdzdGF0aXN0aWNzJzogJ1N0YXRpc3RpY3MnLFxyXG4gICdub19vcmRlcnNfZm91bmQnOiAnTm8gb3JkZXJzIGZvdW5kJyxcclxuICAndmlld19hbGwnOiAnVmlldyBBbGwnLFxyXG4gICd3b3JrZXJfbWFuYWdlbWVudCc6ICdXb3JrZXIgTWFuYWdlbWVudCcsXHJcbiAgJ3JlbWluZGVyJzogJ1JlbWluZGVyJyxcclxuICAneW91X2hhdmUnOiAnWW91IGhhdmUnLFxyXG4gICd0b2RheV9hcHBvaW50bWVudHNfcmVtaW5kZXInOiAnYXBwb2ludG1lbnRzIHRvZGF5JyxcclxuICAnYW5kJzogJ2FuZCcsXHJcbiAgJ29yZGVyc19uZWVkX2ZvbGxvdyc6ICdvcmRlcnMgdGhhdCBuZWVkIGZvbGxvdy11cCcsXHJcbiAgJ2RldGFpbGVkX3JlcG9ydHMnOiAnRGV0YWlsZWQgUmVwb3J0cycsXHJcbiAgJ3dvcmtlcl9kZXNjcmlwdGlvbic6ICdIZXJlIHlvdSBjYW4gdHJhY2sgeW91ciBhc3NpZ25lZCBvcmRlcnMgYW5kIHVwZGF0ZSB0aGVpciBzdGF0dXMnLFxyXG5cclxuICAvLyDZhdmB2KfYqtmK2K0g2LXZgdit2Kkg2KXYttin2YHYqSDYp9mE2LfZhNio2KfYqlxyXG4gICdvcmRlcl9hZGRfZXJyb3InOiAnQW4gZXJyb3Igb2NjdXJyZWQgd2hpbGUgYWRkaW5nIHRoZSBvcmRlcicsXHJcbiAgJ2NtX3BsYWNlaG9sZGVyJzogJ2NtJyxcclxuICAnc2hvdWxkZXInOiAnU2hvdWxkZXInLFxyXG4gICdzaG91bGRlcl9jaXJjdW1mZXJlbmNlJzogJ1Nob3VsZGVyIENpcmN1bWZlcmVuY2UnLFxyXG4gICdjaGVzdCc6ICdDaGVzdCcsXHJcbiAgJ3dhaXN0JzogJ1dhaXN0JyxcclxuICAnaGlwcyc6ICdIaXBzJyxcclxuICAnZGFydF9sZW5ndGgnOiAnRGFydCBMZW5ndGgnLFxyXG4gICdib2RpY2VfbGVuZ3RoJzogJ0JvZGljZSBMZW5ndGgnLFxyXG4gICduZWNrbGluZSc6ICdOZWNrbGluZScsXHJcbiAgJ2FybXBpdCc6ICdBcm1waXQnLFxyXG4gICdzbGVldmVfbGVuZ3RoJzogJ1NsZWV2ZSBMZW5ndGgnLFxyXG4gICdmb3JlYXJtJzogJ0ZvcmVhcm0nLFxyXG4gICdjdWZmJzogJ0N1ZmYnLFxyXG4gICdmcm9udF9sZW5ndGgnOiAnRnJvbnQgTGVuZ3RoJyxcclxuICAnYmFja19sZW5ndGgnOiAnQmFjayBMZW5ndGgnLFxyXG5cclxuICAvLyDZhdmB2KfYqtmK2K0g2LXZgdit2Kkg2KfZhNi32YTYqNin2KpcclxuICAnZW50ZXJfb3JkZXJfbnVtYmVyJzogJ0VudGVyIG9yZGVyIG51bWJlcicsXHJcbiAgJ3NlYXJjaF9wbGFjZWhvbGRlcic6ICdTZWFyY2ggYnkgbmFtZSwgb3JkZXIgbnVtYmVyLCBvciBkZXNjcmlwdGlvbi4uLicsXHJcbiAgJ3NlYXJjaF9ieV90ZXh0JzogJ1NlYXJjaCBieSBUZXh0JyxcclxuICAnc2VhcmNoX2J5X29yZGVyX251bWJlcic6ICdTZWFyY2ggYnkgT3JkZXIgTnVtYmVyJyxcclxuICAnYWxsX29yZGVycyc6ICdBbGwgT3JkZXJzJyxcclxuICAnbm9fb3JkZXJzX2Fzc2lnbmVkJzogJ05vIG9yZGVycyBhc3NpZ25lZCB0byB5b3UnLFxyXG4gICdub19vcmRlcnNfYXNzaWduZWRfZGVzYyc6ICdObyBvcmRlcnMgaGF2ZSBiZWVuIGFzc2lnbmVkIHRvIHlvdSB5ZXQnLFxyXG4gICdub19vcmRlcnNfZm91bmRfZGVzYyc6ICdObyBvcmRlcnMgZm91bmQgbWF0Y2hpbmcgdGhlIHNlYXJjaCBjcml0ZXJpYScsXHJcbiAgJ3ByaWNlX2xhYmVsJzogJ1ByaWNlJyxcclxuICAnc2FyJzogJ1NBUicsXHJcbiAgJ3ZpZXcnOiAnVmlldycsXHJcbiAgJ2NvbXBsZXRpbmcnOiAnQ29tcGxldGluZy4uLicsXHJcbiAgJ3N0YXJ0X3dvcmsnOiAnU3RhcnQgV29yaycsXHJcbiAgJ2NvbXBsZXRlX29yZGVyJzogJ0NvbXBsZXRlIE9yZGVyJyxcclxuICAnY29tcGxldGVfb3JkZXJfbW9kYWxfdGl0bGUnOiAnQ29tcGxldGUgT3JkZXIgYW5kIFVwbG9hZCBGaW5pc2hlZCBXb3JrIEltYWdlcycsXHJcbiAgJ2ltcG9ydGFudF93YXJuaW5nJzogJ0ltcG9ydGFudCBXYXJuaW5nJyxcclxuICAnY29tcGxldGVfb3JkZXJfd2FybmluZyc6ICdPbmNlIHlvdSBjb21wbGV0ZSB0aGUgb3JkZXIsIHlvdSB3aWxsIG5vdCBiZSBhYmxlIHRvIG1vZGlmeSBpdHMgc3RhdHVzIGFnYWluLiBNYWtlIHN1cmUgdG8gdXBsb2FkIGFsbCBmaW5pc2hlZCB3b3JrIGltYWdlcyBiZWZvcmUgcHJvY2VlZGluZy4nLFxyXG4gICdvcmRlcl9kZWxldGVkX3N1Y2Nlc3NmdWxseSc6ICdPcmRlciBkZWxldGVkIHN1Y2Nlc3NmdWxseScsXHJcblxyXG4gIC8vINmF2YHYp9iq2YrYrSDZhdmD2YjZhiDYrdiw2YEg2KfZhNi32YTYqFxyXG4gICdjb25maXJtX2RlbGV0ZV9vcmRlcic6ICdDb25maXJtIERlbGV0ZSBPcmRlcicsXHJcbiAgJ3dhcm5pbmdfZGVsZXRlX29yZGVyJzogJ1dhcm5pbmc6IERlbGV0ZSBPcmRlcicsXHJcbiAgJ2RlbGV0ZV9vcmRlcl93YXJuaW5nX21lc3NhZ2UnOiAnVGhpcyBhY3Rpb24gY2Fubm90IGJlIHVuZG9uZS4gVGhlIG9yZGVyIGFuZCBhbGwgYXNzb2NpYXRlZCBkYXRhIHdpbGwgYmUgcGVybWFuZW50bHkgZGVsZXRlZC4nLFxyXG4gICdhZG1pbl9lbWFpbCc6ICdBZG1pbiBFbWFpbCcsXHJcbiAgJ2FkbWluX3Bhc3N3b3JkJzogJ0FkbWluIFBhc3N3b3JkJyxcclxuICAnZW50ZXJfYWRtaW5fZW1haWwnOiAnRW50ZXIgYWRtaW4gZW1haWwnLFxyXG4gICdlbnRlcl9hZG1pbl9wYXNzd29yZCc6ICdFbnRlciBhZG1pbiBwYXNzd29yZCcsXHJcbiAgJ3BsZWFzZV9maWxsX2FsbF9maWVsZHMnOiAnUGxlYXNlIGZpbGwgYWxsIGZpZWxkcycsXHJcbiAgJ2VtYWlsX2RvZXNfbm90X21hdGNoJzogJ0VtYWlsIGRvZXMgbm90IG1hdGNoIHRoZSByZWdpc3RlcmVkIGFkbWluIGVtYWlsJyxcclxuICAnaW5jb3JyZWN0X3Bhc3N3b3JkJzogJ0luY29ycmVjdCBwYXNzd29yZCcsXHJcbiAgJ2NvbmZpcm1fZGVsZXRlJzogJ0NvbmZpcm0gRGVsZXRlJyxcclxuXHJcbiAgLy8g2YXZgdin2KrZititINmF2YPZiNmGINin2YTZhdmE2KfYrdi42KfYqiDYp9mE2LXZiNiq2YrYqVxyXG4gICdzdGFydF9yZWNvcmRpbmcnOiAnU3RhcnQgUmVjb3JkaW5nJyxcclxuICAnc3RvcF9yZWNvcmRpbmcnOiAnU3RvcCBSZWNvcmRpbmcnLFxyXG4gICdjbGlja190b19yZWNvcmRfdm9pY2Vfbm90ZSc6ICdDbGljayB0byByZWNvcmQgYSB2b2ljZSBub3RlJyxcclxuICAndm9pY2Vfbm90ZXMnOiAnVm9pY2UgTm90ZXMnLFxyXG4gICd2b2ljZV9ub3RlJzogJ1ZvaWNlIE5vdGUnLFxyXG4gICdtaWNyb3Bob25lX2FjY2Vzc19lcnJvcic6ICdNaWNyb3Bob25lIGFjY2VzcyBlcnJvcicsXHJcblxyXG4gIC8vINmF2YHYp9iq2YrYrSDYtdmB2K3YqSDYp9mE2LnZhdin2YRcclxuICAnd29ya2VyX2FkZGVkX3N1Y2Nlc3MnOiAnV29ya2VyIGFkZGVkIHN1Y2Nlc3NmdWxseScsXHJcbiAgJ2Vycm9yX2FkZGluZ193b3JrZXInOiAnRXJyb3IgYWRkaW5nIHdvcmtlcicsXHJcbiAgJ3dvcmtlcl91cGRhdGVkX3N1Y2Nlc3MnOiAnV29ya2VyIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5JyxcclxuICAnZXJyb3JfdXBkYXRpbmdfd29ya2VyJzogJ0Vycm9yIHVwZGF0aW5nIHdvcmtlcicsXHJcbiAgJ3dvcmtlcl9kZWxldGVkX3N1Y2Nlc3MnOiAnV29ya2VyIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5JyxcclxuICAnd29ya2VyX2RlYWN0aXZhdGVkJzogJ1dvcmtlciBkZWFjdGl2YXRlZCcsXHJcbiAgJ3dvcmtlcl9hY3RpdmF0ZWQnOiAnV29ya2VyIGFjdGl2YXRlZCcsXHJcbiAgJ2FkZGluZyc6ICdBZGRpbmcuLi4nLFxyXG4gICdhZGRfd29ya2VyJzogJ0FkZCBXb3JrZXInLFxyXG4gICdhY3RpdmUnOiAnQWN0aXZlJyxcclxuICAnaW5hY3RpdmUnOiAnSW5hY3RpdmUnLFxyXG4gICdzYXZlX2NoYW5nZXMnOiAnU2F2ZSBDaGFuZ2VzJyxcclxuICAnc2VhcmNoX3dvcmtlcnNfcGxhY2Vob2xkZXInOiAnU2VhcmNoIHdvcmtlcnMuLi4nLFxyXG4gICd3b3JrZXJzX21hbmFnZW1lbnQnOiAnV29ya2VycyBNYW5hZ2VtZW50JyxcclxuICAndmlld19tYW5hZ2VfdGVhbSc6ICdWaWV3IGFuZCBtYW5hZ2UgdGhlIHRhaWxvcmluZyB3b3Jrc2hvcCB0ZWFtJyxcclxuICAnYWRkX25ld193b3JrZXInOiAnQWRkIE5ldyBXb3JrZXInLFxyXG4gICdhZGRfbmV3X3dvcmtlcl9mb3JtJzogJ0FkZCBOZXcgV29ya2VyJyxcclxuICAnZnVsbF9uYW1lX3JlcXVpcmVkJzogJ0Z1bGwgTmFtZSAqJyxcclxuICAnZW1haWxfcmVxdWlyZWQnOiAnRW1haWwgKicsXHJcbiAgJ3Bhc3N3b3JkX3JlcXVpcmVkJzogJ1Bhc3N3b3JkIConLFxyXG4gICdwaG9uZV9yZXF1aXJlZF93b3JrZXInOiAnUGhvbmUgTnVtYmVyIConLFxyXG4gICdlbnRlcl9mdWxsX25hbWUnOiAnRW50ZXIgZnVsbCBuYW1lJyxcclxuICAnZW50ZXJfZW1haWwnOiAnRW50ZXIgZW1haWwnLFxyXG4gICdlbnRlcl9wYXNzd29yZCc6ICdFbnRlciBwYXNzd29yZCcsXHJcbiAgJ2VudGVyX3Bob25lJzogJ0VudGVyIHBob25lIG51bWJlcicsXHJcbiAgJ3NwZWNpYWx0eV9yZXF1aXJlZCc6ICdTcGVjaWFsdHkgKicsXHJcbiAgJ3NwZWNpYWx0eV9leGFtcGxlJzogJ0V4YW1wbGU6IEV2ZW5pbmcgZHJlc3MgdGFpbG9yaW5nJyxcclxuICAnZWRpdF93b3JrZXInOiAnRWRpdCBXb3JrZXInLFxyXG4gICduZXdfcGFzc3dvcmQnOiAnTmV3IFBhc3N3b3JkJyxcclxuICAnbGVhdmVfZW1wdHlfbm9fY2hhbmdlJzogJ0xlYXZlIGVtcHR5IGlmIHlvdSBkb25cXCd0IHdhbnQgdG8gY2hhbmdlIGl0JyxcclxuICAnbm9fd29ya2Vycyc6ICdObyB3b3JrZXJzJyxcclxuICAnbm9fd29ya2Vyc19mb3VuZCc6ICdObyB3b3JrZXJzIGZvdW5kIG1hdGNoaW5nIHRoZSBzZWFyY2ggY3JpdGVyaWEnLFxyXG4gICdqb2luZWRfb24nOiAnSm9pbmVkIG9uJyxcclxuICAndG90YWxfd29ya2Vycyc6ICdUb3RhbCBXb3JrZXJzJyxcclxuICAnYWN0aXZlX3dvcmtlcnMnOiAnQWN0aXZlIFdvcmtlcnMnLFxyXG4gICd0b3RhbF9jb21wbGV0ZWRfb3JkZXJzJzogJ1RvdGFsIENvbXBsZXRlZCBPcmRlcnMnLFxyXG4gICdjb25maXJtX2RlbGV0ZV93b3JrZXInOiAnQXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGRlbGV0ZSB0aGlzIHdvcmtlcj8nLFxyXG5cclxuICAvLyDZhdmB2KfYqtmK2K0g2LXZgdit2Kkg2KfZhNiq2YLYp9ix2YrYsVxyXG4gICdlbmdhZ2VtZW50X2RyZXNzJzogJ0VuZ2FnZW1lbnQgRHJlc3MnLFxyXG4gICdjYXN1YWxfZHJlc3MnOiAnQ2FzdWFsIERyZXNzJyxcclxuICAnb3RoZXInOiAnT3RoZXInLFxyXG4gICd0aGlzX3dlZWsnOiAnVGhpcyBXZWVrJyxcclxuICAndGhpc19tb250aCc6ICdUaGlzIE1vbnRoJyxcclxuICAndGhpc19xdWFydGVyJzogJ1RoaXMgUXVhcnRlcicsXHJcbiAgJ3RoaXNfeWVhcic6ICdUaGlzIFllYXInLFxyXG4gICdyZXBvcnRzX2FuYWx5dGljcyc6ICdSZXBvcnRzICYgQW5hbHl0aWNzJyxcclxuICAnbW9udGhseV90cmVuZCc6ICdNb250aGx5IFRyZW5kJyxcclxuICAncmV2ZW51ZV9sYWJlbCc6ICdSZXZlbnVlJyxcclxuXHJcbiAgLy8g2YXZgdin2KrZititINi12YHYrdipINin2YTZhdmI2KfYudmK2K9cclxuICAnY29uZmlybWVkJzogJ0NvbmZpcm1lZCcsXHJcbiAgJ3BtJzogJ1BNJyxcclxuICAnYW0nOiAnQU0nLFxyXG4gICdhbGxfc3RhdHVzZXMnOiAnQWxsIFN0YXR1c2VzJyxcclxuICAnYWxsX2RhdGVzJzogJ0FsbCBEYXRlcycsXHJcbiAgJ3RvZGF5JzogJ1RvZGF5JyxcclxuICAndG9tb3Jyb3cnOiAnVG9tb3Jyb3cnLFxyXG4gICdhcHBvaW50bWVudHNfbWFuYWdlbWVudCc6ICdBcHBvaW50bWVudHMgTWFuYWdlbWVudCcsXHJcbiAgJ3ZpZXdfbWFuYWdlX2FwcG9pbnRtZW50cyc6ICdWaWV3IGFuZCBtYW5hZ2UgYWxsIHRhaWxvcmluZyBhcHBvaW50bWVudHMnLFxyXG4gICdib29rX25ld19hcHBvaW50bWVudCc6ICdCb29rIE5ldyBBcHBvaW50bWVudCcsXHJcbiAgJ3NlYXJjaF9hcHBvaW50bWVudHNfcGxhY2Vob2xkZXInOiAnU2VhcmNoIGFwcG9pbnRtZW50cy4uLicsXHJcbiAgJ25vX2FwcG9pbnRtZW50cyc6ICdObyBhcHBvaW50bWVudHMnLFxyXG4gICdub19hcHBvaW50bWVudHNfZm91bmQnOiAnTm8gYXBwb2ludG1lbnRzIGZvdW5kIG1hdGNoaW5nIHRoZSBzZWFyY2ggY3JpdGVyaWEnLFxyXG4gICdjcmVhdGVkX29uJzogJ0NyZWF0ZWQgb24nLFxyXG4gICdjb25maXJtX2FwcG9pbnRtZW50JzogJ0NvbmZpcm0gQXBwb2ludG1lbnQnLFxyXG4gICdjYW5jZWxfYXBwb2ludG1lbnQnOiAnQ2FuY2VsIEFwcG9pbnRtZW50JyxcclxuXHJcbiAgLy8g2YXZgdin2KrZititINmF2YPZiNmG2KfYqiDYpdi22KfZgdmK2KlcclxuICAnb2YnOiAnb2YnLFxyXG4gICdpbWFnZXNfdGV4dCc6ICdpbWFnZXMnLFxyXG5cclxuICAvLyDZhdmB2KfYqtmK2K0g2YXZg9mI2YbYp9iqINin2YTYtdmI2LEg2YjYp9mE2KrYrdmF2YrZhFxyXG4gICdtYXhfaW1hZ2VzX3JlYWNoZWQnOiAnTWF4aW11bSBpbWFnZXMgcmVhY2hlZCcsXHJcbiAgJ2Ryb3BfaW1hZ2VzX2hlcmUnOiAnRHJvcCBpbWFnZXMgaGVyZScsXHJcbiAgJ2NsaWNrX29yX2RyYWdfaW1hZ2VzJzogJ0NsaWNrIG9yIGRyYWcgaW1hZ2VzIGhlcmUnLFxyXG4gICdpbWFnZV91cGxvYWRfZm9ybWF0JzogJ1BORywgSlBHLCBKUEVHIHVwIHRvIDVNQicsXHJcbiAgJ21heF9pbWFnZXNfdGV4dCc6ICdNYXhpbXVtJyxcclxuICAnb3JkZXJfbGFiZWwnOiAnT3JkZXInLFxyXG4gICdmb3JfY2xpZW50JzogJ0ZvciBjbGllbnQnLFxyXG5cclxuICAvLyDZhdmB2KfYqtmK2K0g2YXZg9mI2YYg2KrYudiv2YrZhCDYp9mE2LfZhNioXHJcbiAgJ29yZGVyX3VwZGF0ZV9lcnJvcic6ICdFcnJvciB1cGRhdGluZyBvcmRlcicsXHJcbiAgJ3ByaWNlX3Nhcl9yZXF1aXJlZCc6ICdQcmljZSAoU0FSKSAqJyxcclxuICAnc3RhdHVzX3BlbmRpbmcnOiAnUGVuZGluZycsXHJcbiAgJ3N0YXR1c19pbl9wcm9ncmVzcyc6ICdJbiBQcm9ncmVzcycsXHJcbiAgJ3N0YXR1c19jb21wbGV0ZWQnOiAnQ29tcGxldGVkJyxcclxuICAnc3RhdHVzX2RlbGl2ZXJlZCc6ICdEZWxpdmVyZWQnLFxyXG4gICdzdGF0dXNfY2FuY2VsbGVkJzogJ0NhbmNlbGxlZCcsXHJcblxyXG4gIC8vINmG2LXZiNi1INin2YTZgdmI2KrYsVxyXG4gICdob21lJzogJ0hvbWUnLFxyXG4gICd0cmFja19vcmRlcic6ICdUcmFjayBPcmRlcicsXHJcbiAgJ2ZhYnJpY3MnOiAnRmFicmljcycsXHJcbiAgJ2NvbnRhY3RfdXMnOiAnQ29udGFjdCBVcycsXHJcbiAgJ3lhc21pbl9hbHNoYW0nOiAnWWFzbWluIEFsc2hhbScsXHJcbiAgJ2N1c3RvbV9kcmVzc190YWlsb3JpbmcnOiAnQ3VzdG9tIERyZXNzIFRhaWxvcmluZydcclxufVxyXG5cclxuLy8gSG9vayDZhNmE2KrYsdis2YXYqVxyXG5leHBvcnQgZnVuY3Rpb24gdXNlVHJhbnNsYXRpb24oKSB7XHJcbiAgY29uc3QgW2xhbmd1YWdlLCBzZXRMYW5ndWFnZV0gPSB1c2VTdGF0ZTwnYXInIHwgJ2VuJz4oJ2FyJylcclxuXHJcbiAgLy8g2KrYrdmF2YrZhCDYp9mE2YTYutipINin2YTZhdit2YHZiNi42Kkg2LnZhtivINio2K/YoSDYp9mE2KrYt9io2YrZglxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBzYXZlZExhbmd1YWdlID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2Rhc2hib2FyZC1sYW5ndWFnZScpIGFzICdhcicgfCAnZW4nXHJcbiAgICBpZiAoc2F2ZWRMYW5ndWFnZSkge1xyXG4gICAgICBzZXRMYW5ndWFnZShzYXZlZExhbmd1YWdlKVxyXG4gICAgfVxyXG4gIH0sIFtdKVxyXG5cclxuICAvLyDYrdmB2Lgg2KfZhNmE2LrYqSDYudmG2K8g2KrYutmK2YrYsdmH2KdcclxuICBjb25zdCBjaGFuZ2VMYW5ndWFnZSA9IChuZXdMYW5ndWFnZTogJ2FyJyB8ICdlbicpID0+IHtcclxuICAgIHNldExhbmd1YWdlKG5ld0xhbmd1YWdlKVxyXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2Rhc2hib2FyZC1sYW5ndWFnZScsIG5ld0xhbmd1YWdlKVxyXG4gIH1cclxuXHJcbiAgLy8g2K/Yp9mE2Kkg2KfZhNiq2LHYrNmF2KlcclxuICBjb25zdCB0ID0gKGtleTogVHJhbnNsYXRpb25LZXkpOiBzdHJpbmcgPT4ge1xyXG4gICAgY29uc3QgdHJhbnNsYXRpb25zID0gbGFuZ3VhZ2UgPT09ICdhcicgPyBhclRyYW5zbGF0aW9ucyA6IGVuVHJhbnNsYXRpb25zXHJcbiAgICBjb25zdCB0cmFuc2xhdGlvbiA9IHRyYW5zbGF0aW9uc1trZXldXHJcbiAgICBcclxuICAgIGlmICh0eXBlb2YgdHJhbnNsYXRpb24gPT09ICdzdHJpbmcnKSB7XHJcbiAgICAgIHJldHVybiB0cmFuc2xhdGlvblxyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyDYpdiw2Kcg2YTZhSDYqtmI2KzYryDYp9mE2KrYsdis2YXYqdiMINij2LHYrNi5INin2YTZhdmB2KrYp9itINmG2YHYs9mHXHJcbiAgICByZXR1cm4ga2V5XHJcbiAgfVxyXG5cclxuICAvLyDYp9mE2KrYrdmC2YIg2YXZhiDYp9mE2YTYutipINin2YTYrdin2YTZitipXHJcbiAgY29uc3QgaXNBcmFiaWMgPSBsYW5ndWFnZSA9PT0gJ2FyJ1xyXG4gIGNvbnN0IGlzRW5nbGlzaCA9IGxhbmd1YWdlID09PSAnZW4nXHJcblxyXG4gIHJldHVybiB7XHJcbiAgICBsYW5ndWFnZSxcclxuICAgIGNoYW5nZUxhbmd1YWdlLFxyXG4gICAgdCxcclxuICAgIGlzQXJhYmljLFxyXG4gICAgaXNFbmdsaXNoXHJcbiAgfVxyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsImFyVHJhbnNsYXRpb25zIiwiZW5UcmFuc2xhdGlvbnMiLCJ1c2VUcmFuc2xhdGlvbiIsImxhbmd1YWdlIiwic2V0TGFuZ3VhZ2UiLCJzYXZlZExhbmd1YWdlIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImNoYW5nZUxhbmd1YWdlIiwibmV3TGFuZ3VhZ2UiLCJzZXRJdGVtIiwidCIsImtleSIsInRyYW5zbGF0aW9ucyIsInRyYW5zbGF0aW9uIiwiaXNBcmFiaWMiLCJpc0VuZ2xpc2giXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useTranslation.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   appointmentService: () => (/* binding */ appointmentService),\n/* harmony export */   cartService: () => (/* binding */ cartService),\n/* harmony export */   designService: () => (/* binding */ designService),\n/* harmony export */   fabricService: () => (/* binding */ fabricService),\n/* harmony export */   favoriteService: () => (/* binding */ favoriteService),\n/* harmony export */   orderService: () => (/* binding */ orderService),\n/* harmony export */   productService: () => (/* binding */ productService),\n/* harmony export */   statsService: () => (/* binding */ statsService),\n/* harmony export */   userService: () => (/* binding */ userService),\n/* harmony export */   workerService: () => (/* binding */ workerService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\n// ========================================\n// خدمات المستخدمين\n// ========================================\nconst userService = {\n    // تسجيل الدخول\n    async signIn (email, password) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        return {\n            user: data.user,\n            error\n        };\n    },\n    // تسجيل الخروج\n    async signOut () {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n        return {\n            error\n        };\n    },\n    // الحصول على المستخدم الحالي\n    async getCurrentUser () {\n        const { data: { user }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        return {\n            user,\n            error\n        };\n    },\n    // إنشاء مستخدم جديد\n    async createUser (userData) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('users').insert([\n            userData\n        ]).select().single();\n        return {\n            user: data,\n            error\n        };\n    },\n    // تحديث مستخدم\n    async updateUser (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('users').update(updates).eq('id', id).select().single();\n        return {\n            user: data,\n            error\n        };\n    }\n};\n// ========================================\n// خدمات العمال\n// ========================================\nconst workerService = {\n    // الحصول على جميع العمال\n    async getAllWorkers () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('workers').select('*').order('created_at', {\n            ascending: false\n        });\n        return {\n            workers: data,\n            error\n        };\n    },\n    // الحصول على عامل واحد\n    async getWorker (id) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('workers').select('*').eq('id', id).single();\n        return {\n            worker: data,\n            error\n        };\n    },\n    // إنشاء عامل جديد\n    async createWorker (workerData) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('workers').insert([\n            workerData\n        ]).select().single();\n        return {\n            worker: data,\n            error\n        };\n    },\n    // تحديث عامل\n    async updateWorker (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('workers').update(updates).eq('id', id).select().single();\n        return {\n            worker: data,\n            error\n        };\n    },\n    // حذف عامل\n    async deleteWorker (id) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('workers').delete().eq('id', id);\n        return {\n            error\n        };\n    }\n};\n// ========================================\n// خدمات المنتجات\n// ========================================\nconst productService = {\n    // الحصول على جميع المنتجات\n    async getAllProducts () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('products').select('*').eq('is_available', true).order('created_at', {\n            ascending: false\n        });\n        return {\n            products: data,\n            error\n        };\n    },\n    // الحصول على منتج واحد\n    async getProduct (id) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('products').select('*').eq('id', id).single();\n        return {\n            product: data,\n            error\n        };\n    },\n    // إنشاء منتج جديد\n    async createProduct (productData) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('products').insert([\n            productData\n        ]).select().single();\n        return {\n            product: data,\n            error\n        };\n    },\n    // تحديث منتج\n    async updateProduct (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('products').update(updates).eq('id', id).select().single();\n        return {\n            product: data,\n            error\n        };\n    },\n    // حذف منتج\n    async deleteProduct (id) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('products').delete().eq('id', id);\n        return {\n            error\n        };\n    }\n};\n// ========================================\n// خدمات التصاميم\n// ========================================\nconst designService = {\n    // الحصول على جميع التصاميم\n    async getAllDesigns () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('designs').select('*').eq('is_available', true).order('created_at', {\n            ascending: false\n        });\n        return {\n            designs: data,\n            error\n        };\n    },\n    // الحصول على تصميم واحد\n    async getDesign (id) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('designs').select('*').eq('id', id).single();\n        return {\n            design: data,\n            error\n        };\n    },\n    // الحصول على التصاميم حسب الفئة\n    async getDesignsByCategory (category) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('designs').select('*').eq('category', category).eq('is_available', true).order('created_at', {\n            ascending: false\n        });\n        return {\n            designs: data,\n            error\n        };\n    },\n    // إنشاء تصميم جديد\n    async createDesign (designData) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('designs').insert([\n            designData\n        ]).select().single();\n        return {\n            design: data,\n            error\n        };\n    },\n    // تحديث تصميم\n    async updateDesign (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('designs').update(updates).eq('id', id).select().single();\n        return {\n            design: data,\n            error\n        };\n    },\n    // حذف تصميم\n    async deleteDesign (id) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('designs').delete().eq('id', id);\n        return {\n            error\n        };\n    }\n};\n// ========================================\n// خدمات المواعيد\n// ========================================\nconst appointmentService = {\n    // الحصول على جميع المواعيد\n    async getAllAppointments () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('appointments').select('*').order('appointment_date', {\n            ascending: true\n        }).order('appointment_time', {\n            ascending: true\n        });\n        return {\n            appointments: data,\n            error\n        };\n    },\n    // الحصول على موعد واحد\n    async getAppointment (id) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('appointments').select('*').eq('id', id).single();\n        return {\n            appointment: data,\n            error\n        };\n    },\n    // الحصول على المواعيد حسب التاريخ\n    async getAppointmentsByDate (date) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('appointments').select('*').eq('appointment_date', date).order('appointment_time', {\n            ascending: true\n        });\n        return {\n            appointments: data,\n            error\n        };\n    },\n    // إنشاء موعد جديد\n    async createAppointment (appointmentData) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('appointments').insert([\n            appointmentData\n        ]).select().single();\n        return {\n            appointment: data,\n            error\n        };\n    },\n    // تحديث موعد\n    async updateAppointment (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('appointments').update(updates).eq('id', id).select().single();\n        return {\n            appointment: data,\n            error\n        };\n    },\n    // حذف موعد\n    async deleteAppointment (id) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('appointments').delete().eq('id', id);\n        return {\n            error\n        };\n    }\n};\n// ========================================\n// خدمات الطلبات\n// ========================================\nconst orderService = {\n    // الحصول على جميع الطلبات\n    async getAllOrders () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('orders').select('*').order('created_at', {\n            ascending: false\n        });\n        return {\n            orders: data,\n            error\n        };\n    },\n    // الحصول على طلب واحد\n    async getOrder (id) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('orders').select('*').eq('id', id).single();\n        return {\n            order: data,\n            error\n        };\n    },\n    // الحصول على الطلبات حسب الحالة\n    async getOrdersByStatus (status) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('orders').select('*').eq('status', status).order('created_at', {\n            ascending: false\n        });\n        return {\n            orders: data,\n            error\n        };\n    },\n    // إنشاء طلب جديد\n    async createOrder (orderData) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('orders').insert([\n            orderData\n        ]).select().single();\n        return {\n            order: data,\n            error\n        };\n    },\n    // تحديث طلب\n    async updateOrder (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('orders').update(updates).eq('id', id).select().single();\n        return {\n            order: data,\n            error\n        };\n    },\n    // حذف طلب\n    async deleteOrder (id) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('orders').delete().eq('id', id);\n        return {\n            error\n        };\n    }\n};\n// ========================================\n// خدمات الأقمشة\n// ========================================\nconst fabricService = {\n    // الحصول على جميع الأقمشة\n    async getAllFabrics () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('fabrics').select('*').eq('is_available', true).order('created_at', {\n            ascending: false\n        });\n        return {\n            fabrics: data,\n            error\n        };\n    },\n    // الحصول على قماش واحد\n    async getFabric (id) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('fabrics').select('*').eq('id', id).single();\n        return {\n            fabric: data,\n            error\n        };\n    },\n    // إنشاء قماش جديد\n    async createFabric (fabricData) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('fabrics').insert([\n            fabricData\n        ]).select().single();\n        return {\n            fabric: data,\n            error\n        };\n    },\n    // تحديث قماش\n    async updateFabric (id, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('fabrics').update(updates).eq('id', id).select().single();\n        return {\n            fabric: data,\n            error\n        };\n    },\n    // حذف قماش\n    async deleteFabric (id) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('fabrics').delete().eq('id', id);\n        return {\n            error\n        };\n    }\n};\n// ========================================\n// خدمات المفضلة\n// ========================================\nconst favoriteService = {\n    // الحصول على مفضلات المستخدم\n    async getUserFavorites (userId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('favorites').select(`\n        *,\n        products (*)\n      `).eq('user_id', userId).order('created_at', {\n            ascending: false\n        });\n        return {\n            favorites: data,\n            error\n        };\n    },\n    // إضافة إلى المفضلة\n    async addToFavorites (userId, productId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('favorites').insert([\n            {\n                user_id: userId,\n                product_id: productId\n            }\n        ]).select().single();\n        return {\n            favorite: data,\n            error\n        };\n    },\n    // إزالة من المفضلة\n    async removeFromFavorites (userId, productId) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('favorites').delete().eq('user_id', userId).eq('product_id', productId);\n        return {\n            error\n        };\n    },\n    // التحقق من وجود المنتج في المفضلة\n    async isFavorite (userId, productId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('favorites').select('id').eq('user_id', userId).eq('product_id', productId).single();\n        return {\n            isFavorite: !!data,\n            error\n        };\n    }\n};\n// ========================================\n// خدمات سلة التسوق\n// ========================================\nconst cartService = {\n    // الحصول على سلة المستخدم\n    async getUserCart (userId) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('cart_items').select(`\n        *,\n        products (*)\n      `).eq('user_id', userId).order('created_at', {\n            ascending: false\n        });\n        return {\n            cartItems: data,\n            error\n        };\n    },\n    // إضافة منتج إلى السلة\n    async addToCart (userId, productId, quantity = 1, size, color) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('cart_items').insert([\n            {\n                user_id: userId,\n                product_id: productId,\n                quantity,\n                selected_size: size,\n                selected_color: color\n            }\n        ]).select().single();\n        return {\n            cartItem: data,\n            error\n        };\n    },\n    // تحديث كمية المنتج في السلة\n    async updateCartItemQuantity (userId, productId, quantity) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('cart_items').update({\n            quantity\n        }).eq('user_id', userId).eq('product_id', productId).select().single();\n        return {\n            cartItem: data,\n            error\n        };\n    },\n    // إزالة منتج من السلة\n    async removeFromCart (userId, productId) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('cart_items').delete().eq('user_id', userId).eq('product_id', productId);\n        return {\n            error\n        };\n    },\n    // تفريغ سلة المستخدم\n    async clearUserCart (userId) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('cart_items').delete().eq('user_id', userId);\n        return {\n            error\n        };\n    }\n};\n// ========================================\n// خدمات الإحصائيات\n// ========================================\nconst statsService = {\n    // الحصول على إحصائيات الطلبات\n    async getOrderStats () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('order_stats').select('*').single();\n        return {\n            stats: data,\n            error\n        };\n    },\n    // الحصول على إحصائيات المواعيد\n    async getAppointmentStats () {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('appointment_stats').select('*').single();\n        return {\n            stats: data,\n            error\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/database.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCurrentUserId: () => (/* binding */ getCurrentUserId),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://lgwujnpxvrcryebhjdtc.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imxnd3VqbnB4dnJjcnllYmhqZHRjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNzEyNjQsImV4cCI6MjA2NzY0NzI2NH0.-VbkK1R4AXWS6UqzAYL_nKx4yNQ7A-EmF8Z0O24eOWg\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// دالة للحصول على معرف المستخدم الحالي\nconst getCurrentUserId = async ()=>{\n    try {\n        // محاولة الحصول من Supabase أولاً\n        const { data: { user }, error } = await supabase.auth.getUser();\n        if (user && !error) {\n            return user.id;\n        }\n        // إذا لم يكن هناك مستخدم في Supabase، نتحقق من localStorage\n        if (false) {}\n        return null;\n    } catch (error) {\n        console.error('خطأ في الحصول على معرف المستخدم:', error);\n        return null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/authStore.ts":
/*!********************************!*\
  !*** ./src/store/authStore.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// بيانات المستخدمين الافتراضية (سيتم استبدالها بنظام إدارة العمال)\nconst getStoredUsers = ()=>{\n    if (true) return [];\n    const stored = localStorage.getItem('yasmin-users');\n    if (stored) {\n        return JSON.parse(stored);\n    }\n    // المستخدمين الافتراضيين\n    const defaultUsers = [\n        {\n            id: '1',\n            email: '<EMAIL>',\n            password: 'admin123',\n            full_name: 'مدير النظام',\n            role: 'admin',\n            is_active: true\n        }\n    ];\n    localStorage.setItem('yasmin-users', JSON.stringify(defaultUsers));\n    return defaultUsers;\n};\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        user: null,\n        isLoading: false,\n        error: null,\n        signIn: async (email, password)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                console.log('🔐 بدء عملية تسجيل الدخول...', {\n                    email\n                });\n                // محاكاة تأخير الشبكة\n                await new Promise((resolve)=>setTimeout(resolve, 1500));\n                // البحث عن المستخدم في البيانات المحفوظة\n                const users = getStoredUsers();\n                const foundUser = users.find((user)=>user.email.toLowerCase() === email.toLowerCase() && user.password === password);\n                if (foundUser) {\n                    console.log('✅ تم العثور على المستخدم:', foundUser.full_name);\n                    const user = {\n                        id: foundUser.id,\n                        email: foundUser.email,\n                        full_name: foundUser.full_name,\n                        role: foundUser.role,\n                        is_active: foundUser.is_active,\n                        created_at: new Date().toISOString(),\n                        updated_at: new Date().toISOString(),\n                        token: `demo-token-${foundUser.id}-${Date.now()}`\n                    };\n                    // حفظ في localStorage أولاً\n                    if (false) {}\n                    // تحديث حالة المتجر\n                    set({\n                        user,\n                        isLoading: false,\n                        error: null\n                    });\n                    console.log('🎉 تم تسجيل الدخول بنجاح!');\n                    return true;\n                } else {\n                    console.log('❌ بيانات تسجيل الدخول غير صحيحة');\n                    set({\n                        error: 'بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور.',\n                        isLoading: false\n                    });\n                    return false;\n                }\n            } catch (error) {\n                console.error('💥 خطأ في تسجيل الدخول:', error);\n                set({\n                    error: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',\n                    isLoading: false\n                });\n                return false;\n            }\n        },\n        signOut: async ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                // محاكاة تأخير تسجيل الخروج\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                // مسح البيانات من localStorage\n                if (false) {}\n                set({\n                    user: null,\n                    isLoading: false,\n                    error: null\n                });\n            } catch (error) {\n                console.error('خطأ في تسجيل الخروج:', error);\n                set({\n                    isLoading: false,\n                    error: 'خطأ في تسجيل الخروج'\n                });\n            }\n        },\n        setUser: (user)=>{\n            set({\n                user\n            });\n            // تحديث localStorage\n            if (false) {}\n        },\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        checkAuth: async ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                // التحقق من وجود مستخدم محفوظ في localStorage\n                if (false) {}\n                set({\n                    user: null,\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error('خطأ في التحقق من المصادقة:', error);\n                set({\n                    user: null,\n                    isLoading: false\n                });\n            }\n        },\n        isAuthenticated: ()=>{\n            const state = get();\n            return state.user !== null && state.user.is_active;\n        }\n    }), {\n    name: 'yasmin-auth-storage',\n    partialize: (state)=>({\n            user: state.user\n        })\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/authStore.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/dataStore.ts":
/*!********************************!*\
  !*** ./src/store/dataStore.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDataStore: () => (/* binding */ useDataStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/database */ \"(ssr)/./src/lib/database.ts\");\n\n\n\n// دالة مساعدة لتحويل الأخطاء\nconst handleError = (error)=>{\n    if (error?.message) {\n        return error.message;\n    }\n    if (typeof error === 'string') {\n        return error;\n    }\n    return 'خطأ غير معروف';\n};\n// دالة مساعدة لمعالجة الأخطاء في المتجر\nconst setError = (set, error)=>{\n    set({\n        error: handleError(error),\n        isLoading: false\n    });\n};\n// دالة تحويل من قاعدة البيانات إلى الواجهة المحلية\nconst mapDBAppointmentToLocal = (dbAppointment)=>({\n        id: dbAppointment.id,\n        clientName: dbAppointment.client_name,\n        clientPhone: dbAppointment.client_phone,\n        appointmentDate: dbAppointment.appointment_date,\n        appointmentTime: dbAppointment.appointment_time,\n        notes: dbAppointment.notes || undefined,\n        status: dbAppointment.status,\n        createdAt: dbAppointment.created_at,\n        updatedAt: dbAppointment.updated_at\n    });\nconst mapLocalAppointmentToDB = (localAppointment)=>({\n        client_name: localAppointment.clientName,\n        client_phone: localAppointment.clientPhone,\n        appointment_date: localAppointment.appointmentDate,\n        appointment_time: localAppointment.appointmentTime,\n        status: localAppointment.status,\n        notes: localAppointment.notes\n    });\nconst mapDBOrderToLocal = (dbOrder)=>({\n        id: dbOrder.id,\n        clientName: dbOrder.client_name,\n        clientPhone: dbOrder.client_phone,\n        description: dbOrder.description,\n        fabric: dbOrder.fabric || '',\n        measurements: dbOrder.measurements || {},\n        price: dbOrder.price,\n        status: dbOrder.status,\n        assignedWorker: dbOrder.assigned_worker_id || undefined,\n        dueDate: dbOrder.due_date,\n        notes: dbOrder.notes || undefined,\n        voiceNotes: dbOrder.voice_notes || undefined,\n        images: dbOrder.images || undefined,\n        completedImages: dbOrder.completed_images || undefined,\n        createdAt: dbOrder.created_at,\n        updatedAt: dbOrder.updated_at\n    });\nconst mapLocalOrderToDB = (localOrder)=>({\n        client_name: localOrder.clientName,\n        client_phone: localOrder.clientPhone,\n        description: localOrder.description,\n        fabric: localOrder.fabric,\n        measurements: localOrder.measurements,\n        price: localOrder.price,\n        status: localOrder.status,\n        assigned_worker_id: localOrder.assignedWorker,\n        due_date: localOrder.dueDate,\n        notes: localOrder.notes,\n        voice_notes: localOrder.voiceNotes,\n        images: localOrder.images,\n        completed_images: localOrder.completedImages\n    });\nconst mapDBWorkerToLocal = (dbWorker)=>({\n        id: dbWorker.id,\n        email: dbWorker.email,\n        password: '',\n        full_name: dbWorker.full_name,\n        phone: dbWorker.phone || '',\n        specialty: dbWorker.specialty || '',\n        role: 'worker',\n        is_active: dbWorker.is_active,\n        createdAt: dbWorker.created_at,\n        updatedAt: dbWorker.updated_at\n    });\nconst mapLocalWorkerToDB = (localWorker)=>({\n        user_id: '',\n        email: localWorker.email,\n        full_name: localWorker.full_name,\n        phone: localWorker.phone,\n        specialty: localWorker.specialty,\n        role: 'worker',\n        is_active: localWorker.is_active,\n        experience_years: undefined,\n        hourly_rate: undefined,\n        performance_rating: undefined,\n        total_completed_orders: undefined\n    });\nconst useDataStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.persist)((set, get)=>({\n        // البيانات الأولية\n        appointments: [],\n        orders: [],\n        workers: [],\n        isLoading: false,\n        error: null,\n        // إدارة المواعيد\n        addAppointment: async (appointmentData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const dbAppointment = mapLocalAppointmentToDB(appointmentData);\n                const { appointment, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.appointmentService.createAppointment(dbAppointment);\n                if (error) {\n                    set({\n                        error: handleError(error),\n                        isLoading: false\n                    });\n                    return;\n                }\n                if (appointment) {\n                    const localAppointment = mapDBAppointmentToLocal(appointment);\n                    set((state)=>({\n                            appointments: [\n                                ...state.appointments,\n                                localAppointment\n                            ],\n                            error: null,\n                            isLoading: false\n                        }));\n                    console.log('✅ تم إضافة موعد جديد:', localAppointment);\n                }\n            } catch (error) {\n                set({\n                    error: 'خطأ في إضافة الموعد',\n                    isLoading: false\n                });\n                console.error('خطأ في إضافة الموعد:', error);\n            }\n        },\n        updateAppointment: async (id, updates)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const dbUpdates = {};\n                if (updates.clientName) dbUpdates.client_name = updates.clientName;\n                if (updates.clientPhone) dbUpdates.client_phone = updates.clientPhone;\n                if (updates.appointmentDate) dbUpdates.appointment_date = updates.appointmentDate;\n                if (updates.appointmentTime) dbUpdates.appointment_time = updates.appointmentTime;\n                if (updates.status) dbUpdates.status = updates.status;\n                if (updates.notes !== undefined) dbUpdates.notes = updates.notes;\n                const { appointment, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.appointmentService.updateAppointment(id, dbUpdates);\n                if (error) {\n                    set({\n                        error: handleError(error),\n                        isLoading: false\n                    });\n                    return;\n                }\n                if (appointment) {\n                    const localAppointment = mapDBAppointmentToLocal(appointment);\n                    set((state)=>({\n                            appointments: state.appointments.map((apt)=>apt.id === id ? localAppointment : apt),\n                            error: null,\n                            isLoading: false\n                        }));\n                    console.log('✅ تم تحديث الموعد:', id);\n                }\n            } catch (error) {\n                set({\n                    error: 'خطأ في تحديث الموعد',\n                    isLoading: false\n                });\n                console.error('خطأ في تحديث الموعد:', error);\n            }\n        },\n        deleteAppointment: async (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.appointmentService.deleteAppointment(id);\n                if (error) {\n                    setError(set, error);\n                    return;\n                }\n                set((state)=>({\n                        appointments: state.appointments.filter((appointment)=>appointment.id !== id),\n                        error: null,\n                        isLoading: false\n                    }));\n                console.log('✅ تم حذف الموعد:', id);\n            } catch (error) {\n                set({\n                    error: 'خطأ في حذف الموعد',\n                    isLoading: false\n                });\n                console.error('خطأ في حذف الموعد:', error);\n            }\n        },\n        getAppointment: (id)=>{\n            const state = get();\n            return state.appointments.find((appointment)=>appointment.id === id);\n        },\n        loadAppointments: async ()=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { appointments, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.appointmentService.getAllAppointments();\n                if (error) {\n                    setError(set, error);\n                    return;\n                }\n                const localAppointments = appointments?.map(mapDBAppointmentToLocal) || [];\n                set({\n                    appointments: localAppointments,\n                    error: null,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: 'خطأ في تحميل المواعيد',\n                    isLoading: false\n                });\n                console.error('خطأ في تحميل المواعيد:', error);\n            }\n        },\n        // إدارة الطلبات\n        addOrder: async (orderData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const dbOrder = mapLocalOrderToDB(orderData);\n                const { order, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.orderService.createOrder(dbOrder);\n                if (error) {\n                    setError(set, error);\n                    return;\n                }\n                if (order) {\n                    const localOrder = mapDBOrderToLocal(order);\n                    set((state)=>({\n                            orders: [\n                                ...state.orders,\n                                localOrder\n                            ],\n                            error: null,\n                            isLoading: false\n                        }));\n                    console.log('✅ تم إضافة طلب جديد:', localOrder);\n                }\n            } catch (error) {\n                set({\n                    error: 'خطأ في إضافة الطلب',\n                    isLoading: false\n                });\n                console.error('خطأ في إضافة الطلب:', error);\n            }\n        },\n        updateOrder: async (id, updates)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const dbUpdates = {};\n                if (updates.clientName) dbUpdates.client_name = updates.clientName;\n                if (updates.clientPhone) dbUpdates.client_phone = updates.clientPhone;\n                if (updates.description) dbUpdates.description = updates.description;\n                if (updates.fabric) dbUpdates.fabric = updates.fabric;\n                if (updates.measurements) dbUpdates.measurements = updates.measurements;\n                if (updates.price) dbUpdates.price = updates.price;\n                if (updates.status) dbUpdates.status = updates.status;\n                if (updates.assignedWorker) dbUpdates.assigned_worker_id = updates.assignedWorker;\n                if (updates.dueDate) dbUpdates.due_date = updates.dueDate;\n                if (updates.notes !== undefined) dbUpdates.notes = updates.notes;\n                if (updates.voiceNotes) dbUpdates.voice_notes = updates.voiceNotes;\n                if (updates.images) dbUpdates.images = updates.images;\n                if (updates.completedImages) dbUpdates.completed_images = updates.completedImages;\n                const { order, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.orderService.updateOrder(id, dbUpdates);\n                if (error) {\n                    setError(set, error);\n                    return;\n                }\n                if (order) {\n                    const localOrder = mapDBOrderToLocal(order);\n                    set((state)=>({\n                            orders: state.orders.map((ord)=>ord.id === id ? localOrder : ord),\n                            error: null,\n                            isLoading: false\n                        }));\n                    console.log('✅ تم تحديث الطلب:', id);\n                }\n            } catch (error) {\n                set({\n                    error: 'خطأ في تحديث الطلب',\n                    isLoading: false\n                });\n                console.error('خطأ في تحديث الطلب:', error);\n            }\n        },\n        deleteOrder: async (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.orderService.deleteOrder(id);\n                if (error) {\n                    setError(set, error);\n                    return;\n                }\n                set((state)=>({\n                        orders: state.orders.filter((order)=>order.id !== id),\n                        error: null,\n                        isLoading: false\n                    }));\n                console.log('✅ تم حذف الطلب:', id);\n            } catch (error) {\n                set({\n                    error: 'خطأ في حذف الطلب',\n                    isLoading: false\n                });\n                console.error('خطأ في حذف الطلب:', error);\n            }\n        },\n        getOrder: (id)=>{\n            const state = get();\n            return state.orders.find((order)=>order.id === id);\n        },\n        loadOrders: async ()=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { orders, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.orderService.getAllOrders();\n                if (error) {\n                    set({\n                        error: handleError(error),\n                        isLoading: false\n                    });\n                    return;\n                }\n                const localOrders = orders?.map(mapDBOrderToLocal) || [];\n                set({\n                    orders: localOrders,\n                    error: null,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: 'خطأ في تحميل الطلبات',\n                    isLoading: false\n                });\n                console.error('خطأ في تحميل الطلبات:', error);\n            }\n        },\n        // دوال خاصة للعمال\n        startOrderWork: async (orderId, workerId)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { order, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.orderService.updateOrder(orderId, {\n                    status: 'in_progress',\n                    assigned_worker_id: workerId\n                });\n                if (error) {\n                    set({\n                        error: handleError(error),\n                        isLoading: false\n                    });\n                    return;\n                }\n                if (order) {\n                    const localOrder = mapDBOrderToLocal(order);\n                    set((state)=>({\n                            orders: state.orders.map((ord)=>ord.id === orderId ? localOrder : ord),\n                            error: null,\n                            isLoading: false\n                        }));\n                    console.log('✅ تم بدء العمل على الطلب:', orderId);\n                }\n            } catch (error) {\n                set({\n                    error: 'خطأ في بدء العمل على الطلب',\n                    isLoading: false\n                });\n                console.error('خطأ في بدء العمل على الطلب:', error);\n            }\n        },\n        completeOrder: async (orderId, workerId, completedImages)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { order, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.orderService.updateOrder(orderId, {\n                    status: 'completed',\n                    completed_images: completedImages\n                });\n                if (error) {\n                    set({\n                        error: handleError(error),\n                        isLoading: false\n                    });\n                    return;\n                }\n                if (order) {\n                    const localOrder = mapDBOrderToLocal(order);\n                    set((state)=>({\n                            orders: state.orders.map((ord)=>ord.id === orderId ? localOrder : ord),\n                            error: null,\n                            isLoading: false\n                        }));\n                    console.log('✅ تم إكمال الطلب:', orderId);\n                }\n            } catch (error) {\n                set({\n                    error: 'خطأ في إكمال الطلب',\n                    isLoading: false\n                });\n                console.error('خطأ في إكمال الطلب:', error);\n            }\n        },\n        // إدارة العمال\n        addWorker: async (workerData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const dbWorker = mapLocalWorkerToDB(workerData);\n                const { worker, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.workerService.createWorker(dbWorker);\n                if (error) {\n                    set({\n                        error: handleError(error),\n                        isLoading: false\n                    });\n                    return;\n                }\n                if (worker) {\n                    const localWorker = mapDBWorkerToLocal(worker);\n                    set((state)=>({\n                            workers: [\n                                ...state.workers,\n                                localWorker\n                            ],\n                            error: null,\n                            isLoading: false\n                        }));\n                    console.log('✅ تم إضافة عامل جديد:', localWorker);\n                }\n            } catch (error) {\n                set({\n                    error: 'خطأ في إضافة العامل',\n                    isLoading: false\n                });\n                console.error('خطأ في إضافة العامل:', error);\n            }\n        },\n        updateWorker: async (id, updates)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const dbUpdates = {};\n                if (updates.email) dbUpdates.email = updates.email;\n                if (updates.full_name) dbUpdates.full_name = updates.full_name;\n                if (updates.phone) dbUpdates.phone = updates.phone;\n                if (updates.specialty) dbUpdates.specialty = updates.specialty;\n                if (updates.is_active !== undefined) dbUpdates.is_active = updates.is_active;\n                const { worker, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.workerService.updateWorker(id, dbUpdates);\n                if (error) {\n                    set({\n                        error: handleError(error),\n                        isLoading: false\n                    });\n                    return;\n                }\n                if (worker) {\n                    const localWorker = mapDBWorkerToLocal(worker);\n                    set((state)=>({\n                            workers: state.workers.map((wrk)=>wrk.id === id ? localWorker : wrk),\n                            error: null,\n                            isLoading: false\n                        }));\n                    console.log('✅ تم تحديث العامل:', id);\n                }\n            } catch (error) {\n                set({\n                    error: 'خطأ في تحديث العامل',\n                    isLoading: false\n                });\n                console.error('خطأ في تحديث العامل:', error);\n            }\n        },\n        deleteWorker: async (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.workerService.deleteWorker(id);\n                if (error) {\n                    set({\n                        error: handleError(error),\n                        isLoading: false\n                    });\n                    return;\n                }\n                set((state)=>({\n                        workers: state.workers.filter((worker)=>worker.id !== id),\n                        error: null,\n                        isLoading: false\n                    }));\n                console.log('✅ تم حذف العامل:', id);\n            } catch (error) {\n                set({\n                    error: 'خطأ في حذف العامل',\n                    isLoading: false\n                });\n                console.error('خطأ في حذف العامل:', error);\n            }\n        },\n        getWorker: (id)=>{\n            const state = get();\n            return state.workers.find((worker)=>worker.id === id);\n        },\n        loadWorkers: async ()=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { workers, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.workerService.getAllWorkers();\n                if (error) {\n                    set({\n                        error: handleError(error),\n                        isLoading: false\n                    });\n                    return;\n                }\n                const localWorkers = workers?.map(mapDBWorkerToLocal) || [];\n                set({\n                    workers: localWorkers,\n                    error: null,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: 'خطأ في تحميل العمال',\n                    isLoading: false\n                });\n                console.error('خطأ في تحميل العمال:', error);\n            }\n        },\n        // وظائف مساعدة\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        loadData: async ()=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                await Promise.all([\n                    get().loadAppointments(),\n                    get().loadOrders(),\n                    get().loadWorkers()\n                ]);\n                set({\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: 'خطأ في تحميل البيانات',\n                    isLoading: false\n                });\n                console.error('خطأ في تحميل البيانات:', error);\n            }\n        },\n        // إحصائيات\n        getStats: ()=>{\n            const state = get();\n            const totalAppointments = state.appointments.length;\n            const totalOrders = state.orders.length;\n            const totalWorkers = state.workers.length;\n            const pendingAppointments = state.appointments.filter((apt)=>apt.status === 'pending').length;\n            const activeOrders = state.orders.filter((order)=>order.status === 'in_progress').length;\n            const completedOrders = state.orders.filter((order)=>order.status === 'completed').length;\n            const totalRevenue = state.orders.filter((order)=>order.status === 'completed' || order.status === 'delivered').reduce((sum, order)=>sum + order.price, 0);\n            return {\n                totalAppointments,\n                totalOrders,\n                totalWorkers,\n                pendingAppointments,\n                activeOrders,\n                completedOrders,\n                totalRevenue\n            };\n        }\n    }), {\n    name: 'yasmin-alsham-data',\n    partialize: (state)=>({\n            appointments: state.appointments,\n            orders: state.orders,\n            workers: state.workers\n        })\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/dataStore.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/zustand","vendor-chunks/motion-utils","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fappointments%2Fpage&page=%2Fdashboard%2Fappointments%2Fpage&appPaths=%2Fdashboard%2Fappointments%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fappointments%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();