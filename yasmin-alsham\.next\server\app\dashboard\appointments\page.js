(()=>{var e={};e.id=876,e.ids=[876],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},9184:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\appointments\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,t,r)=>{"use strict";var a=r(65773);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26787:(e,t,r)=>{"use strict";r.d(t,{v:()=>o});var a=r(43210);let s=e=>{let t,r=new Set,a=(e,a)=>{let s="function"==typeof e?e(t):e;if(!Object.is(s,t)){let e=t;t=(null!=a?a:"object"!=typeof s||null===s)?s:Object.assign({},t,s),r.forEach(r=>r(t,e))}},s=()=>t,n={setState:a,getState:s,getInitialState:()=>i,subscribe:e=>(r.add(e),()=>r.delete(e))},i=t=e(a,s,n);return n},n=e=>e?s(e):s,i=e=>e,l=e=>{let t=n(e),r=e=>(function(e,t=i){let r=a.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return a.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},o=e=>e?l(e):l},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29718:(e,t,r)=>{Promise.resolve().then(r.bind(r,9184))},33873:e=>{"use strict";e.exports=require("path")},35071:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40228:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48340:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},48730:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},53350:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var a=r(60687),s=r(43210),n=r(26001),i=r(16189),l=r(85814),o=r.n(l),d=r(99720),c=r(34270),p=r(99182),m=r(48730),u=r(5336),x=r(35071),h=r(70334),g=r(96474),b=r(99270),y=r(80462),v=r(40228),f=r(48340);function j(){let{user:e}=(0,d.n)(),{appointments:t,updateAppointment:r,deleteAppointment:l}=(0,c.D)(),{t:j,isArabic:N}=(0,p.B)();(0,i.useRouter)();let[k,w]=(0,s.useState)(""),[A,S]=(0,s.useState)("all"),[_,P]=(0,s.useState)("all"),D=e=>{let t={pending:{label:j("pending"),color:"text-blue-600",bgColor:"bg-blue-100",icon:m.A},confirmed:{label:j("confirmed"),color:"text-green-600",bgColor:"bg-green-100",icon:u.A},completed:{label:j("completed"),color:"text-purple-600",bgColor:"bg-purple-100",icon:u.A},cancelled:{label:j("cancelled"),color:"text-red-600",bgColor:"bg-red-100",icon:x.A}};return t[e]||t.pending},C=e=>{r(e,{status:"confirmed"})},M=e=>{r(e,{status:"completed"})},I=e=>{r(e,{status:"cancelled"})},L=e=>new Date(e).toLocaleDateString("ar-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),q=e=>{let[t,r]=e.split(":"),a=parseInt(t),s=a>=12?j("pm"):j("am");return`${a>12?a-12:0===a?12:a}:${r} ${s}`},z=e=>e===new Date().toISOString().split("T")[0],H=e=>{let t=new Date;return t.setDate(t.getDate()+1),e===t.toISOString().split("T")[0]},O=t.filter(e=>{let t=e.clientName.toLowerCase().includes(k.toLowerCase())||e.clientPhone.includes(k)||e.id.toLowerCase().includes(k.toLowerCase()),r="all"===A||e.status===A,a=!0;if("today"===_)a=z(e.appointmentDate);else if("tomorrow"===_)a=H(e.appointmentDate);else if("week"===_){let t=new Date(e.appointmentDate),r=new Date,s=new Date;s.setDate(r.getDate()+7),a=t>=r&&t<=s}return t&&r&&a});return e?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-8",children:(0,a.jsxs)(o(),{href:"/dashboard",className:"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300",children:[(0,a.jsx)(h.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:j("back_to_dashboard")})]})}),(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold mb-2",children:(0,a.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent",children:j("appointments_management")})}),(0,a.jsx)("p",{className:"text-lg text-gray-600",children:j("view_manage_appointments")})]}),(0,a.jsxs)(o(),{href:"/book-appointment",className:"btn-primary inline-flex items-center justify-center space-x-2 space-x-reverse px-6 py-3 group",children:[(0,a.jsx)(g.A,{className:"w-5 h-5 group-hover:scale-110 transition-transform duration-300"}),(0,a.jsx)("span",{children:j("book_new_appointment")})]})]}),(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 mb-8",children:(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(b.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",value:k,onChange:e=>w(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:j("search_appointments_placeholder")})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(y.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsxs)("select",{value:A,onChange:e=>S(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",children:[(0,a.jsx)("option",{value:"all",children:j("all_statuses")}),(0,a.jsx)("option",{value:"pending",children:j("pending")}),(0,a.jsx)("option",{value:"confirmed",children:j("confirmed")}),(0,a.jsx)("option",{value:"completed",children:j("completed")}),(0,a.jsx)("option",{value:"cancelled",children:j("cancelled")})]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(v.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsxs)("select",{value:_,onChange:e=>P(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",children:[(0,a.jsx)("option",{value:"all",children:j("all_dates")}),(0,a.jsx)("option",{value:"today",children:j("today")}),(0,a.jsx)("option",{value:"tomorrow",children:j("tomorrow")}),(0,a.jsx)("option",{value:"week",children:j("this_week")})]})]})]})}),(0,a.jsx)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"space-y-6",children:0===O.length?(0,a.jsxs)("div",{className:"text-center py-12 bg-white/80 backdrop-blur-sm rounded-2xl border border-pink-100",children:[(0,a.jsx)(v.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-medium text-gray-600 mb-2",children:j("no_appointments")}),(0,a.jsx)("p",{className:"text-gray-500",children:j("no_appointments_found")})]}):O.map((e,t)=>(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*t},className:`bg-white/80 backdrop-blur-sm rounded-2xl p-6 border transition-all duration-300 hover:shadow-lg ${z(e.appointmentDate)?"border-pink-300 bg-pink-50/50":"border-pink-100"}`,children:(0,a.jsxs)("div",{className:"grid lg:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-1",children:e.clientName}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(f.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.clientPhone})]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["#",e.id]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${D(e.status).bgColor} ${D(e.status).color}`,children:D(e.status).label}),z(e.appointmentDate)&&(0,a.jsx)("span",{className:"px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs font-medium",children:j("today")})]})]}),e.notes&&(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:(0,a.jsx)("p",{className:"text-sm text-gray-700",children:e.notes})})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-1",children:j("date_time")}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse text-sm text-gray-600",children:[(0,a.jsx)(v.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:L(e.appointmentDate)})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse text-sm text-gray-600",children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:q(e.appointmentTime)})]})]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2 space-x-reverse",children:(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[j("created_on")," ",new Date(e.createdAt).toLocaleDateString(N?"ar-US":"en-US")]})})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:["pending"===e.status&&(0,a.jsx)("button",{onClick:()=>C(e.id),className:"btn-primary py-2 px-4 text-sm",children:j("confirm_appointment")}),"confirmed"===e.status&&(0,a.jsx)("button",{onClick:()=>M(e.id),className:"btn-secondary py-2 px-4 text-sm",children:j("mark_attended")}),"cancelled"!==e.status&&"completed"!==e.status&&(0,a.jsx)("button",{onClick:()=>I(e.id),className:"text-red-600 hover:text-red-700 py-2 px-4 text-sm border border-red-200 rounded-lg hover:bg-red-50 transition-all duration-300",children:j("cancel_appointment")})]})]})},e.id))}),(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"mt-12 grid grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:t.filter(e=>"pending"===e.status).length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:j("pending")})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-1",children:t.filter(e=>"confirmed"===e.status).length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:j("confirmed")})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600 mb-1",children:t.filter(e=>z(e.appointmentDate)).length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:j("today")})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600 mb-1",children:t.filter(e=>"completed"===e.status).length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:j("completed")})]})]})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:j("loading")})]})})}},59350:(e,t,r)=>{"use strict";r.d(t,{Zr:()=>s});let a=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>a(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>a(t)(e)}}},s=(e,t)=>(r,s,n)=>{let i,l={storage:function(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var t;let a=e=>null===e?null:JSON.parse(e,void 0),s=null!=(t=r.getItem(e))?t:null;return s instanceof Promise?s.then(a):a(s)},setItem:(e,t)=>r.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>r.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},o=!1,d=new Set,c=new Set,p=l.storage;if(!p)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${l.name}', the given storage is currently unavailable.`),r(...e)},s,n);let m=()=>{let e=l.partialize({...s()});return p.setItem(l.name,{state:e,version:l.version})},u=n.setState;n.setState=(e,t)=>{u(e,t),m()};let x=e((...e)=>{r(...e),m()},s,n);n.getInitialState=()=>x;let h=()=>{var e,t;if(!p)return;o=!1,d.forEach(e=>{var t;return e(null!=(t=s())?t:x)});let n=(null==(t=l.onRehydrateStorage)?void 0:t.call(l,null!=(e=s())?e:x))||void 0;return a(p.getItem.bind(p))(l.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===l.version)return[!1,e.state];else{if(l.migrate){let t=l.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[a,n]=e;if(r(i=l.merge(n,null!=(t=s())?t:x),!0),a)return m()}).then(()=>{null==n||n(i,void 0),i=s(),o=!0,c.forEach(e=>e(i))}).catch(e=>{null==n||n(void 0,e)})};return n.persist={setOptions:e=>{l={...l,...e},e.storage&&(p=e.storage)},clearStorage:()=>{null==p||p.removeItem(l.name)},getOptions:()=>l,rehydrate:()=>h(),hasHydrated:()=>o,onHydrate:e=>(d.add(e),()=>{d.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},l.skipHydration||h(),i||x}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70334:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},74014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=r(65239),s=r(48088),n=r(88170),i=r.n(n),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["dashboard",{children:["appointments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9184)),"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\appointments\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\appointments\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/appointments/page",pathname:"/dashboard/appointments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},76574:(e,t,r)=>{Promise.resolve().then(r.bind(r,53350))},79551:e=>{"use strict";e.exports=require("url")},80462:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},96474:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,507,146,814,985],()=>r(74014));module.exports=a})();