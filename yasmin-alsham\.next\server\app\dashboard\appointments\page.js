(()=>{var e={};e.id=876,e.ids=[876],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},9184:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\appointments\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\appointments\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12719:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["dashboard",{children:["appointments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9184)),"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\appointments\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\appointments\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/appointments/page",pathname:"/dashboard/appointments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},16189:(e,t,r)=>{"use strict";var s=r(65773);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26787:(e,t,r)=>{"use strict";r.d(t,{v:()=>l});var s=r(43210);let a=e=>{let t,r=new Set,s=(e,s)=>{let a="function"==typeof e?e(t):e;if(!Object.is(a,t)){let e=t;t=(null!=s?s:"object"!=typeof a||null===a)?a:Object.assign({},t,a),r.forEach(r=>r(t,e))}},a=()=>t,n={setState:s,getState:a,getInitialState:()=>i,subscribe:e=>(r.add(e),()=>r.delete(e))},i=t=e(s,a,n);return n},n=e=>e?a(e):a,i=e=>e,o=e=>{let t=n(e),r=e=>(function(e,t=i){let r=s.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return s.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},l=e=>e?o(e):o},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29718:(e,t,r)=>{Promise.resolve().then(r.bind(r,9184))},33873:e=>{"use strict";e.exports=require("path")},34270:(e,t,r)=>{"use strict";r.d(t,{D:()=>i});var s=r(26787),a=r(59350);let n=()=>Date.now().toString(36)+Math.random().toString(36).substr(2),i=(0,s.v)()((0,a.Zr)((e,t)=>({appointments:[],orders:[],workers:[],isLoading:!1,error:null,addAppointment:t=>{let r={...t,id:n(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({appointments:[...e.appointments,r],error:null})),console.log("✅ تم إضافة موعد جديد:",r)},updateAppointment:(t,r)=>{e(e=>({appointments:e.appointments.map(e=>e.id===t?{...e,...r,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الموعد:",t)},deleteAppointment:t=>{e(e=>({appointments:e.appointments.filter(e=>e.id!==t),error:null})),console.log("✅ تم حذف الموعد:",t)},getAppointment:e=>t().appointments.find(t=>t.id===e),addOrder:t=>{let r={...t,id:n(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({orders:[...e.orders,r],error:null})),console.log("✅ تم إضافة طلب جديد:",r)},updateOrder:(t,r)=>{e(e=>({orders:e.orders.map(e=>e.id===t?{...e,...r,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الطلب:",t)},deleteOrder:t=>{e(e=>({orders:e.orders.filter(e=>e.id!==t),error:null})),console.log("✅ تم حذف الطلب:",t)},getOrder:e=>t().orders.find(t=>t.id===e),addWorker:t=>{let r={...t,id:n(),role:"worker",is_active:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({workers:[...e.workers,r],error:null})),console.log("✅ تم إضافة عامل جديد:",r)},updateWorker:(t,r)=>{e(e=>({workers:e.workers.map(e=>e.id===t?{...e,...r,updatedAt:new Date().toISOString()}:e),error:null})),r.email||r.password||r.full_name,console.log("✅ تم تحديث العامل:",t)},deleteWorker:t=>{e(e=>({workers:e.workers.filter(e=>e.id!==t),error:null})),console.log("✅ تم حذف العامل:",t)},getWorker:e=>t().workers.find(t=>t.id===e),startOrderWork:(t,r)=>{e(e=>({orders:e.orders.map(e=>e.id===t&&e.assignedWorker===r?{...e,status:"in_progress",updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم بدء العمل في الطلب:",t)},completeOrder:(t,r,s=[])=>{e(e=>({orders:e.orders.map(e=>e.id===t&&e.assignedWorker===r?{...e,status:"completed",completedImages:s.length>0?s:void 0,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم إنهاء الطلب:",t)},clearError:()=>{e({error:null})},loadData:()=>{e({isLoading:!0}),e({isLoading:!1})},getStats:()=>{let e=t();return{totalAppointments:e.appointments.length,totalOrders:e.orders.length,totalWorkers:e.workers.length,pendingAppointments:e.appointments.filter(e=>"pending"===e.status).length,activeOrders:e.orders.filter(e=>["pending","in_progress"].includes(e.status)).length,completedOrders:e.orders.filter(e=>"completed"===e.status).length,totalRevenue:e.orders.filter(e=>"completed"===e.status).reduce((e,t)=>e+t.price,0)}}}),{name:"yasmin-data-storage",partialize:e=>({appointments:e.appointments,orders:e.orders,workers:e.workers})}))},35071:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40228:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48340:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},48730:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},53350:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var s=r(60687),a=r(43210),n=r(26001),i=r(16189),o=r(85814),l=r.n(o),d=r(99720),c=r(34270),p=r(99182),m=r(48730),u=r(5336),x=r(35071),g=r(70334),h=r(96474),b=r(99270),y=r(80462),v=r(40228),f=r(48340);function j(){let{user:e}=(0,d.n)(),{appointments:t,updateAppointment:r,deleteAppointment:o}=(0,c.D)(),{t:j,isArabic:w}=(0,p.B)();(0,i.useRouter)();let[k,N]=(0,a.useState)(""),[A,S]=(0,a.useState)("all"),[_,D]=(0,a.useState)("all"),I=e=>{let t={pending:{label:j("pending"),color:"text-blue-600",bgColor:"bg-blue-100",icon:m.A},confirmed:{label:j("confirmed"),color:"text-green-600",bgColor:"bg-green-100",icon:u.A},completed:{label:j("completed"),color:"text-purple-600",bgColor:"bg-purple-100",icon:u.A},cancelled:{label:j("cancelled"),color:"text-red-600",bgColor:"bg-red-100",icon:x.A}};return t[e]||t.pending},O=e=>{r(e,{status:"confirmed"})},P=e=>{r(e,{status:"completed"})},L=e=>{r(e,{status:"cancelled"})},C=e=>new Date(e).toLocaleDateString("ar-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),M=e=>{let[t,r]=e.split(":"),s=parseInt(t),a=s>=12?j("pm"):j("am");return`${s>12?s-12:0===s?12:s}:${r} ${a}`},q=e=>e===new Date().toISOString().split("T")[0],z=e=>{let t=new Date;return t.setDate(t.getDate()+1),e===t.toISOString().split("T")[0]},E=t.filter(e=>{let t=e.clientName.toLowerCase().includes(k.toLowerCase())||e.clientPhone.includes(k)||e.id.toLowerCase().includes(k.toLowerCase()),r="all"===A||e.status===A,s=!0;if("today"===_)s=q(e.appointmentDate);else if("tomorrow"===_)s=z(e.appointmentDate);else if("week"===_){let t=new Date(e.appointmentDate),r=new Date,a=new Date;a.setDate(r.getDate()+7),s=t>=r&&t<=a}return t&&r&&s});return e?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-8",children:(0,s.jsxs)(l(),{href:"/dashboard",className:"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300",children:[(0,s.jsx)(g.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:j("back_to_dashboard")})]})}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold mb-2",children:(0,s.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent",children:j("appointments_management")})}),(0,s.jsx)("p",{className:"text-lg text-gray-600",children:j("view_manage_appointments")})]}),(0,s.jsxs)(l(),{href:"/book-appointment",className:"btn-primary inline-flex items-center justify-center space-x-2 space-x-reverse px-6 py-3 group",children:[(0,s.jsx)(h.A,{className:"w-5 h-5 group-hover:scale-110 transition-transform duration-300"}),(0,s.jsx)("span",{children:j("book_new_appointment")})]})]}),(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 mb-8",children:(0,s.jsxs)("div",{className:"grid md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(b.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,s.jsx)("input",{type:"text",value:k,onChange:e=>N(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:j("search_appointments_placeholder")})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(y.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,s.jsxs)("select",{value:A,onChange:e=>S(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",children:[(0,s.jsx)("option",{value:"all",children:j("all_statuses")}),(0,s.jsx)("option",{value:"pending",children:j("pending")}),(0,s.jsx)("option",{value:"confirmed",children:j("confirmed")}),(0,s.jsx)("option",{value:"completed",children:j("completed")}),(0,s.jsx)("option",{value:"cancelled",children:j("cancelled")})]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(v.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,s.jsxs)("select",{value:_,onChange:e=>D(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",children:[(0,s.jsx)("option",{value:"all",children:j("all_dates")}),(0,s.jsx)("option",{value:"today",children:j("today")}),(0,s.jsx)("option",{value:"tomorrow",children:j("tomorrow")}),(0,s.jsx)("option",{value:"week",children:j("this_week")})]})]})]})}),(0,s.jsx)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"space-y-6",children:0===E.length?(0,s.jsxs)("div",{className:"text-center py-12 bg-white/80 backdrop-blur-sm rounded-2xl border border-pink-100",children:[(0,s.jsx)(v.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-medium text-gray-600 mb-2",children:j("no_appointments")}),(0,s.jsx)("p",{className:"text-gray-500",children:j("no_appointments_found")})]}):E.map((e,t)=>(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*t},className:`bg-white/80 backdrop-blur-sm rounded-2xl p-6 border transition-all duration-300 hover:shadow-lg ${q(e.appointmentDate)?"border-pink-300 bg-pink-50/50":"border-pink-100"}`,children:(0,s.jsxs)("div",{className:"grid lg:grid-cols-4 gap-6",children:[(0,s.jsxs)("div",{className:"lg:col-span-2",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-1",children:e.clientName}),(0,s.jsxs)("div",{className:"space-y-1 text-sm text-gray-600",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)(f.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.clientPhone})]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["#",e.id]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${I(e.status).bgColor} ${I(e.status).color}`,children:I(e.status).label}),q(e.appointmentDate)&&(0,s.jsx)("span",{className:"px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs font-medium",children:j("today")})]})]}),e.notes&&(0,s.jsx)("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:(0,s.jsx)("p",{className:"text-sm text-gray-700",children:e.notes})})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-1",children:j("date_time")}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse text-sm text-gray-600",children:[(0,s.jsx)(v.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:C(e.appointmentDate)})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse text-sm text-gray-600",children:[(0,s.jsx)(m.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:M(e.appointmentTime)})]})]})]}),(0,s.jsx)("div",{className:"flex items-center space-x-2 space-x-reverse",children:(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:[j("created_on")," ",new Date(e.createdAt).toLocaleDateString(w?"ar-US":"en-US")]})})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-2",children:["pending"===e.status&&(0,s.jsx)("button",{onClick:()=>O(e.id),className:"btn-primary py-2 px-4 text-sm",children:j("confirm_appointment")}),"confirmed"===e.status&&(0,s.jsx)("button",{onClick:()=>P(e.id),className:"btn-secondary py-2 px-4 text-sm",children:j("mark_attended")}),"cancelled"!==e.status&&"completed"!==e.status&&(0,s.jsx)("button",{onClick:()=>L(e.id),className:"text-red-600 hover:text-red-700 py-2 px-4 text-sm border border-red-200 rounded-lg hover:bg-red-50 transition-all duration-300",children:j("cancel_appointment")})]})]})},e.id))}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"mt-12 grid grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:t.filter(e=>"pending"===e.status).length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:j("pending")})]}),(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-1",children:t.filter(e=>"confirmed"===e.status).length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:j("confirmed")})]}),(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-orange-600 mb-1",children:t.filter(e=>q(e.appointmentDate)).length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:j("today")})]}),(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-purple-600 mb-1",children:t.filter(e=>"completed"===e.status).length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:j("completed")})]})]})]})}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:j("loading")})]})})}},59350:(e,t,r)=>{"use strict";r.d(t,{Zr:()=>a});let s=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>s(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>s(t)(e)}}},a=(e,t)=>(r,a,n)=>{let i,o={storage:function(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var t;let s=e=>null===e?null:JSON.parse(e,void 0),a=null!=(t=r.getItem(e))?t:null;return a instanceof Promise?a.then(s):s(a)},setItem:(e,t)=>r.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>r.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},l=!1,d=new Set,c=new Set,p=o.storage;if(!p)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),r(...e)},a,n);let m=()=>{let e=o.partialize({...a()});return p.setItem(o.name,{state:e,version:o.version})},u=n.setState;n.setState=(e,t)=>{u(e,t),m()};let x=e((...e)=>{r(...e),m()},a,n);n.getInitialState=()=>x;let g=()=>{var e,t;if(!p)return;l=!1,d.forEach(e=>{var t;return e(null!=(t=a())?t:x)});let n=(null==(t=o.onRehydrateStorage)?void 0:t.call(o,null!=(e=a())?e:x))||void 0;return s(p.getItem.bind(p))(o.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===o.version)return[!1,e.state];else{if(o.migrate){let t=o.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[s,n]=e;if(r(i=o.merge(n,null!=(t=a())?t:x),!0),s)return m()}).then(()=>{null==n||n(i,void 0),i=a(),l=!0,c.forEach(e=>e(i))}).catch(e=>{null==n||n(void 0,e)})};return n.persist={setOptions:e=>{o={...o,...e},e.storage&&(p=e.storage)},clearStorage:()=>{null==p||p.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>g(),hasHydrated:()=>l,onHydrate:e=>(d.add(e),()=>{d.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},o.skipHydration||g(),i||x}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70334:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},76574:(e,t,r)=>{Promise.resolve().then(r.bind(r,53350))},79551:e=>{"use strict";e.exports=require("url")},80462:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},96474:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},99720:(e,t,r)=>{"use strict";r.d(t,{n:()=>i});var s=r(26787),a=r(59350);let n=()=>[],i=(0,s.v)()((0,a.Zr)((e,t)=>({user:null,isLoading:!1,error:null,signIn:async(t,r)=>{e({isLoading:!0,error:null});try{console.log("\uD83D\uDD10 بدء عملية تسجيل الدخول...",{email:t}),await new Promise(e=>setTimeout(e,1500));let s=n().find(e=>e.email.toLowerCase()===t.toLowerCase()&&e.password===r);if(!s)return console.log("❌ بيانات تسجيل الدخول غير صحيحة"),e({error:"بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور.",isLoading:!1}),!1;{console.log("✅ تم العثور على المستخدم:",s.full_name);let t={id:s.id,email:s.email,full_name:s.full_name,role:s.role,is_active:s.is_active,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),token:`demo-token-${s.id}-${Date.now()}`};return e({user:t,isLoading:!1,error:null}),console.log("\uD83C\uDF89 تم تسجيل الدخول بنجاح!"),!0}}catch(t){return console.error("\uD83D\uDCA5 خطأ في تسجيل الدخول:",t),e({error:"حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.",isLoading:!1}),!1}},signOut:async()=>{e({isLoading:!0});try{await new Promise(e=>setTimeout(e,500)),e({user:null,isLoading:!1,error:null})}catch(t){console.error("خطأ في تسجيل الخروج:",t),e({isLoading:!1,error:"خطأ في تسجيل الخروج"})}},setUser:t=>{e({user:t})},clearError:()=>{e({error:null})},checkAuth:async()=>{e({isLoading:!0});try{e({user:null,isLoading:!1})}catch(t){console.error("خطأ في التحقق من المصادقة:",t),e({user:null,isLoading:!1})}},isAuthenticated:()=>{let e=t();return null!==e.user&&e.user.is_active}}),{name:"yasmin-auth-storage",partialize:e=>({user:e.user})}))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,507,146,814,59],()=>r(12719));module.exports=s})();