"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/designs/[id]/page",{

/***/ "(app-pages-browser)/./src/app/designs/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/designs/[id]/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DesignDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Heart,ShoppingBag,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Heart,ShoppingBag,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Heart,ShoppingBag,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Heart,ShoppingBag,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Heart,ShoppingBag,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Heart,ShoppingBag,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Heart,ShoppingBag,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _data_designs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/data/designs */ \"(app-pages-browser)/./src/data/designs.ts\");\n/* harmony import */ var _store_shopStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/shopStore */ \"(app-pages-browser)/./src/store/shopStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction DesignDetailPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const designId = parseInt(params.id);\n    const design = _data_designs__WEBPACK_IMPORTED_MODULE_4__.allDesigns.find((d)=>d.id === designId);\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isGalleryOpen, setIsGalleryOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSize, setSelectedSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedColor, setSelectedColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // متجر التسوق\n    const { addToFavorites, removeFromFavorites, isFavorite, addToCart } = (0,_store_shopStore__WEBPACK_IMPORTED_MODULE_5__.useShopStore)();\n    const [addedToCart, setAddedToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DesignDetailPage.useEffect\": ()=>{\n            if ((design === null || design === void 0 ? void 0 : design.sizes) && design.sizes.length > 0) {\n                setSelectedSize(design.sizes[0]);\n            }\n            if ((design === null || design === void 0 ? void 0 : design.colors) && design.colors.length > 0) {\n                setSelectedColor(design.colors[0]);\n            }\n        }\n    }[\"DesignDetailPage.useEffect\"], [\n        design\n    ]);\n    if (!design) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-800 mb-4\",\n                        children: \"التصميم غير موجود\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/designs\",\n                        className: \"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"العودة إلى التصاميم\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this);\n    }\n    const handleToggleFavorite = ()=>{\n        const product = {\n            id: design.id.toString(),\n            name: design.title,\n            price: design.price,\n            image: design.images[0],\n            description: design.description,\n            category: design.category\n        };\n        if (isFavorite(product.id)) {\n            removeFromFavorites(product.id);\n        } else {\n            addToFavorites(product);\n        }\n    };\n    const handleAddToCart = ()=>{\n        const product = {\n            id: design.id.toString(),\n            name: design.title,\n            price: design.price,\n            image: design.images[0],\n            description: design.description,\n            category: design.category\n        };\n        addToCart(product, 1, selectedSize, selectedColor);\n        setAddedToCart(true);\n        setTimeout(()=>{\n            setAddedToCart(false);\n        }, 2000);\n    };\n    const nextImage = ()=>{\n        setCurrentImageIndex((prev)=>(prev + 1) % design.images.length);\n    };\n    const prevImage = ()=>{\n        setCurrentImageIndex((prev)=>prev === 0 ? design.images.length - 1 : prev - 1);\n    };\n    const openGallery = ()=>{\n        setIsGalleryOpen(true);\n        document.body.style.overflow = 'hidden';\n    };\n    const closeGallery = ()=>{\n        setIsGalleryOpen(false);\n        document.body.style.overflow = 'unset';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-16 lg:pt-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6\n                },\n                className: \"fixed top-20 lg:top-24 left-4 lg:left-8 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    href: \"/designs\",\n                    className: \"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300 bg-white/80 backdrop-blur-sm px-3 py-2 rounded-lg shadow-sm border border-pink-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4 lg:w-5 lg:h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm lg:text-base\",\n                            children: \"العودة إلى التصاميم\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-2 gap-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative aspect-[4/5] bg-gradient-to-br from-pink-100 via-rose-100 to-purple-100 rounded-2xl overflow-hidden mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: design.images[currentImageIndex],\n                                                alt: \"\".concat(design.title, \" - صورة \").concat(currentImageIndex + 1),\n                                                className: \"w-full h-full object-cover cursor-pointer\",\n                                                onClick: openGallery\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 15\n                                            }, this),\n                                            design.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: prevImage,\n                                                        className: \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-all duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: nextImage,\n                                                        className: \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-all duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this),\n                                    design.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 overflow-x-auto\",\n                                        children: design.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setCurrentImageIndex(index),\n                                                className: \"flex-shrink-0 w-20 h-24 rounded-lg overflow-hidden border-2 transition-all duration-300 \".concat(currentImageIndex === index ? 'border-pink-500' : 'border-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: image,\n                                                    alt: \"\".concat(design.title, \" - صورة \").concat(index + 1),\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-pink-100 to-rose-100 text-pink-700 px-3 py-1 rounded-full text-sm font-medium\",\n                                                children: design.category\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl lg:text-4xl font-bold text-gray-800 mt-4 mb-4\",\n                                                children: design.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg text-gray-600 leading-relaxed\",\n                                                children: design.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-pink-600\",\n                                        children: (0,_store_shopStore__WEBPACK_IMPORTED_MODULE_5__.formatPrice)(design.price)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this),\n                                    design.rating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    ...Array(5)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-5 h-5 \".concat(i < Math.floor(design.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300')\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: [\n                                                    design.rating,\n                                                    \" (\",\n                                                    design.reviews_count,\n                                                    \" تقييم)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    design.sizes && design.sizes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-800 mb-3\",\n                                                children: \"المقاس\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: design.sizes.map((size)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedSize(size),\n                                                        className: \"px-4 py-2 border rounded-lg transition-all duration-300 \".concat(selectedSize === size ? 'border-pink-500 bg-pink-50 text-pink-700' : 'border-gray-300 hover:border-pink-300'),\n                                                        children: size\n                                                    }, size, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    design.colors && design.colors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-800 mb-3\",\n                                                children: \"اللون\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: design.colors.map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedColor(color),\n                                                        className: \"px-4 py-2 border rounded-lg transition-all duration-300 \".concat(selectedColor === color ? 'border-pink-500 bg-pink-50 text-pink-700' : 'border-gray-300 hover:border-pink-300'),\n                                                        children: color\n                                                    }, color, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleAddToCart,\n                                                disabled: addedToCart,\n                                                className: \"flex-1 flex items-center justify-center space-x-2 space-x-reverse py-3 px-6 rounded-full text-lg font-medium transition-all duration-300 \".concat(addedToCart ? 'bg-green-500 text-white' : 'bg-gradient-to-r from-pink-500 to-purple-600 text-white hover:shadow-lg'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: addedToCart ? 'تم الإضافة للسلة' : 'أضف للسلة'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleToggleFavorite,\n                                                className: \"p-3 rounded-full border-2 transition-all duration-300 \".concat(isFavorite(design.id.toString()) ? 'border-red-500 bg-red-500 text-white' : 'border-pink-300 text-pink-600 hover:bg-pink-50'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-6 h-6 \".concat(isFavorite(design.id.toString()) ? 'fill-current' : '')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://wa.me/+966598862609?text=أريد استفسار عن \".concat(design.title),\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"block w-full text-center py-3 px-6 border border-green-500 text-green-600 rounded-full hover:bg-green-50 transition-colors duration-300 font-medium\",\n                                        children: \"استفسار عبر واتساب\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    isGalleryOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4\",\n                        onClick: closeGallery,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative max-w-4xl w-full\",\n                            onClick: (e)=>e.stopPropagation(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: closeGallery,\n                                    className: \"absolute top-4 right-4 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-2 transition-colors duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: design.images[currentImageIndex],\n                                    alt: \"\".concat(design.title, \" - صورة \").concat(currentImageIndex + 1),\n                                    className: \"w-full h-auto max-h-[80vh] object-contain rounded-lg\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this),\n                                design.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: prevImage,\n                                            className: \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-colors duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: nextImage,\n                                            className: \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-colors duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Heart_ShoppingBag_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\[id]\\\\page.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n_s(DesignDetailPage, \"lpDqvJxXU+bOKS+c4Stx7OeI8IA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _store_shopStore__WEBPACK_IMPORTED_MODULE_5__.useShopStore\n    ];\n});\n_c = DesignDetailPage;\nvar _c;\n$RefreshReg$(_c, \"DesignDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/designs/[id]/page.tsx\n"));

/***/ })

});