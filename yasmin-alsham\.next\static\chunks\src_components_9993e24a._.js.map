{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef } from 'react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { motion } from 'framer-motion'\nimport { Menu, X, Calendar, Search, Scissors, Palette, Home } from 'lucide-react'\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n  const [clickCount, setClickCount] = useState(0)\n  const clickTimeoutRef = useRef<NodeJS.Timeout | null>(null)\n  const router = useRouter()\n\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen)\n\n  const handleLogoClick = (e: React.MouseEvent) => {\n    e.preventDefault()\n\n    setClickCount(prev => prev + 1)\n\n    // إذا كان هذا النقر الثالث، انتقل لصفحة تسجيل الدخول\n    if (clickCount === 2) {\n      router.push('/login')\n      setClickCount(0)\n      if (clickTimeoutRef.current) {\n        clearTimeout(clickTimeoutRef.current)\n      }\n      return\n    }\n\n    // إعادة تعيين العداد بعد ثانيتين\n    if (clickTimeoutRef.current) {\n      clearTimeout(clickTimeoutRef.current)\n    }\n\n    clickTimeoutRef.current = setTimeout(() => {\n      setClickCount(0)\n    }, 2000)\n  }\n\n  const menuItems = [\n    { href: '/', label: 'الرئيسية', icon: Home },\n    { href: '/designs', label: 'الفساتين الجاهزة', icon: Palette },\n    { href: '/book-appointment', label: 'حجز موعد', icon: Calendar },\n    { href: '/track-order', label: 'تتبع الطلب', icon: Search },\n    { href: '/fabrics', label: 'الأقمشة', icon: Scissors },\n  ]\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-pink-100 shadow-sm\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16 lg:h-20\">\n          {/* الشعار */}\n          <motion.div\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.5 }}\n            className=\"flex items-center space-x-2 space-x-reverse cursor-pointer hover:opacity-80 transition-opacity duration-300\"\n            onClick={handleLogoClick}\n          >\n\n              <div className=\"text-right\">\n                <h1 className=\"text-xl lg:text-2xl font-bold bg-gradient-to-r from-pink-600 to-rose-600 bg-clip-text text-transparent\">\n                  ياسمين الشام\n                </h1>\n                <p className=\"text-xs lg:text-sm text-gray-600 font-medium\">\n                  تفصيل فساتين حسب الطلب\n                </p>\n              </div>\n            </motion.div>\n\n          {/* القائمة الرئيسية - الشاشات الكبيرة */}\n          <nav className=\"hidden lg:flex items-center space-x-8 space-x-reverse\">\n            {menuItems.map((item, index) => (\n              <motion.div\n                key={item.href}\n                initial={{ opacity: 0, y: -10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n              >\n                <Link\n                  href={item.href}\n                  className=\"icon-text-spacing text-gray-700 hover:text-pink-600 transition-colors duration-300 font-medium group\"\n                >\n                  {item.icon && (\n                    <item.icon className=\"w-4 h-4 menu-item-icon group-hover:scale-110 transition-transform duration-300\" />\n                  )}\n                  <span className=\"relative\">\n                    {item.label}\n                    <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-pink-400 to-rose-400 group-hover:w-full transition-all duration-300\"></span>\n                  </span>\n                </Link>\n              </motion.div>\n            ))}\n          </nav>\n\n          {/* زر القائمة - الشاشات الصغيرة */}\n          <motion.button\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.5 }}\n            onClick={toggleMenu}\n            className=\"lg:hidden p-2 rounded-lg bg-gradient-to-r from-pink-100 to-rose-100 text-pink-600 hover:from-pink-200 hover:to-rose-200 transition-all duration-300\"\n          >\n            {isMenuOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n          </motion.button>\n        </div>\n\n        {/* القائمة المنسدلة - الشاشات الصغيرة */}\n        <motion.div\n          initial={{ opacity: 0, height: 0 }}\n          animate={{\n            opacity: isMenuOpen ? 1 : 0,\n            height: isMenuOpen ? 'auto' : 0,\n          }}\n          transition={{ duration: 0.3 }}\n          className=\"lg:hidden overflow-hidden bg-white border-t border-pink-100\"\n        >\n          <nav className=\"py-4 space-y-2\">\n            {menuItems.map((item, index) => (\n              <motion.div\n                key={item.href}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{\n                  opacity: isMenuOpen ? 1 : 0,\n                  x: isMenuOpen ? 0 : -20,\n                }}\n                transition={{ duration: 0.3, delay: index * 0.1 }}\n              >\n                <Link\n                  href={item.href}\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 hover:text-pink-600 hover:bg-pink-50 rounded-lg transition-all duration-300 font-medium\"\n                >\n                  {item.icon && <item.icon className=\"w-5 h-5\" />}\n                  <span>{item.label}</span>\n                </Link>\n              </motion.div>\n            ))}\n          </nav>\n        </motion.div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACtD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa,IAAM,cAAc,CAAC;IAExC,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAEhB,cAAc,CAAA,OAAQ,OAAO;QAE7B,qDAAqD;QACrD,IAAI,eAAe,GAAG;YACpB,OAAO,IAAI,CAAC;YACZ,cAAc;YACd,IAAI,gBAAgB,OAAO,EAAE;gBAC3B,aAAa,gBAAgB,OAAO;YACtC;YACA;QACF;QAEA,iCAAiC;QACjC,IAAI,gBAAgB,OAAO,EAAE;YAC3B,aAAa,gBAAgB,OAAO;QACtC;QAEA,gBAAgB,OAAO,GAAG,WAAW;YACnC,cAAc;QAChB,GAAG;IACL;IAEA,MAAM,YAAY;QAChB;YAAE,MAAM;YAAK,OAAO;YAAY,MAAM,sMAAA,CAAA,OAAI;QAAC;QAC3C;YAAE,MAAM;YAAY,OAAO;YAAoB,MAAM,2MAAA,CAAA,UAAO;QAAC;QAC7D;YAAE,MAAM;YAAqB,OAAO;YAAY,MAAM,6MAAA,CAAA,WAAQ;QAAC;QAC/D;YAAE,MAAM;YAAgB,OAAO;YAAc,MAAM,yMAAA,CAAA,SAAM;QAAC;QAC1D;YAAE,MAAM;YAAY,OAAO;YAAW,MAAM,6MAAA,CAAA,WAAQ;QAAC;KACtD;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;4BACV,SAAS;sCAGP,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyG;;;;;;kDAGvH,6LAAC;wCAAE,WAAU;kDAA+C;;;;;;;;;;;;;;;;;sCAOlE,6LAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;8CAEhD,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAU;;4CAET,KAAK,IAAI,kBACR,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;0DAEvB,6LAAC;gDAAK,WAAU;;oDACb,KAAK,KAAK;kEACX,6LAAC;wDAAK,WAAU;;;;;;;;;;;;;;;;;;mCAdf,KAAK,IAAI;;;;;;;;;;sCAsBpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,SAAS;4BACT,WAAU;sCAET,2BAAa,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK9D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBACP,SAAS,aAAa,IAAI;wBAC1B,QAAQ,aAAa,SAAS;oBAChC;oBACA,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCACP,SAAS,aAAa,IAAI;oCAC1B,GAAG,aAAa,IAAI,CAAC;gCACvB;gCACA,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;0CAEhD,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,cAAc;oCAC7B,WAAU;;wCAET,KAAK,IAAI,kBAAI,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACnC,6LAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;+BAdd,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAuB9B;GAzIwB;;QAIP,qIAAA,CAAA,YAAS;;;KAJF", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/components/Hero.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { Calendar, Sparkles, Heart, Star } from 'lucide-react'\n\nexport default function Hero() {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden pt-16 lg:pt-20\">\n      {/* خلفية متدرجة */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50\"></div>\n      \n      {/* عناصر زخرفية متحركة */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <motion.div\n          animate={{\n            rotate: 360,\n            scale: [1, 1.1, 1],\n          }}\n          transition={{\n            duration: 20,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n          className=\"absolute top-20 right-20 w-32 h-32 bg-gradient-to-br from-pink-200/30 to-rose-200/30 rounded-full blur-xl\"\n        />\n        <motion.div\n          animate={{\n            rotate: -360,\n            scale: [1, 1.2, 1],\n          }}\n          transition={{\n            duration: 25,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n          className=\"absolute bottom-20 left-20 w-40 h-40 bg-gradient-to-br from-purple-200/30 to-pink-200/30 rounded-full blur-xl\"\n        />\n        <motion.div\n          animate={{\n            y: [-20, 20, -20],\n          }}\n          transition={{\n            duration: 6,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n          }}\n          className=\"absolute top-1/2 left-1/4 w-6 h-6 text-pink-300\"\n        >\n          <Sparkles className=\"w-full h-full\" />\n        </motion.div>\n        <motion.div\n          animate={{\n            y: [20, -20, 20],\n          }}\n          transition={{\n            duration: 8,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n          }}\n          className=\"absolute top-1/3 right-1/4 w-8 h-8 text-rose-300\"\n        >\n          <Heart className=\"w-full h-full\" />\n        </motion.div>\n      </div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* تخطيط للشاشات الصغيرة والمتوسطة */}\n        <div className=\"lg:hidden\">\n          <div className=\"text-center space-y-8\">\n            {/* العنوان الرئيسي والفرعي */}\n            <motion.div\n              initial={{ opacity: 0, y: -30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8 }}\n              className=\"space-y-6\"\n            >\n              {/* العنوان الرئيسي */}\n              <motion.h1\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.2 }}\n                className=\"text-4xl sm:text-5xl font-bold leading-tight\"\n              >\n                <span className=\"bg-gradient-to-r from-pink-600 via-rose-600 to-purple-600 bg-clip-text text-transparent\">\n                  ياسمين الشام\n                </span>\n              </motion.h1>\n\n              {/* العنوان الفرعي */}\n              <motion.p\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.4 }}\n                className=\"text-xl sm:text-2xl text-gray-700 font-medium\"\n              >\n                تفصيل فساتين حسب الطلب بأناقة دمشقية\n              </motion.p>\n            </motion.div>\n\n            {/* الصورة */}\n            <motion.div\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n              className=\"relative\"\n            >\n              <div className=\"relative w-full h-80 sm:h-96 md:h-[400px] rounded-2xl overflow-hidden shadow-2xl mx-auto max-w-sm sm:max-w-md\">\n                <Image\n                  src=\"/yasmin.jpg?v=2024\"\n                  alt=\"ياسمين الشام - تفصيل فساتين حسب الطلب\"\n                  fill\n                  className=\"object-cover object-center\"\n                  sizes=\"(max-width: 640px) 90vw, (max-width: 768px) 80vw, 60vw\"\n                  priority\n                />\n                {/* تأثير الإطار */}\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent\"></div>\n              </div>\n\n              {/* عناصر زخرفية حول الصورة */}\n              <motion.div\n                animate={{ rotate: 360 }}\n                transition={{ duration: 30, repeat: Infinity, ease: \"linear\" }}\n                className=\"absolute -top-4 -right-4 w-6 h-6 text-pink-400\"\n              >\n                <Star className=\"w-full h-full\" />\n              </motion.div>\n              <motion.div\n                animate={{ rotate: -360 }}\n                transition={{ duration: 25, repeat: Infinity, ease: \"linear\" }}\n                className=\"absolute -bottom-4 -left-4 w-6 h-6 text-purple-400\"\n              >\n                <Sparkles className=\"w-full h-full\" />\n              </motion.div>\n            </motion.div>\n\n            {/* الوصف والأزرار */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.8 }}\n              className=\"space-y-6\"\n            >\n              {/* الوصف */}\n              <p className=\"text-lg text-gray-600 leading-relaxed max-w-2xl mx-auto\">\n                نحن متخصصون في تفصيل الفساتين الراقية بلمسة دمشقية أصيلة.\n                من فساتين الزفاف الفاخرة إلى فساتين السهرة الأنيقة،\n                نحول أحلامك إلى واقع بأيدي خبيرة وتصاميم مبتكرة.\n              </p>\n\n              {/* الأزرار */}\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Link\n                  href=\"/book-appointment\"\n                  className=\"bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg border border-pink-500 hover:border-pink-600 transition-all duration-300 inline-flex items-center justify-center space-x-2 space-x-reverse text-lg group shadow-lg hover:shadow-xl\"\n                >\n                  <Calendar className=\"w-5 h-5 group-hover:scale-110 transition-transform duration-300\" />\n                  <span>حجز موعد</span>\n                </Link>\n\n                <Link\n                  href=\"/designs\"\n                  className=\"btn-secondary inline-flex items-center justify-center space-x-2 space-x-reverse text-lg group\"\n                >\n                  <Sparkles className=\"w-5 h-5 group-hover:scale-110 transition-transform duration-300\" />\n                  <span>استكشفي تصاميمنا الجاهزة</span>\n                </Link>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n\n        {/* تخطيط للشاشات الكبيرة */}\n        <div className=\"hidden lg:grid lg:grid-cols-2 gap-12 items-center\">\n          {/* المحتوى النصي */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"text-right space-y-8\"\n          >\n\n\n            {/* العنوان الرئيسي */}\n            <div className=\"space-y-4\">\n              <motion.h1\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.2 }}\n                className=\"text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight\"\n              >\n                <span className=\"bg-gradient-to-r from-pink-600 via-rose-600 to-purple-600 bg-clip-text text-transparent\">\n                  ياسمين الشام\n                </span>\n              </motion.h1>\n\n              <motion.p\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.4 }}\n                className=\"text-xl sm:text-2xl lg:text-3xl text-gray-700 font-medium\"\n              >\n                تفصيل فساتين حسب الطلب\n                <br />\n                <span className=\"text-lg sm:text-xl text-pink-600\">بأناقة دمشقية أصيلة</span>\n              </motion.p>\n            </div>\n\n            {/* الوصف */}\n            <motion.p\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n              className=\"text-lg text-gray-600 leading-relaxed max-w-2xl mx-auto lg:mx-0\"\n            >\n              نحن نجمع بين التراث الدمشقي العريق والتصاميم العصرية لنقدم لك فساتين تعكس شخصيتك وتبرز جمالك الطبيعي.\n              كل فستان قصة، وكل قصة تحكي عن الأناقة والجمال.\n            </motion.p>\n\n            {/* الأزرار */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.8 }}\n              className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\"\n            >\n              <Link\n                href=\"/book-appointment\"\n                className=\"bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg border border-pink-500 hover:border-pink-600 transition-all duration-300 inline-flex items-center justify-center space-x-2 space-x-reverse text-lg group shadow-lg hover:shadow-xl\"\n              >\n                <Calendar className=\"w-5 h-5 group-hover:scale-110 transition-transform duration-300\" />\n                <span>حجز موعد</span>\n              </Link>\n\n              <Link\n                href=\"/designs\"\n                className=\"btn-secondary inline-flex items-center justify-center space-x-2 space-x-reverse text-lg group\"\n              >\n                <Sparkles className=\"w-5 h-5 group-hover:scale-110 transition-transform duration-300\" />\n                <span>استكشفي تصاميمنا الجاهزة</span>\n              </Link>\n            </motion.div>\n          </motion.div>\n\n          {/* الصورة */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"relative order-first lg:order-last\"\n          >\n            <div className=\"relative w-full h-80 sm:h-96 md:h-[450px] lg:h-[500px] xl:h-[600px] rounded-2xl overflow-hidden shadow-2xl mx-auto max-w-md md:max-w-lg lg:max-w-none\">\n              <Image\n                src=\"/yasmin.jpg?v=2024\"\n                alt=\"ياسمين الشام - تفصيل فساتين حسب الطلب\"\n                fill\n                className=\"object-cover object-center\"\n                sizes=\"(max-width: 640px) 100vw, (max-width: 768px) 90vw, (max-width: 1024px) 60vw, 50vw\"\n                priority\n              />\n\n              {/* تأثير الإطار */}\n              <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent\"></div>\n            </div>\n\n            {/* عناصر زخرفية حول الصورة */}\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 30, repeat: Infinity, ease: \"linear\" }}\n              className=\"absolute -top-4 -right-4 w-8 h-8 text-pink-400\"\n            >\n              <Star className=\"w-full h-full\" />\n            </motion.div>\n            <motion.div\n              animate={{ rotate: -360 }}\n              transition={{ duration: 25, repeat: Infinity, ease: \"linear\" }}\n              className=\"absolute -bottom-4 -left-4 w-6 h-6 text-rose-400\"\n            >\n              <Heart className=\"w-full h-full\" />\n            </motion.div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,QAAQ;4BACR,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;wBACA,WAAU;;;;;;kCAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,QAAQ,CAAC;4BACT,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;wBACA,WAAU;;;;;;kCAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,GAAG;gCAAC,CAAC;gCAAI;gCAAI,CAAC;6BAAG;wBACnB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;wBACA,WAAU;kCAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAEtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,GAAG;gCAAC;gCAAI,CAAC;gCAAI;6BAAG;wBAClB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;wBACA,WAAU;kCAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAIrB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;;sDAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,WAAU;sDAEV,cAAA,6LAAC;gDAAK,WAAU;0DAA0F;;;;;;;;;;;sDAM5G,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,WAAU;sDACX;;;;;;;;;;;;8CAMH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,IAAI;oDACJ,WAAU;oDACV,OAAM;oDACN,QAAQ;;;;;;8DAGV,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAIjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,QAAQ;4CAAI;4CACvB,YAAY;gDAAE,UAAU;gDAAI,QAAQ;gDAAU,MAAM;4CAAS;4CAC7D,WAAU;sDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,QAAQ,CAAC;4CAAI;4CACxB,YAAY;gDAAE,UAAU;gDAAI,QAAQ;gDAAU,MAAM;4CAAS;4CAC7D,WAAU;sDAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAGV,6LAAC;4CAAE,WAAU;sDAA0D;;;;;;sDAOvE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;sEAAK;;;;;;;;;;;;8DAGR,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAKV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gDACR,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;0DAEV,cAAA,6LAAC;oDAAK,WAAU;8DAA0F;;;;;;;;;;;0DAK5G,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;;oDACX;kEAEC,6LAAC;;;;;kEACD,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;;;;;;;;;;;;;kDAKvD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;kDACX;;;;;;kDAMD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAEV,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;kEAAK;;;;;;;;;;;;0DAGR,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;0CAMZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,IAAI;gDACJ,WAAU;gDACV,OAAM;gDACN,QAAQ;;;;;;0DAIV,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,QAAQ;wCAAI;wCACvB,YAAY;4CAAE,UAAU;4CAAI,QAAQ;4CAAU,MAAM;wCAAS;wCAC7D,WAAU;kDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,QAAQ,CAAC;wCAAI;wCACxB,YAAY;4CAAE,UAAU;4CAAI,QAAQ;4CAAU,MAAM;wCAAS;wCAC7D,WAAU;kDAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/B;KAxRwB", "debugId": null}}, {"offset": {"line": 1031, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/components/ReadyDesigns.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion } from 'framer-motion'\nimport Link from 'next/link'\nimport { Eye, Heart, Star, Palette, X, ChevronLeft, ChevronRight } from 'lucide-react'\n\nexport default function ReadyDesigns() {\n  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null)\n  const [isGalleryOpen, setIsGalleryOpen] = useState(false)\n  const [currentImageIndexes, setCurrentImageIndexes] = useState<{[key: number]: number}>({\n    1: 0, 2: 0, 3: 0, 4: 0\n  })\n  const readyDesigns = [\n    {\n      id: 1,\n      title: 'فستان زفاف كلاسيكي',\n      description: 'فستان زفاف أنيق بتصميم كلاسيكي مع تطريز يدوي',\n      category: 'فساتين زفاف',\n      images: [\n        '/wedding-dress-1.jpg.jpg',\n        '/wedding-dress-1a.jpg.jpg',\n        '/wedding-dress-1b.jpg.jpg'\n      ]\n    },\n    {\n      id: 2,\n      title: 'فستان سهرة راقي',\n      description: 'فستان سهرة طويل بقصة عصرية ولمسات ذهبية',\n      category: 'فساتين سهرة',\n      images: [\n        '/wedding-dress-2.jpg.jpg',\n        '/wedding-dress-2a.jpg.jpg',\n        '/wedding-dress-2b.jpg.jpg'\n      ]\n    },\n    {\n      id: 3,\n      title: 'فستان كوكتيل أنيق',\n      description: 'فستان كوكتيل قصير بتصميم عصري ومميز',\n      category: 'فساتين كوكتيل',\n      images: [\n        '/wedding-dress-3.jpg.jpg',\n        '/wedding-dress-3a.jpg.jpg',\n        '/wedding-dress-3b.jpg.jpg'\n      ]\n    },\n    {\n      id: 4,\n      title: 'فستان خطوبة مميز',\n      description: 'فستان خطوبة بتصميم رومانسي وتفاصيل دقيقة',\n      category: 'فساتين خطوبة',\n      images: [\n        '/wedding-dress-4.jpg.jpg',\n        '/wedding-dress-4a.jpg.jpg',\n        '/wedding-dress-4b.jpg.jpg'\n      ]\n    }\n  ]\n\n  // دوال التنقل بين صور البطاقة\n  const nextCardImage = (designId: number, e: React.MouseEvent) => {\n    e.stopPropagation()\n    setCurrentImageIndexes(prev => ({\n      ...prev,\n      [designId]: (prev[designId] + 1) % 3\n    }))\n  }\n\n  const prevCardImage = (designId: number, e: React.MouseEvent) => {\n    e.stopPropagation()\n    setCurrentImageIndexes(prev => ({\n      ...prev,\n      [designId]: prev[designId] === 0 ? 2 : prev[designId] - 1\n    }))\n  }\n\n  const setCardImage = (designId: number, imageIndex: number, e: React.MouseEvent) => {\n    e.stopPropagation()\n    setCurrentImageIndexes(prev => ({\n      ...prev,\n      [designId]: imageIndex\n    }))\n  }\n\n  // دوال إدارة المعرض\n  const openGallery = (index: number) => {\n    setSelectedImageIndex(index)\n    setIsGalleryOpen(true)\n    document.body.style.overflow = 'hidden' // منع التمرير في الخلفية\n  }\n\n  const closeGallery = () => {\n    setIsGalleryOpen(false)\n    setSelectedImageIndex(null)\n    document.body.style.overflow = 'unset' // إعادة التمرير\n  }\n\n  const nextImage = () => {\n    if (selectedImageIndex !== null) {\n      const designId = readyDesigns[selectedImageIndex].id\n      setCurrentImageIndexes(prev => ({\n        ...prev,\n        [designId]: (prev[designId] + 1) % 3\n      }))\n    }\n  }\n\n  const prevImage = () => {\n    if (selectedImageIndex !== null) {\n      const designId = readyDesigns[selectedImageIndex].id\n      setCurrentImageIndexes(prev => ({\n        ...prev,\n        [designId]: prev[designId] === 0 ? 2 : prev[designId] - 1\n      }))\n    }\n  }\n\n  // إدارة مفتاح Escape\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === 'Escape' && isGalleryOpen) {\n        closeGallery()\n      }\n      if (event.key === 'ArrowRight' && isGalleryOpen) {\n        nextImage()\n      }\n      if (event.key === 'ArrowLeft' && isGalleryOpen) {\n        prevImage()\n      }\n    }\n\n    document.addEventListener('keydown', handleKeyDown)\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown)\n      document.body.style.overflow = 'unset' // تنظيف عند إلغاء المكون\n    }\n  }, [isGalleryOpen, selectedImageIndex])\n\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* العنوان */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\">\n              تصاميمنا الجاهزة\n            </span>\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed mb-6\">\n            مجموعة مختارة من أجمل تصاميمنا الجاهزة التي يمكنك طلبها مباشرة أو تخصيصها حسب ذوقك\n          </p>\n\n          {/* ملاحظة مهمة */}\n          <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 max-w-2xl mx-auto\">\n            <p className=\"text-green-800 font-medium text-center\">\n              ✨ الفساتين الجاهزة متوفرة للشراء المباشر - لا يتطلب حجز موعد\n            </p>\n          </div>\n        </motion.div>\n\n        {/* شبكة التصاميم */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12\">\n          {readyDesigns.map((design, index) => (\n            <motion.div\n              key={design.id}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"group\"\n            >\n              <div className=\"relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105\">\n                {/* الصورة */}\n                <div\n                  className=\"aspect-[4/5] bg-gradient-to-br from-pink-100 via-rose-100 to-purple-100 relative overflow-hidden cursor-pointer\"\n                  onClick={(e) => {\n                    e.stopPropagation()\n                    openGallery(index)\n                  }}\n                >\n\n\n                  {/* الصورة الحالية */}\n                  <img\n                    src={design.images[currentImageIndexes[design.id]]}\n                    alt={`${design.title} - صورة ${currentImageIndexes[design.id] + 1}`}\n                    className=\"w-full h-full object-cover transition-opacity duration-300\"\n                  />\n\n                  {/* أزرار التنقل */}\n                  <button\n                    onClick={(e) => prevCardImage(design.id, e)}\n                    className=\"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-all duration-300 z-10\"\n                    aria-label=\"الصورة السابقة\"\n                  >\n                    <ChevronRight className=\"w-4 h-4\" />\n                  </button>\n\n                  <button\n                    onClick={(e) => nextCardImage(design.id, e)}\n                    className=\"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-all duration-300 z-10\"\n                    aria-label=\"الصورة التالية\"\n                  >\n                    <ChevronLeft className=\"w-4 h-4\" />\n                  </button>\n\n                  {/* مؤشرات الصور */}\n                  <div className=\"absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1 space-x-reverse opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                    {design.images.map((_, imgIndex) => (\n                      <button\n                        key={imgIndex}\n                        onClick={(e) => setCardImage(design.id, imgIndex, e)}\n                        className={`w-2 h-2 rounded-full transition-colors duration-300 ${\n                          currentImageIndexes[design.id] === imgIndex ? 'bg-white' : 'bg-white/50'\n                        }`}\n                        aria-label={`عرض الصورة ${imgIndex + 1}`}\n                      />\n                    ))}\n                  </div>\n                  \n                  {/* تأثير التمرير */}\n                  <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center\">\n                    <div className=\"opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white/90 rounded-full p-3\">\n                      <Eye className=\"w-6 h-6 text-pink-600\" />\n                    </div>\n                  </div>\n                  \n                  {/* زر الإعجاب */}\n                  <button className=\"absolute top-3 left-3 bg-white/80 hover:bg-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-all duration-300 hover:scale-110\">\n                    <Heart className=\"w-4 h-4 text-pink-500\" />\n                  </button>\n                </div>\n                \n                {/* المعلومات */}\n                <Link href={`/designs/${design.id}`}>\n                  <div className=\"p-4 cursor-pointer hover:bg-gray-50 transition-colors duration-300\">\n                    <div className=\"mb-3\">\n                      <span className=\"bg-gradient-to-r from-pink-100 to-rose-100 text-pink-700 px-2 py-1 rounded-full text-xs font-medium\">\n                        {design.category}\n                      </span>\n                    </div>\n\n                    <h3 className=\"font-bold text-gray-800 mb-2 group-hover:text-pink-600 transition-colors duration-300\">\n                      {design.title}\n                    </h3>\n\n                    <p className=\"text-sm text-gray-600 leading-relaxed\">\n                      {design.description}\n                    </p>\n                  </div>\n                </Link>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* زر عرض جميع التصاميم */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"text-center\"\n        >\n          <Link\n            href=\"/designs\"\n            className=\"inline-flex items-center space-x-3 space-x-reverse bg-gradient-to-r from-pink-600 to-purple-600 text-white px-10 py-4 rounded-full font-bold text-lg hover:shadow-lg transform hover:scale-105 transition-all duration-300\"\n          >\n            <Eye className=\"w-6 h-6\" />\n            <span>عرض جميع التصاميم</span>\n          </Link>\n        </motion.div>\n\n        {/* نافذة المعرض المنبثقة */}\n        {isGalleryOpen && selectedImageIndex !== null && (\n          <div\n            className=\"fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4\"\n            onClick={closeGallery}\n            role=\"dialog\"\n            aria-modal=\"true\"\n            aria-labelledby=\"gallery-title\"\n          >\n            <div className=\"relative max-w-4xl w-full\" onClick={(e) => e.stopPropagation()}>\n              {/* زر الإغلاق */}\n              <button\n                onClick={closeGallery}\n                className=\"absolute top-4 right-4 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-2 transition-colors duration-300\"\n                aria-label=\"إغلاق المعرض\"\n              >\n                <X className=\"w-6 h-6\" />\n              </button>\n\n              {/* أزرار التنقل */}\n              <button\n                onClick={prevImage}\n                className=\"absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-colors duration-300\"\n                aria-label=\"الصورة السابقة\"\n              >\n                <ChevronRight className=\"w-6 h-6\" />\n              </button>\n\n              <button\n                onClick={nextImage}\n                className=\"absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-colors duration-300\"\n                aria-label=\"الصورة التالية\"\n              >\n                <ChevronLeft className=\"w-6 h-6\" />\n              </button>\n\n              {/* الصورة */}\n              <motion.div\n                key={selectedImageIndex}\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.3 }}\n                className=\"bg-white rounded-2xl overflow-hidden shadow-2xl\"\n              >\n                <div className=\"aspect-[4/5] bg-gradient-to-br from-pink-100 via-rose-100 to-purple-100 flex items-center justify-center\">\n                  <img\n                    src={readyDesigns[selectedImageIndex].images[currentImageIndexes[readyDesigns[selectedImageIndex].id]]}\n                    alt={`${readyDesigns[selectedImageIndex].title} - صورة ${currentImageIndexes[readyDesigns[selectedImageIndex].id] + 1}`}\n                    className=\"w-full h-full object-cover\"\n                  />\n                </div>\n              </motion.div>\n            </div>\n          </div>\n        )}\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;QACtF,GAAG;QAAG,GAAG;QAAG,GAAG;QAAG,GAAG;IACvB;IACA,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,QAAQ;gBACN;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,QAAQ;gBACN;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,QAAQ;gBACN;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,QAAQ;gBACN;gBACA;gBACA;aACD;QACH;KACD;IAED,8BAA8B;IAC9B,MAAM,gBAAgB,CAAC,UAAkB;QACvC,EAAE,eAAe;QACjB,uBAAuB,CAAA,OAAQ,CAAC;gBAC9B,GAAG,IAAI;gBACP,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI;YACrC,CAAC;IACH;IAEA,MAAM,gBAAgB,CAAC,UAAkB;QACvC,EAAE,eAAe;QACjB,uBAAuB,CAAA,OAAQ,CAAC;gBAC9B,GAAG,IAAI;gBACP,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,GAAG;YAC1D,CAAC;IACH;IAEA,MAAM,eAAe,CAAC,UAAkB,YAAoB;QAC1D,EAAE,eAAe;QACjB,uBAAuB,CAAA,OAAQ,CAAC;gBAC9B,GAAG,IAAI;gBACP,CAAC,SAAS,EAAE;YACd,CAAC;IACH;IAEA,oBAAoB;IACpB,MAAM,cAAc,CAAC;QACnB,sBAAsB;QACtB,iBAAiB;QACjB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,SAAS,yBAAyB;;IACnE;IAEA,MAAM,eAAe;QACnB,iBAAiB;QACjB,sBAAsB;QACtB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,gBAAgB;;IACzD;IAEA,MAAM,YAAY;QAChB,IAAI,uBAAuB,MAAM;YAC/B,MAAM,WAAW,YAAY,CAAC,mBAAmB,CAAC,EAAE;YACpD,uBAAuB,CAAA,OAAQ,CAAC;oBAC9B,GAAG,IAAI;oBACP,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI;gBACrC,CAAC;QACH;IACF;IAEA,MAAM,YAAY;QAChB,IAAI,uBAAuB,MAAM;YAC/B,MAAM,WAAW,YAAY,CAAC,mBAAmB,CAAC,EAAE;YACpD,uBAAuB,CAAA,OAAQ,CAAC;oBAC9B,GAAG,IAAI;oBACP,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,GAAG;gBAC1D,CAAC;QACH;IACF;IAEA,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;wDAAgB,CAAC;oBACrB,IAAI,MAAM,GAAG,KAAK,YAAY,eAAe;wBAC3C;oBACF;oBACA,IAAI,MAAM,GAAG,KAAK,gBAAgB,eAAe;wBAC/C;oBACF;oBACA,IAAI,MAAM,GAAG,KAAK,eAAe,eAAe;wBAC9C;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;0CAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,yBAAyB;;gBAClE;;QACF;iCAAG;QAAC;QAAe;KAAmB;IAEtC,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCACZ,cAAA,6LAAC;gCAAK,WAAU;0CAA6E;;;;;;;;;;;sCAI/F,6LAAC;4BAAE,WAAU;sCAA+D;;;;;;sCAK5E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAyC;;;;;;;;;;;;;;;;;8BAO1D,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,QAAQ,sBACzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCACC,WAAU;wCACV,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,YAAY;wCACd;;0DAKA,6LAAC;gDACC,KAAK,OAAO,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC,CAAC;gDAClD,KAAK,GAAG,OAAO,KAAK,CAAC,QAAQ,EAAE,mBAAmB,CAAC,OAAO,EAAE,CAAC,GAAG,GAAG;gDACnE,WAAU;;;;;;0DAIZ,6LAAC;gDACC,SAAS,CAAC,IAAM,cAAc,OAAO,EAAE,EAAE;gDACzC,WAAU;gDACV,cAAW;0DAEX,cAAA,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;0DAG1B,6LAAC;gDACC,SAAS,CAAC,IAAM,cAAc,OAAO,EAAE,EAAE;gDACzC,WAAU;gDACV,cAAW;0DAEX,cAAA,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;0DAIzB,6LAAC;gDAAI,WAAU;0DACZ,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,yBACrB,6LAAC;wDAEC,SAAS,CAAC,IAAM,aAAa,OAAO,EAAE,EAAE,UAAU;wDAClD,WAAW,CAAC,oDAAoD,EAC9D,mBAAmB,CAAC,OAAO,EAAE,CAAC,KAAK,WAAW,aAAa,eAC3D;wDACF,cAAY,CAAC,WAAW,EAAE,WAAW,GAAG;uDALnC;;;;;;;;;;0DAWX,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAKnB,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAKrB,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE;kDACjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEACb,OAAO,QAAQ;;;;;;;;;;;8DAIpB,6LAAC;oDAAG,WAAU;8DACX,OAAO,KAAK;;;;;;8DAGf,6LAAC;oDAAE,WAAU;8DACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;2BAnFtB,OAAO,EAAE;;;;;;;;;;8BA6FpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,6LAAC;0CAAK;;;;;;;;;;;;;;;;;gBAKT,iBAAiB,uBAAuB,sBACvC,6LAAC;oBACC,WAAU;oBACV,SAAS;oBACT,MAAK;oBACL,cAAW;oBACX,mBAAgB;8BAEhB,cAAA,6LAAC;wBAAI,WAAU;wBAA4B,SAAS,CAAC,IAAM,EAAE,eAAe;;0CAE1E,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;0CAIf,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;0CAG1B,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAIzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,KAAK,YAAY,CAAC,mBAAmB,CAAC,MAAM,CAAC,mBAAmB,CAAC,YAAY,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;wCACtG,KAAK,GAAG,YAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,QAAQ,EAAE,mBAAmB,CAAC,YAAY,CAAC,mBAAmB,CAAC,EAAE,CAAC,GAAG,GAAG;wCACvH,WAAU;;;;;;;;;;;+BAVT;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBrB;GA3UwB;KAAA", "debugId": null}}, {"offset": {"line": 1618, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/components/Services.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport { Calendar, Search, Scissors, Palette, Heart, Sparkles, ChevronDown } from 'lucide-react'\nimport Link from 'next/link'\n\nexport default function Services() {\n  const [showAllServices, setShowAllServices] = useState(false)\n\n  const services = [\n    {\n      icon: Calendar,\n      title: 'حجز موعد',\n      description: 'احجزي موعدك بسهولة عبر نظامنا الذكي. نظام تلقائي يوزع المواعيد على مدار أيام العمل.',\n      link: '/book-appointment',\n      color: 'from-pink-400 to-rose-400',\n      bgColor: 'from-pink-50 to-rose-50'\n    },\n    {\n      icon: Search,\n      title: 'استعلام عن الطلب',\n      description: 'تابعي حالة طلبك في أي وقت. اعرفي مرحلة التفصيل والموعد المتوقع للتسليم.',\n      link: '/track-order',\n      color: 'from-purple-400 to-pink-400',\n      bgColor: 'from-purple-50 to-pink-50'\n    },\n    {\n      icon: Scissors,\n      title: 'تفصيل احترافي',\n      description: 'فريق من أمهر الخياطين المتخصصين في تفصيل الفساتين النسائية بأعلى معايير الجودة.',\n      link: '/about',\n      color: 'from-rose-400 to-purple-400',\n      bgColor: 'from-rose-50 to-purple-50'\n    },\n    {\n      icon: Sparkles,\n      title: 'أقمشة متنوعة',\n      description: 'مجموعة واسعة من أجود أنواع الأقمشة والألوان لتناسب جميع الأذواق والمناسبات.',\n      link: '/fabrics',\n      color: 'from-indigo-400 to-purple-400',\n      bgColor: 'from-indigo-50 to-purple-50'\n    },\n    {\n      icon: Palette,\n      title: 'فساتين جاهزة',\n      description: 'مجموعة متنوعة من الفساتين الجاهزة للشراء المباشر بتصاميم عصرية وأنيقة.',\n      link: '/designs',\n      color: 'from-emerald-400 to-teal-400',\n      bgColor: 'from-emerald-50 to-teal-50'\n    },\n    {\n      icon: Heart,\n      title: 'رضا العملاء',\n      description: 'نضمن رضاك التام عن النتيجة النهائية. فريقنا يعمل بحب وشغف لإسعادك.',\n      link: '/testimonials',\n      color: 'from-red-400 to-pink-400',\n      bgColor: 'from-red-50 to-pink-50'\n    }\n  ]\n\n  // عرض خدمتين فقط على الجوال إذا لم يتم الضغط على \"عرض الكل\"\n  const displayedServices = showAllServices ? services : services.slice(0, 2)\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* العنوان */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\">\n              خدماتنا المميزة\n            </span>\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            نقدم لك تجربة متكاملة من الاستشارة وحتى التسليم، مع ضمان أعلى مستويات الجودة والاحترافية\n          </p>\n        </motion.div>\n\n        {/* شبكة الخدمات */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {/* عرض جميع الخدمات على الشاشات الكبيرة، وخدمتين فقط على الجوال */}\n          <div className=\"hidden md:contents\">\n            {services.map((service, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"group\"\n              >\n                <Link href={service.link}>\n                  <div className={`relative p-8 rounded-2xl bg-gradient-to-br ${service.bgColor} border border-gray-100 hover:shadow-xl transition-all duration-500 transform hover:scale-105 cursor-pointer h-full`}>\n                    {/* الأيقونة */}\n                    <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${service.color} flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>\n                      <service.icon className=\"w-8 h-8 text-white\" />\n                    </div>\n\n                    {/* المحتوى */}\n                    <div className=\"space-y-4\">\n                      <h3 className=\"text-xl font-bold text-gray-800 group-hover:text-pink-600 transition-colors duration-300\">\n                        {service.title}\n                      </h3>\n                      <p className=\"text-gray-600 leading-relaxed\">\n                        {service.description}\n                      </p>\n                    </div>\n\n                    {/* سهم التنقل */}\n                    <div className=\"absolute bottom-6 left-6 opacity-0 group-hover:opacity-100 transform translate-x-2 group-hover:translate-x-0 transition-all duration-300\">\n                      <div className=\"w-8 h-8 rounded-full bg-white shadow-lg flex items-center justify-center\">\n                        <svg className=\"w-4 h-4 text-pink-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n                        </svg>\n                      </div>\n                    </div>\n\n                    {/* تأثير الإضاءة */}\n                    <div className=\"absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none\"></div>\n                  </div>\n                </Link>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* عرض الخدمات على الجوال */}\n          <div className=\"md:hidden col-span-full\">\n            {displayedServices.map((service, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"group mb-6\"\n              >\n                <Link href={service.link}>\n                  <div className={`relative p-6 rounded-2xl bg-gradient-to-br ${service.bgColor} border border-gray-100 hover:shadow-xl transition-all duration-500 transform hover:scale-105 cursor-pointer`}>\n                    {/* الأيقونة */}\n                    <div className={`w-14 h-14 rounded-2xl bg-gradient-to-br ${service.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>\n                      <service.icon className=\"w-7 h-7 text-white\" />\n                    </div>\n\n                    {/* المحتوى */}\n                    <div className=\"space-y-3\">\n                      <h3 className=\"text-lg font-bold text-gray-800 group-hover:text-pink-600 transition-colors duration-300\">\n                        {service.title}\n                      </h3>\n                      <p className=\"text-gray-600 leading-relaxed text-sm\">\n                        {service.description}\n                      </p>\n                    </div>\n\n                    {/* سهم التنقل */}\n                    <div className=\"absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transform translate-x-2 group-hover:translate-x-0 transition-all duration-300\">\n                      <div className=\"w-6 h-6 rounded-full bg-white shadow-lg flex items-center justify-center\">\n                        <svg className=\"w-3 h-3 text-pink-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n                        </svg>\n                      </div>\n                    </div>\n\n                    {/* تأثير الإضاءة */}\n                    <div className=\"absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none\"></div>\n                  </div>\n                </Link>\n              </motion.div>\n            ))}\n\n            {/* زر عرض جميع الخدمات على الجوال */}\n            {!showAllServices && (\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.3 }}\n                viewport={{ once: true }}\n                className=\"text-center mt-6\"\n              >\n                <button\n                  onClick={() => setShowAllServices(true)}\n                  className=\"inline-flex items-center justify-center space-x-2 space-x-reverse bg-gradient-to-r from-pink-100 to-purple-100 hover:from-pink-200 hover:to-purple-200 text-pink-700 font-semibold py-3 px-6 rounded-full border border-pink-200 hover:border-pink-300 transition-all duration-300 transform hover:scale-105\"\n                >\n                  <span>عرض جميع الخدمات المميزة</span>\n                  <ChevronDown className=\"w-5 h-5\" />\n                </button>\n              </motion.div>\n            )}\n          </div>\n        </div>\n\n        {/* دعوة للعمل */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-gradient-to-r from-pink-50 to-purple-50 rounded-3xl p-8 lg:p-12\">\n            <h3 className=\"text-2xl lg:text-3xl font-bold text-gray-800 mb-4\">\n              جاهزة لتبدئي رحلتك معنا؟\n            </h3>\n            <p className=\"text-lg text-gray-600 mb-8 max-w-2xl mx-auto\">\n              احجزي موعدك الآن واتركي لنا مهمة تحويل حلمك إلى فستان يعكس شخصيتك المميزة\n            </p>\n            <Link\n              href=\"/book-appointment\"\n              className=\"btn-primary inline-flex items-center space-x-2 space-x-reverse text-lg group\"\n            >\n              <Calendar className=\"w-5 h-5 group-hover:scale-110 transition-transform duration-300\" />\n              <span>احجزي موعدك الآن</span>\n            </Link>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,WAAW;QACf;YACE,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;YAC<PERSON>,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,2MAAA,CAAA,UAAO;YACb,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;QACX;KACD;IAED,4DAA4D;IAC5D,MAAM,oBAAoB,kBAAkB,WAAW,SAAS,KAAK,CAAC,GAAG;IAEzE,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCACZ,cAAA,6LAAC;gCAAK,WAAU;0CAA6E;;;;;;;;;;;sCAI/F,6LAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAMzE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;8CAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,QAAQ,IAAI;kDACtB,cAAA,6LAAC;4CAAI,WAAW,CAAC,2CAA2C,EAAE,QAAQ,OAAO,CAAC,mHAAmH,CAAC;;8DAEhM,6LAAC;oDAAI,WAAW,CAAC,wCAAwC,EAAE,QAAQ,KAAK,CAAC,8FAA8F,CAAC;8DACtK,cAAA,6LAAC,QAAQ,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAI1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,QAAQ,KAAK;;;;;;sEAEhB,6LAAC;4DAAE,WAAU;sEACV,QAAQ,WAAW;;;;;;;;;;;;8DAKxB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAwB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC/E,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8DAM3E,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;mCAlCd;;;;;;;;;;sCA0CX,6LAAC;4BAAI,WAAU;;gCACZ,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM,QAAQ,IAAI;sDACtB,cAAA,6LAAC;gDAAI,WAAW,CAAC,2CAA2C,EAAE,QAAQ,OAAO,CAAC,4GAA4G,CAAC;;kEAEzL,6LAAC;wDAAI,WAAW,CAAC,wCAAwC,EAAE,QAAQ,KAAK,CAAC,8FAA8F,CAAC;kEACtK,cAAA,6LAAC,QAAQ,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAI1B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EACX,QAAQ,KAAK;;;;;;0EAEhB,6LAAC;gEAAE,WAAU;0EACV,QAAQ,WAAW;;;;;;;;;;;;kEAKxB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;gEAAwB,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EAC/E,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;kEAM3E,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;uCAlCd;;;;;gCAyCR,CAAC,iCACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;8CAEV,cAAA,6LAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;;0DAEV,6LAAC;0DAAK;;;;;;0DACN,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAG5D,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;GAzNwB;KAAA", "debugId": null}}, {"offset": {"line": 2148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/components/About.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { Award, Users, Clock, Heart, Star, Scissors, Sparkles } from 'lucide-react'\n\nexport default function About() {\n  const features = [\n    {\n      icon: Award,\n      title: 'خبرة عريقة',\n      description: 'أكثر من 25 سنة في عالم تفصيل الفساتين النسائية'\n    },\n    {\n      icon: Users,\n      title: 'فريق محترف',\n      description: 'خياطون مهرة متخصصون في التفصيل الراقي'\n    },\n    {\n      icon: Clock,\n      title: 'التزام بالمواعيد',\n      description: 'نضمن تسليم طلبك في الموعد المحدد'\n    },\n    {\n      icon: Heart,\n      title: 'صنع بحب',\n      description: 'كل فستان يُصنع بعناية فائقة وحب للتفاصيل'\n    }\n  ]\n\n  return (\n    <section className=\"relative py-24 overflow-hidden\">\n      {/* خلفية متدرجة مع تأثيرات بصرية */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50\">\n        <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(236,72,153,0.1),transparent_50%)]\"></div>\n        <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(168,85,247,0.1),transparent_50%)]\"></div>\n      </div>\n\n      {/* عناصر زخرفية متحركة */}\n      <motion.div\n        animate={{\n          rotate: [0, 360],\n          scale: [1, 1.1, 1]\n        }}\n        transition={{\n          duration: 25,\n          repeat: Infinity,\n          ease: \"linear\"\n        }}\n        className=\"absolute top-20 left-10 w-16 h-16 text-pink-200/30\"\n      >\n        <Sparkles className=\"w-full h-full\" />\n      </motion.div>\n\n      <motion.div\n        animate={{\n          rotate: [360, 0],\n          y: [0, -20, 0]\n        }}\n        transition={{\n          duration: 20,\n          repeat: Infinity,\n          ease: \"easeInOut\"\n        }}\n        className=\"absolute bottom-20 right-10 w-12 h-12 text-purple-200/30\"\n      >\n        <Star className=\"w-full h-full\" />\n      </motion.div>\n\n      <div className=\"relative container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* العنوان الرئيسي */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-20\"\n        >\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"inline-flex items-center gap-3 mb-6 px-6 py-3 bg-white/80 backdrop-blur-sm rounded-full border border-pink-200/50 shadow-lg\"\n          >\n            <Scissors className=\"w-6 h-6 text-pink-500\" />\n            <span className=\"text-pink-600 font-semibold text-lg\">حكايتنا</span>\n          </motion.div>\n\n          <motion.h2\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n            viewport={{ once: true }}\n            className=\"text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight\"\n          >\n            <span className=\"bg-gradient-to-r from-pink-600 via-rose-500 to-purple-600 bg-clip-text text-transparent\">\n              قصة ياسمين الشام\n            </span>\n          </motion.h2>\n\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\"\n          >\n            رحلة من الشغف والإبداع تمتد عبر عقود من الزمن\n          </motion.p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-20 items-center\">\n          {/* المحتوى النصي */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-10\"\n          >\n            {/* النص الرئيسي */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              viewport={{ once: true }}\n              className=\"bg-white/60 backdrop-blur-sm rounded-3xl p-8 border border-pink-100/50 shadow-xl\"\n            >\n              <div className=\"space-y-6 text-lg text-gray-700 leading-relaxed\">\n                <p className=\"text-xl font-medium text-gray-800 mb-6\">\n                  في قلب دمشق العريقة، وُلدت فكرة \"ياسمين الشام\" من حب عميق للتراث والأناقة.\n                </p>\n\n                <p>\n                  نحن لسنا مجرد محل خياطة، بل نحن حكواتيون نحيك قصص الجمال بخيوط الحرير والحب.\n                  كل فستان نصنعه يحمل في طياته روح الحرفية الدمشقية الأصيلة، ممزوجة بلمسة عصرية\n                  تواكب أحدث صيحات الموضة.\n                </p>\n\n                <p>\n                  نؤمن أن كل امرأة تستحق أن تشعر بالجمال والثقة. فريقنا من الخياطين المهرة يعمل\n                  بشغف وإتقان لتحويل رؤيتك إلى واقع ملموس، مستخدمين أجود الأقمشة ومطبقين أعلى\n                  معايير الجودة في كل خطوة من خطوات التفصيل.\n                </p>\n              </div>\n            </motion.div>\n\n            {/* المميزات */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              viewport={{ once: true }}\n              className=\"grid sm:grid-cols-2 gap-4\"\n            >\n              {features.map((feature, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  whileInView={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}\n                  viewport={{ once: true }}\n                  whileHover={{\n                    scale: 1.02,\n                    transition: { duration: 0.2 }\n                  }}\n                  className=\"group relative overflow-hidden\"\n                >\n                  <div className=\"relative flex items-start space-x-4 space-x-reverse p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-pink-100/50 shadow-lg hover:shadow-xl transition-all duration-300\">\n                    <div className=\"w-14 h-14 bg-gradient-to-br from-pink-400 to-rose-500 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg group-hover:scale-110 transition-transform duration-300\">\n                      <feature.icon className=\"w-7 h-7 text-white\" />\n                    </div>\n                    <div className=\"flex-1\">\n                      <h3 className=\"font-bold text-gray-800 mb-2 text-lg\">{feature.title}</h3>\n                      <p className=\"text-gray-600 leading-relaxed\">{feature.description}</p>\n                    </div>\n\n                    {/* تأثير الهوفر */}\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-pink-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl\"></div>\n                  </div>\n                </motion.div>\n              ))}\n            </motion.div>\n          </motion.div>\n\n          {/* الصورة والعناصر البصرية */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"relative\"\n          >\n            {/* الصورة الرئيسية */}\n            <div className=\"relative group\">\n              <motion.div\n                whileHover={{ scale: 1.02 }}\n                transition={{ duration: 0.3 }}\n                className=\"relative w-full h-96 lg:h-[500px] rounded-3xl overflow-hidden shadow-2xl\"\n              >\n                <img\n                  src=\"/workshop.jpg\"\n                  alt=\"ورشة ياسمين الشام\"\n                  className=\"w-full h-full object-cover group-hover:scale-105 transition-transform duration-700\"\n                />\n\n                {/* تأثيرات التدرج */}\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent\"></div>\n                <div className=\"absolute inset-0 bg-gradient-to-br from-pink-500/10 via-transparent to-purple-500/10\"></div>\n\n                {/* نص على الصورة */}\n                <div className=\"absolute bottom-6 right-6 left-6\">\n                  <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl p-4 border border-white/20\">\n                    <p className=\"text-gray-800 font-semibold text-lg\">ورشة ياسمين الشام</p>\n                    <p className=\"text-gray-600\">حيث تولد الأحلام وتصبح واقعاً</p>\n                  </div>\n                </div>\n              </motion.div>\n\n              {/* عناصر زخرفية حول الصورة */}\n              <motion.div\n                animate={{\n                  rotate: [0, 360],\n                  scale: [1, 1.2, 1]\n                }}\n                transition={{\n                  duration: 15,\n                  repeat: Infinity,\n                  ease: \"linear\"\n                }}\n                className=\"absolute -top-6 -right-6 w-12 h-12 text-pink-400\"\n              >\n                <Star className=\"w-full h-full drop-shadow-lg\" />\n              </motion.div>\n\n              <motion.div\n                animate={{\n                  rotate: [360, 0],\n                  y: [0, -10, 0]\n                }}\n                transition={{\n                  duration: 12,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }}\n                className=\"absolute -bottom-4 -left-4 w-10 h-10 text-purple-400\"\n              >\n                <Sparkles className=\"w-full h-full drop-shadow-lg\" />\n              </motion.div>\n\n              {/* إطار زخرفي */}\n              <div className=\"absolute -inset-4 bg-gradient-to-r from-pink-200/20 to-purple-200/20 rounded-3xl -z-10 blur-xl\"></div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,WAAW;QACf;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBACP,QAAQ;wBAAC;wBAAG;qBAAI;oBAChB,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;gBACpB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;gBACA,WAAU;0BAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;0BAGtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBACP,QAAQ;wBAAC;wBAAK;qBAAE;oBAChB,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;gBAChB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;gBACA,WAAU;0BAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;0BAGlB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAsC;;;;;;;;;;;;0CAGxD,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAEV,cAAA,6LAAC;oCAAK,WAAU;8CAA0F;;;;;;;;;;;0CAK5G,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;;;;;;;kCAKH,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;8DAItD,6LAAC;8DAAE;;;;;;8DAMH,6LAAC;8DAAE;;;;;;;;;;;;;;;;;kDASP,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAET,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAI;gDAClC,aAAa;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDACpC,YAAY;oDAAE,UAAU;oDAAK,OAAO,MAAM,QAAQ;gDAAI;gDACtD,UAAU;oDAAE,MAAM;gDAAK;gDACvB,YAAY;oDACV,OAAO;oDACP,YAAY;wDAAE,UAAU;oDAAI;gDAC9B;gDACA,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,QAAQ,IAAI;gEAAC,WAAU;;;;;;;;;;;sEAE1B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAwC,QAAQ,KAAK;;;;;;8EACnE,6LAAC;oEAAE,WAAU;8EAAiC,QAAQ,WAAW;;;;;;;;;;;;sEAInE,6LAAC;4DAAI,WAAU;;;;;;;;;;;;+CArBZ;;;;;;;;;;;;;;;;0CA6Bb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAGV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAU;;8DAEV,6LAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;;;;;;8DAIZ,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;8DAGf,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAsC;;;;;;0EACnD,6LAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;sDAMnC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDACP,QAAQ;oDAAC;oDAAG;iDAAI;gDAChB,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;4CACpB;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;4CACR;4CACA,WAAU;sDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAGlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDACP,QAAQ;oDAAC;oDAAK;iDAAE;gDAChB,GAAG;oDAAC;oDAAG,CAAC;oDAAI;iDAAE;4CAChB;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;4CACR;4CACA,WAAU;sDAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAItB,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B;KA7PwB", "debugId": null}}, {"offset": {"line": 2774, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/components/FAQ.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { useState } from 'react'\nimport { ChevronDown, ChevronUp, HelpCircle } from 'lucide-react'\n\nexport default function FAQ() {\n  const [openIndex, setOpenIndex] = useState<number | null>(0)\n\n  const faqs = [\n    {\n      question: 'كم يستغرق تفصيل الفستان؟',\n      answer: 'عادة ما يستغرق تفصيل الفستان من 7 إلى 14 يوم عمل، حسب تعقيد التصميم والتفاصيل المطلوبة. نحرص على إعطائك موعد دقيق عند أخذ المقاسات.'\n    },\n    {\n      question: 'هل يمكنني إحضار تصميم خاص بي؟',\n      answer: 'بالطبع! نرحب بأفكارك وتصاميمك الخاصة. فريقنا سيعمل معك لتحويل رؤيتك إلى واقع، مع تقديم النصائح المهنية لضمان أفضل النتائج.'\n    },\n    {\n      question: 'ما هي أوقات العمل؟',\n      answer: 'نعمل 6 أيام في الأسبوع (عدا يوم الجمعة) من الساعة 4:00 مساءً حتى 10:00 مساءً. يمكنك حجز موعدك عبر موقعنا الإلكتروني.'\n    },\n    {\n      question: 'هل تقدمون خدمة التعديلات؟',\n      answer: 'نعم، يوجد لدينا فريق نسائي متخصص بإجراء التعديلات.'\n    },\n    {\n      question: 'ما أنواع الأقمشة المتوفرة؟',\n      answer: 'لدينا مجموعة واسعة من الأقمشة التي تخص فساتين السهرة والزفاف. يمكنك أيضاً إحضار القماش الخاص بك.'\n    },\n    {\n      question: 'كيف يمكنني تتبع حالة طلبي؟',\n      answer: 'يمكنك تتبع حالة طلبك عبر صفحة \"استعلام عن الطلب\" في موقعنا بطريقتين: إما باستخدام رقم الطلب (Order ID) الذي تحصلين عليه عند تأكيد الطلب، أو باستخدام رقم الهاتف المستخدم عند إجراء الطلب. ستحصلين على تحديثات منتظمة عبر الرسائل النصية.'\n    },\n    {\n      question: 'هل تقدمون خدمة التوصيل؟',\n      answer: 'نعم، يوجد لدينا خدمة توصيل لجميع مناطق المملكة العربية السعودية.'\n    }\n  ]\n\n  const toggleFAQ = (index: number) => {\n    setOpenIndex(openIndex === index ? null : index)\n  }\n\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* العنوان */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\">\n              الأسئلة الشائعة\n            </span>\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            إجابات على أكثر الأسئلة شيوعاً حول خدماتنا وطريقة العمل\n          </p>\n        </motion.div>\n\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"grid gap-4\">\n            {faqs.map((faq, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"bg-white/80 backdrop-blur-sm rounded-2xl border border-pink-100 overflow-hidden hover:shadow-lg transition-all duration-300\"\n              >\n                <button\n                  onClick={() => toggleFAQ(index)}\n                  className=\"w-full px-6 py-6 text-right flex items-center justify-between hover:bg-pink-50/50 transition-colors duration-300\"\n                >\n                  <div className=\"flex items-center space-x-3 space-x-reverse\">\n                    <div className=\"w-8 h-8 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full flex items-center justify-center flex-shrink-0\">\n                      <HelpCircle className=\"w-4 h-4 text-white\" />\n                    </div>\n                    <h3 className=\"text-lg font-bold text-gray-800 text-right\">\n                      {faq.question}\n                    </h3>\n                  </div>\n                  \n                  <div className=\"flex-shrink-0 mr-4\">\n                    {openIndex === index ? (\n                      <ChevronUp className=\"w-6 h-6 text-pink-600\" />\n                    ) : (\n                      <ChevronDown className=\"w-6 h-6 text-gray-400\" />\n                    )}\n                  </div>\n                </button>\n\n                <motion.div\n                  initial={false}\n                  animate={{\n                    height: openIndex === index ? 'auto' : 0,\n                    opacity: openIndex === index ? 1 : 0\n                  }}\n                  transition={{ duration: 0.3, ease: 'easeInOut' }}\n                  className=\"overflow-hidden\"\n                >\n                  <div className=\"px-6 pb-6\">\n                    <div className=\"bg-gradient-to-r from-pink-50 to-rose-50 rounded-xl p-6 border-r-4 border-pink-400\">\n                      <p className=\"text-gray-700 leading-relaxed text-right\">\n                        {faq.answer}\n                      </p>\n                    </div>\n                  </div>\n                </motion.div>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* دعوة للتواصل */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            viewport={{ once: true }}\n            className=\"text-center mt-16 p-8 bg-white/80 backdrop-blur-sm rounded-3xl border border-pink-100\"\n          >\n            <div className=\"w-16 h-16 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <HelpCircle className=\"w-8 h-8 text-white\" />\n            </div>\n            \n            <h3 className=\"text-2xl font-bold text-gray-800 mb-4\">\n              لديك سؤال آخر؟\n            </h3>\n            \n            <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n              لا تترددي في التواصل معنا. فريقنا جاهز للإجابة على جميع استفساراتك ومساعدتك في اختيار الأنسب لك.\n            </p>\n            \n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a\n                href=\"tel:+966598862609\"\n                className=\"btn-primary inline-flex items-center justify-center space-x-2 space-x-reverse\"\n              >\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                </svg>\n                <span>اتصلي بنا</span>\n              </a>\n              \n              <a\n                href=\"https://wa.me/+966598862609\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"btn-secondary inline-flex items-center justify-center space-x-2 space-x-reverse\"\n              >\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"/>\n                </svg>\n                <span>واتساب</span>\n              </a>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,MAAM,OAAO;QACX;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;QACA;YACE,UAAU;YACV,QAAQ;QACV;KACD;IAED,MAAM,YAAY,CAAC;QACjB,aAAa,cAAc,QAAQ,OAAO;IAC5C;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCACZ,cAAA,6LAAC;gCAAK,WAAU;0CAA6E;;;;;;;;;;;sCAI/F,6LAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAKzE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CACC,SAAS,IAAM,UAAU;4CACzB,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,iOAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;sEAExB,6LAAC;4DAAG,WAAU;sEACX,IAAI,QAAQ;;;;;;;;;;;;8DAIjB,6LAAC;oDAAI,WAAU;8DACZ,cAAc,sBACb,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;6EAErB,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAK7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;4CACT,SAAS;gDACP,QAAQ,cAAc,QAAQ,SAAS;gDACvC,SAAS,cAAc,QAAQ,IAAI;4CACrC;4CACA,YAAY;gDAAE,UAAU;gDAAK,MAAM;4CAAY;4CAC/C,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;kEACV,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;mCAzCd;;;;;;;;;;sCAmDX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,iOAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAGxB,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAItD,6LAAC;oCAAE,WAAU;8CAAuC;;;;;;8CAIpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,6LAAC;8DAAK;;;;;;;;;;;;sDAGR,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,6LAAC;wDAAK,GAAE;;;;;;;;;;;8DAEV,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;GAjKwB;KAAA", "debugId": null}}, {"offset": {"line": 3175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/components/Footer.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport Link from 'next/link'\nimport {\n  Scissors,\n  MapPin,\n  Phone,\n  Mail,\n  Clock,\n  Calendar,\n  Search,\n  Palette,\n  Heart,\n  Star\n} from 'lucide-react'\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear()\n\n  const quickLinks = [\n    { href: '/', label: 'الرئيسية', icon: Scissors },\n    { href: '/book-appointment', label: 'حجز موعد', icon: Calendar },\n    { href: '/track-order', label: 'تتبع الطلب', icon: Search },\n    { href: '/fabrics', label: 'الأقمشة', icon: Palette }\n  ]\n\n  const workingHours = [\n    { day: 'السبت - الخميس', time: '4:00 م - 10:00 م' },\n    { day: 'الجمعة', time: 'مغلق' }\n  ]\n\n  const socialLinks = [\n    {\n      name: 'فيسبوك',\n      href: '#',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n        </svg>\n      )\n    },\n    {\n      name: 'إنستغرام',\n      href: '#',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.897-.875-1.387-2.026-1.387-3.323s.49-2.448 1.297-3.323c.875-.897 2.026-1.387 3.323-1.387s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z\"/>\n        </svg>\n      )\n    },\n    {\n      name: 'واتساب',\n      href: 'https://wa.me/+966598862609',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"/>\n        </svg>\n      )\n    }\n  ]\n\n  return (\n    <footer className=\"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        <div className=\"grid lg:grid-cols-4 md:grid-cols-2 gap-12\">\n          {/* معلومات المحل */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"space-y-6\"\n          >\n            <div className=\"flex items-center space-x-3 space-x-reverse\">\n              <div className=\"w-12 h-12 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full flex items-center justify-center\">\n                <Scissors className=\"w-6 h-6 text-white\" />\n              </div>\n              <div>\n                <h3 className=\"text-2xl font-bold bg-gradient-to-r from-pink-400 to-rose-400 bg-clip-text text-transparent\">\n                  ياسمين الشام\n                </h3>\n                <p className=\"text-gray-400 text-sm\">تفصيل فساتين حسب الطلب</p>\n              </div>\n            </div>\n            \n            <p className=\"text-gray-300 leading-relaxed\">\n              نحن متخصصون في تفصيل الفساتين الراقية بلمسة دمشقية أصيلة. نجمع بين التراث العريق والتصاميم العصرية لنقدم لك فساتين تعكس شخصيتك وتبرز جمالك الطبيعي.\n            </p>\n\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              {socialLinks.map((social, index) => (\n                <motion.a\n                  key={social.name}\n                  href={social.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  whileInView={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.4, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"w-10 h-10 bg-gradient-to-br from-pink-500 to-rose-500 rounded-full flex items-center justify-center hover:from-pink-600 hover:to-rose-600 transition-all duration-300 transform hover:scale-110\"\n                >\n                  {social.icon}\n                </motion.a>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* روابط سريعة */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.1 }}\n            viewport={{ once: true }}\n          >\n            <h4 className=\"text-xl font-bold mb-6 text-pink-400\">روابط سريعة</h4>\n            <ul className=\"space-y-4\">\n              {quickLinks.map((link, index) => (\n                <motion.li\n                  key={link.href}\n                  initial={{ opacity: 0, x: -20 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.4, delay: 0.2 + index * 0.1 }}\n                  viewport={{ once: true }}\n                >\n                  <Link\n                    href={link.href}\n                    className=\"icon-text-spacing text-gray-300 hover:text-pink-400 transition-colors duration-300 group\"\n                  >\n                    <link.icon className=\"w-4 h-4 menu-item-icon group-hover:scale-110 transition-transform duration-300\" />\n                    <span>{link.label}</span>\n                  </Link>\n                </motion.li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* معلومات التواصل */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            <h4 className=\"text-xl font-bold mb-6 text-pink-400\">تواصل معنا</h4>\n            <div className=\"space-y-4\">\n              <div className=\"icon-text-spacing items-start text-gray-300\">\n                <MapPin className=\"w-5 h-5 text-pink-400 mt-1 menu-item-icon\" />\n                <div>\n                  <p className=\"font-medium\">العنوان</p>\n                  <p className=\"text-sm text-gray-400\">الخبر الشمالية، التقاطع السادس، شارع الأمير مشعل، الخبر، السعودية</p>\n                </div>\n              </div>\n\n              <div className=\"icon-text-spacing items-start text-gray-300\">\n                <Phone className=\"w-5 h-5 text-pink-400 mt-1 menu-item-icon\" />\n                <div>\n                  <p className=\"font-medium\">أرقام الهاتف</p>\n                  <div className=\"space-y-1\">\n                    <div>\n                      <p className=\"text-xs text-gray-500\">قسم التفصيل</p>\n                      <a href=\"tel:+966598862609\" className=\"text-sm text-gray-400 hover:text-pink-400 transition-colors duration-300\" dir=\"ltr\">\n                        +966 598 862 609\n                      </a>\n                    </div>\n                    <div>\n                      <p className=\"text-xs text-gray-500\">قسم الفساتين الجاهزة</p>\n                      <a href=\"tel:+966501503639\" className=\"text-sm text-gray-400 hover:text-pink-400 transition-colors duration-300\" dir=\"ltr\">\n                        +966 501 503 639\n                      </a>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"icon-text-spacing items-start text-gray-300\">\n                <Mail className=\"w-5 h-5 text-pink-400 mt-1 menu-item-icon\" />\n                <div>\n                  <p className=\"font-medium\">البريد الإلكتروني</p>\n                  <a href=\"mailto:<EMAIL>\" className=\"text-sm text-gray-400 hover:text-pink-400 transition-colors duration-300\">\n                    <EMAIL>\n                  </a>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* أوقات العمل */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.3 }}\n            viewport={{ once: true }}\n          >\n            <h4 className=\"text-xl font-bold mb-6 text-pink-400\">ساعات العمل</h4>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-3 space-x-reverse text-gray-300 mb-4\">\n                <Clock className=\"w-5 h-5 text-pink-400\" />\n                <span className=\"font-medium\">جدول العمل</span>\n              </div>\n              \n              {workingHours.map((schedule, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, x: 20 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"flex justify-between items-center text-sm\"\n                >\n                  <span className=\"text-gray-300\">{schedule.day}</span>\n                  <span className={`font-medium ${schedule.time === 'مغلق' ? 'text-red-400' : 'text-pink-400'}`}>\n                    {schedule.time}\n                  </span>\n                </motion.div>\n              ))}\n              \n              <div className=\"mt-6 p-4 bg-gradient-to-r from-pink-500/20 to-rose-500/20 rounded-lg border border-pink-500/30\">\n                <p className=\"text-sm text-gray-300 text-center\">\n                  <Heart className=\"w-4 h-4 inline-block ml-1 text-pink-400\" />\n                  نعمل بحب وشغف\n                </p>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* خط الفصل */}\n        <motion.div\n          initial={{ opacity: 0, scaleX: 0 }}\n          whileInView={{ opacity: 1, scaleX: 1 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"border-t border-gray-700 my-12\"\n        ></motion.div>\n\n        {/* حقوق النشر */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.5 }}\n          viewport={{ once: true }}\n          className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\"\n        >\n          <div className=\"text-gray-400 text-sm text-center md:text-right\">\n            <p>© {currentYear} ياسمين الشام. جميع الحقوق محفوظة</p>\n          </div>\n          \n          <div className=\"flex items-center space-x-6 space-x-reverse text-sm text-gray-400\">\n            <Link href=\"/privacy\" className=\"hover:text-pink-400 transition-colors duration-300\">\n              سياسة الخصوصية\n            </Link>\n            <Link href=\"/terms\" className=\"hover:text-pink-400 transition-colors duration-300\">\n              الشروط والأحكام\n            </Link>\n            <div className=\"flex items-center space-x-1 space-x-reverse\">\n              <span>صُنع بـ</span>\n              <Heart className=\"w-4 h-4 text-pink-400\" />\n              <span>في دمشق</span>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAiBe,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,aAAa;QACjB;YAAE,MAAM;YAAK,OAAO;YAAY,MAAM,6MAAA,CAAA,WAAQ;QAAC;QAC/C;YAAE,MAAM;YAAqB,OAAO;YAAY,MAAM,6MAAA,CAAA,WAAQ;QAAC;QAC/D;YAAE,MAAM;YAAgB,OAAO;YAAc,MAAM,yMAAA,CAAA,SAAM;QAAC;QAC1D;YAAE,MAAM;YAAY,OAAO;YAAW,MAAM,2MAAA,CAAA,UAAO;QAAC;KACrD;IAED,MAAM,eAAe;QACnB;YAAE,KAAK;YAAkB,MAAM;QAAmB;QAClD;YAAE,KAAK;YAAU,MAAM;QAAO;KAC/B;IAED,MAAM,cAAc;QAClB;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;KACD;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA8F;;;;;;8DAG5G,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAIzC,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;8CAI7C,6LAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CAEP,MAAM,OAAO,IAAI;4CACjB,QAAO;4CACP,KAAI;4CACJ,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,aAAa;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CACpC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;sDAET,OAAO,IAAI;2CAVP,OAAO,IAAI;;;;;;;;;;;;;;;;sCAiBxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,6LAAC;oCAAG,WAAU;8CACX,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4CAER,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;4CACtD,UAAU;gDAAE,MAAM;4CAAK;sDAEvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;;kEAEV,6LAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;kEACrB,6LAAC;kEAAM,KAAK,KAAK;;;;;;;;;;;;2CAXd,KAAK,IAAI;;;;;;;;;;;;;;;;sCAmBtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAIzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFAAwB;;;;;;sFACrC,6LAAC;4EAAE,MAAK;4EAAoB,WAAU;4EAA2E,KAAI;sFAAM;;;;;;;;;;;;8EAI7H,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFAAwB;;;;;;sFACrC,6LAAC;4EAAE,MAAK;4EAAoB,WAAU;4EAA2E,KAAI;sFAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQnI,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,6LAAC;4DAAE,MAAK;4DAA+B,WAAU;sEAA2E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASpI,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;wCAG/B,aAAa,GAAG,CAAC,CAAC,UAAU,sBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO,MAAM,QAAQ;gDAAI;gDACtD,UAAU;oDAAE,MAAM;gDAAK;gDACvB,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;kEAAiB,SAAS,GAAG;;;;;;kEAC7C,6LAAC;wDAAK,WAAW,CAAC,YAAY,EAAE,SAAS,IAAI,KAAK,SAAS,iBAAiB,iBAAiB;kEAC1F,SAAS,IAAI;;;;;;;+CATX;;;;;sDAcT,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;kEACX,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASvE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,aAAa;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACrC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;;;;;8BAIZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;oCAAE;oCAAG;oCAAY;;;;;;;;;;;;sCAGpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAqD;;;;;;8CAGrF,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAqD;;;;;;8CAGnF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;KAzPwB", "debugId": null}}, {"offset": {"line": 3986, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/components/ScrollToTop.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { ChevronUp, ArrowUp } from 'lucide-react'\n\nexport default function ScrollToTop() {\n  const [isVisible, setIsVisible] = useState(false)\n\n  // إظهار/إخفاء الزر بناءً على موضع التمرير\n  useEffect(() => {\n    const toggleVisibility = () => {\n      if (window.pageYOffset > 300) {\n        setIsVisible(true)\n      } else {\n        setIsVisible(false)\n      }\n    }\n\n    window.addEventListener('scroll', toggleVisibility)\n\n    return () => {\n      window.removeEventListener('scroll', toggleVisibility)\n    }\n  }, [])\n\n  // دالة التمرير لأعلى الصفحة\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    })\n  }\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8, y: 20 }}\n          animate={{ opacity: 1, scale: 1, y: 0 }}\n          exit={{ opacity: 0, scale: 0.8, y: 20 }}\n          transition={{ duration: 0.4, ease: \"easeOut\" }}\n          className=\"fixed bottom-8 left-1/2 transform -translate-x-1/2 z-50\"\n        >\n          <motion.button\n            onClick={scrollToTop}\n            whileHover={{\n              scale: 1.05,\n              y: -2\n            }}\n            whileTap={{\n              scale: 0.95\n            }}\n            className=\"group relative overflow-hidden\"\n            aria-label=\"العودة لأعلى الصفحة\"\n          >\n            {/* الخلفية الزجاجية المموهة */}\n            <div className=\"relative w-12 h-12 bg-white/20 backdrop-blur-md border border-white/30 rounded-full shadow-2xl hover:shadow-pink-500/25 transition-all duration-300\">\n              {/* تأثير التدرج الداخلي */}\n              <div className=\"absolute inset-0 bg-gradient-to-br from-pink-400/30 via-rose-400/20 to-purple-400/30 rounded-full opacity-80 group-hover:opacity-100 transition-opacity duration-300\"></div>\n\n              {/* تأثير الإضاءة */}\n              <div className=\"absolute inset-0 bg-gradient-to-t from-transparent via-white/10 to-white/20 rounded-full\"></div>\n\n              {/* الأيقونة */}\n              <div className=\"relative w-full h-full flex items-center justify-center\">\n                <motion.div\n                  animate={{\n                    y: [0, -2, 0],\n                  }}\n                  transition={{\n                    duration: 2,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"text-pink-600 group-hover:text-pink-700 transition-colors duration-300\"\n                >\n                  <ArrowUp className=\"w-5 h-5 drop-shadow-sm\" strokeWidth={2.5} />\n                </motion.div>\n              </div>\n\n              {/* تأثير الهوفر المتوهج */}\n              <motion.div\n                initial={{ opacity: 0 }}\n                whileHover={{ opacity: 1 }}\n                className=\"absolute inset-0 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-full blur-sm\"\n              />\n\n              {/* حدود متوهجة عند الهوفر */}\n              <motion.div\n                initial={{ opacity: 0, scale: 1 }}\n                whileHover={{\n                  opacity: 1,\n                  scale: 1.1,\n                  transition: { duration: 0.3 }\n                }}\n                className=\"absolute -inset-1 bg-gradient-to-r from-pink-400/40 to-purple-400/40 rounded-full blur-md -z-10\"\n              />\n            </div>\n\n            {/* نص مساعد */}\n            <motion.div\n              initial={{ opacity: 0, y: 10 }}\n              whileHover={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.2 }}\n              className=\"absolute -top-12 left-1/2 transform -translate-x-1/2 whitespace-nowrap\"\n            >\n              <div className=\"bg-gray-800/90 backdrop-blur-sm text-white text-sm px-3 py-1.5 rounded-lg shadow-lg border border-gray-700/50\">\n                العودة للأعلى\n                <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800/90\"></div>\n              </div>\n            </motion.div>\n          </motion.button>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;0DAAmB;oBACvB,IAAI,OAAO,WAAW,GAAG,KAAK;wBAC5B,aAAa;oBACf,OAAO;wBACL,aAAa;oBACf;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAElC;yCAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;gCAAG,EAAE;IAEL,4BAA4B;IAC5B,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,OAAO;gBAAK,GAAG;YAAG;YACzC,SAAS;gBAAE,SAAS;gBAAG,OAAO;gBAAG,GAAG;YAAE;YACtC,MAAM;gBAAE,SAAS;gBAAG,OAAO;gBAAK,GAAG;YAAG;YACtC,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;YAC7C,WAAU;sBAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;gBACT,YAAY;oBACV,OAAO;oBACP,GAAG,CAAC;gBACN;gBACA,UAAU;oBACR,OAAO;gBACT;gBACA,WAAU;gBACV,cAAW;;kCAGX,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCACP,GAAG;4CAAC;4CAAG,CAAC;4CAAG;yCAAE;oCACf;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,MAAM;oCACR;oCACA,WAAU;8CAEV,cAAA,6LAAC,+MAAA,CAAA,UAAO;wCAAC,WAAU;wCAAyB,aAAa;;;;;;;;;;;;;;;;0CAK7D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,SAAS;gCAAE;gCACzB,WAAU;;;;;;0CAIZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCACV,SAAS;oCACT,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAU;;;;;;;;;;;;kCAKd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,YAAY;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC/B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;gCAAgH;8CAE7H,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B;GA/GwB;KAAA", "debugId": null}}]}