(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/store/authStore.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useAuthStore": (()=>useAuthStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
;
;
// بيانات المستخدمين الافتراضية (سيتم استبدالها بنظام إدارة العمال)
const getStoredUsers = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    const stored = localStorage.getItem('yasmin-users');
    if (stored) {
        return JSON.parse(stored);
    }
    // المستخدمين الافتراضيين
    const defaultUsers = [
        {
            id: '1',
            email: '<EMAIL>',
            password: 'admin123',
            full_name: 'مدير النظام',
            role: 'admin',
            is_active: true
        }
    ];
    localStorage.setItem('yasmin-users', JSON.stringify(defaultUsers));
    return defaultUsers;
};
const useAuthStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        user: null,
        isLoading: false,
        error: null,
        signIn: async (email, password)=>{
            set({
                isLoading: true,
                error: null
            });
            try {
                console.log('🔐 بدء عملية تسجيل الدخول...', {
                    email
                });
                // محاكاة تأخير الشبكة
                await new Promise((resolve)=>setTimeout(resolve, 1500));
                // البحث عن المستخدم في البيانات المحفوظة
                const users = getStoredUsers();
                const foundUser = users.find((user)=>user.email.toLowerCase() === email.toLowerCase() && user.password === password);
                if (foundUser) {
                    console.log('✅ تم العثور على المستخدم:', foundUser.full_name);
                    const user = {
                        id: foundUser.id,
                        email: foundUser.email,
                        full_name: foundUser.full_name,
                        role: foundUser.role,
                        is_active: foundUser.is_active,
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                        token: `demo-token-${foundUser.id}-${Date.now()}`
                    };
                    // حفظ في localStorage أولاً
                    if ("TURBOPACK compile-time truthy", 1) {
                        localStorage.setItem('yasmin-auth-user', JSON.stringify(user));
                        console.log('💾 تم حفظ المستخدم في localStorage');
                    }
                    // تحديث حالة المتجر
                    set({
                        user,
                        isLoading: false,
                        error: null
                    });
                    console.log('🎉 تم تسجيل الدخول بنجاح!');
                    return true;
                } else {
                    console.log('❌ بيانات تسجيل الدخول غير صحيحة');
                    set({
                        error: 'بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور.',
                        isLoading: false
                    });
                    return false;
                }
            } catch (error) {
                console.error('💥 خطأ في تسجيل الدخول:', error);
                set({
                    error: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
                    isLoading: false
                });
                return false;
            }
        },
        signOut: async ()=>{
            set({
                isLoading: true
            });
            try {
                // محاكاة تأخير تسجيل الخروج
                await new Promise((resolve)=>setTimeout(resolve, 500));
                // مسح البيانات من localStorage
                if ("TURBOPACK compile-time truthy", 1) {
                    localStorage.removeItem('yasmin-auth-user');
                }
                set({
                    user: null,
                    isLoading: false,
                    error: null
                });
            } catch (error) {
                console.error('خطأ في تسجيل الخروج:', error);
                set({
                    isLoading: false,
                    error: 'خطأ في تسجيل الخروج'
                });
            }
        },
        setUser: (user)=>{
            set({
                user
            });
            // تحديث localStorage
            if ("TURBOPACK compile-time truthy", 1) {
                if (user) {
                    localStorage.setItem('yasmin-auth-user', JSON.stringify(user));
                } else {
                    localStorage.removeItem('yasmin-auth-user');
                }
            }
        },
        clearError: ()=>{
            set({
                error: null
            });
        },
        checkAuth: async ()=>{
            set({
                isLoading: true
            });
            try {
                // التحقق من وجود مستخدم محفوظ في localStorage
                if ("TURBOPACK compile-time truthy", 1) {
                    const savedUser = localStorage.getItem('yasmin-auth-user');
                    if (savedUser) {
                        const user = JSON.parse(savedUser);
                        set({
                            user,
                            isLoading: false
                        });
                        return;
                    }
                }
                set({
                    user: null,
                    isLoading: false
                });
            } catch (error) {
                console.error('خطأ في التحقق من المصادقة:', error);
                set({
                    user: null,
                    isLoading: false
                });
            }
        },
        isAuthenticated: ()=>{
            const state = get();
            return state.user !== null && state.user.is_active;
        }
    }), {
    name: 'yasmin-auth-storage',
    partialize: (state)=>({
            user: state.user
        })
}));
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/dataStore.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useDataStore": (()=>useDataStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
;
;
// توليد ID فريد
const generateId = ()=>{
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
};
const useDataStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        // البيانات الأولية
        appointments: [],
        orders: [],
        workers: [],
        isLoading: false,
        error: null,
        // إدارة المواعيد
        addAppointment: (appointmentData)=>{
            const appointment = {
                ...appointmentData,
                id: generateId(),
                status: 'pending',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            set((state)=>({
                    appointments: [
                        ...state.appointments,
                        appointment
                    ],
                    error: null
                }));
            console.log('✅ تم إضافة موعد جديد:', appointment);
        },
        updateAppointment: (id, updates)=>{
            set((state)=>({
                    appointments: state.appointments.map((appointment)=>appointment.id === id ? {
                            ...appointment,
                            ...updates,
                            updatedAt: new Date().toISOString()
                        } : appointment),
                    error: null
                }));
            console.log('✅ تم تحديث الموعد:', id);
        },
        deleteAppointment: (id)=>{
            set((state)=>({
                    appointments: state.appointments.filter((appointment)=>appointment.id !== id),
                    error: null
                }));
            console.log('✅ تم حذف الموعد:', id);
        },
        getAppointment: (id)=>{
            const state = get();
            return state.appointments.find((appointment)=>appointment.id === id);
        },
        // إدارة الطلبات
        addOrder: (orderData)=>{
            const order = {
                ...orderData,
                id: generateId(),
                status: 'pending',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            set((state)=>({
                    orders: [
                        ...state.orders,
                        order
                    ],
                    error: null
                }));
            console.log('✅ تم إضافة طلب جديد:', order);
        },
        updateOrder: (id, updates)=>{
            set((state)=>({
                    orders: state.orders.map((order)=>order.id === id ? {
                            ...order,
                            ...updates,
                            updatedAt: new Date().toISOString()
                        } : order),
                    error: null
                }));
            console.log('✅ تم تحديث الطلب:', id);
        },
        deleteOrder: (id)=>{
            set((state)=>({
                    orders: state.orders.filter((order)=>order.id !== id),
                    error: null
                }));
            console.log('✅ تم حذف الطلب:', id);
        },
        getOrder: (id)=>{
            const state = get();
            return state.orders.find((order)=>order.id === id);
        },
        // إدارة العمال
        addWorker: (workerData)=>{
            const worker = {
                ...workerData,
                id: generateId(),
                role: 'worker',
                is_active: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            set((state)=>({
                    workers: [
                        ...state.workers,
                        worker
                    ],
                    error: null
                }));
            // إضافة العامل إلى نظام المصادقة
            if ("TURBOPACK compile-time truthy", 1) {
                const users = JSON.parse(localStorage.getItem('yasmin-users') || '[]');
                users.push({
                    id: worker.id,
                    email: worker.email,
                    password: worker.password,
                    full_name: worker.full_name,
                    role: 'worker',
                    is_active: true
                });
                localStorage.setItem('yasmin-users', JSON.stringify(users));
            }
            console.log('✅ تم إضافة عامل جديد:', worker);
        },
        updateWorker: (id, updates)=>{
            set((state)=>({
                    workers: state.workers.map((worker)=>worker.id === id ? {
                            ...worker,
                            ...updates,
                            updatedAt: new Date().toISOString()
                        } : worker),
                    error: null
                }));
            // تحديث بيانات المصادقة إذا تم تغيير البريد أو كلمة المرور
            if (updates.email || updates.password || updates.full_name) {
                if ("TURBOPACK compile-time truthy", 1) {
                    const users = JSON.parse(localStorage.getItem('yasmin-users') || '[]');
                    const userIndex = users.findIndex((user)=>user.id === id);
                    if (userIndex !== -1) {
                        if (updates.email) users[userIndex].email = updates.email;
                        if (updates.password) users[userIndex].password = updates.password;
                        if (updates.full_name) users[userIndex].full_name = updates.full_name;
                        localStorage.setItem('yasmin-users', JSON.stringify(users));
                    }
                }
            }
            console.log('✅ تم تحديث العامل:', id);
        },
        deleteWorker: (id)=>{
            set((state)=>({
                    workers: state.workers.filter((worker)=>worker.id !== id),
                    error: null
                }));
            // حذف من نظام المصادقة
            if ("TURBOPACK compile-time truthy", 1) {
                const users = JSON.parse(localStorage.getItem('yasmin-users') || '[]');
                const filteredUsers = users.filter((user)=>user.id !== id);
                localStorage.setItem('yasmin-users', JSON.stringify(filteredUsers));
            }
            console.log('✅ تم حذف العامل:', id);
        },
        getWorker: (id)=>{
            const state = get();
            return state.workers.find((worker)=>worker.id === id);
        },
        // دوال خاصة للعمال
        startOrderWork: (orderId, workerId)=>{
            set((state)=>({
                    orders: state.orders.map((order)=>order.id === orderId && order.assignedWorker === workerId ? {
                            ...order,
                            status: 'in_progress',
                            updatedAt: new Date().toISOString()
                        } : order),
                    error: null
                }));
            console.log('✅ تم بدء العمل في الطلب:', orderId);
        },
        completeOrder: (orderId, workerId, completedImages = [])=>{
            set((state)=>({
                    orders: state.orders.map((order)=>order.id === orderId && order.assignedWorker === workerId ? {
                            ...order,
                            status: 'completed',
                            completedImages: completedImages.length > 0 ? completedImages : undefined,
                            updatedAt: new Date().toISOString()
                        } : order),
                    error: null
                }));
            console.log('✅ تم إنهاء الطلب:', orderId);
        },
        // وظائف مساعدة
        clearError: ()=>{
            set({
                error: null
            });
        },
        loadData: ()=>{
            set({
                isLoading: true
            });
            // البيانات محفوظة تلقائياً بواسطة persist middleware
            set({
                isLoading: false
            });
        },
        // إحصائيات
        getStats: ()=>{
            const state = get();
            return {
                totalAppointments: state.appointments.length,
                totalOrders: state.orders.length,
                totalWorkers: state.workers.length,
                pendingAppointments: state.appointments.filter((a)=>a.status === 'pending').length,
                activeOrders: state.orders.filter((o)=>[
                        'pending',
                        'in_progress'
                    ].includes(o.status)).length,
                completedOrders: state.orders.filter((o)=>o.status === 'completed').length,
                totalRevenue: state.orders.filter((o)=>o.status === 'completed').reduce((sum, order)=>sum + order.price, 0)
            };
        }
    }), {
    name: 'yasmin-data-storage',
    partialize: (state)=>({
            appointments: state.appointments,
            orders: state.orders,
            workers: state.workers
        })
}));
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useTranslation.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useTranslation": (()=>useTranslation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
// الترجمات العربية
const arTranslations = {
    // التنقل والعناوين الرئيسية
    'dashboard': 'لوحة التحكم',
    'orders': 'الطلبات',
    'appointments': 'المواعيد',
    'settings': 'الإعدادات',
    'logout': 'تسجيل الخروج',
    'profile': 'الملف الشخصي',
    'notifications': 'الإشعارات',
    // الأزرار والإجراءات
    'add_new_order': 'إضافة طلب جديد',
    'book_appointment': 'حجز موعد',
    'view_details': 'عرض التفاصيل',
    'edit': 'تعديل',
    'delete': 'حذف',
    'save': 'حفظ',
    'cancel': 'إلغاء',
    'submit': 'إرسال',
    'search': 'بحث',
    'filter': 'تصفية',
    'export': 'تصدير',
    'print': 'طباعة',
    // الحالات
    'pending': 'في الانتظار',
    'in_progress': 'قيد التنفيذ',
    'completed': 'مكتمل',
    'delivered': 'تم التسليم',
    'cancelled': 'ملغي',
    // الإحصائيات
    'active_orders': 'الطلبات النشطة',
    'completed_orders': 'الطلبات المكتملة',
    'total_orders': 'إجمالي الطلبات',
    'my_active_orders': 'طلباتي النشطة',
    'my_completed_orders': 'طلباتي المكتملة',
    'my_total_orders': 'إجمالي طلباتي',
    'today_appointments': 'مواعيد اليوم',
    // الأدوار
    'admin': 'مدير',
    'worker': 'عامل',
    // الرسائل
    'welcome_back': 'مرحباً بعودتك',
    'overview_today': 'إليك نظرة عامة على أنشطة اليوم',
    'no_orders_found': 'لا توجد طلبات',
    'no_appointments_found': 'لا توجد مواعيد',
    'loading': 'جاري التحميل...',
    'error': 'خطأ',
    'success': 'نجح',
    // أقسام لوحة التحكم
    'recent_orders': 'الطلبات الحديثة',
    'quick_actions': 'الإجراءات السريعة',
    'statistics': 'الإحصائيات',
    'recent_activity': 'النشاط الحديث',
    // تفاصيل الطلب
    'order_details': 'تفاصيل الطلب',
    'customer_info': 'معلومات الزبون',
    'order_info': 'معلومات الطلب',
    'measurements': 'المقاسات',
    'images': 'الصور',
    'notes': 'الملاحظات',
    'voice_notes': 'ملاحظات صوتية',
    'voice_notes_optional': 'ملاحظات صوتية (اختياري)',
    // التواريخ والأوقات
    'order_date': 'تاريخ الطلب',
    'delivery_date': 'موعد التسليم',
    'created_at': 'تاريخ الإنشاء',
    'updated_at': 'تاريخ التحديث',
    // أخرى
    'language': 'اللغة',
    'arabic': 'العربية',
    'english': 'English',
    'change_language': 'تغيير اللغة',
    'next_language': 'اللغة التالية',
    // نصوص رفع الصور
    'click_or_drag_images': 'انقر أو اسحب الصور هنا',
    'drop_images_here': 'اتركها هنا...',
    'image_upload_format': 'PNG, JPG, GIF حتى 10MB لكل صورة',
    'max_images_text': 'حد أقصى',
    'images_text': 'صور',
    'of': 'من',
    'add_image': 'إضافة صورة',
    'max_images_reached': 'تم الوصول للحد الأقصى من الصور',
    // نصوص الملاحظات الصوتية
    'start_recording': 'بدء التسجيل',
    'stop_recording': 'إيقاف التسجيل',
    'click_to_record_voice_note': 'اضغطي لتسجيل ملاحظة صوتية',
    'voice_note': 'ملاحظة صوتية',
    'microphone_access_error': 'لا يمكن الوصول إلى الميكروفون. يرجى التأكد من الأذونات.',
    // Delete Order Modal
    'confirm_delete_order': 'تأكيد حذف الطلب',
    'warning_delete_order': 'تحذير: حذف الطلب',
    'delete_order_warning_message': 'هذا الإجراء لا يمكن التراجع عنه. سيتم حذف الطلب وجميع البيانات المرتبطة به نهائياً.',
    'order_details': 'تفاصيل الطلب',
    'admin_email': 'البريد الإلكتروني للمدير',
    'admin_password': 'كلمة مرور المدير',
    'enter_admin_email': 'أدخل البريد الإلكتروني للمدير',
    'enter_admin_password': 'أدخل كلمة مرور المدير',
    'please_fill_all_fields': 'يرجى ملء جميع الحقول',
    'email_does_not_match': 'البريد الإلكتروني لا يطابق بريد المدير المسجل',
    'incorrect_password': 'كلمة المرور غير صحيحة',
    'confirm_delete': 'تأكيد الحذف',
    'delete_order': 'حذف الطلب',
    'order_deleted_successfully': 'تم حذف الطلب بنجاح',
    // نصوص إضافية مفقودة
    'workers_management': 'إدارة العمال',
    'reports': 'التقارير',
    'reminder': 'تذكير',
    'view_all': 'عرض الكل',
    'my_tools': 'أدواتي',
    'client': 'العميل',
    'type': 'النوع',
    'status': 'الحالة',
    'due_date': 'موعد التسليم',
    'assigned_worker': 'العامل المكلف',
    'priority': 'الأولوية',
    'high': 'عالية',
    'medium': 'متوسطة',
    'low': 'منخفضة',
    'urgent': 'عاجل',
    'normal': 'عادي',
    // نصوص الصفحات
    'search_placeholder': 'البحث بالاسم أو رقم الطلب أو وصف الطلب...',
    'search_by_text': 'البحث بالنص',
    'search_by_order_number': 'البحث برقم الطلب',
    'enter_order_number': 'أدخل رقم الطلب...',
    'filter_status': 'فلتر الحالة',
    'all_orders': 'جميع الطلبات',
    'order_date_label': 'تاريخ الطلب',
    'delivery_date_label': 'موعد التسليم',
    'worker_label': 'العامل',
    'price_label': 'السعر',
    'status_label': 'الحالة',
    'actions': 'الإجراءات',
    'start_work': 'بدء العمل',
    'complete_work': 'إنهاء العمل',
    'view_order': 'عرض الطلب',
    'edit_order': 'تعديل الطلب',
    'sar': 'ر.س',
    'no_worker_assigned': 'لم يتم تعيين عامل',
    'assigned_to_me': 'مُكلف لي',
    'not_assigned_to_me': 'غير مُكلف لي',
    'order_details': 'تفاصيل الطلب',
    'customer_information': 'معلومات الزبون',
    'name': 'الاسم:',
    'description': 'الوصف:',
    'fabric_type': 'نوع القماش:',
    'status': 'الحالة',
    'order_date': 'تاريخ الطلب',
    'delivery_date': 'موعد التسليم',
    'assigned_worker': 'العامل المسؤول',
    'measurements_cm': 'المقاسات (سم)',
    'basic_measurements': 'المقاسات الأساسية',
    'shoulder': 'الكتف',
    'shoulder_circumference': 'دوران الكتف',
    'chest_bust': 'الصدر',
    'waist': 'الخصر',
    'hips': 'الأرداف',
    'advanced_tailoring_measurements': 'مقاسات التفصيل المتقدمة',
    'dart_length': 'طول البنس',
    'bodice_length': 'طول الصدرية',
    'neckline': 'فتحة الصدر',
    'armpit': 'الإبط',
    'sleeve_measurements': 'مقاسات الأكمام',
    'sleeve_length': 'طول الكم',
    'forearm': 'الزند',
    'cuff': 'الأسوارة',
    'length_measurements': 'مقاسات الطول',
    'front_length': 'طول الأمام',
    'back_length': 'طول الخلف',
    'additional_measurements': 'مقاسات إضافية',
    'dress_length': 'طول الفستان',
    'shoulder_width': 'عرض الكتف',
    'sleeve_length_old': 'طول الأكمام',
    'design_images': 'صور التصميم',
    'notes': 'الملاحظات',
    'voice_notes': 'ملاحظات صوتية',
    // نصوص لوحة التحكم
    'homepage': 'الصفحة الرئيسية',
    'home': 'الرئيسية',
    'admin_dashboard': 'لوحة تحكم المدير',
    'worker_dashboard': 'لوحة تحكم العامل',
    'logout': 'تسجيل الخروج',
    'welcome_worker': 'مرحباً بك في لوحة تحكم العامل',
    'worker_description': 'يمكنك متابعة طلباتك المخصصة لك وتحديث حالتها من هنا',
    'quick_actions': 'الإجراءات السريعة',
    'worker_management': 'إدارة العمال',
    'reports': 'التقارير',
    'detailed_reports': 'عرض التقارير التفصيلية',
    'reminder': 'تذكير',
    'today_appointments_reminder': 'مواعيد اليوم',
    'orders_need_follow': 'طلبات تحتاج متابعة',
    'view_all': 'عرض جميع',
    'and': 'و',
    'you_have': 'لديك',
    // نصوص EditOrderModal
    'edit_order': 'تعديل الطلب',
    'fill_required_fields': 'يرجى ملء جميع الحقول المطلوبة',
    'order_updated_success': 'تم تحديث الطلب بنجاح!',
    'order_update_error': 'حدث خطأ أثناء تحديث الطلب',
    'client_name_required': 'اسم الزبونة *',
    'phone_required': 'رقم الهاتف *',
    'order_description_required': 'وصف الطلب *',
    'fabric_type_optional': 'نوع القماش',
    'price_sar_required': 'السعر (ريال سعودي) *',
    'delivery_date_required': 'موعد التسليم *',
    'status_and_worker': 'الحالة والعامل',
    'order_status': 'حالة الطلب',
    'responsible_worker': 'العامل المسؤول',
    'choose_worker': 'اختر العامل المسؤول',
    'measurements_cm': 'المقاسات (بالسنتيمتر)',
    'cm_placeholder': 'سم',
    'additional_notes_placeholder': 'أي ملاحظات أو تفاصيل إضافية...',
    'voice_notes_section': 'الملاحظات الصوتية',
    'cancel': 'إلغاء',
    'saving': 'جاري الحفظ...',
    'save_changes': 'حفظ التغييرات',
    'customer_information': 'معلومات الزبون',
    'order_details': 'تفاصيل الطلب',
    'design_images': 'صور التصميم',
    // مقاسات التفصيل
    'basic_measurements': 'المقاسات الأساسية',
    'advanced_measurements': 'مقاسات التفصيل المتقدمة',
    'sleeve_measurements': 'مقاسات الأكمام',
    'length_measurements': 'مقاسات الطول',
    'shoulder': 'الكتف',
    'shoulder_circumference': 'دوران الكتف',
    'chest': 'الصدر',
    'waist': 'الخصر',
    'hips': 'الأرداف',
    'dart_length': 'طول البنس',
    'bodice_length': 'طول الصدرية',
    'neckline': 'فتحة الصدر',
    'armpit': 'الإبط',
    'sleeve_length': 'طول الكم',
    'forearm': 'الزند',
    'cuff': 'الأسوارة',
    'front_length': 'طول الأمام',
    'back_length': 'طول الخلف',
    'notes_section': 'الملاحظات',
    // حالات الطلب
    'status_pending': 'في الانتظار',
    'status_in_progress': 'قيد التنفيذ',
    'status_completed': 'مكتمل',
    'status_delivered': 'تم التسليم',
    'status_cancelled': 'ملغي',
    // نصوص OrderModal
    'not_specified': 'غير محدد',
    'close': 'إغلاق',
    'design_image_alt': 'صورة التصميم',
    'completed_work_images': 'صور العمل المكتمل',
    'completed_work_description': 'تم رفع هذه الصور من قبل العامل عند إنهاء الطلب',
    'completed_work_image_alt': 'صورة العمل المكتمل',
    // نصوص صفحة الطلبات
    'back_to_dashboard': 'العودة إلى',
    'orders_management': 'إدارة الطلبات',
    'view_manage_orders': 'عرض وإدارة جميع طلبات التفصيل',
    'no_orders_assigned': 'لا توجد طلبات مخصصة لك',
    'no_orders_assigned_desc': 'لم يتم تخصيص أي طلبات لك بعد',
    'no_orders_found_desc': 'لم يتم العثور على طلبات تطابق معايير البحث',
    'fabric_label': 'القماش:',
    'notes_label': 'ملاحظات:',
    'view': 'عرض',
    'start_work': 'بدء التنفيذ',
    'complete_order': 'إنهاء الطلب',
    'quick_stats': 'إحصائيات سريعة',
    'complete_order_modal_title': 'إنهاء الطلب',
    'order_label': 'طلب:',
    'for_client': 'للعميلة:',
    'important_warning': 'تنبيه مهم:',
    'complete_order_warning': 'بعد الضغط على "إنهاء الطلب" سيتم تغيير حالة الطلب إلى "مكتمل" ولن تتمكن من التراجع عن هذا الإجراء. تأكد من اكتمال العمل قبل المتابعة.',
    'completing': 'جاري الإنهاء...',
    // نصوص صفحة التقارير
    'checking_permissions': 'جاري التحقق من الصلاحيات...',
    'back_to_dashboard': 'العودة إلى لوحة التحكم',
    'reports_analytics': 'التقارير والإحصائيات',
    'comprehensive_analysis': 'تحليل شامل لأداء المحل والمبيعات',
    'this_week': 'هذا الأسبوع',
    'this_month': 'هذا الشهر',
    'this_quarter': 'هذا الربع',
    'this_year': 'هذا العام',
    'export': 'تصدير',
    'key_indicators': 'المؤشرات الرئيسية',
    'total_revenue': 'إجمالي الإيرادات',
    'total_orders': 'إجمالي الطلبات',
    'completed_orders': 'طلبات مكتملة',
    'average_order_value': 'متوسط قيمة الطلب',
    'top_workers_month': 'أفضل العمال هذا الشهر',
    'orders_by_type': 'الطلبات حسب النوع',
    'monthly_trend': 'الاتجاه الشهري للإيرادات والطلبات',
    'orders_count': 'طلب',
    'revenue_label': 'الإيرادات',
    'orders_label': 'الطلبات',
    'wedding_dress': 'فستان زفاف',
    'evening_dress': 'فستان سهرة',
    'engagement_dress': 'فستان خطوبة',
    'casual_dress': 'فستان يومي',
    'other': 'أخرى',
    // نصوص صفحة العمال
    'fill_required_fields': 'يرجى ملء جميع الحقول المطلوبة',
    'worker_added_success': 'تم إضافة العامل بنجاح!',
    'error_adding_worker': 'حدث خطأ أثناء إضافة العامل',
    'worker_updated_success': 'تم تحديث العامل بنجاح!',
    'error_updating_worker': 'حدث خطأ أثناء تحديث العامل',
    'confirm_delete_worker': 'هل أنت متأكد من حذف هذا العامل؟',
    'worker_deleted_success': 'تم حذف العامل بنجاح!',
    'worker_deactivated': 'تم إلغاء تفعيل العامل',
    'worker_activated': 'تم تفعيل العامل',
    'workers_management': 'إدارة العمال',
    'view_manage_team': 'عرض وإدارة فريق العمل والخياطين',
    'add_new_worker': 'إضافة عامل جديد',
    'search_workers_placeholder': 'البحث بالاسم أو البريد الإلكتروني أو التخصص...',
    'add_new_worker_form': 'إضافة عامل جديد',
    'full_name_required': 'الاسم الكامل *',
    'enter_full_name': 'أدخل الاسم الكامل',
    'email_required': 'البريد الإلكتروني *',
    'enter_email': 'أدخل البريد الإلكتروني',
    'password_required': 'كلمة المرور *',
    'enter_password': 'أدخل كلمة المرور',
    'phone_required': 'رقم الهاتف *',
    'enter_phone': 'أدخل رقم الهاتف',
    'specialty_required': 'التخصص *',
    'specialty_example': 'مثال: فساتين الزفاف، التطريز',
    'adding': 'جاري الإضافة...',
    'add_worker': 'إضافة العامل',
    'edit_worker': 'تعديل العامل',
    'new_password': 'كلمة المرور الجديدة',
    'leave_empty_no_change': 'اتركها فارغة إذا لم تريد تغييرها',
    'status': 'الحالة',
    'active': 'نشط',
    'inactive': 'غير نشط',
    'saving': 'جاري الحفظ...',
    'save_changes': 'حفظ التغييرات',
    'no_workers': 'لا توجد عمال',
    'no_workers_found': 'لم يتم العثور على عمال يطابقون معايير البحث',
    'completed_orders': 'طلب مكتمل',
    'joined_on': 'انضم في:',
    'total_workers': 'إجمالي العمال',
    'active_workers': 'عمال نشطين',
    'total_completed_orders': 'إجمالي الطلبات المكتملة',
    // نصوص صفحة المواعيد
    'loading': 'جاري التحميل...',
    'appointments_management': 'إدارة المواعيد',
    'view_manage_appointments': 'عرض وإدارة جميع المواعيد المحجوزة',
    'book_new_appointment': 'حجز موعد جديد',
    'search_appointments_placeholder': 'البحث بالاسم أو الهاتف أو رقم الموعد...',
    'all_statuses': 'جميع الحالات',
    'pending': 'في الانتظار',
    'confirmed': 'مؤكد',
    'completed': 'مكتمل',
    'cancelled': 'ملغي',
    'all_dates': 'جميع التواريخ',
    'today': 'اليوم',
    'tomorrow': 'غداً',
    'this_week': 'هذا الأسبوع',
    'no_appointments': 'لا توجد مواعيد',
    'no_appointments_found': 'لم يتم العثور على مواعيد تطابق معايير البحث',
    'client_info': 'معلومات العميل',
    'appointment_details': 'تفاصيل الموعد',
    'date_time': 'التاريخ والوقت',
    'created_on': 'تم الإنشاء:',
    'actions': 'الإجراءات',
    'confirm_appointment': 'تأكيد الموعد',
    'mark_attended': 'تم الحضور',
    'cancel_appointment': 'إلغاء الموعد',
    'quick_stats': 'إحصائيات سريعة',
    'am': 'ص',
    'pm': 'م',
    // نصوص صفحة إضافة الطلبات
    'add_new_order': 'إضافة طلب جديد',
    'add_new_order_description': 'أضف طلب تفصيل جديد مع جميع التفاصيل والمقاسات المطلوبة',
    'basic_information': 'المعلومات الأساسية',
    'client_name_required': 'اسم الزبونة *',
    'enter_client_name': 'أدخل اسم الزبونة',
    'phone_required': 'رقم الهاتف *',
    'enter_phone': 'أدخل رقم الهاتف',
    'order_description_required': 'وصف الطلب *',
    'order_description_placeholder': 'مثال: فستان زفاف أبيض مطرز',
    'fabric_type': 'نوع القماش',
    'fabric_type_placeholder': 'مثال: ساتان، شيفون، دانتيل',
    'price_sar': 'السعر (ريال سعودي) *',
    'responsible_worker': 'العامل المسؤول',
    'choose_worker': 'اختر العامل المسؤول',
    'delivery_date_required': 'موعد التسليم *',
    'design_images': 'صور التصميم',
    'measurements_cm': 'المقاسات (بالسنتيمتر)',
    'basic_measurements': 'المقاسات الأساسية',
    'shoulder': 'الكتف',
    'shoulder_circumference': 'دوران الكتف',
    'chest': 'الصدر',
    'waist': 'الخصر',
    'hips': 'الأرداف',
    'advanced_measurements': 'مقاسات التفصيل المتقدمة',
    'dart_length': 'طول البنس',
    'bodice_length': 'طول الصدرية',
    'neckline': 'فتحة الصدر',
    'armpit': 'الإبط',
    'sleeve_measurements': 'مقاسات الأكمام',
    'sleeve_length': 'طول الكم',
    'forearm': 'الزند',
    'cuff': 'الأسوارة',
    'length_measurements': 'مقاسات الطول',
    'front_length': 'طول الأمام',
    'back_length': 'طول الخلف',
    'additional_notes': 'ملاحظات إضافية',
    'additional_notes_placeholder': 'أي ملاحظات أو تفاصيل إضافية حول التصميم أو التفصيل...',
    'voice_notes': 'الملاحظات الصوتية',
    'saving': 'جاري الحفظ...',
    'save_order': 'حفظ الطلب',
    'cancel': 'إلغاء',
    'cm_placeholder': 'سم',
    'fill_required_fields': 'يرجى ملء جميع الحقول المطلوبة',
    'order_added_success': 'تم إضافة الطلب بنجاح! سيتم توجيهك إلى صفحة الطلبات...',
    'order_add_error': 'حدث خطأ أثناء إضافة الطلب. يرجى المحاولة مرة أخرى.',
    'back_to_dashboard': 'العودة إلى لوحة التحكم',
    // نصوص الفوتر
    'home': 'الرئيسية',
    'book_appointment': 'حجز موعد',
    'track_order': 'استعلام عن الطلب',
    'fabrics': 'الأقمشة',
    'saturday_thursday': 'السبت - الخميس',
    'friday': 'الجمعة',
    'closed': 'مغلق',
    'facebook': 'فيسبوك',
    'instagram': 'إنستغرام',
    'whatsapp': 'واتساب',
    'yasmin_alsham': 'ياسمين الشام',
    'custom_dress_tailoring': 'تفصيل فساتين حسب الطلب',
    'footer_description': 'نحن نجمع بين التراث الدمشقي العريق والتصاميم العصرية لنقدم لك فساتين تعكس شخصيتك وتبرز جمالك الطبيعي.',
    'quick_links': 'روابط سريعة',
    'contact_us': 'تواصلي معنا',
    'address': 'العنوان',
    'address_text': 'الخبر الشمالية، التقاطع السادس، شارع الأمير مشعل، الخبر، السعودية',
    'phone_numbers': 'أرقام الهاتف',
    'tailoring_department': 'قسم التفصيل - ياسمين الشام',
    'ready_made_department': 'قسم الجاهز والمقاسات - ياسمين الشام 2',
    'email': 'البريد الإلكتروني',
    'working_hours': 'أوقات العمل',
    'work_schedule': 'مواعيد العمل',
    'work_with_love': 'نعمل بحب لإسعادك',
    'all_rights_reserved': 'جميع الحقوق محفوظة.',
    'privacy_policy': 'سياسة الخصوصية',
    'terms_conditions': 'الشروط والأحكام',
    'made_with': 'صُنع بـ',
    'in_damascus': 'في دمشق'
};
// الترجمات الإنجليزية
const enTranslations = {
    // التنقل والعناوين الرئيسية
    'dashboard': 'Dashboard',
    'orders': 'Orders',
    'appointments': 'Appointments',
    'settings': 'Settings',
    'logout': 'Logout',
    'profile': 'Profile',
    'notifications': 'Notifications',
    // الأزرار والإجراءات
    'add_new_order': 'Add New Order',
    'book_appointment': 'Book Appointment',
    'view_details': 'View Details',
    'edit': 'Edit',
    'delete': 'Delete',
    'save': 'Save',
    'cancel': 'Cancel',
    'submit': 'Submit',
    'search': 'Search',
    'filter': 'Filter',
    'export': 'Export',
    'print': 'Print',
    // الحالات
    'pending': 'Pending',
    'in_progress': 'In Progress',
    'completed': 'Completed',
    'delivered': 'Delivered',
    'cancelled': 'Cancelled',
    // الإحصائيات
    'active_orders': 'Active Orders',
    'completed_orders': 'Completed Orders',
    'total_orders': 'Total Orders',
    'my_active_orders': 'My Active Orders',
    'my_completed_orders': 'My Completed Orders',
    'my_total_orders': 'My Total Orders',
    'today_appointments': 'Today\'s Appointments',
    // الأدوار
    'admin': 'Admin',
    'worker': 'Worker',
    // الرسائل
    'welcome_back': 'Welcome Back',
    'overview_today': 'Here\'s an overview of today\'s activities',
    'no_orders_found': 'No orders found',
    'no_appointments_found': 'No appointments found',
    'loading': 'Loading...',
    'error': 'Error',
    'success': 'Success',
    // أقسام لوحة التحكم
    'recent_orders': 'Recent Orders',
    'quick_actions': 'Quick Actions',
    'statistics': 'Statistics',
    'recent_activity': 'Recent Activity',
    // تفاصيل الطلب
    'order_details': 'Order Details',
    'customer_info': 'Customer Information',
    'order_info': 'Order Information',
    'measurements': 'Measurements',
    'images': 'Images',
    'notes': 'Notes',
    'voice_notes': 'Voice Notes',
    'voice_notes_optional': 'Voice Notes (Optional)',
    // التواريخ والأوقات
    'order_date': 'Order Date',
    'delivery_date': 'Delivery Date',
    'created_at': 'Created At',
    'updated_at': 'Updated At',
    // أخرى
    'language': 'Language',
    'arabic': 'العربية',
    'english': 'English',
    'change_language': 'Change Language',
    'next_language': 'Next Language',
    // نصوص رفع الصور
    'click_or_drag_images': 'Click or drag images here',
    'drop_images_here': 'Drop them here...',
    'image_upload_format': 'PNG, JPG, GIF up to 10MB per image',
    'max_images_text': 'maximum',
    'images_text': 'images',
    'of': 'of',
    'add_image': 'Add image',
    'max_images_reached': 'Maximum number of images reached',
    // نصوص الملاحظات الصوتية
    'start_recording': 'Start Recording',
    'stop_recording': 'Stop Recording',
    'click_to_record_voice_note': 'Click to record a voice note',
    'voice_note': 'Voice Note',
    'microphone_access_error': 'Cannot access microphone. Please check permissions.',
    // Delete Order Modal
    'confirm_delete_order': 'Confirm Delete Order',
    'warning_delete_order': 'Warning: Delete Order',
    'delete_order_warning_message': 'This action cannot be undone. The order and all associated data will be permanently deleted.',
    'order_details': 'Order Details',
    'admin_email': 'Admin Email',
    'admin_password': 'Admin Password',
    'enter_admin_email': 'Enter admin email',
    'enter_admin_password': 'Enter admin password',
    'please_fill_all_fields': 'Please fill all fields',
    'email_does_not_match': 'Email does not match the registered admin email',
    'incorrect_password': 'Incorrect password',
    'confirm_delete': 'Confirm Delete',
    'delete_order': 'Delete Order',
    'order_deleted_successfully': 'Order deleted successfully',
    // نصوص إضافية مفقودة
    'workers_management': 'Workers Management',
    'reports': 'Reports',
    'reminder': 'Reminder',
    'view_all': 'View All',
    'my_tools': 'My Tools',
    'client': 'Client',
    'type': 'Type',
    'status': 'Status',
    'due_date': 'Due Date',
    'assigned_worker': 'Assigned Worker',
    'priority': 'Priority',
    'high': 'High',
    'medium': 'Medium',
    'low': 'Low',
    'urgent': 'Urgent',
    'normal': 'Normal',
    // نصوص الصفحات
    'search_placeholder': 'Search by name, order number, or description...',
    'search_by_text': 'Search by Text',
    'search_by_order_number': 'Search by Order Number',
    'enter_order_number': 'Enter order number...',
    'filter_status': 'Filter Status',
    'all_orders': 'All Orders',
    'order_date_label': 'Order Date',
    'delivery_date_label': 'Delivery Date',
    'worker_label': 'Worker',
    'price_label': 'Price',
    'status_label': 'Status',
    'actions': 'Actions',
    'start_work': 'Start Work',
    'complete_work': 'Complete Work',
    'view_order': 'View Order',
    'edit_order': 'Edit Order',
    'sar': 'SAR',
    'no_worker_assigned': 'No worker assigned',
    'assigned_to_me': 'Assigned to me',
    'not_assigned_to_me': 'Not assigned to me',
    'order_details': 'Order Details',
    'customer_information': 'Customer Information',
    'name': 'Name:',
    'description': 'Description:',
    'fabric_type': 'Fabric Type:',
    'status': 'Status',
    'order_date': 'Order Date',
    'delivery_date': 'Delivery Date',
    'assigned_worker': 'Assigned Worker',
    'measurements_cm': 'Measurements (cm)',
    'basic_measurements': 'Basic Measurements',
    'shoulder': 'Shoulder',
    'shoulder_circumference': 'Shoulder Circumference',
    'chest_bust': 'Chest/Bust',
    'waist': 'Waist',
    'hips': 'Hips',
    'advanced_tailoring_measurements': 'Advanced Tailoring Measurements',
    'dart_length': 'Dart Length',
    'bodice_length': 'Bodice Length',
    'neckline': 'Neckline',
    'armpit': 'Armpit',
    'sleeve_measurements': 'Sleeve Measurements',
    'sleeve_length': 'Sleeve Length',
    'forearm': 'Forearm',
    'cuff': 'Cuff',
    'length_measurements': 'Length Measurements',
    'front_length': 'Front Length',
    'back_length': 'Back Length',
    'additional_measurements': 'Additional Measurements',
    'dress_length': 'Dress Length',
    'shoulder_width': 'Shoulder Width',
    'sleeve_length_old': 'Sleeve Length',
    'design_images': 'Design Images',
    'notes': 'Notes',
    'voice_notes': 'Voice Notes',
    // نصوص لوحة التحكم
    'homepage': 'Homepage',
    'home': 'Home',
    'admin_dashboard': 'Admin Dashboard',
    'worker_dashboard': 'Worker Dashboard',
    'logout': 'Logout',
    'welcome_worker': 'Welcome to Worker Dashboard',
    'worker_description': 'You can track your assigned orders and update their status here',
    'quick_actions': 'Quick Actions',
    'worker_management': 'Worker Management',
    'reports': 'Reports',
    'detailed_reports': 'View Detailed Reports',
    'reminder': 'Reminder',
    'today_appointments_reminder': 'Today\'s Appointments',
    'orders_need_follow': 'Orders Need Follow-up',
    'view_all': 'View All',
    'and': 'and',
    'you_have': 'You have',
    // نصوص EditOrderModal
    'edit_order': 'Edit Order',
    'fill_required_fields': 'Please fill all required fields',
    'order_updated_success': 'Order updated successfully!',
    'order_update_error': 'Error occurred while updating order',
    'client_name_required': 'Client Name *',
    'phone_required': 'Phone Number *',
    'order_description_required': 'Order Description *',
    'fabric_type_optional': 'Fabric Type',
    'price_sar_required': 'Price (SAR) *',
    'delivery_date_required': 'Delivery Date *',
    'status_and_worker': 'Status and Worker',
    'order_status': 'Order Status',
    'responsible_worker': 'Responsible Worker',
    'choose_worker': 'Choose Responsible Worker',
    'measurements_cm': 'Measurements (cm)',
    'cm_placeholder': 'cm',
    'additional_notes_placeholder': 'Any additional notes or details...',
    'voice_notes_section': 'Voice Notes',
    'cancel': 'Cancel',
    'saving': 'Saving...',
    'save_changes': 'Save Changes',
    'customer_information': 'Customer Information',
    'order_details': 'Order Details',
    'design_images': 'Design Images',
    // مقاسات التفصيل
    'basic_measurements': 'Basic Measurements',
    'advanced_measurements': 'Advanced Tailoring Measurements',
    'sleeve_measurements': 'Sleeve Measurements',
    'length_measurements': 'Length Measurements',
    'shoulder': 'Shoulder',
    'shoulder_circumference': 'Shoulder Circumference',
    'chest': 'Chest',
    'waist': 'Waist',
    'hips': 'Hips',
    'dart_length': 'Dart Length',
    'bodice_length': 'Bodice Length',
    'neckline': 'Neckline',
    'armpit': 'Armpit',
    'sleeve_length': 'Sleeve Length',
    'forearm': 'Forearm',
    'cuff': 'Cuff',
    'front_length': 'Front Length',
    'back_length': 'Back Length',
    'notes_section': 'Notes',
    // حالات الطلب
    'status_pending': 'Pending',
    'status_in_progress': 'In Progress',
    'status_completed': 'Completed',
    'status_delivered': 'Delivered',
    'status_cancelled': 'Cancelled',
    // نصوص OrderModal
    'not_specified': 'Not Specified',
    'close': 'Close',
    'design_image_alt': 'Design Image',
    'completed_work_images': 'Completed Work Images',
    'completed_work_description': 'These images were uploaded by the worker upon order completion',
    'completed_work_image_alt': 'Completed Work Image',
    // نصوص صفحة الطلبات
    'back_to_dashboard': 'Back to',
    'orders_management': 'Orders Management',
    'view_manage_orders': 'View and manage all tailoring orders',
    'no_orders_assigned': 'No orders assigned to you',
    'no_orders_assigned_desc': 'No orders have been assigned to you yet',
    'no_orders_found_desc': 'No orders found matching the search criteria',
    'fabric_label': 'Fabric:',
    'notes_label': 'Notes:',
    'view': 'View',
    'start_work': 'Start Work',
    'complete_order': 'Complete Order',
    'quick_stats': 'Quick Stats',
    'complete_order_modal_title': 'Complete Order',
    'order_label': 'Order:',
    'for_client': 'For client:',
    'important_warning': 'Important Warning:',
    'complete_order_warning': 'After clicking "Complete Order", the order status will be changed to "completed" and you will not be able to undo this action. Make sure the work is complete before proceeding.',
    'completing': 'Completing...',
    // نصوص صفحة التقارير
    'checking_permissions': 'Checking permissions...',
    'back_to_dashboard': 'Back to Dashboard',
    'reports_analytics': 'Reports & Analytics',
    'comprehensive_analysis': 'Comprehensive analysis of store performance and sales',
    'this_week': 'This Week',
    'this_month': 'This Month',
    'this_quarter': 'This Quarter',
    'this_year': 'This Year',
    'export': 'Export',
    'key_indicators': 'Key Indicators',
    'total_revenue': 'Total Revenue',
    'total_orders': 'Total Orders',
    'completed_orders': 'Completed Orders',
    'average_order_value': 'Average Order Value',
    'top_workers_month': 'Top Workers This Month',
    'orders_by_type': 'Orders by Type',
    'monthly_trend': 'Monthly Revenue & Orders Trend',
    'orders_count': 'orders',
    'revenue_label': 'Revenue',
    'orders_label': 'Orders',
    'wedding_dress': 'Wedding Dress',
    'evening_dress': 'Evening Dress',
    'engagement_dress': 'Engagement Dress',
    'casual_dress': 'Casual Dress',
    'other': 'Other',
    // نصوص صفحة العمال
    'fill_required_fields': 'Please fill all required fields',
    'worker_added_success': 'Worker added successfully!',
    'error_adding_worker': 'Error occurred while adding worker',
    'worker_updated_success': 'Worker updated successfully!',
    'error_updating_worker': 'Error occurred while updating worker',
    'confirm_delete_worker': 'Are you sure you want to delete this worker?',
    'worker_deleted_success': 'Worker deleted successfully!',
    'worker_deactivated': 'Worker deactivated',
    'worker_activated': 'Worker activated',
    'workers_management': 'Workers Management',
    'view_manage_team': 'View and manage work team and tailors',
    'add_new_worker': 'Add New Worker',
    'search_workers_placeholder': 'Search by name, email, or specialty...',
    'add_new_worker_form': 'Add New Worker',
    'full_name_required': 'Full Name *',
    'enter_full_name': 'Enter full name',
    'email_required': 'Email *',
    'enter_email': 'Enter email',
    'password_required': 'Password *',
    'enter_password': 'Enter password',
    'phone_required': 'Phone Number *',
    'enter_phone': 'Enter phone number',
    'specialty_required': 'Specialty *',
    'specialty_example': 'Example: Wedding dresses, Embroidery',
    'adding': 'Adding...',
    'add_worker': 'Add Worker',
    'edit_worker': 'Edit Worker',
    'new_password': 'New Password',
    'leave_empty_no_change': 'Leave empty if you don\'t want to change it',
    'status': 'Status',
    'active': 'Active',
    'inactive': 'Inactive',
    'saving': 'Saving...',
    'save_changes': 'Save Changes',
    'no_workers': 'No Workers',
    'no_workers_found': 'No workers found matching the search criteria',
    'completed_orders': 'completed orders',
    'joined_on': 'Joined on:',
    'total_workers': 'Total Workers',
    'active_workers': 'Active Workers',
    'total_completed_orders': 'Total Completed Orders',
    // نصوص صفحة المواعيد
    'loading': 'Loading...',
    'appointments_management': 'Appointments Management',
    'view_manage_appointments': 'View and manage all booked appointments',
    'book_new_appointment': 'Book New Appointment',
    'search_appointments_placeholder': 'Search by name, phone, or appointment number...',
    'all_statuses': 'All Statuses',
    'pending': 'Pending',
    'confirmed': 'Confirmed',
    'completed': 'Completed',
    'cancelled': 'Cancelled',
    'all_dates': 'All Dates',
    'today': 'Today',
    'tomorrow': 'Tomorrow',
    'this_week': 'This Week',
    'no_appointments': 'No Appointments',
    'no_appointments_found': 'No appointments found matching the search criteria',
    'client_info': 'Client Information',
    'appointment_details': 'Appointment Details',
    'date_time': 'Date & Time',
    'created_on': 'Created:',
    'actions': 'Actions',
    'confirm_appointment': 'Confirm Appointment',
    'mark_attended': 'Mark Attended',
    'cancel_appointment': 'Cancel Appointment',
    'quick_stats': 'Quick Statistics',
    'am': 'AM',
    'pm': 'PM',
    // نصوص صفحة إضافة الطلبات
    'add_new_order': 'Add New Order',
    'add_new_order_description': 'Add a new tailoring order with all required details and measurements',
    'basic_information': 'Basic Information',
    'client_name_required': 'Client Name *',
    'enter_client_name': 'Enter client name',
    'phone_required': 'Phone Number *',
    'enter_phone': 'Enter phone number',
    'order_description_required': 'Order Description *',
    'order_description_placeholder': 'Example: White embroidered wedding dress',
    'fabric_type': 'Fabric Type',
    'fabric_type_placeholder': 'Example: Satin, Chiffon, Lace',
    'price_sar': 'Price (Saudi Riyal) *',
    'responsible_worker': 'Responsible Worker',
    'choose_worker': 'Choose responsible worker',
    'delivery_date_required': 'Delivery Date *',
    'design_images': 'Design Images',
    'measurements_cm': 'Measurements (in centimeters)',
    'basic_measurements': 'Basic Measurements',
    'shoulder': 'Shoulder',
    'shoulder_circumference': 'Shoulder Circumference',
    'chest': 'Chest',
    'waist': 'Waist',
    'hips': 'Hips',
    'advanced_measurements': 'Advanced Tailoring Measurements',
    'dart_length': 'Dart Length',
    'bodice_length': 'Bodice Length',
    'neckline': 'Neckline',
    'armpit': 'Armpit',
    'sleeve_measurements': 'Sleeve Measurements',
    'sleeve_length': 'Sleeve Length',
    'forearm': 'Forearm',
    'cuff': 'Cuff',
    'length_measurements': 'Length Measurements',
    'front_length': 'Front Length',
    'back_length': 'Back Length',
    'additional_notes': 'Additional Notes',
    'additional_notes_placeholder': 'Any additional notes or details about the design or tailoring...',
    'voice_notes': 'Voice Notes',
    'saving': 'Saving...',
    'save_order': 'Save Order',
    'cancel': 'Cancel',
    'cm_placeholder': 'cm',
    'fill_required_fields': 'Please fill all required fields',
    'order_added_success': 'Order added successfully! You will be redirected to orders page...',
    'order_add_error': 'An error occurred while adding the order. Please try again.',
    'back_to_dashboard': 'Back to Dashboard',
    // Footer texts
    'home': 'Home',
    'book_appointment': 'Book Appointment',
    'track_order': 'Track Order',
    'fabrics': 'Fabrics',
    'saturday_thursday': 'Saturday - Thursday',
    'friday': 'Friday',
    'closed': 'Closed',
    'facebook': 'Facebook',
    'instagram': 'Instagram',
    'whatsapp': 'WhatsApp',
    'yasmin_alsham': 'Yasmin Alsham',
    'custom_dress_tailoring': 'Custom Dress Tailoring',
    'footer_description': 'We combine the ancient Damascene heritage with modern designs to offer you dresses that reflect your personality and highlight your natural beauty.',
    'quick_links': 'Quick Links',
    'contact_us': 'Contact Us',
    'address': 'Address',
    'address_text': 'North Khobar, 6th Intersection, Prince Mishaal Street, Khobar, Saudi Arabia',
    'phone_numbers': 'Phone Numbers',
    'tailoring_department': 'Tailoring Department - Yasmin Alsham',
    'ready_made_department': 'Ready-made & Sizing Department - Yasmin Alsham 2',
    'email': 'Email',
    'working_hours': 'Working Hours',
    'work_schedule': 'Work Schedule',
    'work_with_love': 'We work with love to make you happy',
    'all_rights_reserved': 'All rights reserved.',
    'privacy_policy': 'Privacy Policy',
    'terms_conditions': 'Terms & Conditions',
    'made_with': 'Made with',
    'in_damascus': 'in Damascus',
    // Header texts
    'ready_dresses': 'Ready Dresses',
    // Hero texts
    'hero_subtitle': 'Custom Dress Tailoring with Damascene Elegance',
    'hero_subtitle_desktop': 'Custom Dress Tailoring',
    'hero_subtitle_elegant': 'with Authentic Damascene Elegance',
    'hero_description_mobile': 'We specialize in tailoring elegant dresses with an authentic Damascene touch. From luxurious wedding dresses to elegant evening gowns, we turn your dreams into reality with expert hands and innovative designs.',
    'hero_description_desktop': 'We combine the ancient Damascene heritage with modern designs to offer you dresses that reflect your personality and highlight your natural beauty. Every dress is a story, and every story tells of elegance and beauty.',
    'explore_ready_designs': 'Explore Our Ready Designs',
    'yasmin_alsham_alt': 'Yasmin Alsham - Custom Dress Tailoring'
};
function useTranslation() {
    _s();
    const [language, setLanguage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('ar');
    // تحميل اللغة المحفوظة عند بدء التطبيق
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useTranslation.useEffect": ()=>{
            const savedLanguage = localStorage.getItem('dashboard-language');
            if (savedLanguage) {
                setLanguage(savedLanguage);
            }
        }
    }["useTranslation.useEffect"], []);
    // حفظ اللغة عند تغييرها
    const changeLanguage = (newLanguage)=>{
        setLanguage(newLanguage);
        localStorage.setItem('dashboard-language', newLanguage);
    };
    // دالة الترجمة
    const t = (key)=>{
        const translations = language === 'ar' ? arTranslations : enTranslations;
        const translation = translations[key];
        if (typeof translation === 'string') {
            return translation;
        }
        // إذا لم توجد الترجمة، أرجع المفتاح نفسه
        return key;
    };
    // التحقق من اللغة الحالية
    const isArabic = language === 'ar';
    const isEnglish = language === 'en';
    return {
        language,
        changeLanguage,
        t,
        isArabic,
        isEnglish
    };
}
_s(useTranslation, "uNEeEraTehRiBcHGM2i1R+nM1u0=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ProtectedRoute.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ProtectedRoute)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$authStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/authStore.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/shield.js [app-client] (ecmascript) <export default as Shield>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-client] (ecmascript) <export default as AlertCircle>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
function ProtectedRoute({ children, requiredRole, redirectTo = '/login' }) {
    _s();
    const { user, isLoading, checkAuth } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$authStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [isChecking, setIsChecking] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ProtectedRoute.useEffect": ()=>{
            const initAuth = {
                "ProtectedRoute.useEffect.initAuth": async ()=>{
                    await checkAuth();
                    setIsChecking(false);
                }
            }["ProtectedRoute.useEffect.initAuth"];
            initAuth();
        }
    }["ProtectedRoute.useEffect"], [
        checkAuth
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ProtectedRoute.useEffect": ()=>{
            if (!isChecking && !isLoading) {
                if (!user) {
                    router.push(redirectTo);
                    return;
                }
                if (requiredRole && user.role !== requiredRole) {
                    router.push('/dashboard');
                    return;
                }
                if (!user.is_active) {
                    router.push('/login');
                    return;
                }
            }
        }
    }["ProtectedRoute.useEffect"], [
        user,
        isChecking,
        isLoading,
        requiredRole,
        router,
        redirectTo
    ]);
    // عرض شاشة التحميل أثناء التحقق من المصادقة
    if (isChecking || isLoading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                initial: {
                    opacity: 0,
                    scale: 0.9
                },
                animate: {
                    opacity: 1,
                    scale: 1
                },
                transition: {
                    duration: 0.5
                },
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-16 h-16 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full flex items-center justify-center mx-auto mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__["Shield"], {
                            className: "w-8 h-8 text-white animate-pulse"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ProtectedRoute.tsx",
                            lineNumber: 63,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ProtectedRoute.tsx",
                        lineNumber: 62,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-12 h-12 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ProtectedRoute.tsx",
                        lineNumber: 65,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600 font-medium",
                        children: "جاري التحقق من الصلاحيات..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/ProtectedRoute.tsx",
                        lineNumber: 66,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ProtectedRoute.tsx",
                lineNumber: 56,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ProtectedRoute.tsx",
            lineNumber: 55,
            columnNumber: 7
        }, this);
    }
    // عرض رسالة خطأ إذا لم يكن المستخدم مخول
    if (!user) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                initial: {
                    opacity: 0,
                    y: 20
                },
                animate: {
                    opacity: 1,
                    y: 0
                },
                transition: {
                    duration: 0.6
                },
                className: "text-center max-w-md mx-auto px-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                            className: "w-8 h-8 text-red-600"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ProtectedRoute.tsx",
                            lineNumber: 83,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ProtectedRoute.tsx",
                        lineNumber: 82,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-2xl font-bold text-gray-800 mb-4",
                        children: "غير مخول للوصول"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ProtectedRoute.tsx",
                        lineNumber: 85,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600 mb-6",
                        children: "يجب تسجيل الدخول للوصول إلى هذه الصفحة"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ProtectedRoute.tsx",
                        lineNumber: 86,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>router.push('/login'),
                        className: "btn-primary px-6 py-3",
                        children: "تسجيل الدخول"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ProtectedRoute.tsx",
                        lineNumber: 89,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ProtectedRoute.tsx",
                lineNumber: 76,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ProtectedRoute.tsx",
            lineNumber: 75,
            columnNumber: 7
        }, this);
    }
    if (requiredRole && user.role !== requiredRole) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                initial: {
                    opacity: 0,
                    y: 20
                },
                animate: {
                    opacity: 1,
                    y: 0
                },
                transition: {
                    duration: 0.6
                },
                className: "text-center max-w-md mx-auto px-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                            className: "w-8 h-8 text-yellow-600"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ProtectedRoute.tsx",
                            lineNumber: 110,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ProtectedRoute.tsx",
                        lineNumber: 109,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-2xl font-bold text-gray-800 mb-4",
                        children: "صلاحيات غير كافية"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ProtectedRoute.tsx",
                        lineNumber: 112,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600 mb-6",
                        children: "ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ProtectedRoute.tsx",
                        lineNumber: 113,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>router.push('/dashboard'),
                        className: "btn-primary px-6 py-3",
                        children: "العودة إلى لوحة التحكم"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ProtectedRoute.tsx",
                        lineNumber: 116,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ProtectedRoute.tsx",
                lineNumber: 103,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ProtectedRoute.tsx",
            lineNumber: 102,
            columnNumber: 7
        }, this);
    }
    if (!user.is_active) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                initial: {
                    opacity: 0,
                    y: 20
                },
                animate: {
                    opacity: 1,
                    y: 0
                },
                transition: {
                    duration: 0.6
                },
                className: "text-center max-w-md mx-auto px-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                            className: "w-8 h-8 text-gray-600"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ProtectedRoute.tsx",
                            lineNumber: 137,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ProtectedRoute.tsx",
                        lineNumber: 136,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-2xl font-bold text-gray-800 mb-4",
                        children: "حساب غير نشط"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ProtectedRoute.tsx",
                        lineNumber: 139,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600 mb-6",
                        children: "تم إلغاء تفعيل حسابك. يرجى التواصل مع المدير."
                    }, void 0, false, {
                        fileName: "[project]/src/components/ProtectedRoute.tsx",
                        lineNumber: 140,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>router.push('/login'),
                        className: "btn-primary px-6 py-3",
                        children: "تسجيل الدخول"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ProtectedRoute.tsx",
                        lineNumber: 143,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ProtectedRoute.tsx",
                lineNumber: 130,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ProtectedRoute.tsx",
            lineNumber: 129,
            columnNumber: 7
        }, this);
    }
    // عرض المحتوى إذا كان كل شيء صحيح
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s(ProtectedRoute, "pAkYW10ywEin/CLzSSlll+3Y4Ig=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$authStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = ProtectedRoute;
var _c;
__turbopack_context__.k.register(_c, "ProtectedRoute");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/dashboard/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>DashboardPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/styled-jsx/style.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$authStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/authStore.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$dataStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/dataStore.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useTranslation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useTranslation.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ProtectedRoute$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ProtectedRoute.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chart-column.js [app-client] (ecmascript) <export default as BarChart3>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-client] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-client] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/package.js [app-client] (ecmascript) <export default as Package>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-client] (ecmascript) <export default as Settings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$out$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LogOut$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/log-out.js [app-client] (ecmascript) <export default as LogOut>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-client] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-client] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-client] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-right.js [app-client] (ecmascript) <export default as ArrowRight>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
function DashboardContent() {
    _s();
    const { user, signOut } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$authStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])();
    const { orders, appointments, getStats } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$dataStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDataStore"])();
    const { t, language, changeLanguage, isArabic } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useTranslation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslation"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const handleSignOut = async ()=>{
        await signOut();
        router.push('/');
    };
    // حساب الإحصائيات الحقيقية
    const realStats = getStats();
    // حساب المواعيد اليوم
    const todayAppointments = appointments.filter((appointment)=>{
        const today = new Date().toISOString().split('T')[0];
        return appointment.appointmentDate === today && appointment.status !== 'cancelled';
    }).length;
    // الإحصائيات حسب الدور
    const getStatsForRole = ()=>{
        if (user.role === 'worker') {
            // إحصائيات العامل - طلباته فقط
            const workerOrders = orders.filter((order)=>order.assignedWorker === user.id);
            const workerCompletedOrders = workerOrders.filter((order)=>order.status === 'completed');
            const workerActiveOrders = workerOrders.filter((order)=>[
                    'pending',
                    'in_progress'
                ].includes(order.status));
            return [
                {
                    title: t('my_active_orders'),
                    value: workerActiveOrders.length.toString(),
                    change: '+0%',
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__["Package"],
                    color: 'from-blue-400 to-blue-600'
                },
                {
                    title: t('my_completed_orders'),
                    value: workerCompletedOrders.length.toString(),
                    change: '+0%',
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"],
                    color: 'from-green-400 to-green-600'
                },
                {
                    title: t('my_total_orders'),
                    value: workerOrders.length.toString(),
                    change: '+0%',
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"],
                    color: 'from-purple-400 to-purple-600'
                }
            ];
        } else {
            // إحصائيات المدير - جميع البيانات
            return [
                {
                    title: t('active_orders'),
                    value: realStats.activeOrders.toString(),
                    change: '+0%',
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__["Package"],
                    color: 'from-blue-400 to-blue-600'
                },
                {
                    title: t('today_appointments'),
                    value: todayAppointments.toString(),
                    change: '+0',
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"],
                    color: 'from-green-400 to-green-600'
                },
                {
                    title: t('completed_orders'),
                    value: realStats.completedOrders.toString(),
                    change: '+0%',
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"],
                    color: 'from-purple-400 to-purple-600'
                },
                {
                    title: t('total_orders'),
                    value: realStats.totalOrders.toString(),
                    change: '+0%',
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"],
                    color: 'from-pink-400 to-pink-600'
                }
            ];
        }
    };
    const stats = getStatsForRole();
    // أحدث الطلبات (آخر 3 طلبات) - مفلترة حسب الدور
    const recentOrders = orders.filter((order)=>{
        // إذا كان المستخدم عامل، اعرض طلباته فقط
        if (user.role === 'worker') {
            return order.assignedWorker === user.id;
        }
        // إذا كان مدير، اعرض جميع الطلبات
        return true;
    }).sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()).slice(0, 3).map((order)=>({
            id: order.id,
            client: order.clientName,
            type: order.description,
            status: order.status,
            dueDate: order.dueDate
        }));
    const getStatusColor = (status)=>{
        const colors = {
            pending: 'bg-yellow-100 text-yellow-800',
            in_progress: 'bg-blue-100 text-blue-800',
            completed: 'bg-green-100 text-green-800',
            delivered: 'bg-purple-100 text-purple-800'
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    };
    const getStatusLabel = (status)=>{
        const labels = {
            pending: t('pending'),
            in_progress: t('in_progress'),
            completed: t('completed'),
            delivered: t('delivered')
        };
        return labels[status] || status;
    };
    const formatDate = (dateString)=>{
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };
    const getStatusInfo = (status)=>{
        const statusMap = {
            pending: {
                label: t('pending'),
                color: 'text-yellow-600',
                bgColor: 'bg-yellow-100'
            },
            in_progress: {
                label: t('in_progress'),
                color: 'text-blue-600',
                bgColor: 'bg-blue-100'
            },
            completed: {
                label: t('completed'),
                color: 'text-green-600',
                bgColor: 'bg-green-100'
            },
            delivered: {
                label: t('delivered'),
                color: 'text-purple-600',
                bgColor: 'bg-purple-100'
            },
            cancelled: {
                label: t('cancelled'),
                color: 'text-red-600',
                bgColor: 'bg-red-100'
            }
        };
        return statusMap[status] || statusMap.pending;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                id: "d53b53f71abf93f",
                children: "button[title*=تغيير\\ اللغة],button[title*=Change\\ Language]{visibility:visible!important;opacity:1!important;z-index:99999!important;flex-shrink:0!important;display:block!important;position:relative!important}@media (width>=1920px){button[title*=تغيير\\ اللغة],button[title*=Change\\ Language]{visibility:visible!important;opacity:1!important;display:block!important}}@media (width>=2560px){button[title*=تغيير\\ اللغة],button[title*=Change\\ Language]{visibility:visible!important;opacity:1!important;display:block!important}}@media (display-mode:fullscreen){button[title*=تغيير\\ اللغة],button[title*=Change\\ Language]{visibility:visible!important;opacity:1!important;display:block!important}}.language-btn-container{overflow:visible!important}"
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "jsx-d53b53f71abf93f" + " " + "min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                        className: "jsx-d53b53f71abf93f" + " " + "bg-white/80 backdrop-blur-md border-b border-pink-100 shadow-sm",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "jsx-d53b53f71abf93f" + " " + "w-full max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "jsx-d53b53f71abf93f" + " " + "hidden lg:flex items-center justify-between h-16",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "jsx-d53b53f71abf93f" + " " + "flex items-center space-x-4 space-x-reverse",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    href: "/",
                                                    className: "text-pink-600 hover:text-pink-700 transition-colors duration-300 group flex items-center space-x-2 space-x-reverse",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__["ArrowRight"], {
                                                            className: "w-5 h-5 group-hover:translate-x-1 transition-transform duration-300"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 240,
                                                            columnNumber: 17
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "jsx-d53b53f71abf93f" + " " + "text-sm font-medium",
                                                            children: t('homepage')
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 241,
                                                            columnNumber: 17
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 236,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "jsx-d53b53f71abf93f" + " " + "w-px h-6 bg-gray-300"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 243,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "jsx-d53b53f71abf93f" + " " + "max-w-md",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                            className: "jsx-d53b53f71abf93f" + " " + "text-xl xl:text-2xl font-bold text-gray-800 truncate",
                                                            children: [
                                                                t('welcome_back'),
                                                                "، ",
                                                                user.full_name || user.email
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 245,
                                                            columnNumber: 17
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "jsx-d53b53f71abf93f" + " " + "text-gray-600 text-sm",
                                                            children: user.role === 'admin' ? t('admin_dashboard') : t('worker_dashboard')
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 248,
                                                            columnNumber: 17
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 244,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "jsx-d53b53f71abf93f" + " " + "px-3 py-1 bg-gradient-to-r from-pink-100 to-rose-100 text-pink-700 rounded-full text-sm font-medium whitespace-nowrap",
                                                    children: user.role === 'admin' ? t('admin') : t('worker')
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 252,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 235,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "jsx-d53b53f71abf93f" + " " + "flex items-center space-x-4 space-x-reverse language-btn-container",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-d53b53f71abf93f" + " " + "flex items-center space-x-3 space-x-reverse language-btn-container",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "jsx-d53b53f71abf93f" + " " + "text-right max-w-xs",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "jsx-d53b53f71abf93f" + " " + "font-medium text-gray-800 truncate",
                                                                children: user.full_name
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 260,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "jsx-d53b53f71abf93f" + " " + "text-sm text-gray-600 truncate",
                                                                children: user.email
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 261,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 259,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        onClick: ()=>changeLanguage(language === 'ar' ? 'en' : 'ar'),
                                                        title: t('change_language'),
                                                        style: {
                                                            display: 'block !important',
                                                            visibility: 'visible !important',
                                                            position: 'relative',
                                                            zIndex: 99999,
                                                            minWidth: '70px',
                                                            flexShrink: 0,
                                                            opacity: 1
                                                        },
                                                        className: "jsx-d53b53f71abf93f" + " " + "px-3 py-1.5 text-sm text-gray-600 hover:text-pink-600 bg-gray-100 hover:bg-pink-50 rounded-full transition-all duration-300 font-medium min-w-[70px] text-center",
                                                        children: language === 'ar' ? 'English' : 'عربي'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 265,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        onClick: handleSignOut,
                                                        title: t('logout'),
                                                        className: "jsx-d53b53f71abf93f" + " " + "p-2 text-gray-600 hover:text-red-600 transition-colors duration-300",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$out$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LogOut$3e$__["LogOut"], {
                                                            className: "w-5 h-5"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 287,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 282,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 258,
                                                columnNumber: 15
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 257,
                                            columnNumber: 13
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                    lineNumber: 234,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "jsx-d53b53f71abf93f" + " " + "lg:hidden",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "jsx-d53b53f71abf93f" + " " + "flex items-center justify-between h-14 sm:h-16",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "jsx-d53b53f71abf93f" + " " + "flex items-center space-x-2 space-x-reverse min-w-0 flex-1 overflow-hidden",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            href: "/",
                                                            className: "text-pink-600 hover:text-pink-700 transition-colors duration-300 group flex items-center space-x-1 space-x-reverse flex-shrink-0",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__["ArrowRight"], {
                                                                    className: "w-4 h-4 sm:w-5 sm:h-5 group-hover:translate-x-1 transition-transform duration-300"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                                    lineNumber: 302,
                                                                    columnNumber: 19
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "jsx-d53b53f71abf93f" + " " + "text-xs sm:text-sm font-medium hidden sm:inline whitespace-nowrap",
                                                                    children: t('homepage')
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                                    lineNumber: 303,
                                                                    columnNumber: 19
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "jsx-d53b53f71abf93f" + " " + "text-xs font-medium sm:hidden whitespace-nowrap",
                                                                    children: t('home')
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                                    lineNumber: 304,
                                                                    columnNumber: 19
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 298,
                                                            columnNumber: 17
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "jsx-d53b53f71abf93f" + " " + "w-px h-4 sm:h-6 bg-gray-300 flex-shrink-0"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 306,
                                                            columnNumber: 17
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "jsx-d53b53f71abf93f" + " " + "min-w-0 flex-1 overflow-hidden",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                                className: "jsx-d53b53f71abf93f" + " " + "text-sm sm:text-lg md:text-xl font-bold text-gray-800 truncate",
                                                                children: [
                                                                    t('welcome_back'),
                                                                    "، ",
                                                                    user.full_name || user.email
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 308,
                                                                columnNumber: 19
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 307,
                                                            columnNumber: 17
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 297,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "jsx-d53b53f71abf93f" + " " + "flex items-center space-x-1 sm:space-x-2 space-x-reverse flex-shrink-0 min-w-0 language-btn-container",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "jsx-d53b53f71abf93f" + " " + "px-2 sm:px-3 py-1 bg-gradient-to-r from-pink-100 to-rose-100 text-pink-700 rounded-full text-xs sm:text-sm font-medium whitespace-nowrap",
                                                            children: t(user.role === 'admin' ? 'admin' : 'worker')
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 315,
                                                            columnNumber: 17
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            onClick: ()=>changeLanguage(language === 'ar' ? 'en' : 'ar'),
                                                            title: t('change_language'),
                                                            style: {
                                                                display: 'block !important',
                                                                visibility: 'visible !important',
                                                                position: 'relative',
                                                                zIndex: 99999,
                                                                minWidth: '60px',
                                                                flexShrink: 0,
                                                                opacity: 1
                                                            },
                                                            className: "jsx-d53b53f71abf93f" + " " + "px-2 sm:px-3 py-1 text-xs sm:text-sm text-gray-600 hover:text-pink-600 bg-gray-100 hover:bg-pink-50 rounded-full transition-all duration-300 flex-shrink-0 font-medium min-w-[60px] sm:min-w-[70px] text-center",
                                                            children: language === 'ar' ? 'English' : 'عربي'
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 318,
                                                            columnNumber: 17
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            onClick: handleSignOut,
                                                            title: t('logout'),
                                                            className: "jsx-d53b53f71abf93f" + " " + "p-1.5 sm:p-2 text-gray-600 hover:text-red-600 transition-colors duration-300 flex-shrink-0",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$log$2d$out$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LogOut$3e$__["LogOut"], {
                                                                className: "w-4 h-4 sm:w-5 sm:h-5"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 340,
                                                                columnNumber: 19
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 335,
                                                            columnNumber: 17
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 314,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 296,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "jsx-d53b53f71abf93f" + " " + "pb-3 sm:pb-4 border-t border-gray-100",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-d53b53f71abf93f" + " " + "pt-2 sm:pt-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "jsx-d53b53f71abf93f" + " " + "text-xs sm:text-sm text-gray-600 mb-1",
                                                        children: [
                                                            t('dashboard'),
                                                            " - ",
                                                            t(user.role === 'admin' ? 'admin' : 'worker')
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 348,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "jsx-d53b53f71abf93f" + " " + "flex items-center justify-between",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "jsx-d53b53f71abf93f" + " " + "min-w-0 flex-1",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "jsx-d53b53f71abf93f" + " " + "font-medium text-gray-800 text-sm sm:text-base truncate",
                                                                    children: user.full_name
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                                    lineNumber: 353,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "jsx-d53b53f71abf93f" + " " + "text-xs sm:text-sm text-gray-600 truncate",
                                                                    children: user.email
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                                    lineNumber: 354,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 352,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 351,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 347,
                                                columnNumber: 15
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 346,
                                            columnNumber: 13
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                    lineNumber: 294,
                                    columnNumber: 11
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/page.tsx",
                            lineNumber: 232,
                            columnNumber: 9
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/page.tsx",
                        lineNumber: 231,
                        columnNumber: 7
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-d53b53f71abf93f" + " " + "w-full max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                initial: {
                                    opacity: 0,
                                    y: 20
                                },
                                animate: {
                                    opacity: 1,
                                    y: 0
                                },
                                transition: {
                                    duration: 0.6
                                },
                                className: "mb-6 sm:mb-8",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "jsx-d53b53f71abf93f" + " " + "flex flex-col gap-4 sm:gap-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "jsx-d53b53f71abf93f" + " " + "text-center sm:text-right overflow-hidden",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                    className: "jsx-d53b53f71abf93f" + " " + "text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-gray-800 mb-2 sm:mb-3 break-words",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "jsx-d53b53f71abf93f" + " " + "block sm:hidden",
                                                            children: t('welcome_back')
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 375,
                                                            columnNumber: 17
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "jsx-d53b53f71abf93f" + " " + "hidden sm:inline",
                                                            children: [
                                                                t('welcome_back'),
                                                                "، "
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 376,
                                                            columnNumber: 17
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "jsx-d53b53f71abf93f" + " " + "text-pink-600 break-words",
                                                            children: user.full_name
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 377,
                                                            columnNumber: 17
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 374,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "jsx-d53b53f71abf93f" + " " + "text-sm sm:text-base md:text-lg text-gray-600 max-w-2xl mx-auto sm:mx-0 break-words",
                                                    children: t('overview_today')
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 379,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 373,
                                            columnNumber: 13
                                        }, this),
                                        user.role === 'admin' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "jsx-d53b53f71abf93f" + " " + "flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center sm:justify-start w-full",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    href: "/dashboard/add-order",
                                                    className: "btn-primary inline-flex items-center justify-center space-x-2 space-x-reverse px-4 sm:px-6 py-3 sm:py-4 group text-sm sm:text-base w-full sm:w-auto min-w-0 flex-shrink-0",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                                            className: "w-4 h-4 sm:w-5 sm:h-5 group-hover:scale-110 transition-transform duration-300 flex-shrink-0"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 391,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "jsx-d53b53f71abf93f" + " " + "whitespace-nowrap",
                                                            children: t('add_new_order')
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 392,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 387,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    href: "/book-appointment",
                                                    className: "btn-secondary inline-flex items-center justify-center space-x-2 space-x-reverse px-4 sm:px-6 py-3 sm:py-4 group text-sm sm:text-base w-full sm:w-auto min-w-0 flex-shrink-0",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                                                            className: "w-4 h-4 sm:w-5 sm:h-5 group-hover:scale-110 transition-transform duration-300 flex-shrink-0"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 399,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "jsx-d53b53f71abf93f" + " " + "whitespace-nowrap",
                                                            children: t('book_appointment')
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 400,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 395,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 386,
                                            columnNumber: 15
                                        }, this),
                                        user.role === 'worker' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "jsx-d53b53f71abf93f" + " " + "bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg sm:rounded-xl p-4 sm:p-6 text-center sm:text-right overflow-hidden",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    className: "jsx-d53b53f71abf93f" + " " + "text-lg sm:text-xl font-semibold text-blue-800 mb-2 break-words",
                                                    children: t('welcome_worker')
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 408,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "jsx-d53b53f71abf93f" + " " + "text-sm sm:text-base text-blue-600 break-words",
                                                    children: t('worker_description')
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 411,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 407,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                    lineNumber: 371,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/page.tsx",
                                lineNumber: 365,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                initial: {
                                    opacity: 0,
                                    y: 30
                                },
                                animate: {
                                    opacity: 1,
                                    y: 0
                                },
                                transition: {
                                    duration: 0.6,
                                    delay: 0.2
                                },
                                className: "hidden lg:grid lg:grid-cols-4 gap-6 mb-8",
                                children: stats.map((stat, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                        initial: {
                                            opacity: 0,
                                            scale: 0.8
                                        },
                                        animate: {
                                            opacity: 1,
                                            scale: 1
                                        },
                                        transition: {
                                            duration: 0.5,
                                            delay: 0.3 + index * 0.1
                                        },
                                        className: "bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-100 hover:shadow-lg transition-all duration-300",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-d53b53f71abf93f" + " " + "flex items-center justify-between mb-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "jsx-d53b53f71abf93f" + " " + `w-12 h-12 bg-gradient-to-r ${stat.color} rounded-lg flex items-center justify-center`,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(stat.icon, {
                                                            className: "w-6 h-6 text-white"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 438,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 437,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "jsx-d53b53f71abf93f" + " " + "text-sm font-medium text-green-600 flex items-center space-x-1",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                                                className: "w-4 h-4"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 441,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "jsx-d53b53f71abf93f",
                                                                children: stat.change
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 442,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 440,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 436,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "jsx-d53b53f71abf93f" + " " + "text-2xl font-bold text-gray-800 mb-1",
                                                children: stat.value
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 446,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "jsx-d53b53f71abf93f" + " " + "text-gray-600 text-sm",
                                                children: stat.title
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 447,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, index, true, {
                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                        lineNumber: 429,
                                        columnNumber: 13
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/page.tsx",
                                lineNumber: 422,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                initial: {
                                    opacity: 0,
                                    y: 30
                                },
                                animate: {
                                    opacity: 1,
                                    y: 0
                                },
                                transition: {
                                    duration: 0.6,
                                    delay: 0.2
                                },
                                className: "block lg:hidden mb-8",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "jsx-d53b53f71abf93f" + " " + "bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-pink-100",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "jsx-d53b53f71abf93f" + " " + "text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2 space-x-reverse",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__["BarChart3"], {
                                                    className: "w-5 h-5 text-pink-600"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 461,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "jsx-d53b53f71abf93f",
                                                    children: t('statistics')
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 462,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 460,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "jsx-d53b53f71abf93f" + " " + "grid grid-cols-3 gap-3",
                                            children: stats.map((stat, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                                    initial: {
                                                        opacity: 0,
                                                        scale: 0.8
                                                    },
                                                    animate: {
                                                        opacity: 1,
                                                        scale: 1
                                                    },
                                                    transition: {
                                                        duration: 0.5,
                                                        delay: 0.3 + index * 0.1
                                                    },
                                                    className: "text-center p-3 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border border-gray-200",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "jsx-d53b53f71abf93f" + " " + `w-8 h-8 bg-gradient-to-r ${stat.color} rounded-lg flex items-center justify-center mx-auto mb-2`,
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(stat.icon, {
                                                                className: "w-4 h-4 text-white"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 475,
                                                                columnNumber: 21
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 474,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                            className: "jsx-d53b53f71abf93f" + " " + "text-lg font-bold text-gray-800 mb-1",
                                                            children: stat.value
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 478,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "jsx-d53b53f71abf93f" + " " + "text-xs text-gray-600 leading-tight",
                                                            children: stat.title
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 479,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, index, true, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 467,
                                                    columnNumber: 17
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 465,
                                            columnNumber: 13
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                    lineNumber: 459,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/page.tsx",
                                lineNumber: 453,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-d53b53f71abf93f" + " " + "block lg:hidden space-y-8",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                        initial: {
                                            opacity: 0,
                                            y: 30
                                        },
                                        animate: {
                                            opacity: 1,
                                            y: 0
                                        },
                                        transition: {
                                            duration: 0.6,
                                            delay: 0.4
                                        },
                                        className: "bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-100",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "jsx-d53b53f71abf93f" + " " + "text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__["Package"], {
                                                        className: "w-5 h-5 text-pink-600"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 496,
                                                        columnNumber: 15
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "jsx-d53b53f71abf93f",
                                                        children: t('recent_orders')
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 497,
                                                        columnNumber: 15
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 495,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-d53b53f71abf93f" + " " + "space-y-4",
                                                children: [
                                                    recentOrders.map((order, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                                            initial: {
                                                                opacity: 0,
                                                                y: 20
                                                            },
                                                            animate: {
                                                                opacity: 1,
                                                                y: 0
                                                            },
                                                            transition: {
                                                                duration: 0.4,
                                                                delay: 0.5 + index * 0.1
                                                            },
                                                            className: "flex items-center justify-between p-4 bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg border border-pink-200",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "jsx-d53b53f71abf93f",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                            className: "jsx-d53b53f71abf93f" + " " + "font-medium text-gray-800",
                                                                            children: order.client
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                                            lineNumber: 510,
                                                                            columnNumber: 21
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "jsx-d53b53f71abf93f" + " " + "text-sm text-gray-600",
                                                                            children: order.type
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                                            lineNumber: 511,
                                                                            columnNumber: 21
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "jsx-d53b53f71abf93f" + " " + "text-xs text-gray-500",
                                                                            children: [
                                                                                "#",
                                                                                order.id
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                                            lineNumber: 512,
                                                                            columnNumber: 21
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                                    lineNumber: 509,
                                                                    columnNumber: 19
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "jsx-d53b53f71abf93f" + " " + "text-right flex items-center space-x-2 space-x-reverse",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "jsx-d53b53f71abf93f",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "jsx-d53b53f71abf93f" + " " + `px-2 py-1 rounded-full text-xs font-medium ${getStatusInfo(order.status).bgColor} ${getStatusInfo(order.status).color}`,
                                                                                children: getStatusInfo(order.status).label
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                                lineNumber: 517,
                                                                                columnNumber: 23
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                className: "jsx-d53b53f71abf93f" + " " + "text-xs text-gray-500 mt-1",
                                                                                children: formatDate(order.dueDate)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                                lineNumber: 520,
                                                                                columnNumber: 23
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                                        lineNumber: 516,
                                                                        columnNumber: 21
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                                    lineNumber: 515,
                                                                    columnNumber: 19
                                                                }, this)
                                                            ]
                                                        }, order.id, true, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 502,
                                                            columnNumber: 17
                                                        }, this)),
                                                    recentOrders.length === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "jsx-d53b53f71abf93f" + " " + "text-center py-8",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__["Package"], {
                                                                className: "w-12 h-12 text-gray-400 mx-auto mb-4"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 530,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "jsx-d53b53f71abf93f" + " " + "text-gray-500",
                                                                children: t('no_orders_found')
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 531,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 529,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 500,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/dashboard/orders",
                                                className: "w-full mt-4 btn-secondary py-2 text-sm inline-flex items-center justify-center",
                                                children: [
                                                    t('view_all'),
                                                    " ",
                                                    t('orders')
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 536,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                        lineNumber: 489,
                                        columnNumber: 11
                                    }, this),
                                    user.role === 'admin' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                        initial: {
                                            opacity: 0,
                                            y: 30
                                        },
                                        animate: {
                                            opacity: 1,
                                            y: 0
                                        },
                                        transition: {
                                            duration: 0.6,
                                            delay: 0.6
                                        },
                                        className: "bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-100",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "jsx-d53b53f71abf93f" + " " + "text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                                                        className: "w-5 h-5 text-pink-600"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 555,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "jsx-d53b53f71abf93f",
                                                        children: t('quick_actions')
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 556,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 554,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-d53b53f71abf93f" + " " + "grid gap-4 grid-cols-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        href: "/dashboard/add-order",
                                                        className: "p-4 bg-gradient-to-r from-pink-50 to-pink-100 rounded-lg border border-pink-200 hover:shadow-md transition-all duration-300 text-center block",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                                                className: "w-6 h-6 text-pink-600 mx-auto mb-2"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 564,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "jsx-d53b53f71abf93f" + " " + "text-sm font-medium text-pink-800",
                                                                children: t('add_new_order')
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 565,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 560,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        href: "/dashboard/workers",
                                                        className: "p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200 hover:shadow-md transition-all duration-300 text-center block",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"], {
                                                                className: "w-6 h-6 text-blue-600 mx-auto mb-2"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 572,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "jsx-d53b53f71abf93f" + " " + "text-sm font-medium text-blue-800",
                                                                children: t('worker_management')
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 573,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 568,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        href: "/dashboard/appointments",
                                                        className: "p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border border-purple-200 hover:shadow-md transition-all duration-300 text-center block col-span-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                                                                className: "w-6 h-6 text-purple-600 mx-auto mb-2"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 580,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "jsx-d53b53f71abf93f" + " " + "text-sm font-medium text-purple-800",
                                                                children: t('appointments')
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 581,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 576,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 559,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-d53b53f71abf93f" + " " + "mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: "jsx-d53b53f71abf93f" + " " + "font-medium text-gray-800 mb-2",
                                                        children: t('reminder')
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 586,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "jsx-d53b53f71abf93f" + " " + "text-sm text-gray-600",
                                                        children: [
                                                            t('you_have'),
                                                            " ",
                                                            todayAppointments,
                                                            " ",
                                                            t('today_appointments_reminder'),
                                                            " ",
                                                            t('and'),
                                                            " ",
                                                            realStats.activeOrders,
                                                            " ",
                                                            t('orders_need_follow')
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 587,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 585,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                        lineNumber: 548,
                                        columnNumber: 13
                                    }, this),
                                    user.role === 'admin' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                        initial: {
                                            opacity: 0,
                                            y: 30
                                        },
                                        animate: {
                                            opacity: 1,
                                            y: 0
                                        },
                                        transition: {
                                            duration: 0.6,
                                            delay: 0.8
                                        },
                                        className: "bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-100",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "jsx-d53b53f71abf93f" + " " + "text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__["BarChart3"], {
                                                        className: "w-5 h-5 text-green-600"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 605,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "jsx-d53b53f71abf93f",
                                                        children: t('reports')
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 606,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 604,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/dashboard/reports",
                                                className: "w-full p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200 hover:shadow-md transition-all duration-300 text-center block",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__["BarChart3"], {
                                                        className: "w-8 h-8 text-green-600 mx-auto mb-2"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 613,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "jsx-d53b53f71abf93f" + " " + "text-base font-medium text-green-800",
                                                        children: t('detailed_reports')
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 614,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 609,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                        lineNumber: 598,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/page.tsx",
                                lineNumber: 487,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-d53b53f71abf93f" + " " + "hidden lg:grid lg:grid-cols-2 gap-8",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                        initial: {
                                            opacity: 0,
                                            x: -30
                                        },
                                        animate: {
                                            opacity: 1,
                                            x: 0
                                        },
                                        transition: {
                                            duration: 0.6,
                                            delay: 0.4
                                        },
                                        className: "bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-100",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "jsx-d53b53f71abf93f" + " " + "text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__["Package"], {
                                                        className: "w-5 h-5 text-pink-600"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 630,
                                                        columnNumber: 15
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "jsx-d53b53f71abf93f",
                                                        children: t('recent_orders')
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 631,
                                                        columnNumber: 15
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 629,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-d53b53f71abf93f" + " " + "space-y-4",
                                                children: recentOrders.map((order, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                                        initial: {
                                                            opacity: 0,
                                                            y: 20
                                                        },
                                                        animate: {
                                                            opacity: 1,
                                                            y: 0
                                                        },
                                                        transition: {
                                                            duration: 0.4,
                                                            delay: 0.6 + index * 0.1
                                                        },
                                                        className: "flex items-center justify-between p-4 bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg border border-pink-200",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "jsx-d53b53f71abf93f",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                        className: "jsx-d53b53f71abf93f" + " " + "font-medium text-gray-800",
                                                                        children: order.client
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                                        lineNumber: 644,
                                                                        columnNumber: 21
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        className: "jsx-d53b53f71abf93f" + " " + "text-sm text-gray-600",
                                                                        children: order.type
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                                        lineNumber: 645,
                                                                        columnNumber: 21
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        className: "jsx-d53b53f71abf93f" + " " + "text-xs text-gray-500",
                                                                        children: [
                                                                            "#",
                                                                            order.id
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                                        lineNumber: 646,
                                                                        columnNumber: 21
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 643,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "jsx-d53b53f71abf93f" + " " + "text-right flex items-center space-x-2 space-x-reverse",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "jsx-d53b53f71abf93f",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "jsx-d53b53f71abf93f" + " " + `px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`,
                                                                            children: getStatusLabel(order.status)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                                            lineNumber: 651,
                                                                            columnNumber: 23
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "jsx-d53b53f71abf93f" + " " + "text-xs text-gray-500 mt-1 flex items-center space-x-1 space-x-reverse",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                                                                    className: "w-3 h-3"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                                                    lineNumber: 655,
                                                                                    columnNumber: 25
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "jsx-d53b53f71abf93f",
                                                                                    children: order.dueDate
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                                                    lineNumber: 656,
                                                                                    columnNumber: 25
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                                            lineNumber: 654,
                                                                            columnNumber: 23
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                                    lineNumber: 650,
                                                                    columnNumber: 21
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 649,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, order.id, true, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 636,
                                                        columnNumber: 17
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 634,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/dashboard/orders",
                                                className: "w-full mt-4 btn-secondary py-2 text-sm inline-flex items-center justify-center",
                                                children: [
                                                    t('view_all'),
                                                    " ",
                                                    t('orders')
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 666,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                        lineNumber: 623,
                                        columnNumber: 11
                                    }, this),
                                    user.role === 'admin' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                        initial: {
                                            opacity: 0,
                                            x: 30
                                        },
                                        animate: {
                                            opacity: 1,
                                            x: 0
                                        },
                                        transition: {
                                            duration: 0.6,
                                            delay: 0.6
                                        },
                                        className: "bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-100",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "jsx-d53b53f71abf93f" + " " + "text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                                                        className: "w-5 h-5 text-pink-600"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 683,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "jsx-d53b53f71abf93f",
                                                        children: t('quick_actions')
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 684,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 682,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-d53b53f71abf93f" + " " + "grid gap-4 grid-cols-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        href: "/dashboard/add-order",
                                                        className: "p-4 bg-gradient-to-r from-pink-50 to-pink-100 rounded-lg border border-pink-200 hover:shadow-md transition-all duration-300 text-center block",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                                                className: "w-6 h-6 text-pink-600 mx-auto mb-2"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 692,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "jsx-d53b53f71abf93f" + " " + "text-sm font-medium text-pink-800",
                                                                children: t('add_new_order')
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 693,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 688,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        href: "/dashboard/workers",
                                                        className: "p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200 hover:shadow-md transition-all duration-300 text-center block",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"], {
                                                                className: "w-6 h-6 text-blue-600 mx-auto mb-2"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 700,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "jsx-d53b53f71abf93f" + " " + "text-sm font-medium text-blue-800",
                                                                children: t('worker_management')
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 701,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 696,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        href: "/dashboard/reports",
                                                        className: "p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200 hover:shadow-md transition-all duration-300 text-center block",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__["BarChart3"], {
                                                                className: "w-6 h-6 text-green-600 mx-auto mb-2"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 708,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "jsx-d53b53f71abf93f" + " " + "text-sm font-medium text-green-800",
                                                                children: t('reports')
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 709,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 704,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        href: "/dashboard/appointments",
                                                        className: "p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border border-purple-200 hover:shadow-md transition-all duration-300 text-center block",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                                                                className: "w-6 h-6 text-purple-600 mx-auto mb-2"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 716,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "jsx-d53b53f71abf93f" + " " + "text-sm font-medium text-purple-800",
                                                                children: t('appointments')
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 717,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 712,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 687,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-d53b53f71abf93f" + " " + "mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: "jsx-d53b53f71abf93f" + " " + "font-medium text-gray-800 mb-2",
                                                        children: t('reminder')
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 722,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "jsx-d53b53f71abf93f" + " " + "text-sm text-gray-600",
                                                        children: [
                                                            t('you_have'),
                                                            " ",
                                                            todayAppointments,
                                                            " ",
                                                            t('today_appointments_reminder'),
                                                            " ",
                                                            t('and'),
                                                            " ",
                                                            realStats.activeOrders,
                                                            " ",
                                                            t('orders_need_follow')
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 723,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 721,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                        lineNumber: 676,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/page.tsx",
                                lineNumber: 621,
                                columnNumber: 9
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/page.tsx",
                        lineNumber: 363,
                        columnNumber: 7
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/page.tsx",
                lineNumber: 229,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
_s(DashboardContent, "ZM+LLRuzjVsxsRuyAC8tKdar6Vo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$authStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$dataStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDataStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useTranslation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = DashboardContent;
function DashboardPage() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ProtectedRoute$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DashboardContent, {}, void 0, false, {
            fileName: "[project]/src/app/dashboard/page.tsx",
            lineNumber: 739,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/dashboard/page.tsx",
        lineNumber: 738,
        columnNumber: 5
    }, this);
}
_c1 = DashboardPage;
var _c, _c1;
__turbopack_context__.k.register(_c, "DashboardContent");
__turbopack_context__.k.register(_c1, "DashboardPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_fffc5453._.js.map