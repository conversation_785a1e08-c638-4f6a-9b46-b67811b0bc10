(()=>{var e={};e.id=161,e.ids=[161],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31829:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(65239),a=t(48088),n=t(88170),l=t.n(n),i=t(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(r,o);let d={children:["",{children:["dashboard",{children:["workers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,63071)),"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\workers\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\workers\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/workers/page",pathname:"/dashboard/workers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33873:e=>{"use strict";e.exports=require("path")},34161:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>k});var s=t(60687),a=t(43210),n=t(26001),l=t(16189),i=t(85814),o=t.n(i),d=t(99720),c=t(34270),p=t(99182),m=t(70334),u=t(96474),x=t(99270),g=t(5336),h=t(35071),b=t(41312),y=t(58869),f=t(41550),v=t(48340),j=t(63143),w=t(88233);function k(){let{user:e}=(0,d.n)(),{workers:r,addWorker:t,updateWorker:i,deleteWorker:k,orders:N}=(0,c.D)(),{t:_,isArabic:A}=(0,p.B)();(0,l.useRouter)();let[S,C]=(0,a.useState)(""),[q,P]=(0,a.useState)(!1),[L,D]=(0,a.useState)({email:"",password:"",full_name:"",phone:"",specialty:""}),[O,I]=(0,a.useState)(null),[M,z]=(0,a.useState)(!1),[W,E]=(0,a.useState)(!1),[H,U]=(0,a.useState)(null),G=async e=>{if(e.preventDefault(),!L.email||!L.password||!L.full_name||!L.phone||!L.specialty)return void U({type:"error",text:_("fill_required_fields")});E(!0),U(null);try{await new Promise(e=>setTimeout(e,1e3)),t({email:L.email,password:L.password,full_name:L.full_name,phone:L.phone,specialty:L.specialty,is_active:!0}),U({type:"success",text:_("worker_added_success")}),D({email:"",password:"",full_name:"",phone:"",specialty:""}),P(!1)}catch(e){U({type:"error",text:_("error_adding_worker")})}finally{E(!1)}},R=e=>{I({...e,password:""}),z(!0)},T=async e=>{if(e.preventDefault(),!O.email||!O.full_name||!O.phone||!O.specialty)return void U({type:"error",text:_("fill_required_fields")});E(!0),U(null);try{await new Promise(e=>setTimeout(e,1e3));let e={email:O.email,full_name:O.full_name,phone:O.phone,specialty:O.specialty,is_active:O.is_active};O.password&&(e.password=O.password),i(O.id,e),U({type:"success",text:_("worker_updated_success")}),z(!1),I(null)}catch(e){U({type:"error",text:_("error_updating_worker")})}finally{E(!1)}},Y=e=>{confirm(_("confirm_delete_worker"))&&(k(e),U({type:"success",text:_("worker_deleted_success")}))},$=(e,r)=>{i(e,{is_active:!r}),U({type:"success",text:r?_("worker_deactivated"):_("worker_activated")})},Z=e=>N.filter(r=>r.assignedWorker===e&&"completed"===r.status).length,B=e=>new Date(e).toLocaleDateString("ar-US",{year:"numeric",month:"long",day:"numeric"}),K=r.filter(e=>e.full_name.toLowerCase().includes(S.toLowerCase())||e.email.toLowerCase().includes(S.toLowerCase())||e.specialty.toLowerCase().includes(S.toLowerCase()));return e&&"admin"===e.role?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-8",children:(0,s.jsxs)(o(),{href:"/dashboard",className:"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300",children:[(0,s.jsx)(m.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:_("back_to_dashboard")})]})}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold mb-2",children:(0,s.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent",children:_("workers_management")})}),(0,s.jsx)("p",{className:"text-lg text-gray-600",children:_("view_manage_team")})]}),(0,s.jsxs)("button",{onClick:()=>P(!0),className:"btn-primary inline-flex items-center justify-center space-x-2 space-x-reverse px-6 py-3 group",children:[(0,s.jsx)(u.A,{className:"w-5 h-5 group-hover:scale-110 transition-transform duration-300"}),(0,s.jsx)("span",{children:_("add_new_worker")})]})]}),(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 mb-8",children:(0,s.jsxs)("div",{className:"relative max-w-md",children:[(0,s.jsx)(x.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,s.jsx)("input",{type:"text",value:S,onChange:e=>C(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:_("search_workers_placeholder")})]})}),H&&(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:`mb-6 p-4 rounded-lg flex items-center space-x-3 space-x-reverse ${"success"===H.type?"bg-green-50 text-green-800 border border-green-200":"bg-red-50 text-red-800 border border-red-200"}`,children:["success"===H.type?(0,s.jsx)(g.A,{className:"w-5 h-5 text-green-600"}):(0,s.jsx)(h.A,{className:"w-5 h-5 text-red-600"}),(0,s.jsx)("span",{children:H.text})]}),q&&(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:_("add_new_worker_form")}),(0,s.jsx)("button",{onClick:()=>P(!1),className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)(h.A,{className:"w-6 h-6"})})]}),(0,s.jsxs)("form",{onSubmit:G,className:"grid md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("full_name_required")}),(0,s.jsx)("input",{type:"text",value:L.full_name,onChange:e=>D(r=>({...r,full_name:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("enter_full_name"),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("email_required")}),(0,s.jsx)("input",{type:"email",value:L.email,onChange:e=>D(r=>({...r,email:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("enter_email"),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("password_required")}),(0,s.jsx)("input",{type:"password",value:L.password,onChange:e=>D(r=>({...r,password:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("enter_password"),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("phone_required")}),(0,s.jsx)("input",{type:"tel",value:L.phone,onChange:e=>D(r=>({...r,phone:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("enter_phone"),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("specialty_required")}),(0,s.jsx)("input",{type:"text",value:L.specialty,onChange:e=>D(r=>({...r,specialty:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("specialty_example"),required:!0})]}),(0,s.jsxs)("div",{className:"md:col-span-2 flex gap-4 pt-4",children:[(0,s.jsx)("button",{type:"submit",disabled:W,className:"btn-primary py-3 px-6 disabled:opacity-50 disabled:cursor-not-allowed",children:W?_("adding"):_("add_worker")}),(0,s.jsx)("button",{type:"button",onClick:()=>P(!1),className:"btn-secondary py-3 px-6",children:_("cancel")})]})]})]}),M&&O&&(0,s.jsxs)(n.P.div,{initial:{opacity:0},animate:{opacity:1},className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm",onClick:()=>z(!1)}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},className:"relative bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,s.jsx)("div",{className:"sticky top-0 bg-white border-b border-gray-200 p-6 rounded-t-2xl",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:_("edit_worker")}),(0,s.jsx)("button",{onClick:()=>z(!1),className:"text-gray-400 hover:text-gray-600",children:(0,s.jsx)(h.A,{className:"w-6 h-6"})})]})}),(0,s.jsxs)("form",{onSubmit:T,className:"p-6 space-y-4",children:[(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("full_name_required")}),(0,s.jsx)("input",{type:"text",value:O.full_name,onChange:e=>I(r=>({...r,full_name:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("email_required")}),(0,s.jsx)("input",{type:"email",value:O.email,onChange:e=>I(r=>({...r,email:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("new_password")}),(0,s.jsx)("input",{type:"password",value:O.password,onChange:e=>I(r=>({...r,password:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("leave_empty_no_change")})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("phone_required")}),(0,s.jsx)("input",{type:"tel",value:O.phone,onChange:e=>I(r=>({...r,phone:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("specialty_required")}),(0,s.jsx)("input",{type:"text",value:O.specialty,onChange:e=>I(r=>({...r,specialty:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("status")}),(0,s.jsxs)("select",{value:O.is_active?"active":"inactive",onChange:e=>I(r=>({...r,is_active:"active"===e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",children:[(0,s.jsx)("option",{value:"active",children:_("active")}),(0,s.jsx)("option",{value:"inactive",children:_("inactive")})]})]})]}),(0,s.jsxs)("div",{className:"flex gap-4 pt-4",children:[(0,s.jsx)("button",{type:"submit",disabled:W,className:"btn-primary py-3 px-6 disabled:opacity-50 disabled:cursor-not-allowed",children:W?_("saving"):_("save_changes")}),(0,s.jsx)("button",{type:"button",onClick:()=>z(!1),className:"btn-secondary py-3 px-6",children:_("cancel")})]})]})]})]}),(0,s.jsx)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"grid lg:grid-cols-2 gap-6 mb-12",children:0===K.length?(0,s.jsxs)("div",{className:"lg:col-span-2 text-center py-12 bg-white/80 backdrop-blur-sm rounded-2xl border border-pink-100",children:[(0,s.jsx)(b.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-medium text-gray-600 mb-2",children:_("no_workers")}),(0,s.jsx)("p",{className:"text-gray-500",children:_("no_workers_found")})]}):K.map((e,r)=>(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*r},className:`bg-white/80 backdrop-blur-sm rounded-2xl p-6 border transition-all duration-300 hover:shadow-lg ${e.is_active?"border-pink-100":"border-gray-200 opacity-75"}`,children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full flex items-center justify-center",children:(0,s.jsx)(y.A,{className:"w-6 h-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-gray-800",children:e.full_name}),(0,s.jsx)("p",{className:"text-sm text-pink-600 font-medium",children:e.specialty})]})]}),(0,s.jsx)("div",{className:"flex items-center space-x-2 space-x-reverse",children:e.is_active?(0,s.jsxs)("span",{className:"px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium flex items-center space-x-1 space-x-reverse",children:[(0,s.jsx)(g.A,{className:"w-3 h-3"}),(0,s.jsx)("span",{children:_("active")})]}):(0,s.jsxs)("span",{className:"px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium flex items-center space-x-1 space-x-reverse",children:[(0,s.jsx)(h.A,{className:"w-3 h-3"}),(0,s.jsx)("span",{children:_("inactive")})]})})]}),(0,s.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse text-sm text-gray-600",children:[(0,s.jsx)(f.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.email})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse text-sm text-gray-600",children:[(0,s.jsx)(v.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.phone})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 gap-4 mb-6",children:(0,s.jsxs)("div",{className:"text-center p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg",children:[(0,s.jsx)("div",{className:"text-lg font-bold text-green-600",children:Z(e.id)}),(0,s.jsx)("div",{className:"text-xs text-gray-600",children:_("completed_orders")})]})}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 mb-4",children:[_("joined_on")," ",B(e.createdAt)]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("button",{onClick:()=>R(e),className:"flex-1 btn-secondary py-2 text-sm inline-flex items-center justify-center space-x-1 space-x-reverse",children:[(0,s.jsx)(j.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:_("edit")})]}),(0,s.jsx)("button",{onClick:()=>$(e.id,e.is_active),className:`px-3 py-2 text-sm rounded-lg transition-all duration-300 ${e.is_active?"bg-red-100 text-red-700 hover:bg-red-200":"bg-green-100 text-green-700 hover:bg-green-200"}`,children:e.is_active?(0,s.jsx)(h.A,{className:"w-4 h-4"}):(0,s.jsx)(g.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{onClick:()=>Y(e.id),className:"px-3 py-2 text-red-600 hover:text-red-700 border border-red-200 rounded-lg hover:bg-red-50 transition-all duration-300",children:(0,s.jsx)(w.A,{className:"w-4 h-4"})})]})]},e.id))}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"grid grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:r.length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:_("total_workers")})]}),(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-1",children:r.filter(e=>e.is_active).length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:_("active_workers")})]}),(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-purple-600 mb-1",children:r.reduce((e,r)=>e+Z(r.id),0)}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:_("total_completed_orders")})]}),(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-600 mb-1",children:r.filter(e=>e.is_active).length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:_("active_workers")})]})]})]})}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:_("checking_permissions")})]})})}},34270:(e,r,t)=>{"use strict";t.d(r,{D:()=>l});var s=t(26787),a=t(59350);let n=()=>Date.now().toString(36)+Math.random().toString(36).substr(2),l=(0,s.v)()((0,a.Zr)((e,r)=>({appointments:[],orders:[],workers:[],isLoading:!1,error:null,addAppointment:r=>{let t={...r,id:n(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({appointments:[...e.appointments,t],error:null})),console.log("✅ تم إضافة موعد جديد:",t)},updateAppointment:(r,t)=>{e(e=>({appointments:e.appointments.map(e=>e.id===r?{...e,...t,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الموعد:",r)},deleteAppointment:r=>{e(e=>({appointments:e.appointments.filter(e=>e.id!==r),error:null})),console.log("✅ تم حذف الموعد:",r)},getAppointment:e=>r().appointments.find(r=>r.id===e),addOrder:r=>{let t={...r,id:n(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({orders:[...e.orders,t],error:null})),console.log("✅ تم إضافة طلب جديد:",t)},updateOrder:(r,t)=>{e(e=>({orders:e.orders.map(e=>e.id===r?{...e,...t,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الطلب:",r)},deleteOrder:r=>{e(e=>({orders:e.orders.filter(e=>e.id!==r),error:null})),console.log("✅ تم حذف الطلب:",r)},getOrder:e=>r().orders.find(r=>r.id===e),addWorker:r=>{let t={...r,id:n(),role:"worker",is_active:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({workers:[...e.workers,t],error:null})),console.log("✅ تم إضافة عامل جديد:",t)},updateWorker:(r,t)=>{e(e=>({workers:e.workers.map(e=>e.id===r?{...e,...t,updatedAt:new Date().toISOString()}:e),error:null})),t.email||t.password||t.full_name,console.log("✅ تم تحديث العامل:",r)},deleteWorker:r=>{e(e=>({workers:e.workers.filter(e=>e.id!==r),error:null})),console.log("✅ تم حذف العامل:",r)},getWorker:e=>r().workers.find(r=>r.id===e),startOrderWork:(r,t)=>{e(e=>({orders:e.orders.map(e=>e.id===r&&e.assignedWorker===t?{...e,status:"in_progress",updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم بدء العمل في الطلب:",r)},completeOrder:(r,t,s=[])=>{e(e=>({orders:e.orders.map(e=>e.id===r&&e.assignedWorker===t?{...e,status:"completed",completedImages:s.length>0?s:void 0,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم إنهاء الطلب:",r)},clearError:()=>{e({error:null})},loadData:()=>{e({isLoading:!0}),e({isLoading:!1})},getStats:()=>{let e=r();return{totalAppointments:e.appointments.length,totalOrders:e.orders.length,totalWorkers:e.workers.length,pendingAppointments:e.appointments.filter(e=>"pending"===e.status).length,activeOrders:e.orders.filter(e=>["pending","in_progress"].includes(e.status)).length,completedOrders:e.orders.filter(e=>"completed"===e.status).length,totalRevenue:e.orders.filter(e=>"completed"===e.status).reduce((e,r)=>e+r.price,0)}}}),{name:"yasmin-data-storage",partialize:e=>({appointments:e.appointments,orders:e.orders,workers:e.workers})}))},35071:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},38023:(e,r,t)=>{Promise.resolve().then(t.bind(t,63071))},41312:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41550:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},48340:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63071:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\workers\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\workers\\page.tsx","default")},63143:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},68287:(e,r,t)=>{Promise.resolve().then(t.bind(t,34161))},79551:e=>{"use strict";e.exports=require("url")},99270:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},99720:(e,r,t)=>{"use strict";t.d(r,{n:()=>l});var s=t(26787),a=t(59350);let n=()=>[],l=(0,s.v)()((0,a.Zr)((e,r)=>({user:null,isLoading:!1,error:null,signIn:async(r,t)=>{e({isLoading:!0,error:null});try{console.log("\uD83D\uDD10 بدء عملية تسجيل الدخول...",{email:r}),await new Promise(e=>setTimeout(e,1500));let s=n().find(e=>e.email.toLowerCase()===r.toLowerCase()&&e.password===t);if(!s)return console.log("❌ بيانات تسجيل الدخول غير صحيحة"),e({error:"بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور.",isLoading:!1}),!1;{console.log("✅ تم العثور على المستخدم:",s.full_name);let r={id:s.id,email:s.email,full_name:s.full_name,role:s.role,is_active:s.is_active,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),token:`demo-token-${s.id}-${Date.now()}`};return e({user:r,isLoading:!1,error:null}),console.log("\uD83C\uDF89 تم تسجيل الدخول بنجاح!"),!0}}catch(r){return console.error("\uD83D\uDCA5 خطأ في تسجيل الدخول:",r),e({error:"حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.",isLoading:!1}),!1}},signOut:async()=>{e({isLoading:!0});try{await new Promise(e=>setTimeout(e,500)),e({user:null,isLoading:!1,error:null})}catch(r){console.error("خطأ في تسجيل الخروج:",r),e({isLoading:!1,error:"خطأ في تسجيل الخروج"})}},setUser:r=>{e({user:r})},clearError:()=>{e({error:null})},checkAuth:async()=>{e({isLoading:!0});try{e({user:null,isLoading:!1})}catch(r){console.error("خطأ في التحقق من المصادقة:",r),e({user:null,isLoading:!1})}},isAuthenticated:()=>{let e=r();return null!==e.user&&e.user.is_active}}),{name:"yasmin-auth-storage",partialize:e=>({user:e.user})}))}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,507,146,814,267,59],()=>t(31829));module.exports=s})();