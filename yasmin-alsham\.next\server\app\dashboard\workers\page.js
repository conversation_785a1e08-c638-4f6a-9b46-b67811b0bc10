(()=>{var e={};e.id=161,e.ids=[161],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34161:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>N});var t=s(60687),a=s(43210),i=s(26001),l=s(16189),n=s(85814),d=s.n(n),o=s(99720),c=s(34270),p=s(99182),x=s(70334),m=s(96474),u=s(99270),h=s(5336),b=s(35071),g=s(41312),y=s(58869),f=s(41550),v=s(48340),j=s(63143),w=s(88233);function N(){let{user:e}=(0,o.n)(),{workers:r,addWorker:s,updateWorker:n,deleteWorker:N,orders:k}=(0,c.D)(),{t:_,isArabic:A}=(0,p.B)();(0,l.useRouter)();let[C,q]=(0,a.useState)(""),[P,S]=(0,a.useState)(!1),[M,L]=(0,a.useState)({email:"",password:"",full_name:"",phone:"",specialty:""}),[D,z]=(0,a.useState)(null),[H,G]=(0,a.useState)(!1),[I,U]=(0,a.useState)(!1),[E,Y]=(0,a.useState)(null),R=async e=>{if(e.preventDefault(),!M.email||!M.password||!M.full_name||!M.phone||!M.specialty)return void Y({type:"error",text:_("fill_required_fields")});U(!0),Y(null);try{await new Promise(e=>setTimeout(e,1e3)),s({email:M.email,password:M.password,full_name:M.full_name,phone:M.phone,specialty:M.specialty,is_active:!0}),Y({type:"success",text:_("worker_added_success")}),L({email:"",password:"",full_name:"",phone:"",specialty:""}),S(!1)}catch(e){Y({type:"error",text:_("error_adding_worker")})}finally{U(!1)}},T=e=>{z({...e,password:""}),G(!0)},$=async e=>{if(e.preventDefault(),!D||!D.email||!D.full_name||!D.phone||!D.specialty)return void Y({type:"error",text:_("fill_required_fields")});U(!0),Y(null);try{await new Promise(e=>setTimeout(e,1e3));let e={email:D.email,full_name:D.full_name,phone:D.phone,specialty:D.specialty,is_active:D.is_active};D.password&&(e.password=D.password),n(D.id,e),Y({type:"success",text:_("worker_updated_success")}),G(!1),z(null)}catch(e){Y({type:"error",text:_("error_updating_worker")})}finally{U(!1)}},B=e=>{confirm(_("confirm_delete_worker"))&&(N(e),Y({type:"success",text:_("worker_deleted_success")}))},K=(e,r)=>{n(e,{is_active:!r}),Y({type:"success",text:r?_("worker_deactivated"):_("worker_activated")})},O=e=>k.filter(r=>r.assignedWorker===e&&"completed"===r.status).length,W=e=>new Date(e).toLocaleDateString("ar-US",{year:"numeric",month:"long",day:"numeric"}),X=r.filter(e=>e.full_name.toLowerCase().includes(C.toLowerCase())||e.email.toLowerCase().includes(C.toLowerCase())||e.specialty.toLowerCase().includes(C.toLowerCase()));return e&&"admin"===e.role?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-8",children:(0,t.jsxs)(d(),{href:"/dashboard",className:"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300",children:[(0,t.jsx)(x.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:_("back_to_dashboard")})]})}),(0,t.jsxs)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold mb-2",children:(0,t.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent",children:_("workers_management")})}),(0,t.jsx)("p",{className:"text-lg text-gray-600",children:_("view_manage_team")})]}),(0,t.jsxs)("button",{onClick:()=>S(!0),className:"btn-primary inline-flex items-center justify-center space-x-2 space-x-reverse px-6 py-3 group",children:[(0,t.jsx)(m.A,{className:"w-5 h-5 group-hover:scale-110 transition-transform duration-300"}),(0,t.jsx)("span",{children:_("add_new_worker")})]})]}),(0,t.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 mb-8",children:(0,t.jsxs)("div",{className:"relative max-w-md",children:[(0,t.jsx)(u.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,t.jsx)("input",{type:"text",value:C,onChange:e=>q(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:_("search_workers_placeholder")})]})}),E&&(0,t.jsxs)(i.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:`mb-6 p-4 rounded-lg flex items-center space-x-3 space-x-reverse ${"success"===E.type?"bg-green-50 text-green-800 border border-green-200":"bg-red-50 text-red-800 border border-red-200"}`,children:["success"===E.type?(0,t.jsx)(h.A,{className:"w-5 h-5 text-green-600"}):(0,t.jsx)(b.A,{className:"w-5 h-5 text-red-600"}),(0,t.jsx)("span",{children:E.text})]}),P&&(0,t.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:_("add_new_worker_form")}),(0,t.jsx)("button",{onClick:()=>S(!1),className:"text-gray-500 hover:text-gray-700",children:(0,t.jsx)(b.A,{className:"w-6 h-6"})})]}),(0,t.jsxs)("form",{onSubmit:R,className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("full_name_required")}),(0,t.jsx)("input",{type:"text",value:M.full_name,onChange:e=>L(r=>({...r,full_name:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("enter_full_name"),required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("email_required")}),(0,t.jsx)("input",{type:"email",value:M.email,onChange:e=>L(r=>({...r,email:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("enter_email"),required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("password_required")}),(0,t.jsx)("input",{type:"password",value:M.password,onChange:e=>L(r=>({...r,password:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("enter_password"),required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("phone_required")}),(0,t.jsx)("input",{type:"tel",value:M.phone,onChange:e=>L(r=>({...r,phone:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("enter_phone"),required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("specialty_required")}),(0,t.jsx)("input",{type:"text",value:M.specialty,onChange:e=>L(r=>({...r,specialty:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("specialty_example"),required:!0})]}),(0,t.jsxs)("div",{className:"md:col-span-2 flex gap-4 pt-4",children:[(0,t.jsx)("button",{type:"submit",disabled:I,className:"btn-primary py-3 px-6 disabled:opacity-50 disabled:cursor-not-allowed",children:I?_("adding"):_("add_worker")}),(0,t.jsx)("button",{type:"button",onClick:()=>S(!1),className:"btn-secondary py-3 px-6",children:_("cancel")})]})]})]}),H&&D&&(0,t.jsxs)(i.P.div,{initial:{opacity:0},animate:{opacity:1},className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm",onClick:()=>G(!1)}),(0,t.jsxs)(i.P.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},className:"relative bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)("div",{className:"sticky top-0 bg-white border-b border-gray-200 p-6 rounded-t-2xl",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:_("edit_worker")}),(0,t.jsx)("button",{onClick:()=>G(!1),className:"text-gray-400 hover:text-gray-600",children:(0,t.jsx)(b.A,{className:"w-6 h-6"})})]})}),(0,t.jsxs)("form",{onSubmit:$,className:"p-6 space-y-4",children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("full_name_required")}),(0,t.jsx)("input",{type:"text",value:D.full_name,onChange:e=>z(r=>({...r,full_name:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("email_required")}),(0,t.jsx)("input",{type:"email",value:D.email,onChange:e=>z(r=>({...r,email:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("new_password")}),(0,t.jsx)("input",{type:"password",value:D.password,onChange:e=>z(r=>({...r,password:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("leave_empty_no_change")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("phone_required")}),(0,t.jsx)("input",{type:"tel",value:D.phone,onChange:e=>z(r=>({...r,phone:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("specialty_required")}),(0,t.jsx)("input",{type:"text",value:D.specialty,onChange:e=>z(r=>({...r,specialty:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("status")}),(0,t.jsxs)("select",{value:D.is_active?"active":"inactive",onChange:e=>z(r=>({...r,is_active:"active"===e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",children:[(0,t.jsx)("option",{value:"active",children:_("active")}),(0,t.jsx)("option",{value:"inactive",children:_("inactive")})]})]})]}),(0,t.jsxs)("div",{className:"flex gap-4 pt-4",children:[(0,t.jsx)("button",{type:"submit",disabled:I,className:"btn-primary py-3 px-6 disabled:opacity-50 disabled:cursor-not-allowed",children:I?_("saving"):_("save_changes")}),(0,t.jsx)("button",{type:"button",onClick:()=>G(!1),className:"btn-secondary py-3 px-6",children:_("cancel")})]})]})]})]}),(0,t.jsx)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"grid lg:grid-cols-2 gap-6 mb-12",children:0===X.length?(0,t.jsxs)("div",{className:"lg:col-span-2 text-center py-12 bg-white/80 backdrop-blur-sm rounded-2xl border border-pink-100",children:[(0,t.jsx)(g.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-xl font-medium text-gray-600 mb-2",children:_("no_workers")}),(0,t.jsx)("p",{className:"text-gray-500",children:_("no_workers_found")})]}):X.map((e,r)=>(0,t.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*r},className:`bg-white/80 backdrop-blur-sm rounded-2xl p-6 border transition-all duration-300 hover:shadow-lg ${e.is_active?"border-pink-100":"border-gray-200 opacity-75"}`,children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full flex items-center justify-center",children:(0,t.jsx)(y.A,{className:"w-6 h-6 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-bold text-gray-800",children:e.full_name}),(0,t.jsx)("p",{className:"text-sm text-pink-600 font-medium",children:e.specialty})]})]}),(0,t.jsx)("div",{className:"flex items-center space-x-2 space-x-reverse",children:e.is_active?(0,t.jsxs)("span",{className:"px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium flex items-center space-x-1 space-x-reverse",children:[(0,t.jsx)(h.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{children:_("active")})]}):(0,t.jsxs)("span",{className:"px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium flex items-center space-x-1 space-x-reverse",children:[(0,t.jsx)(b.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{children:_("inactive")})]})})]}),(0,t.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse text-sm text-gray-600",children:[(0,t.jsx)(f.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:e.email})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse text-sm text-gray-600",children:[(0,t.jsx)(v.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:e.phone})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 gap-4 mb-6",children:(0,t.jsxs)("div",{className:"text-center p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-lg font-bold text-green-600",children:O(e.id)}),(0,t.jsx)("div",{className:"text-xs text-gray-600",children:_("completed_orders")})]})}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mb-4",children:[_("joined_on")," ",W(e.createdAt)]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("button",{onClick:()=>T(e),className:"flex-1 btn-secondary py-2 text-sm inline-flex items-center justify-center space-x-1 space-x-reverse",children:[(0,t.jsx)(j.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:_("edit")})]}),(0,t.jsx)("button",{onClick:()=>K(e.id,e.is_active),className:`px-3 py-2 text-sm rounded-lg transition-all duration-300 ${e.is_active?"bg-red-100 text-red-700 hover:bg-red-200":"bg-green-100 text-green-700 hover:bg-green-200"}`,children:e.is_active?(0,t.jsx)(b.A,{className:"w-4 h-4"}):(0,t.jsx)(h.A,{className:"w-4 h-4"})}),(0,t.jsx)("button",{onClick:()=>B(e.id),className:"px-3 py-2 text-red-600 hover:text-red-700 border border-red-200 rounded-lg hover:bg-red-50 transition-all duration-300",children:(0,t.jsx)(w.A,{className:"w-4 h-4"})})]})]},e.id))}),(0,t.jsxs)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"grid grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:r.length}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:_("total_workers")})]}),(0,t.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-1",children:r.filter(e=>e.is_active).length}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:_("active_workers")})]}),(0,t.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-purple-600 mb-1",children:r.reduce((e,r)=>e+O(r.id),0)}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:_("total_completed_orders")})]}),(0,t.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-yellow-600 mb-1",children:r.filter(e=>e.is_active).length}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:_("active_workers")})]})]})]})}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:_("checking_permissions")})]})})}},35071:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},38023:(e,r,s)=>{Promise.resolve().then(s.bind(s,63071))},41312:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41550:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},48340:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63071:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\workers\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\workers\\page.tsx","default")},63143:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},68287:(e,r,s)=>{Promise.resolve().then(s.bind(s,34161))},79551:e=>{"use strict";e.exports=require("url")},86788:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>o});var t=s(65239),a=s(48088),i=s(88170),l=s.n(i),n=s(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(r,d);let o={children:["",{children:["dashboard",{children:["workers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,63071)),"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\workers\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\workers\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/workers/page",pathname:"/dashboard/workers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},99270:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,507,146,814,267,985],()=>s(86788));module.exports=t})();