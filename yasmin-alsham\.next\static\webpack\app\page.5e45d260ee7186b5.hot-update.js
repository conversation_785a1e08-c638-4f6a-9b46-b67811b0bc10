"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Heart,Mail,MapPin,Palette,Phone,Scissors,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scissors.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Heart,Mail,MapPin,Palette,Phone,Scissors,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Heart,Mail,MapPin,Palette,Phone,Scissors,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Heart,Mail,MapPin,Palette,Phone,Scissors,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Heart,Mail,MapPin,Palette,Phone,Scissors,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Heart,Mail,MapPin,Palette,Phone,Scissors,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Heart,Mail,MapPin,Palette,Phone,Scissors,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Heart,Mail,MapPin,Palette,Phone,Scissors,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Heart,Mail,MapPin,Palette,Phone,Scissors,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Footer() {\n    const currentYear = new Date().getFullYear();\n    const quickLinks = [\n        {\n            href: '/',\n            label: 'الرئيسية',\n            icon: _barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            href: '/book-appointment',\n            label: 'حجز موعد',\n            icon: _barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            href: '/track-order',\n            label: 'تتبع الطلب',\n            icon: _barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            href: '/fabrics',\n            label: 'الأقمشة',\n            icon: _barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        }\n    ];\n    const workingHours = [\n        {\n            day: 'السبت - الخميس',\n            time: '4:00 م - 10:00 م'\n        },\n        {\n            day: 'الجمعة',\n            time: 'مغلق'\n        }\n    ];\n    const socialLinks = [\n        {\n            name: 'فيسبوك',\n            href: '#',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            name: 'إنستغرام',\n            href: '#',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.897-.875-1.387-2.026-1.387-3.323s.49-2.448 1.297-3.323c.875-.897 2.026-1.387 3.323-1.387s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            name: 'واتساب',\n            href: 'https://wa.me/+966598862609',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold bg-gradient-to-r from-pink-400 to-rose-400 bg-clip-text text-transparent\",\n                                                    children: \"ياسمين الشام\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"تفصيل فساتين حسب الطلب\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 leading-relaxed\",\n                                    children: \"نحن متخصصون في تفصيل الفساتين الراقية بلمسة دمشقية أصيلة. نجمع بين التراث العريق والتصاميم العصرية لنقدم لك فساتين تعكس شخصيتك وتبرز جمالك الطبيعي.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4 space-x-reverse\",\n                                    children: socialLinks.map((social, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.a, {\n                                            href: social.href,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.8\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                scale: 1\n                                            },\n                                            transition: {\n                                                duration: 0.4,\n                                                delay: index * 0.1\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"w-10 h-10 bg-gradient-to-br from-pink-500 to-rose-500 rounded-full flex items-center justify-center hover:from-pink-600 hover:to-rose-600 transition-all duration-300 transform hover:scale-110\",\n                                            children: social.icon\n                                        }, social.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"md:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg lg:text-xl font-bold mb-4 lg:mb-6 text-pink-400\",\n                                    children: \"روابط سريعة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 lg:space-y-4\",\n                                    children: quickLinks.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.li, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.4,\n                                                delay: 0.2 + index * 0.1\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"icon-text-spacing text-gray-300 hover:text-pink-400 transition-colors duration-300 group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(link.icon, {\n                                                        className: \"w-4 h-4 menu-item-icon group-hover:scale-110 transition-transform duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: link.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.href, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"md:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg lg:text-xl font-bold mb-4 lg:mb-6 text-pink-400\",\n                                    children: \"تواصل معنا\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 lg:space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"icon-text-spacing items-start text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5 text-pink-400 mt-1 menu-item-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: \"العنوان\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"الخبر الشمالية، التقاطع السادس، شارع الأمير مشعل، الخبر، السعودية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"icon-text-spacing items-start text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5 text-pink-400 mt-1 menu-item-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: \"أرقام الهاتف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: \"قسم التفصيل\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                                            lineNumber: 164,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"tel:+966598862609\",\n                                                                            className: \"text-sm text-gray-400 hover:text-pink-400 transition-colors duration-300\",\n                                                                            dir: \"ltr\",\n                                                                            children: \"+966 598 862 609\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                                            lineNumber: 165,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: \"قسم الفساتين الجاهزة\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                                            lineNumber: 170,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"tel:+966501503639\",\n                                                                            className: \"text-sm text-gray-400 hover:text-pink-400 transition-colors duration-300\",\n                                                                            dir: \"ltr\",\n                                                                            children: \"+966 501 503 639\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                                            lineNumber: 171,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 169,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"icon-text-spacing items-start text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 text-pink-400 mt-1 menu-item-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: \"البريد الإلكتروني\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"mailto:<EMAIL>\",\n                                                            className: \"text-sm text-gray-400 hover:text-pink-400 transition-colors duration-300\",\n                                                            children: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.3\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"md:col-span-2 lg:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg lg:text-xl font-bold mb-4 lg:mb-6 text-pink-400\",\n                                    children: \"ساعات العمل\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 lg:space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 space-x-reverse text-gray-300 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5 text-pink-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: \"جدول العمل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this),\n                                        workingHours.map((schedule, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 20\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.4,\n                                                    delay: 0.4 + index * 0.1\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                className: \"flex justify-between items-center text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: schedule.day\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium \".concat(schedule.time === 'مغلق' ? 'text-red-400' : 'text-pink-400'),\n                                                        children: schedule.time\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 p-4 bg-gradient-to-r from-pink-500/20 to-rose-500/20 rounded-lg border border-pink-500/30\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-300 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 inline-block ml-1 text-pink-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"نعمل بحب وشغف\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scaleX: 0\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        scaleX: 1\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"border-t border-gray-700 my-12\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.5\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-400 text-sm text-center md:text-right\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" ياسمين الشام. جميع الحقوق محفوظة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-6 space-x-reverse text-sm text-gray-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/privacy\",\n                                    className: \"hover:text-pink-400 transition-colors duration-300\",\n                                    children: \"سياسة الخصوصية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/terms\",\n                                    className: \"hover:text-pink-400 transition-colors duration-300\",\n                                    children: \"الشروط والأحكام\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"صُنع بـ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Heart_Mail_MapPin_Palette_Phone_Scissors_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4 text-pink-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"في دمشق\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n_c = Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0Zvb3Rlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRXNDO0FBQ1Y7QUFZUDtBQUVOLFNBQVNXO0lBQ3RCLE1BQU1DLGNBQWMsSUFBSUMsT0FBT0MsV0FBVztJQUUxQyxNQUFNQyxhQUFhO1FBQ2pCO1lBQUVDLE1BQU07WUFBS0MsT0FBTztZQUFZQyxNQUFNaEIsMElBQVFBO1FBQUM7UUFDL0M7WUFBRWMsTUFBTTtZQUFxQkMsT0FBTztZQUFZQyxNQUFNWCwwSUFBUUE7UUFBQztRQUMvRDtZQUFFUyxNQUFNO1lBQWdCQyxPQUFPO1lBQWNDLE1BQU1WLDBJQUFNQTtRQUFDO1FBQzFEO1lBQUVRLE1BQU07WUFBWUMsT0FBTztZQUFXQyxNQUFNVCwwSUFBT0E7UUFBQztLQUNyRDtJQUVELE1BQU1VLGVBQWU7UUFDbkI7WUFBRUMsS0FBSztZQUFrQkMsTUFBTTtRQUFtQjtRQUNsRDtZQUFFRCxLQUFLO1lBQVVDLE1BQU07UUFBTztLQUMvQjtJQUVELE1BQU1DLGNBQWM7UUFDbEI7WUFDRUMsTUFBTTtZQUNOUCxNQUFNO1lBQ05FLG9CQUNFLDhEQUFDTTtnQkFBSUMsV0FBVTtnQkFBVUMsTUFBSztnQkFBZUMsU0FBUTswQkFDbkQsNEVBQUNDO29CQUFLQyxHQUFFOzs7Ozs7Ozs7OztRQUdkO1FBQ0E7WUFDRU4sTUFBTTtZQUNOUCxNQUFNO1lBQ05FLG9CQUNFLDhEQUFDTTtnQkFBSUMsV0FBVTtnQkFBVUMsTUFBSztnQkFBZUMsU0FBUTswQkFDbkQsNEVBQUNDO29CQUFLQyxHQUFFOzs7Ozs7Ozs7OztRQUdkO1FBQ0E7WUFDRU4sTUFBTTtZQUNOUCxNQUFNO1lBQ05FLG9CQUNFLDhEQUFDTTtnQkFBSUMsV0FBVTtnQkFBVUMsTUFBSztnQkFBZUMsU0FBUTswQkFDbkQsNEVBQUNDO29CQUFLQyxHQUFFOzs7Ozs7Ozs7OztRQUdkO0tBQ0Q7SUFFRCxxQkFDRSw4REFBQ0M7UUFBT0wsV0FBVTtrQkFDaEIsNEVBQUNNO1lBQUlOLFdBQVU7OzhCQUNiLDhEQUFDTTtvQkFBSU4sV0FBVTs7c0NBRWIsOERBQUN6QixpREFBTUEsQ0FBQytCLEdBQUc7NEJBQ1RDLFNBQVM7Z0NBQUVDLFNBQVM7Z0NBQUdDLEdBQUc7NEJBQUc7NEJBQzdCQyxhQUFhO2dDQUFFRixTQUFTO2dDQUFHQyxHQUFHOzRCQUFFOzRCQUNoQ0UsWUFBWTtnQ0FBRUMsVUFBVTs0QkFBSTs0QkFDNUJDLFVBQVU7Z0NBQUVDLE1BQU07NEJBQUs7NEJBQ3ZCZCxXQUFVOzs4Q0FFViw4REFBQ007b0NBQUlOLFdBQVU7O3NEQUNiLDhEQUFDTTs0Q0FBSU4sV0FBVTtzREFDYiw0RUFBQ3ZCLDBJQUFRQTtnREFBQ3VCLFdBQVU7Ozs7Ozs7Ozs7O3NEQUV0Qiw4REFBQ007OzhEQUNDLDhEQUFDUztvREFBR2YsV0FBVTs4REFBOEY7Ozs7Ozs4REFHNUcsOERBQUNnQjtvREFBRWhCLFdBQVU7OERBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSXpDLDhEQUFDZ0I7b0NBQUVoQixXQUFVOzhDQUFnQzs7Ozs7OzhDQUk3Qyw4REFBQ007b0NBQUlOLFdBQVU7OENBQ1pILFlBQVlvQixHQUFHLENBQUMsQ0FBQ0MsUUFBUUMsc0JBQ3hCLDhEQUFDNUMsaURBQU1BLENBQUM2QyxDQUFDOzRDQUVQN0IsTUFBTTJCLE9BQU8zQixJQUFJOzRDQUNqQjhCLFFBQU87NENBQ1BDLEtBQUk7NENBQ0pmLFNBQVM7Z0RBQUVDLFNBQVM7Z0RBQUdlLE9BQU87NENBQUk7NENBQ2xDYixhQUFhO2dEQUFFRixTQUFTO2dEQUFHZSxPQUFPOzRDQUFFOzRDQUNwQ1osWUFBWTtnREFBRUMsVUFBVTtnREFBS1ksT0FBT0wsUUFBUTs0Q0FBSTs0Q0FDaEROLFVBQVU7Z0RBQUVDLE1BQU07NENBQUs7NENBQ3ZCZCxXQUFVO3NEQUVUa0IsT0FBT3pCLElBQUk7MkNBVlB5QixPQUFPcEIsSUFBSTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FpQnhCLDhEQUFDdkIsaURBQU1BLENBQUMrQixHQUFHOzRCQUNUQyxTQUFTO2dDQUFFQyxTQUFTO2dDQUFHQyxHQUFHOzRCQUFHOzRCQUM3QkMsYUFBYTtnQ0FBRUYsU0FBUztnQ0FBR0MsR0FBRzs0QkFBRTs0QkFDaENFLFlBQVk7Z0NBQUVDLFVBQVU7Z0NBQUtZLE9BQU87NEJBQUk7NEJBQ3hDWCxVQUFVO2dDQUFFQyxNQUFNOzRCQUFLOzRCQUN2QmQsV0FBVTs7OENBRVYsOERBQUN5QjtvQ0FBR3pCLFdBQVU7OENBQTBEOzs7Ozs7OENBQ3hFLDhEQUFDMEI7b0NBQUcxQixXQUFVOzhDQUNYVixXQUFXMkIsR0FBRyxDQUFDLENBQUNVLE1BQU1SLHNCQUNyQiw4REFBQzVDLGlEQUFNQSxDQUFDcUQsRUFBRTs0Q0FFUnJCLFNBQVM7Z0RBQUVDLFNBQVM7Z0RBQUdxQixHQUFHLENBQUM7NENBQUc7NENBQzlCbkIsYUFBYTtnREFBRUYsU0FBUztnREFBR3FCLEdBQUc7NENBQUU7NENBQ2hDbEIsWUFBWTtnREFBRUMsVUFBVTtnREFBS1ksT0FBTyxNQUFNTCxRQUFROzRDQUFJOzRDQUN0RE4sVUFBVTtnREFBRUMsTUFBTTs0Q0FBSztzREFFdkIsNEVBQUN0QyxrREFBSUE7Z0RBQ0hlLE1BQU1vQyxLQUFLcEMsSUFBSTtnREFDZlMsV0FBVTs7a0VBRVYsOERBQUMyQixLQUFLbEMsSUFBSTt3REFBQ08sV0FBVTs7Ozs7O2tFQUNyQiw4REFBQzhCO2tFQUFNSCxLQUFLbkMsS0FBSzs7Ozs7Ozs7Ozs7OzJDQVhkbUMsS0FBS3BDLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBbUJ0Qiw4REFBQ2hCLGlEQUFNQSxDQUFDK0IsR0FBRzs0QkFDVEMsU0FBUztnQ0FBRUMsU0FBUztnQ0FBR0MsR0FBRzs0QkFBRzs0QkFDN0JDLGFBQWE7Z0NBQUVGLFNBQVM7Z0NBQUdDLEdBQUc7NEJBQUU7NEJBQ2hDRSxZQUFZO2dDQUFFQyxVQUFVO2dDQUFLWSxPQUFPOzRCQUFJOzRCQUN4Q1gsVUFBVTtnQ0FBRUMsTUFBTTs0QkFBSzs0QkFDdkJkLFdBQVU7OzhDQUVWLDhEQUFDeUI7b0NBQUd6QixXQUFVOzhDQUEwRDs7Ozs7OzhDQUN4RSw4REFBQ007b0NBQUlOLFdBQVU7O3NEQUNiLDhEQUFDTTs0Q0FBSU4sV0FBVTs7OERBQ2IsOERBQUN0QiwwSUFBTUE7b0RBQUNzQixXQUFVOzs7Ozs7OERBQ2xCLDhEQUFDTTs7c0VBQ0MsOERBQUNVOzREQUFFaEIsV0FBVTtzRUFBYzs7Ozs7O3NFQUMzQiw4REFBQ2dCOzREQUFFaEIsV0FBVTtzRUFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFJekMsOERBQUNNOzRDQUFJTixXQUFVOzs4REFDYiw4REFBQ3JCLDBJQUFLQTtvREFBQ3FCLFdBQVU7Ozs7Ozs4REFDakIsOERBQUNNOztzRUFDQyw4REFBQ1U7NERBQUVoQixXQUFVO3NFQUFjOzs7Ozs7c0VBQzNCLDhEQUFDTTs0REFBSU4sV0FBVTs7OEVBQ2IsOERBQUNNOztzRkFDQyw4REFBQ1U7NEVBQUVoQixXQUFVO3NGQUF3Qjs7Ozs7O3NGQUNyQyw4REFBQ29COzRFQUFFN0IsTUFBSzs0RUFBb0JTLFdBQVU7NEVBQTJFK0IsS0FBSTtzRkFBTTs7Ozs7Ozs7Ozs7OzhFQUk3SCw4REFBQ3pCOztzRkFDQyw4REFBQ1U7NEVBQUVoQixXQUFVO3NGQUF3Qjs7Ozs7O3NGQUNyQyw4REFBQ29COzRFQUFFN0IsTUFBSzs0RUFBb0JTLFdBQVU7NEVBQTJFK0IsS0FBSTtzRkFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQVFuSSw4REFBQ3pCOzRDQUFJTixXQUFVOzs4REFDYiw4REFBQ3BCLDBJQUFJQTtvREFBQ29CLFdBQVU7Ozs7Ozs4REFDaEIsOERBQUNNOztzRUFDQyw4REFBQ1U7NERBQUVoQixXQUFVO3NFQUFjOzs7Ozs7c0VBQzNCLDhEQUFDb0I7NERBQUU3QixNQUFLOzREQUErQlMsV0FBVTtzRUFBMkU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FTcEksOERBQUN6QixpREFBTUEsQ0FBQytCLEdBQUc7NEJBQ1RDLFNBQVM7Z0NBQUVDLFNBQVM7Z0NBQUdDLEdBQUc7NEJBQUc7NEJBQzdCQyxhQUFhO2dDQUFFRixTQUFTO2dDQUFHQyxHQUFHOzRCQUFFOzRCQUNoQ0UsWUFBWTtnQ0FBRUMsVUFBVTtnQ0FBS1ksT0FBTzs0QkFBSTs0QkFDeENYLFVBQVU7Z0NBQUVDLE1BQU07NEJBQUs7NEJBQ3ZCZCxXQUFVOzs4Q0FFViw4REFBQ3lCO29DQUFHekIsV0FBVTs4Q0FBMEQ7Ozs7Ozs4Q0FDeEUsOERBQUNNO29DQUFJTixXQUFVOztzREFDYiw4REFBQ007NENBQUlOLFdBQVU7OzhEQUNiLDhEQUFDbkIsMklBQUtBO29EQUFDbUIsV0FBVTs7Ozs7OzhEQUNqQiw4REFBQzhCO29EQUFLOUIsV0FBVTs4REFBYzs7Ozs7Ozs7Ozs7O3dDQUcvQk4sYUFBYXVCLEdBQUcsQ0FBQyxDQUFDZSxVQUFVYixzQkFDM0IsOERBQUM1QyxpREFBTUEsQ0FBQytCLEdBQUc7Z0RBRVRDLFNBQVM7b0RBQUVDLFNBQVM7b0RBQUdxQixHQUFHO2dEQUFHO2dEQUM3Qm5CLGFBQWE7b0RBQUVGLFNBQVM7b0RBQUdxQixHQUFHO2dEQUFFO2dEQUNoQ2xCLFlBQVk7b0RBQUVDLFVBQVU7b0RBQUtZLE9BQU8sTUFBTUwsUUFBUTtnREFBSTtnREFDdEROLFVBQVU7b0RBQUVDLE1BQU07Z0RBQUs7Z0RBQ3ZCZCxXQUFVOztrRUFFViw4REFBQzhCO3dEQUFLOUIsV0FBVTtrRUFBaUJnQyxTQUFTckMsR0FBRzs7Ozs7O2tFQUM3Qyw4REFBQ21DO3dEQUFLOUIsV0FBVyxlQUEyRSxPQUE1RGdDLFNBQVNwQyxJQUFJLEtBQUssU0FBUyxpQkFBaUI7a0VBQ3pFb0MsU0FBU3BDLElBQUk7Ozs7Ozs7K0NBVFh1Qjs7Ozs7c0RBY1QsOERBQUNiOzRDQUFJTixXQUFVO3NEQUNiLDRFQUFDZ0I7Z0RBQUVoQixXQUFVOztrRUFDWCw4REFBQ2YsMklBQUtBO3dEQUFDZSxXQUFVOzs7Ozs7b0RBQTRDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBU3ZFLDhEQUFDekIsaURBQU1BLENBQUMrQixHQUFHO29CQUNUQyxTQUFTO3dCQUFFQyxTQUFTO3dCQUFHeUIsUUFBUTtvQkFBRTtvQkFDakN2QixhQUFhO3dCQUFFRixTQUFTO3dCQUFHeUIsUUFBUTtvQkFBRTtvQkFDckN0QixZQUFZO3dCQUFFQyxVQUFVO3dCQUFLWSxPQUFPO29CQUFJO29CQUN4Q1gsVUFBVTt3QkFBRUMsTUFBTTtvQkFBSztvQkFDdkJkLFdBQVU7Ozs7Ozs4QkFJWiw4REFBQ3pCLGlEQUFNQSxDQUFDK0IsR0FBRztvQkFDVEMsU0FBUzt3QkFBRUMsU0FBUzt3QkFBR0MsR0FBRztvQkFBRztvQkFDN0JDLGFBQWE7d0JBQUVGLFNBQVM7d0JBQUdDLEdBQUc7b0JBQUU7b0JBQ2hDRSxZQUFZO3dCQUFFQyxVQUFVO3dCQUFLWSxPQUFPO29CQUFJO29CQUN4Q1gsVUFBVTt3QkFBRUMsTUFBTTtvQkFBSztvQkFDdkJkLFdBQVU7O3NDQUVWLDhEQUFDTTs0QkFBSU4sV0FBVTtzQ0FDYiw0RUFBQ2dCOztvQ0FBRTtvQ0FBRzdCO29DQUFZOzs7Ozs7Ozs7Ozs7c0NBR3BCLDhEQUFDbUI7NEJBQUlOLFdBQVU7OzhDQUNiLDhEQUFDeEIsa0RBQUlBO29DQUFDZSxNQUFLO29DQUFXUyxXQUFVOzhDQUFxRDs7Ozs7OzhDQUdyRiw4REFBQ3hCLGtEQUFJQTtvQ0FBQ2UsTUFBSztvQ0FBU1MsV0FBVTs4Q0FBcUQ7Ozs7Ozs4Q0FHbkYsOERBQUNNO29DQUFJTixXQUFVOztzREFDYiw4REFBQzhCO3NEQUFLOzs7Ozs7c0RBQ04sOERBQUM3QywySUFBS0E7NENBQUNlLFdBQVU7Ozs7OztzREFDakIsOERBQUM4QjtzREFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPcEI7S0E1UHdCNUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca2hhbGVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcWUFTTUlOIEFMU0hBTVxceWFzbWluLWFsc2hhbVxcc3JjXFxjb21wb25lbnRzXFxGb290ZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBtb3Rpb24gfSBmcm9tICdmcmFtZXItbW90aW9uJ1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuaW1wb3J0IHtcbiAgU2Npc3NvcnMsXG4gIE1hcFBpbixcbiAgUGhvbmUsXG4gIE1haWwsXG4gIENsb2NrLFxuICBDYWxlbmRhcixcbiAgU2VhcmNoLFxuICBQYWxldHRlLFxuICBIZWFydCxcbiAgU3RhclxufSBmcm9tICdsdWNpZGUtcmVhY3QnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEZvb3RlcigpIHtcbiAgY29uc3QgY3VycmVudFllYXIgPSBuZXcgRGF0ZSgpLmdldEZ1bGxZZWFyKClcblxuICBjb25zdCBxdWlja0xpbmtzID0gW1xuICAgIHsgaHJlZjogJy8nLCBsYWJlbDogJ9in2YTYsdim2YrYs9mK2KknLCBpY29uOiBTY2lzc29ycyB9LFxuICAgIHsgaHJlZjogJy9ib29rLWFwcG9pbnRtZW50JywgbGFiZWw6ICfYrdis2LIg2YXZiNi52K8nLCBpY29uOiBDYWxlbmRhciB9LFxuICAgIHsgaHJlZjogJy90cmFjay1vcmRlcicsIGxhYmVsOiAn2KrYqtio2Lkg2KfZhNi32YTYqCcsIGljb246IFNlYXJjaCB9LFxuICAgIHsgaHJlZjogJy9mYWJyaWNzJywgbGFiZWw6ICfYp9mE2KPZgtmF2LTYqScsIGljb246IFBhbGV0dGUgfVxuICBdXG5cbiAgY29uc3Qgd29ya2luZ0hvdXJzID0gW1xuICAgIHsgZGF5OiAn2KfZhNiz2KjYqiAtINin2YTYrtmF2YrYsycsIHRpbWU6ICc0OjAwINmFIC0gMTA6MDAg2YUnIH0sXG4gICAgeyBkYXk6ICfYp9mE2KzZhdi52KknLCB0aW1lOiAn2YXYutmE2YInIH1cbiAgXVxuXG4gIGNvbnN0IHNvY2lhbExpbmtzID0gW1xuICAgIHtcbiAgICAgIG5hbWU6ICfZgdmK2LPYqNmI2YMnLFxuICAgICAgaHJlZjogJyMnLFxuICAgICAgaWNvbjogKFxuICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTVcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgIDxwYXRoIGQ9XCJNMjQgMTIuMDczYzAtNi42MjctNS4zNzMtMTItMTItMTJzLTEyIDUuMzczLTEyIDEyYzAgNS45OSA0LjM4OCAxMC45NTQgMTAuMTI1IDExLjg1NHYtOC4zODVINy4wNzh2LTMuNDdoMy4wNDdWOS40M2MwLTMuMDA3IDEuNzkyLTQuNjY5IDQuNTMzLTQuNjY5IDEuMzEyIDAgMi42ODYuMjM1IDIuNjg2LjIzNXYyLjk1M0gxNS44M2MtMS40OTEgMC0xLjk1Ni45MjUtMS45NTYgMS44NzR2Mi4yNWgzLjMyOGwtLjUzMiAzLjQ3aC0yLjc5NnY4LjM4NUMxOS42MTIgMjMuMDI3IDI0IDE4LjA2MiAyNCAxMi4wNzN6XCIvPlxuICAgICAgICA8L3N2Zz5cbiAgICAgIClcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICfYpdmG2LPYqti62LHYp9mFJyxcbiAgICAgIGhyZWY6ICcjJyxcbiAgICAgIGljb246IChcbiAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICA8cGF0aCBkPVwiTTEyLjAxNyAwQzUuMzk2IDAgLjAyOSA1LjM2Ny4wMjkgMTEuOTg3YzAgNi42MiA1LjM2NyAxMS45ODcgMTEuOTg4IDExLjk4NyA2LjYyIDAgMTEuOTg3LTUuMzY3IDExLjk4Ny0xMS45ODdDMjQuMDE0IDUuMzY3IDE4LjYzNy4wMDEgMTIuMDE3LjAwMXpNOC40NDkgMTYuOTg4Yy0xLjI5NyAwLTIuNDQ4LS40OS0zLjMyMy0xLjI5N0M0LjE5OCAxNC44OTUgMy43MDggMTMuNzQ0IDMuNzA4IDEyLjQ0N3MuNDktMi40NDggMS4yOTctMy4zMjNDNS45MDIgOC4xOTggNy4wNTMgNy43MDggOC4zNSA3LjcwOHMyLjQ0OC40OSAzLjMyMyAxLjI5N2MuODk3Ljg3NSAxLjM4NyAyLjAyNiAxLjM4NyAzLjMyM3MtLjQ5IDIuNDQ4LTEuMjk3IDMuMzIzYy0uODc1Ljg5Ny0yLjAyNiAxLjM4Ny0zLjMyMyAxLjM4N3ptNy43MTggMGMtMS4yOTcgMC0yLjQ0OC0uNDktMy4zMjMtMS4yOTctLjg5Ny0uODc1LTEuMzg3LTIuMDI2LTEuMzg3LTMuMzIzcy40OS0yLjQ0OCAxLjI5Ny0zLjMyM2MuODc1LS44OTcgMi4wMjYtMS4zODcgMy4zMjMtMS4zODdzMi40NDguNDkgMy4zMjMgMS4yOTdjLjg5Ny44NzUgMS4zODcgMi4wMjYgMS4zODcgMy4zMjNzLS40OSAyLjQ0OC0xLjI5NyAzLjMyM2MtLjg3NS44OTctMi4wMjYgMS4zODctMy4zMjMgMS4zODd6XCIvPlxuICAgICAgICA8L3N2Zz5cbiAgICAgIClcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICfZiNin2KrYs9in2KgnLFxuICAgICAgaHJlZjogJ2h0dHBzOi8vd2EubWUvKzk2NjU5ODg2MjYwOScsXG4gICAgICBpY29uOiAoXG4gICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgPHBhdGggZD1cIk0xNy40NzIgMTQuMzgyYy0uMjk3LS4xNDktMS43NTgtLjg2Ny0yLjAzLS45NjctLjI3My0uMDk5LS40NzEtLjE0OC0uNjcuMTUtLjE5Ny4yOTctLjc2Ny45NjYtLjk0IDEuMTY0LS4xNzMuMTk5LS4zNDcuMjIzLS42NDQuMDc1LS4yOTctLjE1LTEuMjU1LS40NjMtMi4zOS0xLjQ3NS0uODgzLS43ODgtMS40OC0xLjc2MS0xLjY1My0yLjA1OS0uMTczLS4yOTctLjAxOC0uNDU4LjEzLS42MDYuMTM0LS4xMzMuMjk4LS4zNDcuNDQ2LS41Mi4xNDktLjE3NC4xOTgtLjI5OC4yOTgtLjQ5Ny4wOTktLjE5OC4wNS0uMzcxLS4wMjUtLjUyLS4wNzUtLjE0OS0uNjY5LTEuNjEyLS45MTYtMi4yMDctLjI0Mi0uNTc5LS40ODctLjUtLjY2OS0uNTEtLjE3My0uMDA4LS4zNzEtLjAxLS41Ny0uMDEtLjE5OCAwLS41Mi4wNzQtLjc5Mi4zNzItLjI3Mi4yOTctMS4wNCAxLjAxNi0xLjA0IDIuNDc5IDAgMS40NjIgMS4wNjUgMi44NzUgMS4yMTMgMy4wNzQuMTQ5LjE5OCAyLjA5NiAzLjIgNS4wNzcgNC40ODcuNzA5LjMwNiAxLjI2Mi40ODkgMS42OTQuNjI1LjcxMi4yMjcgMS4zNi4xOTUgMS44NzEuMTE4LjU3MS0uMDg1IDEuNzU4LS43MTkgMi4wMDYtMS40MTMuMjQ4LS42OTQuMjQ4LTEuMjg5LjE3My0xLjQxMy0uMDc0LS4xMjQtLjI3Mi0uMTk4LS41Ny0uMzQ3bS01LjQyMSA3LjQwM2gtLjAwNGE5Ljg3IDkuODcgMCAwMS01LjAzMS0xLjM3OGwtLjM2MS0uMjE0LTMuNzQxLjk4Mi45OTgtMy42NDgtLjIzNS0uMzc0YTkuODYgOS44NiAwIDAxLTEuNTEtNS4yNmMuMDAxLTUuNDUgNC40MzYtOS44ODQgOS44ODgtOS44ODQgMi42NCAwIDUuMTIyIDEuMDMgNi45ODggMi44OThhOS44MjUgOS44MjUgMCAwMTIuODkzIDYuOTk0Yy0uMDAzIDUuNDUtNC40MzcgOS44ODQtOS44ODUgOS44ODRtOC40MTMtMTguMjk3QTExLjgxNSAxMS44MTUgMCAwMDEyLjA1IDBDNS40OTUgMCAuMTYgNS4zMzUuMTU3IDExLjg5MmMwIDIuMDk2LjU0NyA0LjE0MiAxLjU4OCA1Ljk0NUwuMDU3IDI0bDYuMzA1LTEuNjU0YTExLjg4MiAxMS44ODIgMCAwMDUuNjgzIDEuNDQ4aC4wMDVjNi41NTQgMCAxMS44OS01LjMzNSAxMS44OTMtMTEuODkzQTExLjgyMSAxMS44MjEgMCAwMDIwLjg4NSAzLjQ4OFwiLz5cbiAgICAgICAgPC9zdmc+XG4gICAgICApXG4gICAgfVxuICBdXG5cbiAgcmV0dXJuIChcbiAgICA8Zm9vdGVyIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tZ3JheS05MDAgdmlhLWdyYXktODAwIHRvLWdyYXktOTAwIHRleHQtd2hpdGVcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LTggcHktNiBsZzpweS0xNlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgc206Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLTQgc206Z2FwLTYgbGc6Z2FwLTEyXCI+XG4gICAgICAgICAgey8qINmF2LnZhNmI2YXYp9iqINin2YTZhdit2YQgKi99XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMzAgfX1cbiAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNiB9fVxuICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwic3BhY2UteS02XCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBzcGFjZS14LXJldmVyc2VcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1waW5rLTQwMCB0by1yb3NlLTQwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8U2Npc3NvcnMgY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCBiZy1ncmFkaWVudC10by1yIGZyb20tcGluay00MDAgdG8tcm9zZS00MDAgYmctY2xpcC10ZXh0IHRleHQtdHJhbnNwYXJlbnRcIj5cbiAgICAgICAgICAgICAgICAgINmK2KfYs9mF2YrZhiDYp9mE2LTYp9mFXG4gICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc21cIj7YqtmB2LXZitmEINmB2LPYp9iq2YrZhiDYrdiz2Kgg2KfZhNi32YTYqDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAg2YbYrdmGINmF2KrYrti12LXZiNmGINmB2Yog2KrZgdi12YrZhCDYp9mE2YHYs9in2KrZitmGINin2YTYsdin2YLZitipINio2YTZhdiz2Kkg2K/Zhdi02YLZitipINij2LXZitmE2KkuINmG2KzZhdi5INio2YrZhiDYp9mE2KrYsdin2Ksg2KfZhNi52LHZitmCINmI2KfZhNiq2LXYp9mF2YrZhSDYp9mE2LnYtdix2YrYqSDZhNmG2YLYr9mFINmE2YMg2YHYs9in2KrZitmGINiq2LnZg9izINi02K7YtdmK2KrZgyDZiNiq2KjYsdiyINis2YXYp9mE2YMg2KfZhNi32KjZiti52YouXG4gICAgICAgICAgICA8L3A+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00IHNwYWNlLXgtcmV2ZXJzZVwiPlxuICAgICAgICAgICAgICB7c29jaWFsTGlua3MubWFwKChzb2NpYWwsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgPG1vdGlvbi5hXG4gICAgICAgICAgICAgICAgICBrZXk9e3NvY2lhbC5uYW1lfVxuICAgICAgICAgICAgICAgICAgaHJlZj17c29jaWFsLmhyZWZ9XG4gICAgICAgICAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxuICAgICAgICAgICAgICAgICAgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiXG4gICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHNjYWxlOiAwLjggfX1cbiAgICAgICAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHNjYWxlOiAxIH19XG4gICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjQsIGRlbGF5OiBpbmRleCAqIDAuMSB9fVxuICAgICAgICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0xMCBoLTEwIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tcGluay01MDAgdG8tcm9zZS01MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGhvdmVyOmZyb20tcGluay02MDAgaG92ZXI6dG8tcm9zZS02MDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIHRyYW5zZm9ybSBob3ZlcjpzY2FsZS0xMTBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtzb2NpYWwuaWNvbn1cbiAgICAgICAgICAgICAgICA8L21vdGlvbi5hPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgIHsvKiDYsdmI2KfYqNi3INiz2LHZiti52KkgKi99XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMzAgfX1cbiAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNiwgZGVsYXk6IDAuMSB9fVxuICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwibWQ6Y29sLXNwYW4tMVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgbGc6dGV4dC14bCBmb250LWJvbGQgbWItNCBsZzptYi02IHRleHQtcGluay00MDBcIj7YsdmI2KfYqNi3INiz2LHZiti52Kk8L2g0PlxuICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInNwYWNlLXktMiBsZzpzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAge3F1aWNrTGlua3MubWFwKChsaW5rLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgIDxtb3Rpb24ubGlcbiAgICAgICAgICAgICAgICAgIGtleT17bGluay5ocmVmfVxuICAgICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB4OiAtMjAgfX1cbiAgICAgICAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNCwgZGVsYXk6IDAuMiArIGluZGV4ICogMC4xIH19XG4gICAgICAgICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgaHJlZj17bGluay5ocmVmfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpY29uLXRleHQtc3BhY2luZyB0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtcGluay00MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwIGdyb3VwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPGxpbmsuaWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00IG1lbnUtaXRlbS1pY29uIGdyb3VwLWhvdmVyOnNjYWxlLTExMCB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj57bGluay5sYWJlbH08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgPC9tb3Rpb24ubGk+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICB7Lyog2YXYudmE2YjZhdin2Kog2KfZhNiq2YjYp9i12YQgKi99XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMzAgfX1cbiAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNiwgZGVsYXk6IDAuMiB9fVxuICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwibWQ6Y29sLXNwYW4tMVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgbGc6dGV4dC14bCBmb250LWJvbGQgbWItNCBsZzptYi02IHRleHQtcGluay00MDBcIj7YqtmI2KfYtdmEINmF2LnZhtinPC9oND5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zIGxnOnNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImljb24tdGV4dC1zcGFjaW5nIGl0ZW1zLXN0YXJ0IHRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAgICA8TWFwUGluIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1waW5rLTQwMCBtdC0xIG1lbnUtaXRlbS1pY29uXCIgLz5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj7Yp9mE2LnZhtmI2KfZhjwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPtin2YTYrtio2LEg2KfZhNi02YXYp9mE2YrYqdiMINin2YTYqtmC2KfYt9i5INin2YTYs9in2K/Ys9iMINi02KfYsdi5INin2YTYo9mF2YrYsSDZhdi02LnZhNiMINin2YTYrtio2LHYjCDYp9mE2LPYudmI2K/ZitipPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImljb24tdGV4dC1zcGFjaW5nIGl0ZW1zLXN0YXJ0IHRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAgICA8UGhvbmUgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXBpbmstNDAwIG10LTEgbWVudS1pdGVtLWljb25cIiAvPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPtij2LHZgtin2YUg2KfZhNmH2KfYqtmBPC9wPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj7Zgtiz2YUg2KfZhNiq2YHYtdmK2YQ8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPGEgaHJlZj1cInRlbDorOTY2NTk4ODYyNjA5XCIgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtcGluay00MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwXCIgZGlyPVwibHRyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICArOTY2IDU5OCA4NjIgNjA5XG4gICAgICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj7Zgtiz2YUg2KfZhNmB2LPYp9iq2YrZhiDYp9mE2KzYp9mH2LLYqTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8YSBocmVmPVwidGVsOis5NjY1MDE1MDM2MzlcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1waW5rLTQwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDBcIiBkaXI9XCJsdHJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICs5NjYgNTAxIDUwMyA2MzlcbiAgICAgICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaWNvbi10ZXh0LXNwYWNpbmcgaXRlbXMtc3RhcnQgdGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgIDxNYWlsIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1waW5rLTQwMCBtdC0xIG1lbnUtaXRlbS1pY29uXCIgLz5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj7Yp9mE2KjYsdmK2K8g2KfZhNil2YTZg9iq2LHZiNmG2Yo8L3A+XG4gICAgICAgICAgICAgICAgICA8YSBocmVmPVwibWFpbHRvOmluZm9AeWFzbWluYWxzaGFtLmNvbVwiIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXBpbmstNDAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICBpbmZvQHlhc21pbmFsc2hhbS5jb21cbiAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICB7Lyog2KPZiNmC2KfYqiDYp9mE2LnZhdmEICovfVxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDMwIH19XG4gICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYsIGRlbGF5OiAwLjMgfX1cbiAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1kOmNvbC1zcGFuLTIgbGc6Y29sLXNwYW4tMVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgbGc6dGV4dC14bCBmb250LWJvbGQgbWItNCBsZzptYi02IHRleHQtcGluay00MDBcIj7Ys9in2LnYp9iqINin2YTYudmF2YQ8L2g0PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTMgbGc6c3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHNwYWNlLXgtcmV2ZXJzZSB0ZXh0LWdyYXktMzAwIG1iLTRcIj5cbiAgICAgICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXBpbmstNDAwXCIgLz5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPtis2K/ZiNmEINin2YTYudmF2YQ8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAge3dvcmtpbmdIb3Vycy5tYXAoKHNjaGVkdWxlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB4OiAyMCB9fVxuICAgICAgICAgICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxuICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC40LCBkZWxheTogMC40ICsgaW5kZXggKiAwLjEgfX1cbiAgICAgICAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwXCI+e3NjaGVkdWxlLmRheX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2Bmb250LW1lZGl1bSAke3NjaGVkdWxlLnRpbWUgPT09ICfZhdi62YTZgicgPyAndGV4dC1yZWQtNDAwJyA6ICd0ZXh0LXBpbmstNDAwJ31gfT5cbiAgICAgICAgICAgICAgICAgICAge3NjaGVkdWxlLnRpbWV9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiBwLTQgYmctZ3JhZGllbnQtdG8tciBmcm9tLXBpbmstNTAwLzIwIHRvLXJvc2UtNTAwLzIwIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1waW5rLTUwMC8zMFwiPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTMwMCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPEhlYXJ0IGNsYXNzTmFtZT1cInctNCBoLTQgaW5saW5lLWJsb2NrIG1sLTEgdGV4dC1waW5rLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICDZhti52YXZhCDYqNit2Kgg2YjYtNi62YFcbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Lyog2K7YtyDYp9mE2YHYtdmEICovfVxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgc2NhbGVYOiAwIH19XG4gICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgc2NhbGVYOiAxIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC44LCBkZWxheTogMC40IH19XG4gICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci10IGJvcmRlci1ncmF5LTcwMCBteS0xMlwiXG4gICAgICAgID48L21vdGlvbi5kaXY+XG5cbiAgICAgICAgey8qINit2YLZiNmCINin2YTZhti02LEgKi99XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYsIGRlbGF5OiAwLjUgfX1cbiAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBtZDpmbGV4LXJvdyBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIHNwYWNlLXktNCBtZDpzcGFjZS15LTBcIlxuICAgICAgICA+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc20gdGV4dC1jZW50ZXIgbWQ6dGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgPHA+wqkge2N1cnJlbnRZZWFyfSDZitin2LPZhdmK2YYg2KfZhNi02KfZhS4g2KzZhdmK2Lkg2KfZhNit2YLZiNmCINmF2K3ZgdmI2LjYqTwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNiBzcGFjZS14LXJldmVyc2UgdGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICA8TGluayBocmVmPVwiL3ByaXZhY3lcIiBjbGFzc05hbWU9XCJob3Zlcjp0ZXh0LXBpbmstNDAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMFwiPlxuICAgICAgICAgICAgICDYs9mK2KfYs9ipINin2YTYrti12YjYtdmK2KlcbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvdGVybXNcIiBjbGFzc05hbWU9XCJob3Zlcjp0ZXh0LXBpbmstNDAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMFwiPlxuICAgICAgICAgICAgICDYp9mE2LTYsdmI2Lcg2YjYp9mE2KPYrdmD2KfZhVxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTEgc3BhY2UteC1yZXZlcnNlXCI+XG4gICAgICAgICAgICAgIDxzcGFuPti12Y/Zhti5INio2YA8L3NwYW4+XG4gICAgICAgICAgICAgIDxIZWFydCBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtcGluay00MDBcIiAvPlxuICAgICAgICAgICAgICA8c3Bhbj7ZgdmKINiv2YXYtNmCPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZm9vdGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsibW90aW9uIiwiTGluayIsIlNjaXNzb3JzIiwiTWFwUGluIiwiUGhvbmUiLCJNYWlsIiwiQ2xvY2siLCJDYWxlbmRhciIsIlNlYXJjaCIsIlBhbGV0dGUiLCJIZWFydCIsIkZvb3RlciIsImN1cnJlbnRZZWFyIiwiRGF0ZSIsImdldEZ1bGxZZWFyIiwicXVpY2tMaW5rcyIsImhyZWYiLCJsYWJlbCIsImljb24iLCJ3b3JraW5nSG91cnMiLCJkYXkiLCJ0aW1lIiwic29jaWFsTGlua3MiLCJuYW1lIiwic3ZnIiwiY2xhc3NOYW1lIiwiZmlsbCIsInZpZXdCb3giLCJwYXRoIiwiZCIsImZvb3RlciIsImRpdiIsImluaXRpYWwiLCJvcGFjaXR5IiwieSIsIndoaWxlSW5WaWV3IiwidHJhbnNpdGlvbiIsImR1cmF0aW9uIiwidmlld3BvcnQiLCJvbmNlIiwiaDMiLCJwIiwibWFwIiwic29jaWFsIiwiaW5kZXgiLCJhIiwidGFyZ2V0IiwicmVsIiwic2NhbGUiLCJkZWxheSIsImg0IiwidWwiLCJsaW5rIiwibGkiLCJ4Iiwic3BhbiIsImRpciIsInNjaGVkdWxlIiwic2NhbGVYIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Footer.tsx\n"));

/***/ })

});