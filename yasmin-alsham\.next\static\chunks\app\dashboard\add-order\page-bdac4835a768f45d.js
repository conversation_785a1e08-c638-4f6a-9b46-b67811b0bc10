(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[580],{5525:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5820:(e,s,r)=>{Promise.resolve().then(r.bind(r,9254))},9053:(e,s,r)=>{"use strict";r.d(s,{A:()=>o});var a=r(5155),t=r(2115),n=r(5695),i=r(3294),l=r(6408),d=r(5525),c=r(5339);function o(e){let{children:s,requiredRole:r,redirectTo:o="/login"}=e,{user:m,isLoading:u,checkAuth:h}=(0,i.n)(),x=(0,n.useRouter)(),[p,b]=(0,t.useState)(!0);return((0,t.useEffect)(()=>{(async()=>{await h(),b(!1)})()},[h]),(0,t.useEffect)(()=>{if(!p&&!u){if(!m)return void x.push(o);if(r&&m.role!==r)return void x.push("/dashboard");if(!m.is_active)return void x.push("/login")}},[m,p,u,r,x,o]),p||u)?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(d.A,{className:"w-8 h-8 text-white animate-pulse"})}),(0,a.jsx)("div",{className:"w-12 h-12 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 font-medium",children:"جاري التحقق من الصلاحيات..."})]})}):m?r&&m.role!==r?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center max-w-md mx-auto px-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(c.A,{className:"w-8 h-8 text-yellow-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"صلاحيات غير كافية"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة"}),(0,a.jsx)("button",{onClick:()=>x.push("/dashboard"),className:"btn-primary px-6 py-3",children:"العودة إلى لوحة التحكم"})]})}):m.is_active?(0,a.jsx)(a.Fragment,{children:s}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center max-w-md mx-auto px-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(c.A,{className:"w-8 h-8 text-gray-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"حساب غير نشط"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"تم إلغاء تفعيل حسابك. يرجى التواصل مع المدير."}),(0,a.jsx)("button",{onClick:()=>x.push("/login"),className:"btn-primary px-6 py-3",children:"تسجيل الدخول"})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center max-w-md mx-auto px-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(c.A,{className:"w-8 h-8 text-red-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"غير مخول للوصول"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"يجب تسجيل الدخول للوصول إلى هذه الصفحة"}),(0,a.jsx)("button",{onClick:()=>x.push("/login"),className:"btn-primary px-6 py-3",children:"تسجيل الدخول"})]})})}},9254:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>w});var a=r(5155),t=r(2115),n=r(6408),i=r(5695),l=r(6874),d=r.n(l),c=r(3294),o=r(1364),m=r(9137),u=r(9053),h=r(5552),x=r(1297),p=r(2642),b=r(2138),g=r(646),f=r(5339),j=r(1007),v=r(7213),y=r(6140),N=r(1497),_=r(4229);function k(){let{user:e}=(0,c.n)(),{addOrder:s,workers:r}=(0,o.D)(),{t:l,isArabic:u}=(0,m.B)(),k=(0,i.useRouter)(),[w,C]=(0,t.useState)({clientName:"",clientPhone:"",description:"",fabric:"",measurements:{shoulder:"",shoulderCircumference:"",chest:"",waist:"",hips:"",dartLength:"",bodiceLength:"",neckline:"",armpit:"",sleeveLength:"",forearm:"",cuff:"",frontLength:"",backLength:""},price:"",assignedWorker:"",dueDate:"",notes:"",voiceNotes:[],images:[]}),[A,L]=(0,t.useState)(!1),[P,q]=(0,t.useState)(null),D=(e,s)=>{C(r=>({...r,[e]:s}))},S=(e,s)=>{C(r=>({...r,measurements:{...r.measurements,[e]:s}}))},E=async e=>{if(e.preventDefault(),!w.clientName||!w.clientPhone||!w.description||!w.dueDate||!w.price)return void q({type:"error",text:l("fill_required_fields")});L(!0),q(null);try{await new Promise(e=>setTimeout(e,1500)),s({clientName:w.clientName,clientPhone:w.clientPhone,description:w.description,fabric:w.fabric,measurements:{shoulder:w.measurements.shoulder?Number(w.measurements.shoulder):void 0,shoulderCircumference:w.measurements.shoulderCircumference?Number(w.measurements.shoulderCircumference):void 0,chest:w.measurements.chest?Number(w.measurements.chest):void 0,waist:w.measurements.waist?Number(w.measurements.waist):void 0,hips:w.measurements.hips?Number(w.measurements.hips):void 0,dartLength:w.measurements.dartLength?Number(w.measurements.dartLength):void 0,bodiceLength:w.measurements.bodiceLength?Number(w.measurements.bodiceLength):void 0,neckline:w.measurements.neckline?Number(w.measurements.neckline):void 0,armpit:w.measurements.armpit?Number(w.measurements.armpit):void 0,sleeveLength:w.measurements.sleeveLength?Number(w.measurements.sleeveLength):void 0,forearm:w.measurements.forearm?Number(w.measurements.forearm):void 0,cuff:w.measurements.cuff?Number(w.measurements.cuff):void 0,frontLength:w.measurements.frontLength?Number(w.measurements.frontLength):void 0,backLength:w.measurements.backLength?Number(w.measurements.backLength):void 0},price:Number(w.price),assignedWorker:w.assignedWorker||void 0,dueDate:w.dueDate,notes:w.notes||void 0,voiceNotes:w.voiceNotes.length>0?w.voiceNotes:void 0,images:w.images.length>0?w.images:void 0,status:"pending"}),q({type:"success",text:l("order_added_success")}),setTimeout(()=>{k.push("/dashboard/orders")},2e3)}catch(e){console.error("Error adding order:",e),q({type:"error",text:l("order_add_error")})}finally{L(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-8",children:(0,a.jsxs)(d(),{href:"/dashboard",className:"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300",children:[(0,a.jsx)(b.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:l("back_to_dashboard")})]})}),(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold mb-4",children:(0,a.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent",children:l("add_new_order")})}),(0,a.jsx)("p",{className:"text-lg text-gray-600",children:l("add_new_order_description")})]}),P&&(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-8 p-4 rounded-lg flex items-center space-x-3 space-x-reverse max-w-4xl mx-auto ".concat("success"===P.type?"bg-green-50 text-green-800 border border-green-200":"bg-red-50 text-red-800 border border-red-200"),children:["success"===P.type?(0,a.jsx)(g.A,{className:"w-5 h-5 text-green-600"}):(0,a.jsx)(f.A,{className:"w-5 h-5 text-red-600"}),(0,a.jsx)("span",{children:P.text})]}),(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"max-w-4xl mx-auto",children:(0,a.jsxs)("form",{onSubmit:E,className:"space-y-8",children:[(0,a.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(j.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsx)("span",{children:l("basic_information")})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:l("client_name_required")}),(0,a.jsx)("input",{type:"text",value:w.clientName,onChange:e=>D("clientName",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:l("enter_client_name"),required:!0})]}),(0,a.jsx)("div",{children:(0,a.jsx)(p.A,{value:w.clientPhone,onChange:e=>D("clientPhone",e),type:"phone",label:l("phone_required"),placeholder:l("enter_phone"),required:!0,disabled:A})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:l("order_description_required")}),(0,a.jsx)("input",{type:"text",value:w.description,onChange:e=>D("description",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:l("order_description_placeholder"),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:l("fabric_type")}),(0,a.jsx)("input",{type:"text",value:w.fabric,onChange:e=>D("fabric",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:l("fabric_type_placeholder")})]}),(0,a.jsx)("div",{children:(0,a.jsx)(p.A,{value:w.price,onChange:e=>D("price",e),type:"price",label:l("price_sar"),placeholder:"0",required:!0,disabled:A})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:l("responsible_worker")}),(0,a.jsxs)("select",{value:w.assignedWorker,onChange:e=>D("assignedWorker",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",children:[(0,a.jsx)("option",{value:"",children:l("choose_worker")}),r.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.full_name," - ",e.specialty]},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:l("delivery_date_required")}),(0,a.jsx)("input",{type:"date",value:w.dueDate,onChange:e=>D("dueDate",e.target.value),min:new Date().toISOString().split("T")[0],className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",required:!0})]})]})]}),(0,a.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(v.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsx)("span",{children:l("design_images")})]}),(0,a.jsx)(h.A,{images:w.images,onImagesChange:e=>D("images",e),maxImages:10})]}),(0,a.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(y.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsx)("span",{children:l("measurements_cm")})]}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2",children:l("basic_measurements")}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{children:(0,a.jsx)(p.A,{value:w.measurements.shoulder,onChange:e=>S("shoulder",e),type:"measurement",label:l("shoulder"),placeholder:l("cm_placeholder"),disabled:A})}),(0,a.jsx)("div",{children:(0,a.jsx)(p.A,{value:w.measurements.shoulderCircumference,onChange:e=>S("shoulderCircumference",e),type:"measurement",label:l("shoulder_circumference"),placeholder:l("cm_placeholder"),disabled:A})}),(0,a.jsx)("div",{children:(0,a.jsx)(p.A,{value:w.measurements.chest,onChange:e=>S("chest",e),type:"measurement",label:l("chest"),placeholder:l("cm_placeholder"),disabled:A})}),(0,a.jsx)("div",{children:(0,a.jsx)(p.A,{value:w.measurements.waist,onChange:e=>S("waist",e),type:"measurement",label:l("waist"),placeholder:l("cm_placeholder"),disabled:A})}),(0,a.jsx)("div",{children:(0,a.jsx)(p.A,{value:w.measurements.hips,onChange:e=>S("hips",e),type:"measurement",label:l("hips"),placeholder:l("cm_placeholder"),disabled:A})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2",children:l("advanced_measurements")}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{children:(0,a.jsx)(p.A,{value:w.measurements.dartLength,onChange:e=>S("dartLength",e),type:"measurement",label:l("dart_length"),placeholder:l("cm_placeholder"),disabled:A})}),(0,a.jsx)("div",{children:(0,a.jsx)(p.A,{value:w.measurements.bodiceLength,onChange:e=>S("bodiceLength",e),type:"measurement",label:l("bodice_length"),placeholder:l("cm_placeholder"),disabled:A})}),(0,a.jsx)("div",{children:(0,a.jsx)(p.A,{value:w.measurements.neckline,onChange:e=>S("neckline",e),type:"measurement",label:l("neckline"),placeholder:l("cm_placeholder"),disabled:A})}),(0,a.jsx)("div",{children:(0,a.jsx)(p.A,{value:w.measurements.armpit,onChange:e=>S("armpit",e),type:"measurement",label:l("armpit"),placeholder:l("cm_placeholder"),disabled:A})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2",children:l("sleeve_measurements")}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{children:(0,a.jsx)(p.A,{value:w.measurements.sleeveLength,onChange:e=>S("sleeveLength",e),type:"measurement",label:l("sleeve_length"),placeholder:l("cm_placeholder"),disabled:A})}),(0,a.jsx)("div",{children:(0,a.jsx)(p.A,{value:w.measurements.forearm,onChange:e=>S("forearm",e),type:"measurement",label:l("forearm"),placeholder:l("cm_placeholder"),disabled:A})}),(0,a.jsx)("div",{children:(0,a.jsx)(p.A,{value:w.measurements.cuff,onChange:e=>S("cuff",e),type:"measurement",label:l("cuff"),placeholder:l("cm_placeholder"),disabled:A})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2",children:l("length_measurements")}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{children:(0,a.jsx)(p.A,{value:w.measurements.frontLength,onChange:e=>S("frontLength",e),type:"measurement",label:l("front_length"),placeholder:l("cm_placeholder"),disabled:A})}),(0,a.jsx)("div",{children:(0,a.jsx)(p.A,{value:w.measurements.backLength,onChange:e=>S("backLength",e),type:"measurement",label:l("back_length"),placeholder:l("cm_placeholder"),disabled:A})})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(N.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsx)("span",{children:l("additional_notes")})]}),(0,a.jsx)("textarea",{value:w.notes,onChange:e=>D("notes",e.target.value),rows:4,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:l("additional_notes_placeholder")}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:l("voice_notes_optional")}),(0,a.jsx)(x.A,{voiceNotes:w.voiceNotes||[],onVoiceNotesChange:e=>{C(s=>({...s,voiceNotes:e}))},disabled:A})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)("button",{type:"submit",disabled:A,className:"btn-primary py-4 px-8 text-lg disabled:opacity-50 disabled:cursor-not-allowed",children:A?(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[(0,a.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,a.jsx)("span",{children:l("saving")})]}):(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[(0,a.jsx)(_.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:l("save_order")})]})}),(0,a.jsx)(d(),{href:"/dashboard",className:"btn-secondary py-4 px-8 text-lg inline-flex items-center justify-center",children:l("cancel")})]})]})})]})})}function w(){return(0,a.jsx)(u.A,{requiredRole:"admin",children:(0,a.jsx)(k,{})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[165,874,69,312,764,555,441,684,358],()=>s(5820)),_N_E=e.O()}]);