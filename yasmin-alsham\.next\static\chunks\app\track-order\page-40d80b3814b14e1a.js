(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[419],{1364:(e,t,r)=>{"use strict";r.d(t,{D:()=>l});var s=r(5453),a=r(6786);let n=()=>Date.now().toString(36)+Math.random().toString(36).substr(2),l=(0,s.v)()((0,a.Zr)((e,t)=>({appointments:[],orders:[],workers:[],isLoading:!1,error:null,addAppointment:t=>{let r={...t,id:n(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({appointments:[...e.appointments,r],error:null})),console.log("✅ تم إضافة موعد جديد:",r)},updateAppointment:(t,r)=>{e(e=>({appointments:e.appointments.map(e=>e.id===t?{...e,...r,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الموعد:",t)},deleteAppointment:t=>{e(e=>({appointments:e.appointments.filter(e=>e.id!==t),error:null})),console.log("✅ تم حذف الموعد:",t)},getAppointment:e=>t().appointments.find(t=>t.id===e),addOrder:t=>{let r={...t,id:n(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({orders:[...e.orders,r],error:null})),console.log("✅ تم إضافة طلب جديد:",r)},updateOrder:(t,r)=>{e(e=>({orders:e.orders.map(e=>e.id===t?{...e,...r,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الطلب:",t)},deleteOrder:t=>{e(e=>({orders:e.orders.filter(e=>e.id!==t),error:null})),console.log("✅ تم حذف الطلب:",t)},getOrder:e=>t().orders.find(t=>t.id===e),addWorker:t=>{let r={...t,id:n(),role:"worker",is_active:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({workers:[...e.workers,r],error:null}));{let e=JSON.parse(localStorage.getItem("yasmin-users")||"[]");e.push({id:r.id,email:r.email,password:r.password,full_name:r.full_name,role:"worker",is_active:!0}),localStorage.setItem("yasmin-users",JSON.stringify(e))}console.log("✅ تم إضافة عامل جديد:",r)},updateWorker:(t,r)=>{if(e(e=>({workers:e.workers.map(e=>e.id===t?{...e,...r,updatedAt:new Date().toISOString()}:e),error:null})),r.email||r.password||r.full_name){let e=JSON.parse(localStorage.getItem("yasmin-users")||"[]"),s=e.findIndex(e=>e.id===t);-1!==s&&(r.email&&(e[s].email=r.email),r.password&&(e[s].password=r.password),r.full_name&&(e[s].full_name=r.full_name),localStorage.setItem("yasmin-users",JSON.stringify(e)))}console.log("✅ تم تحديث العامل:",t)},deleteWorker:t=>{e(e=>({workers:e.workers.filter(e=>e.id!==t),error:null}));{let e=JSON.parse(localStorage.getItem("yasmin-users")||"[]").filter(e=>e.id!==t);localStorage.setItem("yasmin-users",JSON.stringify(e))}console.log("✅ تم حذف العامل:",t)},getWorker:e=>t().workers.find(t=>t.id===e),startOrderWork:(t,r)=>{e(e=>({orders:e.orders.map(e=>e.id===t&&e.assignedWorker===r?{...e,status:"in_progress",updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم بدء العمل في الطلب:",t)},completeOrder:function(t,r){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];e(e=>({orders:e.orders.map(e=>e.id===t&&e.assignedWorker===r?{...e,status:"completed",completedImages:s.length>0?s:void 0,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم إنهاء الطلب:",t)},clearError:()=>{e({error:null})},loadData:()=>{e({isLoading:!0}),e({isLoading:!1})},getStats:()=>{let e=t();return{totalAppointments:e.appointments.length,totalOrders:e.orders.length,totalWorkers:e.workers.length,pendingAppointments:e.appointments.filter(e=>"pending"===e.status).length,activeOrders:e.orders.filter(e=>["pending","in_progress"].includes(e.status)).length,completedOrders:e.orders.filter(e=>"completed"===e.status).length,totalRevenue:e.orders.filter(e=>"completed"===e.status).reduce((e,t)=>e+t.price,0)}}}),{name:"yasmin-data-storage",partialize:e=>({appointments:e.appointments,orders:e.orders,workers:e.workers})}))},2121:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var s=r(5155),a=r(2115),n=r(6408),l=r(4186),i=r(7108),d=r(646),o=r(2138),c=r(9420),m=r(7924),x=r(5339),p=r(1497),u=r(6874),g=r.n(u),h=r(1364),b=r(2642);function j(){let[e,t]=(0,a.useState)(""),[r,u]=(0,a.useState)("order"),[j,f]=(0,a.useState)(null),[N,y]=(0,a.useState)(!1),[v,w]=(0,a.useState)(null),{orders:k,workers:S}=(0,h.D)(),A=async t=>{if(t.preventDefault(),!e.trim())return void w("order"===r?"يرجى إدخال رقم الطلب":"يرجى إدخال رقم الهاتف");y(!0),w(null),f(null);try{await new Promise(e=>setTimeout(e,1e3));let t=null;if(t="order"===r?k.find(t=>t.id.toLowerCase().includes(e.toLowerCase())):k.find(t=>t.clientPhone.includes(e))){let e={order_number:t.id,client_name:t.clientName,client_phone:t.clientPhone,dress_type:t.description,order_date:t.createdAt,due_date:t.dueDate,status:t.status,estimated_price:t.price,progress_percentage:_(t.status),notes:t.notes,fabric:t.fabric,measurements:t.measurements};f(e)}else w("order"===r?"لم يتم العثور على طلب بهذا الرقم. يرجى التأكد من رقم الطلب والمحاولة مرة أخرى.":"لم يتم العثور على طلبات مرتبطة بهذا الرقم. يرجى التأكد من رقم الهاتف والمحاولة مرة أخرى.")}catch(e){w("حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.")}finally{y(!1)}},_=e=>({pending:10,in_progress:50,completed:90,delivered:100})[e]||0,O=e=>{let t={pending:{label:"في الانتظار",color:"text-yellow-600",bgColor:"bg-yellow-100",icon:l.A},assigned:{label:"تم التعيين",color:"text-blue-600",bgColor:"bg-blue-100",icon:i.A},in_progress:{label:"قيد التنفيذ",color:"text-purple-600",bgColor:"bg-purple-100",icon:i.A},completed:{label:"مكتمل",color:"text-green-600",bgColor:"bg-green-100",icon:d.A},delivered:{label:"تم التسليم",color:"text-green-700",bgColor:"bg-green-200",icon:d.A}};return t[e]||t.pending},D=e=>new Date(e).toLocaleDateString("ar-US",{year:"numeric",month:"long",day:"numeric"}),I=e=>({shoulder:"الكتف",shoulderCircumference:"دوران الكتف",chest:"الصدر",waist:"الخصر",hips:"الأرداف",dartLength:"طول البنس",bodiceLength:"طول الصدرية",neckline:"فتحة الصدر",armpit:"الإبط",sleeveLength:"طول الكم",forearm:"الزند",cuff:"الأسوارة",frontLength:"طول الأمام",backLength:"طول الخلف"})[e]||e;return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)(g(),{href:"/",className:"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300 group",children:[(0,s.jsx)(o.A,{className:"w-5 h-5 group-hover:translate-x-1 transition-transform duration-300"}),(0,s.jsx)("span",{className:"font-medium",children:"العودة للصفحة الرئيسية"})]})}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-12",children:[(0,s.jsx)("h1",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6",children:(0,s.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent",children:"استعلام عن الطلب"})}),(0,s.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"تابعي حالة طلبك في أي وقت. أدخلي رقم الطلب لمعرفة مرحلة التفصيل والموعد المتوقع للتسليم"})]}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100 mb-8",children:[(0,s.jsxs)("form",{onSubmit:A,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-lg font-medium text-gray-700 mb-4 text-center",children:"اختاري طريقة البحث"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,s.jsxs)("button",{type:"button",onClick:()=>{u("order"),t(""),w(null)},className:"p-4 rounded-xl border-2 transition-all duration-300 ".concat("order"===r?"border-pink-500 bg-pink-50 text-pink-700":"border-gray-300 bg-white text-gray-700 hover:border-pink-300"),children:[(0,s.jsx)(i.A,{className:"w-6 h-6 mx-auto mb-2"}),(0,s.jsx)("span",{className:"font-medium",children:"رقم الطلب"})]}),(0,s.jsxs)("button",{type:"button",onClick:()=>{u("phone"),t(""),w(null)},className:"p-4 rounded-xl border-2 transition-all duration-300 ".concat("phone"===r?"border-pink-500 bg-pink-50 text-pink-700":"border-gray-300 bg-white text-gray-700 hover:border-pink-300"),children:[(0,s.jsx)(c.A,{className:"w-6 h-6 mx-auto mb-2"}),(0,s.jsx)("span",{className:"font-medium",children:"رقم الهاتف"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-lg font-medium text-gray-700 mb-4 text-center",children:"order"===r?"أدخلي رقم الطلب":"أدخلي رقم الهاتف"}),(0,s.jsxs)("div",{className:"relative",children:["phone"===r?(0,s.jsx)(b.A,{value:e,onChange:t,type:"phone",placeholder:"مثال: 0912345678",className:"px-6 py-4 text-lg rounded-xl text-center",disabled:N}):(0,s.jsx)("input",{type:"text",value:e,onChange:e=>t(e.target.value),className:"w-full px-6 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300 text-center",placeholder:"مثال: order_123",disabled:N}),(0,s.jsx)(m.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-gray-400"})]})]}),(0,s.jsx)("button",{type:"submit",disabled:N,className:"w-full btn-primary py-4 text-lg disabled:opacity-50 disabled:cursor-not-allowed",children:N?(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[(0,s.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,s.jsx)("span",{children:"جاري البحث..."})]}):(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[(0,s.jsx)(m.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"البحث عن الطلب"})]})})]}),v&&(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mt-6 p-4 bg-red-50 text-red-800 border border-red-200 rounded-lg flex items-center space-x-3 space-x-reverse",children:[(0,s.jsx)(x.A,{className:"w-5 h-5 text-red-600"}),(0,s.jsx)("span",{children:v})]})]}),j&&(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"space-y-8",children:[(0,s.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100",children:[(0,s.jsxs)("h2",{className:"text-2xl font-bold text-gray-800 mb-6 flex items-center space-x-3 space-x-reverse",children:[(0,s.jsx)(i.A,{className:"w-6 h-6 text-pink-600"}),(0,s.jsx)("span",{children:"معلومات الطلب"})]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"رقم الطلب"}),(0,s.jsx)("p",{className:"text-lg font-bold text-pink-600",children:j.order_number})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"اسم العميلة"}),(0,s.jsx)("p",{className:"text-lg font-medium text-gray-800",children:j.client_name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"رقم الهاتف"}),(0,s.jsx)("p",{className:"text-lg font-medium text-gray-800",children:j.client_phone})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"وصف الطلب"}),(0,s.jsx)("p",{className:"text-lg font-medium text-gray-800",children:j.dress_type})]}),j.fabric&&(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"نوع القماش"}),(0,s.jsx)("p",{className:"text-lg font-medium text-gray-800",children:j.fabric})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"تاريخ الطلب"}),(0,s.jsx)("p",{className:"text-lg font-medium text-gray-800",children:D(j.order_date)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"موعد التسليم المتوقع"}),(0,s.jsx)("p",{className:"text-lg font-medium text-gray-800",children:D(j.due_date)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"السعر"}),(0,s.jsxs)("p",{className:"text-lg font-bold text-green-600",children:[j.estimated_price.toLocaleString()," ل.س"]})]}),j.notes&&(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"ملاحظات"}),(0,s.jsx)("p",{className:"text-lg font-medium text-gray-800",children:j.notes})]})]})]}),(0,s.jsxs)("div",{className:"mt-6 p-4 bg-gradient-to-r from-pink-50 to-rose-50 rounded-xl border border-pink-200",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full ".concat(O(j.status).bgColor," flex items-center justify-center"),children:a.createElement(O(j.status).icon,{className:"w-5 h-5 ".concat(O(j.status).color)})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-gray-800",children:"حالة الطلب"}),(0,s.jsx)("p",{className:"text-lg font-bold ".concat(O(j.status).color),children:O(j.status).label})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"نسبة الإنجاز"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-pink-600",children:[j.progress_percentage,"%"]})]})]}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,s.jsx)(n.P.div,{initial:{width:0},animate:{width:"".concat(j.progress_percentage,"%")},transition:{duration:1,delay:.5},className:"bg-gradient-to-r from-pink-500 to-rose-500 h-3 rounded-full"})})}),"completed"===j.status&&(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.8},className:"mt-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-200",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,s.jsx)(d.A,{className:"w-6 h-6 text-green-600 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-green-800 mb-1",children:"طلبك جاهز للاستلام!"}),(0,s.jsx)("p",{className:"text-sm text-green-700",children:"مكتمل - بإمكانك الحضور واستلام الفستان في أي وقت تريدينه"})]})]})})]})]}),j.measurements&&Object.keys(j.measurements).length>0&&(0,s.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100",children:[(0,s.jsxs)("h3",{className:"text-2xl font-bold text-gray-800 mb-6 flex items-center space-x-3 space-x-reverse",children:[(0,s.jsx)(i.A,{className:"w-6 h-6 text-pink-600"}),(0,s.jsx)("span",{children:"المقاسات"})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-4",children:Object.entries(j.measurements).map(e=>{let[t,r]=e;return(0,s.jsxs)("div",{className:"p-4 bg-gradient-to-r from-pink-50 to-rose-50 rounded-xl border border-pink-100",children:[(0,s.jsx)("span",{className:"text-sm text-gray-500 block mb-1",children:I(t)}),(0,s.jsxs)("span",{className:"text-lg font-medium text-gray-800",children:[r," سم"]})]},t)})})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-pink-50 to-purple-50 rounded-2xl p-8 border border-pink-100",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-4 text-center",children:"هل لديك استفسار حول طلبك؟"}),(0,s.jsx)("p",{className:"text-gray-600 text-center mb-6",children:"لا تترددي في التواصل معنا للحصول على مزيد من التفاصيل"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsxs)("a",{href:"tel:+966598862609",className:"btn-primary inline-flex items-center justify-center space-x-2 space-x-reverse",children:[(0,s.jsx)(c.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"اتصلي بنا"})]}),(0,s.jsxs)("a",{href:"https://wa.me/+966598862609?text=استفسار عن الطلب رقم: ".concat(j.order_number),target:"_blank",rel:"noopener noreferrer",className:"btn-secondary inline-flex items-center justify-center space-x-2 space-x-reverse",children:[(0,s.jsx)(p.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"واتساب"})]})]})]})]}),!j&&!N&&(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-4 text-center",children:"كيفية العثور على رقم طلبك"}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(p.A,{className:"w-6 h-6 text-white"})}),(0,s.jsx)("h4",{className:"font-bold text-gray-800 mb-2",children:"رسالة التأكيد"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"ستجدين رقم الطلب في رسالة التأكيد التي وصلتك عبر الواتساب أو الرسائل النصية"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(c.A,{className:"w-6 h-6 text-white"})}),(0,s.jsx)("h4",{className:"font-bold text-gray-800 mb-2",children:"باستخدام رقم الهاتف"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"إذا لم تجدي رقم الطلب، يمكنك البحث باستخدام رقم هاتفك المسجل لدينا"})]})]})]})]})]})})}},2642:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var s=r(5155),a=r(2115),n=r(5339);let l=e=>/^\d*\.?\d*$/.test(e),i=e=>/^\d*$/.test(e),d=e=>/^(\+)?\d*$/.test(e),o=e=>e.replace(/[^\d.]/g,""),c=e=>e.replace(/[^\d]/g,""),m=e=>e.startsWith("+")?"+"+e.slice(1).replace(/[^\d]/g,""):e.replace(/[^\d]/g,""),x=e=>{let t=parseFloat(e);return!isNaN(t)&&t>0},p=e=>{let t=parseFloat(e);return!isNaN(t)&&t>0},u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"ar",r={ar:{measurement:"يرجى إدخال رقم صحيح للمقاس",price:"يرجى إدخال سعر صحيح",phone:"يرجى إدخال رقم هاتف صحيح",orderNumber:"يرجى إدخال رقم طلب صحيح",numeric:"يرجى إدخال أرقام فقط",positive:"يرجى إدخال رقم أكبر من الصفر"},en:{measurement:"Please enter a valid measurement",price:"Please enter a valid price",phone:"Please enter a valid phone number",orderNumber:"Please enter a valid order number",numeric:"Please enter numbers only",positive:"Please enter a number greater than zero"}};return r[t][e]||r[t].numeric},g=(e,t,r,s)=>{let a=e,n=!0,g=null;switch(t){case"measurement":x(a=o(e))||""===a||""===a||(g=u("measurement"));break;case"price":p(a=o(e))||""===a||""===a||(g=u("price"));break;case"phone":d(a=m(e))||(g=u("phone"));break;case"orderNumber":i(a=c(e))||(g=u("orderNumber"));break;case"integer":i(a=c(e))||(g=u("numeric"));break;case"decimal":l(a=o(e))||(g=u("numeric"))}r(a),s&&s(g)};function h(e){let{value:t,onChange:r,type:l,placeholder:i,className:d="",disabled:o=!1,required:c=!1,label:m,id:x}=e,[p,u]=(0,a.useState)(null),h="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors duration-200 ".concat(p?"border-red-500 bg-red-50":"border-gray-300"," ").concat(o?"bg-gray-100 cursor-not-allowed":""," ").concat(d);return(0,s.jsxs)("div",{className:"space-y-2",children:[m&&(0,s.jsxs)("label",{htmlFor:x,className:"block text-sm font-medium text-gray-700",children:[m,c&&(0,s.jsx)("span",{className:"text-red-500 mr-1",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:x,type:"text",value:t,onChange:e=>{g(e.target.value,l,r,u)},placeholder:i,className:h,disabled:o,required:c,inputMode:"phone"===l?"tel":"numeric",autoComplete:"phone"===l?"tel":"off"}),p&&(0,s.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2",children:(0,s.jsx)(n.A,{className:"w-5 h-5 text-red-500"})})]}),p&&(0,s.jsxs)("p",{className:"text-sm text-red-600 flex items-center space-x-1 space-x-reverse",children:[(0,s.jsx)(n.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:p})]})]})}},8728:(e,t,r)=>{Promise.resolve().then(r.bind(r,2121))}},e=>{var t=t=>e(e.s=t);e.O(0,[165,874,411,441,684,358],()=>t(8728)),_N_E=e.O()}]);