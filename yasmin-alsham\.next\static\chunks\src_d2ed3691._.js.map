{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/store/authStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\n\n// تعريف نوع المستخدم\nexport interface AuthUser {\n  id: string\n  email: string\n  full_name: string\n  role: 'admin' | 'worker'\n  is_active: boolean\n  created_at: string\n  updated_at: string\n  token?: string\n}\n\ninterface AuthState {\n  user: AuthUser | null\n  isLoading: boolean\n  error: string | null\n\n  // Actions\n  signIn: (email: string, password: string) => Promise<boolean>\n  signOut: () => Promise<void>\n  setUser: (user: AuthUser | null) => void\n  clearError: () => void\n  checkAuth: () => Promise<void>\n  isAuthenticated: () => boolean\n}\n\n// بيانات المستخدمين الافتراضية (سيتم استبدالها بنظام إدارة العمال)\nconst getStoredUsers = () => {\n  if (typeof window === 'undefined') return []\n\n  const stored = localStorage.getItem('yasmin-users')\n  if (stored) {\n    return JSON.parse(stored)\n  }\n\n  // المستخدمين الافتراضيين\n  const defaultUsers = [\n    {\n      id: '1',\n      email: '<EMAIL>',\n      password: 'admin123',\n      full_name: 'مدير النظام',\n      role: 'admin' as const,\n      is_active: true\n    }\n  ]\n\n  localStorage.setItem('yasmin-users', JSON.stringify(defaultUsers))\n  return defaultUsers\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      isLoading: false,\n      error: null,\n\n      signIn: async (email: string, password: string) => {\n        set({ isLoading: true, error: null })\n\n        try {\n          console.log('🔐 بدء عملية تسجيل الدخول...', { email })\n\n          // محاكاة تأخير الشبكة\n          await new Promise(resolve => setTimeout(resolve, 1500))\n\n          // البحث عن المستخدم في البيانات المحفوظة\n          const users = getStoredUsers()\n          const foundUser = users.find(\n            user => user.email.toLowerCase() === email.toLowerCase() && user.password === password\n          )\n\n          if (foundUser) {\n            console.log('✅ تم العثور على المستخدم:', foundUser.full_name)\n\n            const user: AuthUser = {\n              id: foundUser.id,\n              email: foundUser.email,\n              full_name: foundUser.full_name,\n              role: foundUser.role,\n              is_active: foundUser.is_active,\n              created_at: new Date().toISOString(),\n              updated_at: new Date().toISOString(),\n              token: `demo-token-${foundUser.id}-${Date.now()}`\n            }\n\n            // حفظ في localStorage أولاً\n            if (typeof window !== 'undefined') {\n              localStorage.setItem('yasmin-auth-user', JSON.stringify(user))\n              console.log('💾 تم حفظ المستخدم في localStorage')\n            }\n\n            // تحديث حالة المتجر\n            set({ user, isLoading: false, error: null })\n            console.log('🎉 تم تسجيل الدخول بنجاح!')\n\n            return true\n          } else {\n            console.log('❌ بيانات تسجيل الدخول غير صحيحة')\n            set({\n              error: 'بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور.',\n              isLoading: false\n            })\n            return false\n          }\n        } catch (error) {\n          console.error('💥 خطأ في تسجيل الدخول:', error)\n          set({ error: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.', isLoading: false })\n          return false\n        }\n      },\n\n      signOut: async () => {\n        set({ isLoading: true })\n\n        try {\n          // محاكاة تأخير تسجيل الخروج\n          await new Promise(resolve => setTimeout(resolve, 500))\n\n          // مسح البيانات من localStorage\n          if (typeof window !== 'undefined') {\n            localStorage.removeItem('yasmin-auth-user')\n          }\n\n          set({ user: null, isLoading: false, error: null })\n        } catch (error) {\n          console.error('خطأ في تسجيل الخروج:', error)\n          set({ isLoading: false, error: 'خطأ في تسجيل الخروج' })\n        }\n      },\n\n      setUser: (user: AuthUser | null) => {\n        set({ user })\n\n        // تحديث localStorage\n        if (typeof window !== 'undefined') {\n          if (user) {\n            localStorage.setItem('yasmin-auth-user', JSON.stringify(user))\n          } else {\n            localStorage.removeItem('yasmin-auth-user')\n          }\n        }\n      },\n\n      clearError: () => {\n        set({ error: null })\n      },\n\n      checkAuth: async () => {\n        set({ isLoading: true })\n\n        try {\n          // التحقق من وجود مستخدم محفوظ في localStorage\n          if (typeof window !== 'undefined') {\n            const savedUser = localStorage.getItem('yasmin-auth-user')\n            if (savedUser) {\n              const user = JSON.parse(savedUser) as AuthUser\n              set({ user, isLoading: false })\n              return\n            }\n          }\n\n          set({ user: null, isLoading: false })\n        } catch (error) {\n          console.error('خطأ في التحقق من المصادقة:', error)\n          set({ user: null, isLoading: false })\n        }\n      },\n\n      isAuthenticated: () => {\n        const state = get()\n        return state.user !== null && state.user.is_active\n      }\n    }),\n    {\n      name: 'yasmin-auth-storage',\n      partialize: (state) => ({ user: state.user })\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA4BA,mEAAmE;AACnE,MAAM,iBAAiB;IACrB,uCAAmC;;IAAQ;IAE3C,MAAM,SAAS,aAAa,OAAO,CAAC;IACpC,IAAI,QAAQ;QACV,OAAO,KAAK,KAAK,CAAC;IACpB;IAEA,yBAAyB;IACzB,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,WAAW;YACX,MAAM;YACN,WAAW;QACb;KACD;IAED,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;IACpD,OAAO;AACT;AAEO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,WAAW;QACX,OAAO;QAEP,QAAQ,OAAO,OAAe;YAC5B,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,QAAQ,GAAG,CAAC,gCAAgC;oBAAE;gBAAM;gBAEpD,sBAAsB;gBACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,yCAAyC;gBACzC,MAAM,QAAQ;gBACd,MAAM,YAAY,MAAM,IAAI,CAC1B,CAAA,OAAQ,KAAK,KAAK,CAAC,WAAW,OAAO,MAAM,WAAW,MAAM,KAAK,QAAQ,KAAK;gBAGhF,IAAI,WAAW;oBACb,QAAQ,GAAG,CAAC,6BAA6B,UAAU,SAAS;oBAE5D,MAAM,OAAiB;wBACrB,IAAI,UAAU,EAAE;wBAChB,OAAO,UAAU,KAAK;wBACtB,WAAW,UAAU,SAAS;wBAC9B,MAAM,UAAU,IAAI;wBACpB,WAAW,UAAU,SAAS;wBAC9B,YAAY,IAAI,OAAO,WAAW;wBAClC,YAAY,IAAI,OAAO,WAAW;wBAClC,OAAO,CAAC,WAAW,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;oBACnD;oBAEA,4BAA4B;oBAC5B,wCAAmC;wBACjC,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;wBACxD,QAAQ,GAAG,CAAC;oBACd;oBAEA,oBAAoB;oBACpB,IAAI;wBAAE;wBAAM,WAAW;wBAAO,OAAO;oBAAK;oBAC1C,QAAQ,GAAG,CAAC;oBAEZ,OAAO;gBACT,OAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ,IAAI;wBACF,OAAO;wBACP,WAAW;oBACb;oBACA,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,IAAI;oBAAE,OAAO;oBAA8C,WAAW;gBAAM;gBAC5E,OAAO;YACT;QACF;QAEA,SAAS;YACP,IAAI;gBAAE,WAAW;YAAK;YAEtB,IAAI;gBACF,4BAA4B;gBAC5B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,+BAA+B;gBAC/B,wCAAmC;oBACjC,aAAa,UAAU,CAAC;gBAC1B;gBAEA,IAAI;oBAAE,MAAM;oBAAM,WAAW;oBAAO,OAAO;gBAAK;YAClD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,IAAI;oBAAE,WAAW;oBAAO,OAAO;gBAAsB;YACvD;QACF;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;YAAK;YAEX,qBAAqB;YACrB,wCAAmC;gBACjC,IAAI,MAAM;oBACR,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBAC1D,OAAO;oBACL,aAAa,UAAU,CAAC;gBAC1B;YACF;QACF;QAEA,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,WAAW;YACT,IAAI;gBAAE,WAAW;YAAK;YAEtB,IAAI;gBACF,8CAA8C;gBAC9C,wCAAmC;oBACjC,MAAM,YAAY,aAAa,OAAO,CAAC;oBACvC,IAAI,WAAW;wBACb,MAAM,OAAO,KAAK,KAAK,CAAC;wBACxB,IAAI;4BAAE;4BAAM,WAAW;wBAAM;wBAC7B;oBACF;gBACF;gBAEA,IAAI;oBAAE,MAAM;oBAAM,WAAW;gBAAM;YACrC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,IAAI;oBAAE,MAAM;oBAAM,WAAW;gBAAM;YACrC;QACF;QAEA,iBAAiB;YACf,MAAM,QAAQ;YACd,OAAO,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,CAAC,SAAS;QACpD;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YAAE,MAAM,MAAM,IAAI;QAAC,CAAC;AAC9C", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/store/dataStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\n\n// تعريف أنواع البيانات\nexport interface Appointment {\n  id: string\n  clientName: string\n  clientPhone: string\n  appointmentDate: string\n  appointmentTime: string\n  notes?: string\n  status: 'pending' | 'confirmed' | 'completed' | 'cancelled'\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Order {\n  id: string\n  clientName: string\n  clientPhone: string\n  description: string\n  fabric: string\n  measurements: {\n    // المقاسات الأساسية\n    shoulder?: number // الكتف\n    shoulderCircumference?: number // دوران الكتف\n    chest?: number // الصدر\n    waist?: number // الخصر\n    hips?: number // الأرداف\n\n    // مقاسات التفصيل المتقدمة\n    dartLength?: number // طول البنس\n    bodiceLength?: number // طول الصدرية\n    neckline?: number // فتحة الصدر\n    armpit?: number // الإبط\n\n    // مقاسات الأكمام\n    sleeveLength?: number // طول الكم\n    forearm?: number // الزند\n    cuff?: number // الأسوارة\n\n    // مقاسات الطول\n    frontLength?: number // طول الأمام\n    backLength?: number // طول الخلف\n\n    // للتوافق مع النظام القديم (سيتم إزالتها لاحقاً)\n    length?: number // طول الفستان (قديم)\n    shoulders?: number // عرض الكتف (قديم)\n    sleeves?: number // طول الأكمام (قديم)\n  }\n  price: number\n  status: 'pending' | 'in_progress' | 'completed' | 'delivered' | 'cancelled'\n  assignedWorker?: string\n  dueDate: string\n  notes?: string\n  voiceNotes?: Array<{\n    id: string\n    data: string\n    timestamp: number\n    duration?: number\n  }> // ملاحظات صوتية متعددة\n  images?: string[] // مصفوفة من base64 strings للصور\n  completedImages?: string[] // صور العمل المكتمل (للعمال فقط)\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Worker {\n  id: string\n  email: string\n  password: string\n  full_name: string\n  phone: string\n  specialty: string\n  role: 'worker'\n  is_active: boolean\n  createdAt: string\n  updatedAt: string\n}\n\ninterface DataState {\n  // البيانات\n  appointments: Appointment[]\n  orders: Order[]\n  workers: Worker[]\n  \n  // حالة التحميل\n  isLoading: boolean\n  error: string | null\n\n  // إدارة المواعيد\n  addAppointment: (appointment: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>) => void\n  updateAppointment: (id: string, updates: Partial<Appointment>) => void\n  deleteAppointment: (id: string) => void\n  getAppointment: (id: string) => Appointment | undefined\n\n  // إدارة الطلبات\n  addOrder: (order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) => void\n  updateOrder: (id: string, updates: Partial<Order>) => void\n  deleteOrder: (id: string) => void\n  getOrder: (id: string) => Order | undefined\n\n  // دوال خاصة للعمال\n  startOrderWork: (orderId: string, workerId: string) => void\n  completeOrder: (orderId: string, workerId: string, completedImages?: string[]) => void\n\n  // إدارة العمال\n  addWorker: (worker: Omit<Worker, 'id' | 'createdAt' | 'updatedAt' | 'role'>) => void\n  updateWorker: (id: string, updates: Partial<Worker>) => void\n  deleteWorker: (id: string) => void\n  getWorker: (id: string) => Worker | undefined\n\n  // وظائف مساعدة\n  clearError: () => void\n  loadData: () => void\n  \n  // إحصائيات\n  getStats: () => {\n    totalAppointments: number\n    totalOrders: number\n    totalWorkers: number\n    pendingAppointments: number\n    activeOrders: number\n    completedOrders: number\n    totalRevenue: number\n  }\n}\n\n// توليد ID فريد\nconst generateId = () => {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2)\n}\n\nexport const useDataStore = create<DataState>()(\n  persist(\n    (set, get) => ({\n      // البيانات الأولية\n      appointments: [],\n      orders: [],\n      workers: [],\n      isLoading: false,\n      error: null,\n\n      // إدارة المواعيد\n      addAppointment: (appointmentData) => {\n        const appointment: Appointment = {\n          ...appointmentData,\n          id: generateId(),\n          status: 'pending',\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        }\n\n        set((state) => ({\n          appointments: [...state.appointments, appointment],\n          error: null\n        }))\n\n        console.log('✅ تم إضافة موعد جديد:', appointment)\n      },\n\n      updateAppointment: (id, updates) => {\n        set((state) => ({\n          appointments: state.appointments.map(appointment =>\n            appointment.id === id\n              ? { ...appointment, ...updates, updatedAt: new Date().toISOString() }\n              : appointment\n          ),\n          error: null\n        }))\n\n        console.log('✅ تم تحديث الموعد:', id)\n      },\n\n      deleteAppointment: (id) => {\n        set((state) => ({\n          appointments: state.appointments.filter(appointment => appointment.id !== id),\n          error: null\n        }))\n\n        console.log('✅ تم حذف الموعد:', id)\n      },\n\n      getAppointment: (id) => {\n        const state = get()\n        return state.appointments.find(appointment => appointment.id === id)\n      },\n\n      // إدارة الطلبات\n      addOrder: (orderData) => {\n        const order: Order = {\n          ...orderData,\n          id: generateId(),\n          status: 'pending',\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        }\n\n        set((state) => ({\n          orders: [...state.orders, order],\n          error: null\n        }))\n\n        console.log('✅ تم إضافة طلب جديد:', order)\n      },\n\n      updateOrder: (id, updates) => {\n        set((state) => ({\n          orders: state.orders.map(order =>\n            order.id === id\n              ? { ...order, ...updates, updatedAt: new Date().toISOString() }\n              : order\n          ),\n          error: null\n        }))\n\n        console.log('✅ تم تحديث الطلب:', id)\n      },\n\n      deleteOrder: (id) => {\n        set((state) => ({\n          orders: state.orders.filter(order => order.id !== id),\n          error: null\n        }))\n\n        console.log('✅ تم حذف الطلب:', id)\n      },\n\n      getOrder: (id) => {\n        const state = get()\n        return state.orders.find(order => order.id === id)\n      },\n\n      // إدارة العمال\n      addWorker: (workerData) => {\n        const worker: Worker = {\n          ...workerData,\n          id: generateId(),\n          role: 'worker',\n          is_active: true,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        }\n\n        set((state) => ({\n          workers: [...state.workers, worker],\n          error: null\n        }))\n\n        // إضافة العامل إلى نظام المصادقة\n        if (typeof window !== 'undefined') {\n          const users = JSON.parse(localStorage.getItem('yasmin-users') || '[]')\n          users.push({\n            id: worker.id,\n            email: worker.email,\n            password: worker.password,\n            full_name: worker.full_name,\n            role: 'worker',\n            is_active: true\n          })\n          localStorage.setItem('yasmin-users', JSON.stringify(users))\n        }\n\n        console.log('✅ تم إضافة عامل جديد:', worker)\n      },\n\n      updateWorker: (id, updates) => {\n        set((state) => ({\n          workers: state.workers.map(worker =>\n            worker.id === id\n              ? { ...worker, ...updates, updatedAt: new Date().toISOString() }\n              : worker\n          ),\n          error: null\n        }))\n\n        // تحديث بيانات المصادقة إذا تم تغيير البريد أو كلمة المرور\n        if (updates.email || updates.password || updates.full_name) {\n          if (typeof window !== 'undefined') {\n            const users = JSON.parse(localStorage.getItem('yasmin-users') || '[]')\n            const userIndex = users.findIndex((user: any) => user.id === id)\n            if (userIndex !== -1) {\n              if (updates.email) users[userIndex].email = updates.email\n              if (updates.password) users[userIndex].password = updates.password\n              if (updates.full_name) users[userIndex].full_name = updates.full_name\n              localStorage.setItem('yasmin-users', JSON.stringify(users))\n            }\n          }\n        }\n\n        console.log('✅ تم تحديث العامل:', id)\n      },\n\n      deleteWorker: (id) => {\n        set((state) => ({\n          workers: state.workers.filter(worker => worker.id !== id),\n          error: null\n        }))\n\n        // حذف من نظام المصادقة\n        if (typeof window !== 'undefined') {\n          const users = JSON.parse(localStorage.getItem('yasmin-users') || '[]')\n          const filteredUsers = users.filter((user: any) => user.id !== id)\n          localStorage.setItem('yasmin-users', JSON.stringify(filteredUsers))\n        }\n\n        console.log('✅ تم حذف العامل:', id)\n      },\n\n      getWorker: (id) => {\n        const state = get()\n        return state.workers.find(worker => worker.id === id)\n      },\n\n      // دوال خاصة للعمال\n      startOrderWork: (orderId, workerId) => {\n        set((state) => ({\n          orders: state.orders.map(order =>\n            order.id === orderId && order.assignedWorker === workerId\n              ? { ...order, status: 'in_progress', updatedAt: new Date().toISOString() }\n              : order\n          ),\n          error: null\n        }))\n\n        console.log('✅ تم بدء العمل في الطلب:', orderId)\n      },\n\n      completeOrder: (orderId, workerId, completedImages = []) => {\n        set((state) => ({\n          orders: state.orders.map(order =>\n            order.id === orderId && order.assignedWorker === workerId\n              ? {\n                  ...order,\n                  status: 'completed',\n                  completedImages: completedImages.length > 0 ? completedImages : undefined,\n                  updatedAt: new Date().toISOString()\n                }\n              : order\n          ),\n          error: null\n        }))\n\n        console.log('✅ تم إنهاء الطلب:', orderId)\n      },\n\n      // وظائف مساعدة\n      clearError: () => {\n        set({ error: null })\n      },\n\n      loadData: () => {\n        set({ isLoading: true })\n        // البيانات محفوظة تلقائياً بواسطة persist middleware\n        set({ isLoading: false })\n      },\n\n      // إحصائيات\n      getStats: () => {\n        const state = get()\n        return {\n          totalAppointments: state.appointments.length,\n          totalOrders: state.orders.length,\n          totalWorkers: state.workers.length,\n          pendingAppointments: state.appointments.filter(a => a.status === 'pending').length,\n          activeOrders: state.orders.filter(o => ['pending', 'in_progress'].includes(o.status)).length,\n          completedOrders: state.orders.filter(o => o.status === 'completed').length,\n          totalRevenue: state.orders\n            .filter(o => o.status === 'completed')\n            .reduce((sum, order) => sum + order.price, 0)\n        }\n      }\n    }),\n    {\n      name: 'yasmin-data-storage',\n      partialize: (state) => ({\n        appointments: state.appointments,\n        orders: state.orders,\n        workers: state.workers\n      })\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA+HA,gBAAgB;AAChB,MAAM,aAAa;IACjB,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;AACrE;AAEO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,mBAAmB;QACnB,cAAc,EAAE;QAChB,QAAQ,EAAE;QACV,SAAS,EAAE;QACX,WAAW;QACX,OAAO;QAEP,iBAAiB;QACjB,gBAAgB,CAAC;YACf,MAAM,cAA2B;gBAC/B,GAAG,eAAe;gBAClB,IAAI;gBACJ,QAAQ;gBACR,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,cAAc;2BAAI,MAAM,YAAY;wBAAE;qBAAY;oBAClD,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,yBAAyB;QACvC;QAEA,mBAAmB,CAAC,IAAI;YACtB,IAAI,CAAC,QAAU,CAAC;oBACd,cAAc,MAAM,YAAY,CAAC,GAAG,CAAC,CAAA,cACnC,YAAY,EAAE,KAAK,KACf;4BAAE,GAAG,WAAW;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI,OAAO,WAAW;wBAAG,IAClE;oBAEN,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,sBAAsB;QACpC;QAEA,mBAAmB,CAAC;YAClB,IAAI,CAAC,QAAU,CAAC;oBACd,cAAc,MAAM,YAAY,CAAC,MAAM,CAAC,CAAA,cAAe,YAAY,EAAE,KAAK;oBAC1E,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,oBAAoB;QAClC;QAEA,gBAAgB,CAAC;YACf,MAAM,QAAQ;YACd,OAAO,MAAM,YAAY,CAAC,IAAI,CAAC,CAAA,cAAe,YAAY,EAAE,KAAK;QACnE;QAEA,gBAAgB;QAChB,UAAU,CAAC;YACT,MAAM,QAAe;gBACnB,GAAG,SAAS;gBACZ,IAAI;gBACJ,QAAQ;gBACR,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ;2BAAI,MAAM,MAAM;wBAAE;qBAAM;oBAChC,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,wBAAwB;QACtC;QAEA,aAAa,CAAC,IAAI;YAChB,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,QACvB,MAAM,EAAE,KAAK,KACT;4BAAE,GAAG,KAAK;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI,OAAO,WAAW;wBAAG,IAC5D;oBAEN,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,qBAAqB;QACnC;QAEA,aAAa,CAAC;YACZ,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;oBAClD,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,mBAAmB;QACjC;QAEA,UAAU,CAAC;YACT,MAAM,QAAQ;YACd,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACjD;QAEA,eAAe;QACf,WAAW,CAAC;YACV,MAAM,SAAiB;gBACrB,GAAG,UAAU;gBACb,IAAI;gBACJ,MAAM;gBACN,WAAW;gBACX,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,SAAS;2BAAI,MAAM,OAAO;wBAAE;qBAAO;oBACnC,OAAO;gBACT,CAAC;YAED,iCAAiC;YACjC,wCAAmC;gBACjC,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,mBAAmB;gBACjE,MAAM,IAAI,CAAC;oBACT,IAAI,OAAO,EAAE;oBACb,OAAO,OAAO,KAAK;oBACnB,UAAU,OAAO,QAAQ;oBACzB,WAAW,OAAO,SAAS;oBAC3B,MAAM;oBACN,WAAW;gBACb;gBACA,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;YACtD;YAEA,QAAQ,GAAG,CAAC,yBAAyB;QACvC;QAEA,cAAc,CAAC,IAAI;YACjB,IAAI,CAAC,QAAU,CAAC;oBACd,SAAS,MAAM,OAAO,CAAC,GAAG,CAAC,CAAA,SACzB,OAAO,EAAE,KAAK,KACV;4BAAE,GAAG,MAAM;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI,OAAO,WAAW;wBAAG,IAC7D;oBAEN,OAAO;gBACT,CAAC;YAED,2DAA2D;YAC3D,IAAI,QAAQ,KAAK,IAAI,QAAQ,QAAQ,IAAI,QAAQ,SAAS,EAAE;gBAC1D,wCAAmC;oBACjC,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,mBAAmB;oBACjE,MAAM,YAAY,MAAM,SAAS,CAAC,CAAC,OAAc,KAAK,EAAE,KAAK;oBAC7D,IAAI,cAAc,CAAC,GAAG;wBACpB,IAAI,QAAQ,KAAK,EAAE,KAAK,CAAC,UAAU,CAAC,KAAK,GAAG,QAAQ,KAAK;wBACzD,IAAI,QAAQ,QAAQ,EAAE,KAAK,CAAC,UAAU,CAAC,QAAQ,GAAG,QAAQ,QAAQ;wBAClE,IAAI,QAAQ,SAAS,EAAE,KAAK,CAAC,UAAU,CAAC,SAAS,GAAG,QAAQ,SAAS;wBACrE,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;oBACtD;gBACF;YACF;YAEA,QAAQ,GAAG,CAAC,sBAAsB;QACpC;QAEA,cAAc,CAAC;YACb,IAAI,CAAC,QAAU,CAAC;oBACd,SAAS,MAAM,OAAO,CAAC,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;oBACtD,OAAO;gBACT,CAAC;YAED,uBAAuB;YACvB,wCAAmC;gBACjC,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,mBAAmB;gBACjE,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAC,OAAc,KAAK,EAAE,KAAK;gBAC9D,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;YACtD;YAEA,QAAQ,GAAG,CAAC,oBAAoB;QAClC;QAEA,WAAW,CAAC;YACV,MAAM,QAAQ;YACd,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;QACpD;QAEA,mBAAmB;QACnB,gBAAgB,CAAC,SAAS;YACxB,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,QACvB,MAAM,EAAE,KAAK,WAAW,MAAM,cAAc,KAAK,WAC7C;4BAAE,GAAG,KAAK;4BAAE,QAAQ;4BAAe,WAAW,IAAI,OAAO,WAAW;wBAAG,IACvE;oBAEN,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,4BAA4B;QAC1C;QAEA,eAAe,CAAC,SAAS,UAAU,kBAAkB,EAAE;YACrD,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,QACvB,MAAM,EAAE,KAAK,WAAW,MAAM,cAAc,KAAK,WAC7C;4BACE,GAAG,KAAK;4BACR,QAAQ;4BACR,iBAAiB,gBAAgB,MAAM,GAAG,IAAI,kBAAkB;4BAChE,WAAW,IAAI,OAAO,WAAW;wBACnC,IACA;oBAEN,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,qBAAqB;QACnC;QAEA,eAAe;QACf,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,UAAU;YACR,IAAI;gBAAE,WAAW;YAAK;YACtB,qDAAqD;YACrD,IAAI;gBAAE,WAAW;YAAM;QACzB;QAEA,WAAW;QACX,UAAU;YACR,MAAM,QAAQ;YACd,OAAO;gBACL,mBAAmB,MAAM,YAAY,CAAC,MAAM;gBAC5C,aAAa,MAAM,MAAM,CAAC,MAAM;gBAChC,cAAc,MAAM,OAAO,CAAC,MAAM;gBAClC,qBAAqB,MAAM,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;gBAClF,cAAc,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK;wBAAC;wBAAW;qBAAc,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM;gBAC5F,iBAAiB,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;gBAC1E,cAAc,MAAM,MAAM,CACvB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aACzB,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,KAAK,EAAE;YAC/C;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,cAAc,MAAM,YAAY;YAChC,QAAQ,MAAM,MAAM;YACpB,SAAS,MAAM,OAAO;QACxB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/hooks/useTranslation.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\n// نوع البيانات للترجمات\ntype TranslationKey = string\ntype TranslationValue = string | { [key: string]: string }\ntype Translations = { [key: string]: TranslationValue }\n\n// الترجمات العربية\nconst arTranslations: Translations = {\n  // التنقل والعناوين الرئيسية\n  'dashboard': 'لوحة التحكم',\n  'orders': 'الطلبات',\n  'appointments': 'المواعيد',\n  'settings': 'الإعدادات',\n  'logout': 'تسجيل الخروج',\n  'profile': 'الملف الشخصي',\n  'notifications': 'الإشعارات',\n  \n  // الأزرار والإجراءات\n  'add_new_order': 'إضافة طلب جديد',\n  'book_appointment': 'حجز موعد',\n  'view_details': 'عرض التفاصيل',\n  'edit': 'تعديل',\n  'delete': 'حذف',\n  'save': 'حفظ',\n  'cancel': 'إلغاء',\n  'submit': 'إرسال',\n  'search': 'بحث',\n  'filter': 'تصفية',\n  'export': 'تصدير',\n  'print': 'طباعة',\n  \n  // الحالات\n  'pending': 'في الانتظار',\n  'in_progress': 'قيد التنفيذ',\n  'completed': 'مكتمل',\n  'delivered': 'تم التسليم',\n  'cancelled': 'ملغي',\n  \n  // الإحصائيات\n  'active_orders': 'الطلبات النشطة',\n  'completed_orders': 'الطلبات المكتملة',\n  'total_orders': 'إجمالي الطلبات',\n  'my_active_orders': 'طلباتي النشطة',\n  'my_completed_orders': 'طلباتي المكتملة',\n  'my_total_orders': 'إجمالي طلباتي',\n  'today_appointments': 'مواعيد اليوم',\n  \n  // الأدوار\n  'admin': 'مدير',\n  'worker': 'عامل',\n  \n  // الرسائل\n  'welcome_back': 'مرحباً بعودتك',\n  'overview_today': 'إليك نظرة عامة على أنشطة اليوم',\n  'no_orders_found': 'لا توجد طلبات',\n  'no_appointments_found': 'لا توجد مواعيد',\n  'loading': 'جاري التحميل...',\n  'error': 'خطأ',\n  'success': 'نجح',\n  \n  // أقسام لوحة التحكم\n  'recent_orders': 'الطلبات الحديثة',\n  'quick_actions': 'الإجراءات السريعة',\n  'statistics': 'الإحصائيات',\n  'recent_activity': 'النشاط الحديث',\n  \n  // تفاصيل الطلب\n  'order_details': 'تفاصيل الطلب',\n  'customer_info': 'معلومات الزبون',\n  'order_info': 'معلومات الطلب',\n  'measurements': 'المقاسات',\n  'images': 'الصور',\n  'notes': 'الملاحظات',\n  'voice_notes': 'ملاحظات صوتية',\n  'voice_notes_optional': 'ملاحظات صوتية (اختياري)',\n  \n  // التواريخ والأوقات\n  'order_date': 'تاريخ الطلب',\n  'delivery_date': 'موعد التسليم',\n  'created_at': 'تاريخ الإنشاء',\n  'updated_at': 'تاريخ التحديث',\n  \n  // أخرى\n  'language': 'اللغة',\n  'arabic': 'العربية',\n  'english': 'English',\n  'change_language': 'تغيير اللغة',\n  'next_language': 'اللغة التالية',\n\n  // نصوص رفع الصور\n  'click_or_drag_images': 'انقر أو اسحب الصور هنا',\n  'drop_images_here': 'اتركها هنا...',\n  'image_upload_format': 'PNG, JPG, GIF حتى 10MB لكل صورة',\n  'max_images_text': 'حد أقصى',\n  'images_text': 'صور',\n  'of': 'من',\n  'add_image': 'إضافة صورة',\n  'max_images_reached': 'تم الوصول للحد الأقصى من الصور',\n\n  // نصوص الملاحظات الصوتية\n  'start_recording': 'بدء التسجيل',\n  'stop_recording': 'إيقاف التسجيل',\n  'click_to_record_voice_note': 'اضغطي لتسجيل ملاحظة صوتية',\n  'voice_note': 'ملاحظة صوتية',\n  'microphone_access_error': 'لا يمكن الوصول إلى الميكروفون. يرجى التأكد من الأذونات.',\n\n  // Delete Order Modal\n  'confirm_delete_order': 'تأكيد حذف الطلب',\n  'warning_delete_order': 'تحذير: حذف الطلب',\n  'delete_order_warning_message': 'هذا الإجراء لا يمكن التراجع عنه. سيتم حذف الطلب وجميع البيانات المرتبطة به نهائياً.',\n  'order_details': 'تفاصيل الطلب',\n  'admin_email': 'البريد الإلكتروني للمدير',\n  'admin_password': 'كلمة مرور المدير',\n  'enter_admin_email': 'أدخل البريد الإلكتروني للمدير',\n  'enter_admin_password': 'أدخل كلمة مرور المدير',\n  'please_fill_all_fields': 'يرجى ملء جميع الحقول',\n  'email_does_not_match': 'البريد الإلكتروني لا يطابق بريد المدير المسجل',\n  'incorrect_password': 'كلمة المرور غير صحيحة',\n  'confirm_delete': 'تأكيد الحذف',\n  'delete_order': 'حذف الطلب',\n  'order_deleted_successfully': 'تم حذف الطلب بنجاح',\n\n  // نصوص إضافية مفقودة\n  'workers_management': 'إدارة العمال',\n  'reports': 'التقارير',\n  'reminder': 'تذكير',\n  'view_all': 'عرض الكل',\n  'my_tools': 'أدواتي',\n  'client': 'العميل',\n  'type': 'النوع',\n  'status': 'الحالة',\n  'due_date': 'موعد التسليم',\n  'assigned_worker': 'العامل المكلف',\n  'priority': 'الأولوية',\n  'high': 'عالية',\n  'medium': 'متوسطة',\n  'low': 'منخفضة',\n  'urgent': 'عاجل',\n  'normal': 'عادي',\n\n  // نصوص الصفحات\n  'search_placeholder': 'البحث بالاسم أو رقم الطلب أو وصف الطلب...',\n  'search_by_text': 'البحث بالنص',\n  'search_by_order_number': 'البحث برقم الطلب',\n  'enter_order_number': 'أدخل رقم الطلب...',\n  'filter_status': 'فلتر الحالة',\n  'all_orders': 'جميع الطلبات',\n  'order_date_label': 'تاريخ الطلب',\n  'delivery_date_label': 'موعد التسليم',\n  'worker_label': 'العامل',\n  'price_label': 'السعر',\n  'status_label': 'الحالة',\n  'actions': 'الإجراءات',\n  'start_work': 'بدء العمل',\n  'complete_work': 'إنهاء العمل',\n  'view_order': 'عرض الطلب',\n  'edit_order': 'تعديل الطلب',\n  'sar': 'ر.س',\n  'no_worker_assigned': 'لم يتم تعيين عامل',\n  'assigned_to_me': 'مُكلف لي',\n  'not_assigned_to_me': 'غير مُكلف لي',\n\n  'order_details': 'تفاصيل الطلب',\n  'customer_information': 'معلومات الزبون',\n  'name': 'الاسم:',\n  'description': 'الوصف:',\n  'fabric_type': 'نوع القماش:',\n  'status': 'الحالة',\n  'order_date': 'تاريخ الطلب',\n  'delivery_date': 'موعد التسليم',\n  'assigned_worker': 'العامل المسؤول',\n  'measurements_cm': 'المقاسات (سم)',\n  'basic_measurements': 'المقاسات الأساسية',\n  'shoulder': 'الكتف',\n  'shoulder_circumference': 'دوران الكتف',\n  'chest_bust': 'الصدر',\n  'waist': 'الخصر',\n  'hips': 'الأرداف',\n  'advanced_tailoring_measurements': 'مقاسات التفصيل المتقدمة',\n  'dart_length': 'طول البنس',\n  'bodice_length': 'طول الصدرية',\n  'neckline': 'فتحة الصدر',\n  'armpit': 'الإبط',\n  'sleeve_measurements': 'مقاسات الأكمام',\n  'sleeve_length': 'طول الكم',\n  'forearm': 'الزند',\n  'cuff': 'الأسوارة',\n  'length_measurements': 'مقاسات الطول',\n  'front_length': 'طول الأمام',\n  'back_length': 'طول الخلف',\n  'additional_measurements': 'مقاسات إضافية',\n  'dress_length': 'طول الفستان',\n  'shoulder_width': 'عرض الكتف',\n  'sleeve_length_old': 'طول الأكمام',\n  'design_images': 'صور التصميم',\n  'notes': 'الملاحظات',\n  'voice_notes': 'ملاحظات صوتية',\n\n  // نصوص لوحة التحكم\n  'homepage': 'الصفحة الرئيسية',\n  'home': 'الرئيسية',\n  'admin_dashboard': 'لوحة تحكم المدير',\n  'worker_dashboard': 'لوحة تحكم العامل',\n  'logout': 'تسجيل الخروج',\n  'welcome_worker': 'مرحباً بك في لوحة تحكم العامل',\n  'worker_description': 'يمكنك متابعة طلباتك المخصصة لك وتحديث حالتها من هنا',\n\n  'quick_actions': 'الإجراءات السريعة',\n  'worker_management': 'إدارة العمال',\n  'reports': 'التقارير',\n  'detailed_reports': 'عرض التقارير التفصيلية',\n  'reminder': 'تذكير',\n  'today_appointments_reminder': 'مواعيد اليوم',\n  'orders_need_follow': 'طلبات تحتاج متابعة',\n  'view_all': 'عرض جميع',\n  'and': 'و',\n  'you_have': 'لديك',\n\n  // نصوص EditOrderModal\n  'edit_order': 'تعديل الطلب',\n  'fill_required_fields': 'يرجى ملء جميع الحقول المطلوبة',\n  'order_updated_success': 'تم تحديث الطلب بنجاح!',\n  'order_update_error': 'حدث خطأ أثناء تحديث الطلب',\n  'client_name_required': 'اسم الزبونة *',\n  'phone_required': 'رقم الهاتف *',\n  'order_description_required': 'وصف الطلب *',\n  'fabric_type_optional': 'نوع القماش',\n  'price_sar_required': 'السعر (ريال سعودي) *',\n  'delivery_date_required': 'موعد التسليم *',\n  'status_and_worker': 'الحالة والعامل',\n  'order_status': 'حالة الطلب',\n  'responsible_worker': 'العامل المسؤول',\n  'choose_worker': 'اختر العامل المسؤول',\n  'measurements_cm': 'المقاسات (بالسنتيمتر)',\n  'cm_placeholder': 'سم',\n  'additional_notes_placeholder': 'أي ملاحظات أو تفاصيل إضافية...',\n  'voice_notes_section': 'الملاحظات الصوتية',\n  'cancel': 'إلغاء',\n  'saving': 'جاري الحفظ...',\n  'save_changes': 'حفظ التغييرات',\n  'customer_information': 'معلومات الزبون',\n  'order_details': 'تفاصيل الطلب',\n  'design_images': 'صور التصميم',\n\n  // مقاسات التفصيل\n  'basic_measurements': 'المقاسات الأساسية',\n  'advanced_measurements': 'مقاسات التفصيل المتقدمة',\n  'sleeve_measurements': 'مقاسات الأكمام',\n  'length_measurements': 'مقاسات الطول',\n  'shoulder': 'الكتف',\n  'shoulder_circumference': 'دوران الكتف',\n  'chest': 'الصدر',\n  'waist': 'الخصر',\n  'hips': 'الأرداف',\n  'dart_length': 'طول البنس',\n  'bodice_length': 'طول الصدرية',\n  'neckline': 'فتحة الصدر',\n  'armpit': 'الإبط',\n  'sleeve_length': 'طول الكم',\n  'forearm': 'الزند',\n  'cuff': 'الأسوارة',\n  'front_length': 'طول الأمام',\n  'back_length': 'طول الخلف',\n  'notes_section': 'الملاحظات',\n\n  // حالات الطلب\n  'status_pending': 'في الانتظار',\n  'status_in_progress': 'قيد التنفيذ',\n  'status_completed': 'مكتمل',\n  'status_delivered': 'تم التسليم',\n  'status_cancelled': 'ملغي',\n\n  // نصوص OrderModal\n  'not_specified': 'غير محدد',\n  'close': 'إغلاق',\n  'design_image_alt': 'صورة التصميم',\n  'completed_work_images': 'صور العمل المكتمل',\n  'completed_work_description': 'تم رفع هذه الصور من قبل العامل عند إنهاء الطلب',\n  'completed_work_image_alt': 'صورة العمل المكتمل',\n\n  // نصوص صفحة الطلبات\n  'back_to_dashboard': 'العودة إلى',\n  'orders_management': 'إدارة الطلبات',\n  'view_manage_orders': 'عرض وإدارة جميع طلبات التفصيل',\n  'no_orders_assigned': 'لا توجد طلبات مخصصة لك',\n  'no_orders_assigned_desc': 'لم يتم تخصيص أي طلبات لك بعد',\n  'no_orders_found_desc': 'لم يتم العثور على طلبات تطابق معايير البحث',\n  'fabric_label': 'القماش:',\n  'notes_label': 'ملاحظات:',\n  'view': 'عرض',\n  'start_work': 'بدء التنفيذ',\n  'complete_order': 'إنهاء الطلب',\n  'quick_stats': 'إحصائيات سريعة',\n  'complete_order_modal_title': 'إنهاء الطلب',\n  'order_label': 'طلب:',\n  'for_client': 'للعميلة:',\n  'important_warning': 'تنبيه مهم:',\n  'complete_order_warning': 'بعد الضغط على \"إنهاء الطلب\" سيتم تغيير حالة الطلب إلى \"مكتمل\" ولن تتمكن من التراجع عن هذا الإجراء. تأكد من اكتمال العمل قبل المتابعة.',\n  'completing': 'جاري الإنهاء...',\n\n  // نصوص صفحة التقارير\n  'checking_permissions': 'جاري التحقق من الصلاحيات...',\n  'back_to_dashboard': 'العودة إلى لوحة التحكم',\n  'reports_analytics': 'التقارير والإحصائيات',\n  'comprehensive_analysis': 'تحليل شامل لأداء المحل والمبيعات',\n  'this_week': 'هذا الأسبوع',\n  'this_month': 'هذا الشهر',\n  'this_quarter': 'هذا الربع',\n  'this_year': 'هذا العام',\n  'export': 'تصدير',\n  'key_indicators': 'المؤشرات الرئيسية',\n  'total_revenue': 'إجمالي الإيرادات',\n  'total_orders': 'إجمالي الطلبات',\n  'completed_orders': 'طلبات مكتملة',\n  'average_order_value': 'متوسط قيمة الطلب',\n  'top_workers_month': 'أفضل العمال هذا الشهر',\n  'orders_by_type': 'الطلبات حسب النوع',\n  'monthly_trend': 'الاتجاه الشهري للإيرادات والطلبات',\n  'orders_count': 'طلب',\n  'revenue_label': 'الإيرادات',\n  'orders_label': 'الطلبات',\n  'wedding_dress': 'فستان زفاف',\n  'evening_dress': 'فستان سهرة',\n  'engagement_dress': 'فستان خطوبة',\n  'casual_dress': 'فستان يومي',\n  'other': 'أخرى',\n\n  // نصوص صفحة العمال\n  'fill_required_fields': 'يرجى ملء جميع الحقول المطلوبة',\n  'worker_added_success': 'تم إضافة العامل بنجاح!',\n  'error_adding_worker': 'حدث خطأ أثناء إضافة العامل',\n  'worker_updated_success': 'تم تحديث العامل بنجاح!',\n  'error_updating_worker': 'حدث خطأ أثناء تحديث العامل',\n  'confirm_delete_worker': 'هل أنت متأكد من حذف هذا العامل؟',\n  'worker_deleted_success': 'تم حذف العامل بنجاح!',\n  'worker_deactivated': 'تم إلغاء تفعيل العامل',\n  'worker_activated': 'تم تفعيل العامل',\n  'workers_management': 'إدارة العمال',\n  'view_manage_team': 'عرض وإدارة فريق العمل والخياطين',\n  'add_new_worker': 'إضافة عامل جديد',\n  'search_workers_placeholder': 'البحث بالاسم أو البريد الإلكتروني أو التخصص...',\n  'add_new_worker_form': 'إضافة عامل جديد',\n  'full_name_required': 'الاسم الكامل *',\n  'enter_full_name': 'أدخل الاسم الكامل',\n  'email_required': 'البريد الإلكتروني *',\n  'enter_email': 'أدخل البريد الإلكتروني',\n  'password_required': 'كلمة المرور *',\n  'enter_password': 'أدخل كلمة المرور',\n  'phone_required': 'رقم الهاتف *',\n  'enter_phone': 'أدخل رقم الهاتف',\n  'specialty_required': 'التخصص *',\n  'specialty_example': 'مثال: فساتين الزفاف، التطريز',\n  'adding': 'جاري الإضافة...',\n  'add_worker': 'إضافة العامل',\n  'edit_worker': 'تعديل العامل',\n  'new_password': 'كلمة المرور الجديدة',\n  'leave_empty_no_change': 'اتركها فارغة إذا لم تريد تغييرها',\n  'status': 'الحالة',\n  'active': 'نشط',\n  'inactive': 'غير نشط',\n  'saving': 'جاري الحفظ...',\n  'save_changes': 'حفظ التغييرات',\n  'no_workers': 'لا توجد عمال',\n  'no_workers_found': 'لم يتم العثور على عمال يطابقون معايير البحث',\n  'completed_orders': 'طلب مكتمل',\n  'joined_on': 'انضم في:',\n  'total_workers': 'إجمالي العمال',\n  'active_workers': 'عمال نشطين',\n  'total_completed_orders': 'إجمالي الطلبات المكتملة',\n\n  // نصوص صفحة المواعيد\n  'loading': 'جاري التحميل...',\n  'appointments_management': 'إدارة المواعيد',\n  'view_manage_appointments': 'عرض وإدارة جميع المواعيد المحجوزة',\n  'book_new_appointment': 'حجز موعد جديد',\n  'search_appointments_placeholder': 'البحث بالاسم أو الهاتف أو رقم الموعد...',\n  'all_statuses': 'جميع الحالات',\n  'pending': 'في الانتظار',\n  'confirmed': 'مؤكد',\n  'completed': 'مكتمل',\n  'cancelled': 'ملغي',\n  'all_dates': 'جميع التواريخ',\n  'today': 'اليوم',\n  'tomorrow': 'غداً',\n  'this_week': 'هذا الأسبوع',\n  'no_appointments': 'لا توجد مواعيد',\n  'no_appointments_found': 'لم يتم العثور على مواعيد تطابق معايير البحث',\n  'client_info': 'معلومات العميل',\n  'appointment_details': 'تفاصيل الموعد',\n  'date_time': 'التاريخ والوقت',\n  'created_on': 'تم الإنشاء:',\n  'actions': 'الإجراءات',\n  'confirm_appointment': 'تأكيد الموعد',\n  'mark_attended': 'تم الحضور',\n  'cancel_appointment': 'إلغاء الموعد',\n  'quick_stats': 'إحصائيات سريعة',\n  'am': 'ص',\n  'pm': 'م',\n\n  // نصوص صفحة إضافة الطلبات\n  'add_new_order': 'إضافة طلب جديد',\n  'add_new_order_description': 'أضف طلب تفصيل جديد مع جميع التفاصيل والمقاسات المطلوبة',\n  'basic_information': 'المعلومات الأساسية',\n  'client_name_required': 'اسم الزبونة *',\n  'enter_client_name': 'أدخل اسم الزبونة',\n  'phone_required': 'رقم الهاتف *',\n  'enter_phone': 'أدخل رقم الهاتف',\n  'order_description_required': 'وصف الطلب *',\n  'order_description_placeholder': 'مثال: فستان زفاف أبيض مطرز',\n  'fabric_type': 'نوع القماش',\n  'fabric_type_placeholder': 'مثال: ساتان، شيفون، دانتيل',\n  'price_sar': 'السعر (ريال سعودي) *',\n  'responsible_worker': 'العامل المسؤول',\n  'choose_worker': 'اختر العامل المسؤول',\n  'delivery_date_required': 'موعد التسليم *',\n  'design_images': 'صور التصميم',\n  'measurements_cm': 'المقاسات (بالسنتيمتر)',\n  'basic_measurements': 'المقاسات الأساسية',\n  'shoulder': 'الكتف',\n  'shoulder_circumference': 'دوران الكتف',\n  'chest': 'الصدر',\n  'waist': 'الخصر',\n  'hips': 'الأرداف',\n  'advanced_measurements': 'مقاسات التفصيل المتقدمة',\n  'dart_length': 'طول البنس',\n  'bodice_length': 'طول الصدرية',\n  'neckline': 'فتحة الصدر',\n  'armpit': 'الإبط',\n  'sleeve_measurements': 'مقاسات الأكمام',\n  'sleeve_length': 'طول الكم',\n  'forearm': 'الزند',\n  'cuff': 'الأسوارة',\n  'length_measurements': 'مقاسات الطول',\n  'front_length': 'طول الأمام',\n  'back_length': 'طول الخلف',\n  'additional_notes': 'ملاحظات إضافية',\n  'additional_notes_placeholder': 'أي ملاحظات أو تفاصيل إضافية حول التصميم أو التفصيل...',\n  'voice_notes': 'الملاحظات الصوتية',\n  'saving': 'جاري الحفظ...',\n  'save_order': 'حفظ الطلب',\n  'cancel': 'إلغاء',\n  'cm_placeholder': 'سم',\n  'fill_required_fields': 'يرجى ملء جميع الحقول المطلوبة',\n  'order_added_success': 'تم إضافة الطلب بنجاح! سيتم توجيهك إلى صفحة الطلبات...',\n  'order_add_error': 'حدث خطأ أثناء إضافة الطلب. يرجى المحاولة مرة أخرى.',\n  'back_to_dashboard': 'العودة إلى لوحة التحكم',\n\n  // نصوص الفوتر\n  'home': 'الرئيسية',\n  'book_appointment': 'حجز موعد',\n  'track_order': 'استعلام عن الطلب',\n  'fabrics': 'الأقمشة',\n  'saturday_thursday': 'السبت - الخميس',\n  'friday': 'الجمعة',\n  'closed': 'مغلق',\n  'facebook': 'فيسبوك',\n  'instagram': 'إنستغرام',\n  'whatsapp': 'واتساب',\n  'yasmin_alsham': 'ياسمين الشام',\n  'custom_dress_tailoring': 'تفصيل فساتين حسب الطلب',\n  'footer_description': 'نحن نجمع بين التراث الدمشقي العريق والتصاميم العصرية لنقدم لك فساتين تعكس شخصيتك وتبرز جمالك الطبيعي.',\n  'quick_links': 'روابط سريعة',\n  'contact_us': 'تواصلي معنا',\n  'address': 'العنوان',\n  'address_text': 'الخبر الشمالية، التقاطع السادس، شارع الأمير مشعل، الخبر، السعودية',\n  'phone_numbers': 'أرقام الهاتف',\n  'tailoring_department': 'قسم التفصيل - ياسمين الشام',\n  'ready_made_department': 'قسم الجاهز والمقاسات - ياسمين الشام 2',\n  'email': 'البريد الإلكتروني',\n  'working_hours': 'أوقات العمل',\n  'work_schedule': 'مواعيد العمل',\n  'work_with_love': 'نعمل بحب لإسعادك',\n  'all_rights_reserved': 'جميع الحقوق محفوظة.',\n  'privacy_policy': 'سياسة الخصوصية',\n  'terms_conditions': 'الشروط والأحكام',\n  'made_with': 'صُنع بـ',\n  'in_damascus': 'في دمشق',\n\n\n}\n\n// الترجمات الإنجليزية\nconst enTranslations: Translations = {\n  // التنقل والعناوين الرئيسية\n  'dashboard': 'Dashboard',\n  'orders': 'Orders',\n  'appointments': 'Appointments',\n  'settings': 'Settings',\n  'logout': 'Logout',\n  'profile': 'Profile',\n  'notifications': 'Notifications',\n  \n  // الأزرار والإجراءات\n  'add_new_order': 'Add New Order',\n  'book_appointment': 'Book Appointment',\n  'view_details': 'View Details',\n  'edit': 'Edit',\n  'delete': 'Delete',\n  'save': 'Save',\n  'cancel': 'Cancel',\n  'submit': 'Submit',\n  'search': 'Search',\n  'filter': 'Filter',\n  'export': 'Export',\n  'print': 'Print',\n  \n  // الحالات\n  'pending': 'Pending',\n  'in_progress': 'In Progress',\n  'completed': 'Completed',\n  'delivered': 'Delivered',\n  'cancelled': 'Cancelled',\n  \n  // الإحصائيات\n  'active_orders': 'Active Orders',\n  'completed_orders': 'Completed Orders',\n  'total_orders': 'Total Orders',\n  'my_active_orders': 'My Active Orders',\n  'my_completed_orders': 'My Completed Orders',\n  'my_total_orders': 'My Total Orders',\n  'today_appointments': 'Today\\'s Appointments',\n  \n  // الأدوار\n  'admin': 'Admin',\n  'worker': 'Worker',\n  \n  // الرسائل\n  'welcome_back': 'Welcome Back',\n  'overview_today': 'Here\\'s an overview of today\\'s activities',\n  'no_orders_found': 'No orders found',\n  'no_appointments_found': 'No appointments found',\n  'loading': 'Loading...',\n  'error': 'Error',\n  'success': 'Success',\n  \n  // أقسام لوحة التحكم\n  'recent_orders': 'Recent Orders',\n  'quick_actions': 'Quick Actions',\n  'statistics': 'Statistics',\n  'recent_activity': 'Recent Activity',\n  \n  // تفاصيل الطلب\n  'order_details': 'Order Details',\n  'customer_info': 'Customer Information',\n  'order_info': 'Order Information',\n  'measurements': 'Measurements',\n  'images': 'Images',\n  'notes': 'Notes',\n  'voice_notes': 'Voice Notes',\n  'voice_notes_optional': 'Voice Notes (Optional)',\n  \n  // التواريخ والأوقات\n  'order_date': 'Order Date',\n  'delivery_date': 'Delivery Date',\n  'created_at': 'Created At',\n  'updated_at': 'Updated At',\n  \n  // أخرى\n  'language': 'Language',\n  'arabic': 'العربية',\n  'english': 'English',\n  'change_language': 'Change Language',\n  'next_language': 'Next Language',\n\n  // نصوص رفع الصور\n  'click_or_drag_images': 'Click or drag images here',\n  'drop_images_here': 'Drop them here...',\n  'image_upload_format': 'PNG, JPG, GIF up to 10MB per image',\n  'max_images_text': 'maximum',\n  'images_text': 'images',\n  'of': 'of',\n  'add_image': 'Add image',\n  'max_images_reached': 'Maximum number of images reached',\n\n  // نصوص الملاحظات الصوتية\n  'start_recording': 'Start Recording',\n  'stop_recording': 'Stop Recording',\n  'click_to_record_voice_note': 'Click to record a voice note',\n  'voice_note': 'Voice Note',\n  'microphone_access_error': 'Cannot access microphone. Please check permissions.',\n\n  // Delete Order Modal\n  'confirm_delete_order': 'Confirm Delete Order',\n  'warning_delete_order': 'Warning: Delete Order',\n  'delete_order_warning_message': 'This action cannot be undone. The order and all associated data will be permanently deleted.',\n  'order_details': 'Order Details',\n  'admin_email': 'Admin Email',\n  'admin_password': 'Admin Password',\n  'enter_admin_email': 'Enter admin email',\n  'enter_admin_password': 'Enter admin password',\n  'please_fill_all_fields': 'Please fill all fields',\n  'email_does_not_match': 'Email does not match the registered admin email',\n  'incorrect_password': 'Incorrect password',\n  'confirm_delete': 'Confirm Delete',\n  'delete_order': 'Delete Order',\n  'order_deleted_successfully': 'Order deleted successfully',\n\n  // نصوص إضافية مفقودة\n  'workers_management': 'Workers Management',\n  'reports': 'Reports',\n  'reminder': 'Reminder',\n  'view_all': 'View All',\n  'my_tools': 'My Tools',\n  'client': 'Client',\n  'type': 'Type',\n  'status': 'Status',\n  'due_date': 'Due Date',\n  'assigned_worker': 'Assigned Worker',\n  'priority': 'Priority',\n  'high': 'High',\n  'medium': 'Medium',\n  'low': 'Low',\n  'urgent': 'Urgent',\n  'normal': 'Normal',\n\n  // نصوص الصفحات\n  'search_placeholder': 'Search by name, order number, or description...',\n  'search_by_text': 'Search by Text',\n  'search_by_order_number': 'Search by Order Number',\n  'enter_order_number': 'Enter order number...',\n  'filter_status': 'Filter Status',\n  'all_orders': 'All Orders',\n  'order_date_label': 'Order Date',\n  'delivery_date_label': 'Delivery Date',\n  'worker_label': 'Worker',\n  'price_label': 'Price',\n  'status_label': 'Status',\n  'actions': 'Actions',\n  'start_work': 'Start Work',\n  'complete_work': 'Complete Work',\n  'view_order': 'View Order',\n  'edit_order': 'Edit Order',\n  'sar': 'SAR',\n  'no_worker_assigned': 'No worker assigned',\n  'assigned_to_me': 'Assigned to me',\n  'not_assigned_to_me': 'Not assigned to me',\n\n  'order_details': 'Order Details',\n  'customer_information': 'Customer Information',\n  'name': 'Name:',\n  'description': 'Description:',\n  'fabric_type': 'Fabric Type:',\n  'status': 'Status',\n  'order_date': 'Order Date',\n  'delivery_date': 'Delivery Date',\n  'assigned_worker': 'Assigned Worker',\n  'measurements_cm': 'Measurements (cm)',\n  'basic_measurements': 'Basic Measurements',\n  'shoulder': 'Shoulder',\n  'shoulder_circumference': 'Shoulder Circumference',\n  'chest_bust': 'Chest/Bust',\n  'waist': 'Waist',\n  'hips': 'Hips',\n  'advanced_tailoring_measurements': 'Advanced Tailoring Measurements',\n  'dart_length': 'Dart Length',\n  'bodice_length': 'Bodice Length',\n  'neckline': 'Neckline',\n  'armpit': 'Armpit',\n  'sleeve_measurements': 'Sleeve Measurements',\n  'sleeve_length': 'Sleeve Length',\n  'forearm': 'Forearm',\n  'cuff': 'Cuff',\n  'length_measurements': 'Length Measurements',\n  'front_length': 'Front Length',\n  'back_length': 'Back Length',\n  'additional_measurements': 'Additional Measurements',\n  'dress_length': 'Dress Length',\n  'shoulder_width': 'Shoulder Width',\n  'sleeve_length_old': 'Sleeve Length',\n  'design_images': 'Design Images',\n  'notes': 'Notes',\n  'voice_notes': 'Voice Notes',\n\n  // نصوص لوحة التحكم\n  'homepage': 'Homepage',\n  'home': 'Home',\n  'admin_dashboard': 'Admin Dashboard',\n  'worker_dashboard': 'Worker Dashboard',\n  'logout': 'Logout',\n  'welcome_worker': 'Welcome to Worker Dashboard',\n  'worker_description': 'You can track your assigned orders and update their status here',\n\n  'quick_actions': 'Quick Actions',\n  'worker_management': 'Worker Management',\n  'reports': 'Reports',\n  'detailed_reports': 'View Detailed Reports',\n  'reminder': 'Reminder',\n  'today_appointments_reminder': 'Today\\'s Appointments',\n  'orders_need_follow': 'Orders Need Follow-up',\n  'view_all': 'View All',\n  'and': 'and',\n  'you_have': 'You have',\n\n  // نصوص EditOrderModal\n  'edit_order': 'Edit Order',\n  'fill_required_fields': 'Please fill all required fields',\n  'order_updated_success': 'Order updated successfully!',\n  'order_update_error': 'Error occurred while updating order',\n  'client_name_required': 'Client Name *',\n  'phone_required': 'Phone Number *',\n  'order_description_required': 'Order Description *',\n  'fabric_type_optional': 'Fabric Type',\n  'price_sar_required': 'Price (SAR) *',\n  'delivery_date_required': 'Delivery Date *',\n  'status_and_worker': 'Status and Worker',\n  'order_status': 'Order Status',\n  'responsible_worker': 'Responsible Worker',\n  'choose_worker': 'Choose Responsible Worker',\n  'measurements_cm': 'Measurements (cm)',\n  'cm_placeholder': 'cm',\n  'additional_notes_placeholder': 'Any additional notes or details...',\n  'voice_notes_section': 'Voice Notes',\n  'cancel': 'Cancel',\n  'saving': 'Saving...',\n  'save_changes': 'Save Changes',\n  'customer_information': 'Customer Information',\n  'order_details': 'Order Details',\n  'design_images': 'Design Images',\n\n  // مقاسات التفصيل\n  'basic_measurements': 'Basic Measurements',\n  'advanced_measurements': 'Advanced Tailoring Measurements',\n  'sleeve_measurements': 'Sleeve Measurements',\n  'length_measurements': 'Length Measurements',\n  'shoulder': 'Shoulder',\n  'shoulder_circumference': 'Shoulder Circumference',\n  'chest': 'Chest',\n  'waist': 'Waist',\n  'hips': 'Hips',\n  'dart_length': 'Dart Length',\n  'bodice_length': 'Bodice Length',\n  'neckline': 'Neckline',\n  'armpit': 'Armpit',\n  'sleeve_length': 'Sleeve Length',\n  'forearm': 'Forearm',\n  'cuff': 'Cuff',\n  'front_length': 'Front Length',\n  'back_length': 'Back Length',\n  'notes_section': 'Notes',\n\n  // حالات الطلب\n  'status_pending': 'Pending',\n  'status_in_progress': 'In Progress',\n  'status_completed': 'Completed',\n  'status_delivered': 'Delivered',\n  'status_cancelled': 'Cancelled',\n\n  // نصوص OrderModal\n  'not_specified': 'Not Specified',\n  'close': 'Close',\n  'design_image_alt': 'Design Image',\n  'completed_work_images': 'Completed Work Images',\n  'completed_work_description': 'These images were uploaded by the worker upon order completion',\n  'completed_work_image_alt': 'Completed Work Image',\n\n  // نصوص صفحة الطلبات\n  'back_to_dashboard': 'Back to',\n  'orders_management': 'Orders Management',\n  'view_manage_orders': 'View and manage all tailoring orders',\n  'no_orders_assigned': 'No orders assigned to you',\n  'no_orders_assigned_desc': 'No orders have been assigned to you yet',\n  'no_orders_found_desc': 'No orders found matching the search criteria',\n  'fabric_label': 'Fabric:',\n  'notes_label': 'Notes:',\n  'view': 'View',\n  'start_work': 'Start Work',\n  'complete_order': 'Complete Order',\n  'quick_stats': 'Quick Stats',\n  'complete_order_modal_title': 'Complete Order',\n  'order_label': 'Order:',\n  'for_client': 'For client:',\n  'important_warning': 'Important Warning:',\n  'complete_order_warning': 'After clicking \"Complete Order\", the order status will be changed to \"completed\" and you will not be able to undo this action. Make sure the work is complete before proceeding.',\n  'completing': 'Completing...',\n\n  // نصوص صفحة التقارير\n  'checking_permissions': 'Checking permissions...',\n  'back_to_dashboard': 'Back to Dashboard',\n  'reports_analytics': 'Reports & Analytics',\n  'comprehensive_analysis': 'Comprehensive analysis of store performance and sales',\n  'this_week': 'This Week',\n  'this_month': 'This Month',\n  'this_quarter': 'This Quarter',\n  'this_year': 'This Year',\n  'export': 'Export',\n  'key_indicators': 'Key Indicators',\n  'total_revenue': 'Total Revenue',\n  'total_orders': 'Total Orders',\n  'completed_orders': 'Completed Orders',\n  'average_order_value': 'Average Order Value',\n  'top_workers_month': 'Top Workers This Month',\n  'orders_by_type': 'Orders by Type',\n  'monthly_trend': 'Monthly Revenue & Orders Trend',\n  'orders_count': 'orders',\n  'revenue_label': 'Revenue',\n  'orders_label': 'Orders',\n  'wedding_dress': 'Wedding Dress',\n  'evening_dress': 'Evening Dress',\n  'engagement_dress': 'Engagement Dress',\n  'casual_dress': 'Casual Dress',\n  'other': 'Other',\n\n  // نصوص صفحة العمال\n  'fill_required_fields': 'Please fill all required fields',\n  'worker_added_success': 'Worker added successfully!',\n  'error_adding_worker': 'Error occurred while adding worker',\n  'worker_updated_success': 'Worker updated successfully!',\n  'error_updating_worker': 'Error occurred while updating worker',\n  'confirm_delete_worker': 'Are you sure you want to delete this worker?',\n  'worker_deleted_success': 'Worker deleted successfully!',\n  'worker_deactivated': 'Worker deactivated',\n  'worker_activated': 'Worker activated',\n  'workers_management': 'Workers Management',\n  'view_manage_team': 'View and manage work team and tailors',\n  'add_new_worker': 'Add New Worker',\n  'search_workers_placeholder': 'Search by name, email, or specialty...',\n  'add_new_worker_form': 'Add New Worker',\n  'full_name_required': 'Full Name *',\n  'enter_full_name': 'Enter full name',\n  'email_required': 'Email *',\n  'enter_email': 'Enter email',\n  'password_required': 'Password *',\n  'enter_password': 'Enter password',\n  'phone_required': 'Phone Number *',\n  'enter_phone': 'Enter phone number',\n  'specialty_required': 'Specialty *',\n  'specialty_example': 'Example: Wedding dresses, Embroidery',\n  'adding': 'Adding...',\n  'add_worker': 'Add Worker',\n  'edit_worker': 'Edit Worker',\n  'new_password': 'New Password',\n  'leave_empty_no_change': 'Leave empty if you don\\'t want to change it',\n  'status': 'Status',\n  'active': 'Active',\n  'inactive': 'Inactive',\n  'saving': 'Saving...',\n  'save_changes': 'Save Changes',\n  'no_workers': 'No Workers',\n  'no_workers_found': 'No workers found matching the search criteria',\n  'completed_orders': 'completed orders',\n  'joined_on': 'Joined on:',\n  'total_workers': 'Total Workers',\n  'active_workers': 'Active Workers',\n  'total_completed_orders': 'Total Completed Orders',\n\n  // نصوص صفحة المواعيد\n  'loading': 'Loading...',\n  'appointments_management': 'Appointments Management',\n  'view_manage_appointments': 'View and manage all booked appointments',\n  'book_new_appointment': 'Book New Appointment',\n  'search_appointments_placeholder': 'Search by name, phone, or appointment number...',\n  'all_statuses': 'All Statuses',\n  'pending': 'Pending',\n  'confirmed': 'Confirmed',\n  'completed': 'Completed',\n  'cancelled': 'Cancelled',\n  'all_dates': 'All Dates',\n  'today': 'Today',\n  'tomorrow': 'Tomorrow',\n  'this_week': 'This Week',\n  'no_appointments': 'No Appointments',\n  'no_appointments_found': 'No appointments found matching the search criteria',\n  'client_info': 'Client Information',\n  'appointment_details': 'Appointment Details',\n  'date_time': 'Date & Time',\n  'created_on': 'Created:',\n  'actions': 'Actions',\n  'confirm_appointment': 'Confirm Appointment',\n  'mark_attended': 'Mark Attended',\n  'cancel_appointment': 'Cancel Appointment',\n  'quick_stats': 'Quick Statistics',\n  'am': 'AM',\n  'pm': 'PM',\n\n  // نصوص صفحة إضافة الطلبات\n  'add_new_order': 'Add New Order',\n  'add_new_order_description': 'Add a new tailoring order with all required details and measurements',\n  'basic_information': 'Basic Information',\n  'client_name_required': 'Client Name *',\n  'enter_client_name': 'Enter client name',\n  'phone_required': 'Phone Number *',\n  'enter_phone': 'Enter phone number',\n  'order_description_required': 'Order Description *',\n  'order_description_placeholder': 'Example: White embroidered wedding dress',\n  'fabric_type': 'Fabric Type',\n  'fabric_type_placeholder': 'Example: Satin, Chiffon, Lace',\n  'price_sar': 'Price (Saudi Riyal) *',\n  'responsible_worker': 'Responsible Worker',\n  'choose_worker': 'Choose responsible worker',\n  'delivery_date_required': 'Delivery Date *',\n  'design_images': 'Design Images',\n  'measurements_cm': 'Measurements (in centimeters)',\n  'basic_measurements': 'Basic Measurements',\n  'shoulder': 'Shoulder',\n  'shoulder_circumference': 'Shoulder Circumference',\n  'chest': 'Chest',\n  'waist': 'Waist',\n  'hips': 'Hips',\n  'advanced_measurements': 'Advanced Tailoring Measurements',\n  'dart_length': 'Dart Length',\n  'bodice_length': 'Bodice Length',\n  'neckline': 'Neckline',\n  'armpit': 'Armpit',\n  'sleeve_measurements': 'Sleeve Measurements',\n  'sleeve_length': 'Sleeve Length',\n  'forearm': 'Forearm',\n  'cuff': 'Cuff',\n  'length_measurements': 'Length Measurements',\n  'front_length': 'Front Length',\n  'back_length': 'Back Length',\n  'additional_notes': 'Additional Notes',\n  'additional_notes_placeholder': 'Any additional notes or details about the design or tailoring...',\n  'voice_notes': 'Voice Notes',\n  'saving': 'Saving...',\n  'save_order': 'Save Order',\n  'cancel': 'Cancel',\n  'cm_placeholder': 'cm',\n  'fill_required_fields': 'Please fill all required fields',\n  'order_added_success': 'Order added successfully! You will be redirected to orders page...',\n  'order_add_error': 'An error occurred while adding the order. Please try again.',\n  'back_to_dashboard': 'Back to Dashboard',\n\n  // Footer texts\n  'home': 'Home',\n  'book_appointment': 'Book Appointment',\n  'track_order': 'Track Order',\n  'fabrics': 'Fabrics',\n  'saturday_thursday': 'Saturday - Thursday',\n  'friday': 'Friday',\n  'closed': 'Closed',\n  'facebook': 'Facebook',\n  'instagram': 'Instagram',\n  'whatsapp': 'WhatsApp',\n  'yasmin_alsham': 'Yasmin Alsham',\n  'custom_dress_tailoring': 'Custom Dress Tailoring',\n  'footer_description': 'We combine the ancient Damascene heritage with modern designs to offer you dresses that reflect your personality and highlight your natural beauty.',\n  'quick_links': 'Quick Links',\n  'contact_us': 'Contact Us',\n  'address': 'Address',\n  'address_text': 'North Khobar, 6th Intersection, Prince Mishaal Street, Khobar, Saudi Arabia',\n  'phone_numbers': 'Phone Numbers',\n  'tailoring_department': 'Tailoring Department - Yasmin Alsham',\n  'ready_made_department': 'Ready-made & Sizing Department - Yasmin Alsham 2',\n  'email': 'Email',\n  'working_hours': 'Working Hours',\n  'work_schedule': 'Work Schedule',\n  'work_with_love': 'We work with love to make you happy',\n  'all_rights_reserved': 'All rights reserved.',\n  'privacy_policy': 'Privacy Policy',\n  'terms_conditions': 'Terms & Conditions',\n  'made_with': 'Made with',\n  'in_damascus': 'in Damascus',\n\n  // Header texts\n  'ready_dresses': 'Ready Dresses',\n\n  // Hero texts\n  'hero_subtitle': 'Custom Dress Tailoring with Damascene Elegance',\n  'hero_subtitle_desktop': 'Custom Dress Tailoring',\n  'hero_subtitle_elegant': 'with Authentic Damascene Elegance',\n  'hero_description_mobile': 'We specialize in tailoring elegant dresses with an authentic Damascene touch. From luxurious wedding dresses to elegant evening gowns, we turn your dreams into reality with expert hands and innovative designs.',\n  'hero_description_desktop': 'We combine the ancient Damascene heritage with modern designs to offer you dresses that reflect your personality and highlight your natural beauty. Every dress is a story, and every story tells of elegance and beauty.',\n  'explore_ready_designs': 'Explore Our Ready Designs',\n  'yasmin_alsham_alt': 'Yasmin Alsham - Custom Dress Tailoring'\n}\n\n// Hook للترجمة\nexport function useTranslation() {\n  const [language, setLanguage] = useState<'ar' | 'en'>('ar')\n\n  // تحميل اللغة المحفوظة عند بدء التطبيق\n  useEffect(() => {\n    const savedLanguage = localStorage.getItem('dashboard-language') as 'ar' | 'en'\n    if (savedLanguage) {\n      setLanguage(savedLanguage)\n    }\n  }, [])\n\n  // حفظ اللغة عند تغييرها\n  const changeLanguage = (newLanguage: 'ar' | 'en') => {\n    setLanguage(newLanguage)\n    localStorage.setItem('dashboard-language', newLanguage)\n  }\n\n  // دالة الترجمة\n  const t = (key: TranslationKey): string => {\n    const translations = language === 'ar' ? arTranslations : enTranslations\n    const translation = translations[key]\n    \n    if (typeof translation === 'string') {\n      return translation\n    }\n    \n    // إذا لم توجد الترجمة، أرجع المفتاح نفسه\n    return key\n  }\n\n  // التحقق من اللغة الحالية\n  const isArabic = language === 'ar'\n  const isEnglish = language === 'en'\n\n  return {\n    language,\n    changeLanguage,\n    t,\n    isArabic,\n    isEnglish\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AASA,mBAAmB;AACnB,MAAM,iBAA+B;IACnC,4BAA4B;IAC5B,aAAa;IACb,UAAU;IACV,gBAAgB;IAChB,YAAY;IACZ,UAAU;IACV,WAAW;IACX,iBAAiB;IAEjB,qBAAqB;IACrB,iBAAiB;IACjB,oBAAoB;IACpB,gBAAgB;IAChB,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,SAAS;IAET,UAAU;IACV,WAAW;IACX,eAAe;IACf,aAAa;IACb,aAAa;IACb,aAAa;IAEb,aAAa;IACb,iBAAiB;IACjB,oBAAoB;IACpB,gBAAgB;IAChB,oBAAoB;IACpB,uBAAuB;IACvB,mBAAmB;IACnB,sBAAsB;IAEtB,UAAU;IACV,SAAS;IACT,UAAU;IAEV,UAAU;IACV,gBAAgB;IAChB,kBAAkB;IAClB,mBAAmB;IACnB,yBAAyB;IACzB,WAAW;IACX,SAAS;IACT,WAAW;IAEX,oBAAoB;IACpB,iBAAiB;IACjB,iBAAiB;IACjB,cAAc;IACd,mBAAmB;IAEnB,eAAe;IACf,iBAAiB;IACjB,iBAAiB;IACjB,cAAc;IACd,gBAAgB;IAChB,UAAU;IACV,SAAS;IACT,eAAe;IACf,wBAAwB;IAExB,oBAAoB;IACpB,cAAc;IACd,iBAAiB;IACjB,cAAc;IACd,cAAc;IAEd,OAAO;IACP,YAAY;IACZ,UAAU;IACV,WAAW;IACX,mBAAmB;IACnB,iBAAiB;IAEjB,iBAAiB;IACjB,wBAAwB;IACxB,oBAAoB;IACpB,uBAAuB;IACvB,mBAAmB;IACnB,eAAe;IACf,MAAM;IACN,aAAa;IACb,sBAAsB;IAEtB,yBAAyB;IACzB,mBAAmB;IACnB,kBAAkB;IAClB,8BAA8B;IAC9B,cAAc;IACd,2BAA2B;IAE3B,qBAAqB;IACrB,wBAAwB;IACxB,wBAAwB;IACxB,gCAAgC;IAChC,iBAAiB;IACjB,eAAe;IACf,kBAAkB;IAClB,qBAAqB;IACrB,wBAAwB;IACxB,0BAA0B;IAC1B,wBAAwB;IACxB,sBAAsB;IACtB,kBAAkB;IAClB,gBAAgB;IAChB,8BAA8B;IAE9B,qBAAqB;IACrB,sBAAsB;IACtB,WAAW;IACX,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,UAAU;IACV,QAAQ;IACR,UAAU;IACV,YAAY;IACZ,mBAAmB;IACnB,YAAY;IACZ,QAAQ;IACR,UAAU;IACV,OAAO;IACP,UAAU;IACV,UAAU;IAEV,eAAe;IACf,sBAAsB;IACtB,kBAAkB;IAClB,0BAA0B;IAC1B,sBAAsB;IACtB,iBAAiB;IACjB,cAAc;IACd,oBAAoB;IACpB,uBAAuB;IACvB,gBAAgB;IAChB,eAAe;IACf,gBAAgB;IAChB,WAAW;IACX,cAAc;IACd,iBAAiB;IACjB,cAAc;IACd,cAAc;IACd,OAAO;IACP,sBAAsB;IACtB,kBAAkB;IAClB,sBAAsB;IAEtB,iBAAiB;IACjB,wBAAwB;IACxB,QAAQ;IACR,eAAe;IACf,eAAe;IACf,UAAU;IACV,cAAc;IACd,iBAAiB;IACjB,mBAAmB;IACnB,mBAAmB;IACnB,sBAAsB;IACtB,YAAY;IACZ,0BAA0B;IAC1B,cAAc;IACd,SAAS;IACT,QAAQ;IACR,mCAAmC;IACnC,eAAe;IACf,iBAAiB;IACjB,YAAY;IACZ,UAAU;IACV,uBAAuB;IACvB,iBAAiB;IACjB,WAAW;IACX,QAAQ;IACR,uBAAuB;IACvB,gBAAgB;IAChB,eAAe;IACf,2BAA2B;IAC3B,gBAAgB;IAChB,kBAAkB;IAClB,qBAAqB;IACrB,iBAAiB;IACjB,SAAS;IACT,eAAe;IAEf,mBAAmB;IACnB,YAAY;IACZ,QAAQ;IACR,mBAAmB;IACnB,oBAAoB;IACpB,UAAU;IACV,kBAAkB;IAClB,sBAAsB;IAEtB,iBAAiB;IACjB,qBAAqB;IACrB,WAAW;IACX,oBAAoB;IACpB,YAAY;IACZ,+BAA+B;IAC/B,sBAAsB;IACtB,YAAY;IACZ,OAAO;IACP,YAAY;IAEZ,sBAAsB;IACtB,cAAc;IACd,wBAAwB;IACxB,yBAAyB;IACzB,sBAAsB;IACtB,wBAAwB;IACxB,kBAAkB;IAClB,8BAA8B;IAC9B,wBAAwB;IACxB,sBAAsB;IACtB,0BAA0B;IAC1B,qBAAqB;IACrB,gBAAgB;IAChB,sBAAsB;IACtB,iBAAiB;IACjB,mBAAmB;IACnB,kBAAkB;IAClB,gCAAgC;IAChC,uBAAuB;IACvB,UAAU;IACV,UAAU;IACV,gBAAgB;IAChB,wBAAwB;IACxB,iBAAiB;IACjB,iBAAiB;IAEjB,iBAAiB;IACjB,sBAAsB;IACtB,yBAAyB;IACzB,uBAAuB;IACvB,uBAAuB;IACvB,YAAY;IACZ,0BAA0B;IAC1B,SAAS;IACT,SAAS;IACT,QAAQ;IACR,eAAe;IACf,iBAAiB;IACjB,YAAY;IACZ,UAAU;IACV,iBAAiB;IACjB,WAAW;IACX,QAAQ;IACR,gBAAgB;IAChB,eAAe;IACf,iBAAiB;IAEjB,cAAc;IACd,kBAAkB;IAClB,sBAAsB;IACtB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IAEpB,kBAAkB;IAClB,iBAAiB;IACjB,SAAS;IACT,oBAAoB;IACpB,yBAAyB;IACzB,8BAA8B;IAC9B,4BAA4B;IAE5B,oBAAoB;IACpB,qBAAqB;IACrB,qBAAqB;IACrB,sBAAsB;IACtB,sBAAsB;IACtB,2BAA2B;IAC3B,wBAAwB;IACxB,gBAAgB;IAChB,eAAe;IACf,QAAQ;IACR,cAAc;IACd,kBAAkB;IAClB,eAAe;IACf,8BAA8B;IAC9B,eAAe;IACf,cAAc;IACd,qBAAqB;IACrB,0BAA0B;IAC1B,cAAc;IAEd,qBAAqB;IACrB,wBAAwB;IACxB,qBAAqB;IACrB,qBAAqB;IACrB,0BAA0B;IAC1B,aAAa;IACb,cAAc;IACd,gBAAgB;IAChB,aAAa;IACb,UAAU;IACV,kBAAkB;IAClB,iBAAiB;IACjB,gBAAgB;IAChB,oBAAoB;IACpB,uBAAuB;IACvB,qBAAqB;IACrB,kBAAkB;IAClB,iBAAiB;IACjB,gBAAgB;IAChB,iBAAiB;IACjB,gBAAgB;IAChB,iBAAiB;IACjB,iBAAiB;IACjB,oBAAoB;IACpB,gBAAgB;IAChB,SAAS;IAET,mBAAmB;IACnB,wBAAwB;IACxB,wBAAwB;IACxB,uBAAuB;IACvB,0BAA0B;IAC1B,yBAAyB;IACzB,yBAAyB;IACzB,0BAA0B;IAC1B,sBAAsB;IACtB,oBAAoB;IACpB,sBAAsB;IACtB,oBAAoB;IACpB,kBAAkB;IAClB,8BAA8B;IAC9B,uBAAuB;IACvB,sBAAsB;IACtB,mBAAmB;IACnB,kBAAkB;IAClB,eAAe;IACf,qBAAqB;IACrB,kBAAkB;IAClB,kBAAkB;IAClB,eAAe;IACf,sBAAsB;IACtB,qBAAqB;IACrB,UAAU;IACV,cAAc;IACd,eAAe;IACf,gBAAgB;IAChB,yBAAyB;IACzB,UAAU;IACV,UAAU;IACV,YAAY;IACZ,UAAU;IACV,gBAAgB;IAChB,cAAc;IACd,oBAAoB;IACpB,oBAAoB;IACpB,aAAa;IACb,iBAAiB;IACjB,kBAAkB;IAClB,0BAA0B;IAE1B,qBAAqB;IACrB,WAAW;IACX,2BAA2B;IAC3B,4BAA4B;IAC5B,wBAAwB;IACxB,mCAAmC;IACnC,gBAAgB;IAChB,WAAW;IACX,aAAa;IACb,aAAa;IACb,aAAa;IACb,aAAa;IACb,SAAS;IACT,YAAY;IACZ,aAAa;IACb,mBAAmB;IACnB,yBAAyB;IACzB,eAAe;IACf,uBAAuB;IACvB,aAAa;IACb,cAAc;IACd,WAAW;IACX,uBAAuB;IACvB,iBAAiB;IACjB,sBAAsB;IACtB,eAAe;IACf,MAAM;IACN,MAAM;IAEN,0BAA0B;IAC1B,iBAAiB;IACjB,6BAA6B;IAC7B,qBAAqB;IACrB,wBAAwB;IACxB,qBAAqB;IACrB,kBAAkB;IAClB,eAAe;IACf,8BAA8B;IAC9B,iCAAiC;IACjC,eAAe;IACf,2BAA2B;IAC3B,aAAa;IACb,sBAAsB;IACtB,iBAAiB;IACjB,0BAA0B;IAC1B,iBAAiB;IACjB,mBAAmB;IACnB,sBAAsB;IACtB,YAAY;IACZ,0BAA0B;IAC1B,SAAS;IACT,SAAS;IACT,QAAQ;IACR,yBAAyB;IACzB,eAAe;IACf,iBAAiB;IACjB,YAAY;IACZ,UAAU;IACV,uBAAuB;IACvB,iBAAiB;IACjB,WAAW;IACX,QAAQ;IACR,uBAAuB;IACvB,gBAAgB;IAChB,eAAe;IACf,oBAAoB;IACpB,gCAAgC;IAChC,eAAe;IACf,UAAU;IACV,cAAc;IACd,UAAU;IACV,kBAAkB;IAClB,wBAAwB;IACxB,uBAAuB;IACvB,mBAAmB;IACnB,qBAAqB;IAErB,cAAc;IACd,QAAQ;IACR,oBAAoB;IACpB,eAAe;IACf,WAAW;IACX,qBAAqB;IACrB,UAAU;IACV,UAAU;IACV,YAAY;IACZ,aAAa;IACb,YAAY;IACZ,iBAAiB;IACjB,0BAA0B;IAC1B,sBAAsB;IACtB,eAAe;IACf,cAAc;IACd,WAAW;IACX,gBAAgB;IAChB,iBAAiB;IACjB,wBAAwB;IACxB,yBAAyB;IACzB,SAAS;IACT,iBAAiB;IACjB,iBAAiB;IACjB,kBAAkB;IAClB,uBAAuB;IACvB,kBAAkB;IAClB,oBAAoB;IACpB,aAAa;IACb,eAAe;AAGjB;AAEA,sBAAsB;AACtB,MAAM,iBAA+B;IACnC,4BAA4B;IAC5B,aAAa;IACb,UAAU;IACV,gBAAgB;IAChB,YAAY;IACZ,UAAU;IACV,WAAW;IACX,iBAAiB;IAEjB,qBAAqB;IACrB,iBAAiB;IACjB,oBAAoB;IACpB,gBAAgB;IAChB,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,SAAS;IAET,UAAU;IACV,WAAW;IACX,eAAe;IACf,aAAa;IACb,aAAa;IACb,aAAa;IAEb,aAAa;IACb,iBAAiB;IACjB,oBAAoB;IACpB,gBAAgB;IAChB,oBAAoB;IACpB,uBAAuB;IACvB,mBAAmB;IACnB,sBAAsB;IAEtB,UAAU;IACV,SAAS;IACT,UAAU;IAEV,UAAU;IACV,gBAAgB;IAChB,kBAAkB;IAClB,mBAAmB;IACnB,yBAAyB;IACzB,WAAW;IACX,SAAS;IACT,WAAW;IAEX,oBAAoB;IACpB,iBAAiB;IACjB,iBAAiB;IACjB,cAAc;IACd,mBAAmB;IAEnB,eAAe;IACf,iBAAiB;IACjB,iBAAiB;IACjB,cAAc;IACd,gBAAgB;IAChB,UAAU;IACV,SAAS;IACT,eAAe;IACf,wBAAwB;IAExB,oBAAoB;IACpB,cAAc;IACd,iBAAiB;IACjB,cAAc;IACd,cAAc;IAEd,OAAO;IACP,YAAY;IACZ,UAAU;IACV,WAAW;IACX,mBAAmB;IACnB,iBAAiB;IAEjB,iBAAiB;IACjB,wBAAwB;IACxB,oBAAoB;IACpB,uBAAuB;IACvB,mBAAmB;IACnB,eAAe;IACf,MAAM;IACN,aAAa;IACb,sBAAsB;IAEtB,yBAAyB;IACzB,mBAAmB;IACnB,kBAAkB;IAClB,8BAA8B;IAC9B,cAAc;IACd,2BAA2B;IAE3B,qBAAqB;IACrB,wBAAwB;IACxB,wBAAwB;IACxB,gCAAgC;IAChC,iBAAiB;IACjB,eAAe;IACf,kBAAkB;IAClB,qBAAqB;IACrB,wBAAwB;IACxB,0BAA0B;IAC1B,wBAAwB;IACxB,sBAAsB;IACtB,kBAAkB;IAClB,gBAAgB;IAChB,8BAA8B;IAE9B,qBAAqB;IACrB,sBAAsB;IACtB,WAAW;IACX,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,UAAU;IACV,QAAQ;IACR,UAAU;IACV,YAAY;IACZ,mBAAmB;IACnB,YAAY;IACZ,QAAQ;IACR,UAAU;IACV,OAAO;IACP,UAAU;IACV,UAAU;IAEV,eAAe;IACf,sBAAsB;IACtB,kBAAkB;IAClB,0BAA0B;IAC1B,sBAAsB;IACtB,iBAAiB;IACjB,cAAc;IACd,oBAAoB;IACpB,uBAAuB;IACvB,gBAAgB;IAChB,eAAe;IACf,gBAAgB;IAChB,WAAW;IACX,cAAc;IACd,iBAAiB;IACjB,cAAc;IACd,cAAc;IACd,OAAO;IACP,sBAAsB;IACtB,kBAAkB;IAClB,sBAAsB;IAEtB,iBAAiB;IACjB,wBAAwB;IACxB,QAAQ;IACR,eAAe;IACf,eAAe;IACf,UAAU;IACV,cAAc;IACd,iBAAiB;IACjB,mBAAmB;IACnB,mBAAmB;IACnB,sBAAsB;IACtB,YAAY;IACZ,0BAA0B;IAC1B,cAAc;IACd,SAAS;IACT,QAAQ;IACR,mCAAmC;IACnC,eAAe;IACf,iBAAiB;IACjB,YAAY;IACZ,UAAU;IACV,uBAAuB;IACvB,iBAAiB;IACjB,WAAW;IACX,QAAQ;IACR,uBAAuB;IACvB,gBAAgB;IAChB,eAAe;IACf,2BAA2B;IAC3B,gBAAgB;IAChB,kBAAkB;IAClB,qBAAqB;IACrB,iBAAiB;IACjB,SAAS;IACT,eAAe;IAEf,mBAAmB;IACnB,YAAY;IACZ,QAAQ;IACR,mBAAmB;IACnB,oBAAoB;IACpB,UAAU;IACV,kBAAkB;IAClB,sBAAsB;IAEtB,iBAAiB;IACjB,qBAAqB;IACrB,WAAW;IACX,oBAAoB;IACpB,YAAY;IACZ,+BAA+B;IAC/B,sBAAsB;IACtB,YAAY;IACZ,OAAO;IACP,YAAY;IAEZ,sBAAsB;IACtB,cAAc;IACd,wBAAwB;IACxB,yBAAyB;IACzB,sBAAsB;IACtB,wBAAwB;IACxB,kBAAkB;IAClB,8BAA8B;IAC9B,wBAAwB;IACxB,sBAAsB;IACtB,0BAA0B;IAC1B,qBAAqB;IACrB,gBAAgB;IAChB,sBAAsB;IACtB,iBAAiB;IACjB,mBAAmB;IACnB,kBAAkB;IAClB,gCAAgC;IAChC,uBAAuB;IACvB,UAAU;IACV,UAAU;IACV,gBAAgB;IAChB,wBAAwB;IACxB,iBAAiB;IACjB,iBAAiB;IAEjB,iBAAiB;IACjB,sBAAsB;IACtB,yBAAyB;IACzB,uBAAuB;IACvB,uBAAuB;IACvB,YAAY;IACZ,0BAA0B;IAC1B,SAAS;IACT,SAAS;IACT,QAAQ;IACR,eAAe;IACf,iBAAiB;IACjB,YAAY;IACZ,UAAU;IACV,iBAAiB;IACjB,WAAW;IACX,QAAQ;IACR,gBAAgB;IAChB,eAAe;IACf,iBAAiB;IAEjB,cAAc;IACd,kBAAkB;IAClB,sBAAsB;IACtB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IAEpB,kBAAkB;IAClB,iBAAiB;IACjB,SAAS;IACT,oBAAoB;IACpB,yBAAyB;IACzB,8BAA8B;IAC9B,4BAA4B;IAE5B,oBAAoB;IACpB,qBAAqB;IACrB,qBAAqB;IACrB,sBAAsB;IACtB,sBAAsB;IACtB,2BAA2B;IAC3B,wBAAwB;IACxB,gBAAgB;IAChB,eAAe;IACf,QAAQ;IACR,cAAc;IACd,kBAAkB;IAClB,eAAe;IACf,8BAA8B;IAC9B,eAAe;IACf,cAAc;IACd,qBAAqB;IACrB,0BAA0B;IAC1B,cAAc;IAEd,qBAAqB;IACrB,wBAAwB;IACxB,qBAAqB;IACrB,qBAAqB;IACrB,0BAA0B;IAC1B,aAAa;IACb,cAAc;IACd,gBAAgB;IAChB,aAAa;IACb,UAAU;IACV,kBAAkB;IAClB,iBAAiB;IACjB,gBAAgB;IAChB,oBAAoB;IACpB,uBAAuB;IACvB,qBAAqB;IACrB,kBAAkB;IAClB,iBAAiB;IACjB,gBAAgB;IAChB,iBAAiB;IACjB,gBAAgB;IAChB,iBAAiB;IACjB,iBAAiB;IACjB,oBAAoB;IACpB,gBAAgB;IAChB,SAAS;IAET,mBAAmB;IACnB,wBAAwB;IACxB,wBAAwB;IACxB,uBAAuB;IACvB,0BAA0B;IAC1B,yBAAyB;IACzB,yBAAyB;IACzB,0BAA0B;IAC1B,sBAAsB;IACtB,oBAAoB;IACpB,sBAAsB;IACtB,oBAAoB;IACpB,kBAAkB;IAClB,8BAA8B;IAC9B,uBAAuB;IACvB,sBAAsB;IACtB,mBAAmB;IACnB,kBAAkB;IAClB,eAAe;IACf,qBAAqB;IACrB,kBAAkB;IAClB,kBAAkB;IAClB,eAAe;IACf,sBAAsB;IACtB,qBAAqB;IACrB,UAAU;IACV,cAAc;IACd,eAAe;IACf,gBAAgB;IAChB,yBAAyB;IACzB,UAAU;IACV,UAAU;IACV,YAAY;IACZ,UAAU;IACV,gBAAgB;IAChB,cAAc;IACd,oBAAoB;IACpB,oBAAoB;IACpB,aAAa;IACb,iBAAiB;IACjB,kBAAkB;IAClB,0BAA0B;IAE1B,qBAAqB;IACrB,WAAW;IACX,2BAA2B;IAC3B,4BAA4B;IAC5B,wBAAwB;IACxB,mCAAmC;IACnC,gBAAgB;IAChB,WAAW;IACX,aAAa;IACb,aAAa;IACb,aAAa;IACb,aAAa;IACb,SAAS;IACT,YAAY;IACZ,aAAa;IACb,mBAAmB;IACnB,yBAAyB;IACzB,eAAe;IACf,uBAAuB;IACvB,aAAa;IACb,cAAc;IACd,WAAW;IACX,uBAAuB;IACvB,iBAAiB;IACjB,sBAAsB;IACtB,eAAe;IACf,MAAM;IACN,MAAM;IAEN,0BAA0B;IAC1B,iBAAiB;IACjB,6BAA6B;IAC7B,qBAAqB;IACrB,wBAAwB;IACxB,qBAAqB;IACrB,kBAAkB;IAClB,eAAe;IACf,8BAA8B;IAC9B,iCAAiC;IACjC,eAAe;IACf,2BAA2B;IAC3B,aAAa;IACb,sBAAsB;IACtB,iBAAiB;IACjB,0BAA0B;IAC1B,iBAAiB;IACjB,mBAAmB;IACnB,sBAAsB;IACtB,YAAY;IACZ,0BAA0B;IAC1B,SAAS;IACT,SAAS;IACT,QAAQ;IACR,yBAAyB;IACzB,eAAe;IACf,iBAAiB;IACjB,YAAY;IACZ,UAAU;IACV,uBAAuB;IACvB,iBAAiB;IACjB,WAAW;IACX,QAAQ;IACR,uBAAuB;IACvB,gBAAgB;IAChB,eAAe;IACf,oBAAoB;IACpB,gCAAgC;IAChC,eAAe;IACf,UAAU;IACV,cAAc;IACd,UAAU;IACV,kBAAkB;IAClB,wBAAwB;IACxB,uBAAuB;IACvB,mBAAmB;IACnB,qBAAqB;IAErB,eAAe;IACf,QAAQ;IACR,oBAAoB;IACpB,eAAe;IACf,WAAW;IACX,qBAAqB;IACrB,UAAU;IACV,UAAU;IACV,YAAY;IACZ,aAAa;IACb,YAAY;IACZ,iBAAiB;IACjB,0BAA0B;IAC1B,sBAAsB;IACtB,eAAe;IACf,cAAc;IACd,WAAW;IACX,gBAAgB;IAChB,iBAAiB;IACjB,wBAAwB;IACxB,yBAAyB;IACzB,SAAS;IACT,iBAAiB;IACjB,iBAAiB;IACjB,kBAAkB;IAClB,uBAAuB;IACvB,kBAAkB;IAClB,oBAAoB;IACpB,aAAa;IACb,eAAe;IAEf,eAAe;IACf,iBAAiB;IAEjB,aAAa;IACb,iBAAiB;IACjB,yBAAyB;IACzB,yBAAyB;IACzB,2BAA2B;IAC3B,4BAA4B;IAC5B,yBAAyB;IACzB,qBAAqB;AACvB;AAGO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,IAAI,eAAe;gBACjB,YAAY;YACd;QACF;mCAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,iBAAiB,CAAC;QACtB,YAAY;QACZ,aAAa,OAAO,CAAC,sBAAsB;IAC7C;IAEA,eAAe;IACf,MAAM,IAAI,CAAC;QACT,MAAM,eAAe,aAAa,OAAO,iBAAiB;QAC1D,MAAM,cAAc,YAAY,CAAC,IAAI;QAErC,IAAI,OAAO,gBAAgB,UAAU;YACnC,OAAO;QACT;QAEA,yCAAyC;QACzC,OAAO;IACT;IAEA,0BAA0B;IAC1B,MAAM,WAAW,aAAa;IAC9B,MAAM,YAAY,aAAa;IAE/B,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;GAzCgB", "debugId": null}}, {"offset": {"line": 1385, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/components/VoiceNotes.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { motion } from 'framer-motion'\nimport { <PERSON><PERSON>, <PERSON>cO<PERSON>, Play, Pause, Trash2, Download } from 'lucide-react'\nimport { useTranslation } from '@/hooks/useTranslation'\n\ninterface VoiceNote {\n  id: string\n  data: string\n  timestamp: number\n  duration?: number\n}\n\ninterface VoiceNotesProps {\n  voiceNotes?: VoiceNote[]\n  onVoiceNotesChange: (voiceNotes: VoiceNote[]) => void\n  disabled?: boolean\n}\n\nexport default function VoiceNotes({ voiceNotes = [], onVoiceNotesChange, disabled = false }: VoiceNotesProps) {\n  const [isRecording, setIsRecording] = useState(false)\n  const [playingId, setPlayingId] = useState<string | null>(null)\n  const { t } = useTranslation()\n  const [recordingTime, setRecordingTime] = useState(0)\n  const [currentAudioBlob, setCurrentAudioBlob] = useState<Blob | null>(null)\n  const [error, setError] = useState<string | null>(null)\n\n  const mediaRecorderRef = useRef<MediaRecorder | null>(null)\n  const audioRefsRef = useRef<Map<string, HTMLAudioElement>>(new Map())\n  const timerRef = useRef<NodeJS.Timeout | null>(null)\n  const chunksRef = useRef<Blob[]>([])\n\n  // تحويل base64 إلى Blob للتشغيل\n  const base64ToBlob = (base64: string): Blob => {\n    const byteCharacters = atob(base64.split(',')[1])\n    const byteNumbers = new Array(byteCharacters.length)\n    for (let i = 0; i < byteCharacters.length; i++) {\n      byteNumbers[i] = byteCharacters.charCodeAt(i)\n    }\n    const byteArray = new Uint8Array(byteNumbers)\n    return new Blob([byteArray], { type: 'audio/webm' })\n  }\n\n  // بدء التسجيل\n  const startRecording = async () => {\n    try {\n      setError(null)\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })\n      \n      const mediaRecorder = new MediaRecorder(stream)\n      mediaRecorderRef.current = mediaRecorder\n      chunksRef.current = []\n\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          chunksRef.current.push(event.data)\n        }\n      }\n\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(chunksRef.current, { type: 'audio/webm' })\n        setCurrentAudioBlob(blob)\n\n        // تحويل إلى base64 وإضافة إلى القائمة\n        const reader = new FileReader()\n        reader.onloadend = () => {\n          const base64 = reader.result as string\n          const newVoiceNote: VoiceNote = {\n            id: Date.now().toString(),\n            data: base64,\n            timestamp: Date.now(),\n            duration: recordingTime\n          }\n\n          const updatedNotes = [...voiceNotes, newVoiceNote]\n          onVoiceNotesChange(updatedNotes)\n        }\n        reader.readAsDataURL(blob)\n\n        // إيقاف جميع المسارات\n        stream.getTracks().forEach(track => track.stop())\n      }\n\n      mediaRecorder.start()\n      setIsRecording(true)\n      setRecordingTime(0)\n\n      // بدء العداد\n      timerRef.current = setInterval(() => {\n        setRecordingTime(prev => prev + 1)\n      }, 1000)\n\n    } catch (error) {\n      console.error('خطأ في بدء التسجيل:', error)\n      setError(t('microphone_access_error'))\n    }\n  }\n\n  // إيقاف التسجيل\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop()\n      setIsRecording(false)\n      \n      if (timerRef.current) {\n        clearInterval(timerRef.current)\n        timerRef.current = null\n      }\n    }\n  }\n\n  // تشغيل/إيقاف الصوت\n  const togglePlayback = (voiceNote: VoiceNote) => {\n    const audioRefs = audioRefsRef.current\n\n    // إيقاف أي تشغيل حالي\n    if (playingId && playingId !== voiceNote.id) {\n      const currentAudio = audioRefs.get(playingId)\n      if (currentAudio) {\n        currentAudio.pause()\n      }\n    }\n\n    let audio = audioRefs.get(voiceNote.id)\n    if (!audio) {\n      const blob = base64ToBlob(voiceNote.data)\n      audio = new Audio(URL.createObjectURL(blob))\n      audio.onended = () => setPlayingId(null)\n      audioRefs.set(voiceNote.id, audio)\n    }\n\n    if (playingId === voiceNote.id) {\n      audio.pause()\n      setPlayingId(null)\n    } else {\n      audio.play()\n      setPlayingId(voiceNote.id)\n    }\n  }\n\n  // حذف ملاحظة صوتية محددة\n  const deleteVoiceNote = (noteId: string) => {\n    const audioRefs = audioRefsRef.current\n    const audio = audioRefs.get(noteId)\n\n    if (audio) {\n      audio.pause()\n      audioRefs.delete(noteId)\n    }\n\n    if (playingId === noteId) {\n      setPlayingId(null)\n    }\n\n    const updatedNotes = voiceNotes.filter(note => note.id !== noteId)\n    onVoiceNotesChange(updatedNotes)\n  }\n\n  // تنسيق الوقت\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  // تنسيق التاريخ\n  const formatDate = (timestamp: number) => {\n    const date = new Date(timestamp)\n    return date.toLocaleString('ar-SA', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    })\n  }\n\n  // تنظيف الموارد عند إلغاء التحميل\n  useEffect(() => {\n    return () => {\n      if (timerRef.current) {\n        clearInterval(timerRef.current)\n      }\n      const audioRefs = audioRefsRef.current\n      audioRefs.forEach(audio => audio.pause())\n      audioRefs.clear()\n    }\n  }, [])\n\n  return (\n    <div className=\"space-y-4\">\n      {error && (\n        <div className=\"p-3 bg-red-50 text-red-800 border border-red-200 rounded-lg text-sm\">\n          {error}\n        </div>\n      )}\n\n      {/* قسم التسجيل الجديد */}\n      <div className=\"bg-gray-50 rounded-lg p-4 border border-gray-200\">\n        {!isRecording ? (\n          // زر بدء التسجيل\n          <div className=\"text-center\">\n            <button\n              type=\"button\"\n              onClick={startRecording}\n              disabled={disabled}\n              className=\"inline-flex items-center space-x-2 space-x-reverse px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              <Mic className=\"w-5 h-5\" />\n              <span>{t('start_recording')}</span>\n            </button>\n            <p className=\"text-xs text-gray-500 mt-2\">{t('click_to_record_voice_note')}</p>\n          </div>\n        ) : (\n          // واجهة التسجيل\n          <div className=\"text-center\">\n            <motion.div\n              animate={{ scale: [1, 1.1, 1] }}\n              transition={{ duration: 1, repeat: Infinity }}\n              className=\"inline-flex items-center space-x-2 space-x-reverse mb-4\"\n            >\n              <div className=\"w-3 h-3 bg-red-500 rounded-full animate-pulse\"></div>\n              <span className=\"text-red-600 font-medium\">جاري التسجيل...</span>\n            </motion.div>\n\n            <div className=\"text-2xl font-bold text-gray-800 mb-4\">\n              {formatTime(recordingTime)}\n            </div>\n\n            <button\n              type=\"button\"\n              onClick={stopRecording}\n              className=\"inline-flex items-center space-x-2 space-x-reverse px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-300\"\n            >\n              <MicOff className=\"w-5 h-5\" />\n              <span>{t('stop_recording')}</span>\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* قائمة التسجيلات الصوتية */}\n      {voiceNotes.length > 0 && (\n        <div className=\"space-y-3\">\n          <div className=\"flex items-center justify-between\">\n            <h4 className=\"text-sm font-medium text-gray-700\">\n              {t('voice_notes')} ({voiceNotes.length})\n            </h4>\n          </div>\n\n          {voiceNotes.map((note, index) => (\n            <motion.div\n              key={note.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.3, delay: index * 0.1 }}\n              className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-3 space-x-reverse\">\n                  <button\n                    type=\"button\"\n                    onClick={() => togglePlayback(note)}\n                    className=\"p-2 bg-pink-600 text-white rounded-full hover:bg-pink-700 transition-colors duration-300\"\n                  >\n                    {playingId === note.id ? <Pause className=\"w-4 h-4\" /> : <Play className=\"w-4 h-4\" />}\n                  </button>\n\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-800\">\n                      {t('voice_note')} #{index + 1}\n                    </p>\n                    <p className=\"text-xs text-gray-500\">\n                      {formatDate(note.timestamp)}\n                      {note.duration && ` • ${formatTime(note.duration)}`}\n                    </p>\n                  </div>\n                </div>\n\n                <button\n                  type=\"button\"\n                  onClick={() => deleteVoiceNote(note.id)}\n                  disabled={disabled}\n                  className=\"p-2 text-red-600 hover:bg-red-50 rounded-full transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  title={t('delete')}\n                >\n                  <Trash2 className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAoBe,SAAS,WAAW,EAAE,aAAa,EAAE,EAAE,kBAAkB,EAAE,WAAW,KAAK,EAAmB;;IAC3G,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAwB;IACtD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiC,IAAI;IAC/D,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAC/C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU,EAAE;IAEnC,gCAAgC;IAChC,MAAM,eAAe,CAAC;QACpB,MAAM,iBAAiB,KAAK,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;QAChD,MAAM,cAAc,IAAI,MAAM,eAAe,MAAM;QACnD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YAC9C,WAAW,CAAC,EAAE,GAAG,eAAe,UAAU,CAAC;QAC7C;QACA,MAAM,YAAY,IAAI,WAAW;QACjC,OAAO,IAAI,KAAK;YAAC;SAAU,EAAE;YAAE,MAAM;QAAa;IACpD;IAEA,cAAc;IACd,MAAM,iBAAiB;QACrB,IAAI;YACF,SAAS;YACT,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBAAE,OAAO;YAAK;YAEvE,MAAM,gBAAgB,IAAI,cAAc;YACxC,iBAAiB,OAAO,GAAG;YAC3B,UAAU,OAAO,GAAG,EAAE;YAEtB,cAAc,eAAe,GAAG,CAAC;gBAC/B,IAAI,MAAM,IAAI,CAAC,IAAI,GAAG,GAAG;oBACvB,UAAU,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI;gBACnC;YACF;YAEA,cAAc,MAAM,GAAG;gBACrB,MAAM,OAAO,IAAI,KAAK,UAAU,OAAO,EAAE;oBAAE,MAAM;gBAAa;gBAC9D,oBAAoB;gBAEpB,sCAAsC;gBACtC,MAAM,SAAS,IAAI;gBACnB,OAAO,SAAS,GAAG;oBACjB,MAAM,SAAS,OAAO,MAAM;oBAC5B,MAAM,eAA0B;wBAC9B,IAAI,KAAK,GAAG,GAAG,QAAQ;wBACvB,MAAM;wBACN,WAAW,KAAK,GAAG;wBACnB,UAAU;oBACZ;oBAEA,MAAM,eAAe;2BAAI;wBAAY;qBAAa;oBAClD,mBAAmB;gBACrB;gBACA,OAAO,aAAa,CAAC;gBAErB,sBAAsB;gBACtB,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YAChD;YAEA,cAAc,KAAK;YACnB,eAAe;YACf,iBAAiB;YAEjB,aAAa;YACb,SAAS,OAAO,GAAG,YAAY;gBAC7B,iBAAiB,CAAA,OAAQ,OAAO;YAClC,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,SAAS,EAAE;QACb;IACF;IAEA,gBAAgB;IAChB,MAAM,gBAAgB;QACpB,IAAI,iBAAiB,OAAO,IAAI,aAAa;YAC3C,iBAAiB,OAAO,CAAC,IAAI;YAC7B,eAAe;YAEf,IAAI,SAAS,OAAO,EAAE;gBACpB,cAAc,SAAS,OAAO;gBAC9B,SAAS,OAAO,GAAG;YACrB;QACF;IACF;IAEA,oBAAoB;IACpB,MAAM,iBAAiB,CAAC;QACtB,MAAM,YAAY,aAAa,OAAO;QAEtC,sBAAsB;QACtB,IAAI,aAAa,cAAc,UAAU,EAAE,EAAE;YAC3C,MAAM,eAAe,UAAU,GAAG,CAAC;YACnC,IAAI,cAAc;gBAChB,aAAa,KAAK;YACpB;QACF;QAEA,IAAI,QAAQ,UAAU,GAAG,CAAC,UAAU,EAAE;QACtC,IAAI,CAAC,OAAO;YACV,MAAM,OAAO,aAAa,UAAU,IAAI;YACxC,QAAQ,IAAI,MAAM,IAAI,eAAe,CAAC;YACtC,MAAM,OAAO,GAAG,IAAM,aAAa;YACnC,UAAU,GAAG,CAAC,UAAU,EAAE,EAAE;QAC9B;QAEA,IAAI,cAAc,UAAU,EAAE,EAAE;YAC9B,MAAM,KAAK;YACX,aAAa;QACf,OAAO;YACL,MAAM,IAAI;YACV,aAAa,UAAU,EAAE;QAC3B;IACF;IAEA,yBAAyB;IACzB,MAAM,kBAAkB,CAAC;QACvB,MAAM,YAAY,aAAa,OAAO;QACtC,MAAM,QAAQ,UAAU,GAAG,CAAC;QAE5B,IAAI,OAAO;YACT,MAAM,KAAK;YACX,UAAU,MAAM,CAAC;QACnB;QAEA,IAAI,cAAc,QAAQ;YACxB,aAAa;QACf;QAEA,MAAM,eAAe,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC3D,mBAAmB;IACrB;IAEA,cAAc;IACd,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,gBAAgB;IAChB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,cAAc,CAAC,SAAS;YAClC,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;wCAAO;oBACL,IAAI,SAAS,OAAO,EAAE;wBACpB,cAAc,SAAS,OAAO;oBAChC;oBACA,MAAM,YAAY,aAAa,OAAO;oBACtC,UAAU,OAAO;gDAAC,CAAA,QAAS,MAAM,KAAK;;oBACtC,UAAU,KAAK;gBACjB;;QACF;+BAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAKL,6LAAC;gBAAI,WAAU;0BACZ,CAAC,cACA,iBAAiB;8BACjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;;8CAEV,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,6LAAC;8CAAM,EAAE;;;;;;;;;;;;sCAEX,6LAAC;4BAAE,WAAU;sCAA8B,EAAE;;;;;;;;;;;2BAG/C,gBAAgB;8BAChB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;oCAAC;oCAAG;oCAAK;iCAAE;4BAAC;4BAC9B,YAAY;gCAAE,UAAU;gCAAG,QAAQ;4BAAS;4BAC5C,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAK,WAAU;8CAA2B;;;;;;;;;;;;sCAG7C,6LAAC;4BAAI,WAAU;sCACZ,WAAW;;;;;;sCAGd,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;YAOhB,WAAW,MAAM,GAAG,mBACnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;gCACX,EAAE;gCAAe;gCAAG,WAAW,MAAM;gCAAC;;;;;;;;;;;;oBAI1C,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,eAAe;gDAC9B,WAAU;0DAET,cAAc,KAAK,EAAE,iBAAG,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;yEAAe,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAG3E,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;;4DACV,EAAE;4DAAc;4DAAG,QAAQ;;;;;;;kEAE9B,6LAAC;wDAAE,WAAU;;4DACV,WAAW,KAAK,SAAS;4DACzB,KAAK,QAAQ,IAAI,CAAC,GAAG,EAAE,WAAW,KAAK,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;kDAKzD,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,gBAAgB,KAAK,EAAE;wCACtC,UAAU;wCACV,WAAU;wCACV,OAAO,EAAE;kDAET,cAAA,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;2BAlCjB,KAAK,EAAE;;;;;;;;;;;;;;;;;AA2C1B;GApRwB;;QAGR,iIAAA,CAAA,iBAAc;;;KAHN", "debugId": null}}, {"offset": {"line": 1866, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/components/OrderModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport {\n  X,\n  User,\n  Phone,\n  Calendar,\n  Package,\n  Ruler,\n  DollarSign,\n  MessageSquare,\n  UserCheck,\n  Clock,\n  CheckCircle,\n  Image as ImageIcon\n} from 'lucide-react'\nimport { Order, Worker } from '@/store/dataStore'\nimport { useAuthStore } from '@/store/authStore'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport VoiceNotes from './VoiceNotes'\n\ninterface OrderModalProps {\n  order: Order | null\n  workers: Worker[]\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport default function OrderModal({ order, workers, isOpen, onClose }: OrderModalProps) {\n  const { user } = useAuthStore()\n  const { t, isArabic } = useTranslation()\n\n  if (!order) return null\n\n  const getWorkerName = (workerId?: string) => {\n    if (!workerId) return t('not_specified')\n    const worker = workers.find(w => w.id === workerId)\n    return worker ? worker.full_name : t('not_specified')\n  }\n\n  const getStatusInfo = (status: string) => {\n    const statusMap = {\n      pending: {\n        label: t('pending'),\n        color: 'text-yellow-600',\n        bgColor: 'bg-yellow-100'\n      },\n      in_progress: {\n        label: t('in_progress'),\n        color: 'text-blue-600',\n        bgColor: 'bg-blue-100'\n      },\n      completed: {\n        label: t('completed'),\n        color: 'text-green-600',\n        bgColor: 'bg-green-100'\n      },\n      delivered: {\n        label: t('delivered'),\n        color: 'text-purple-600',\n        bgColor: 'bg-purple-100'\n      },\n      cancelled: {\n        label: t('cancelled'),\n        color: 'text-red-600',\n        bgColor: 'bg-red-100'\n      }\n    }\n    return statusMap[status as keyof typeof statusMap] || statusMap.pending\n  }\n\n  // حساب موعد التسليم المعروض (قبل يومين من الموعد الحقيقي)\n  const getDisplayDeliveryDate = (actualDate: string) => {\n    const date = new Date(actualDate)\n    date.setDate(date.getDate() - 2)\n    return date.toISOString()\n  }\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString)\n    return date.toLocaleDateString('ar-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    })\n  }\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\n          {/* خلفية مظلمة */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"absolute inset-0 bg-black/50 backdrop-blur-sm\"\n            onClick={onClose}\n          />\n          \n          {/* النافذة المنبثقة */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9, y: 20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.9, y: 20 }}\n            className=\"relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\"\n          >\n            {/* رأس النافذة */}\n            <div className=\"sticky top-0 bg-white border-b border-gray-200 p-6 rounded-t-2xl\">\n              <div className=\"flex items-center justify-between\">\n                <h2 className=\"text-2xl font-bold text-gray-800\">\n                  {t('order_details')}\n                </h2>\n                <div className=\"flex items-center space-x-3 space-x-reverse\">\n                  <button\n                    onClick={onClose}\n                    className=\"p-2 text-gray-400 hover:text-gray-600 transition-colors duration-300\"\n                  >\n                    <X className=\"w-6 h-6\" />\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* محتوى النافذة */}\n            <div className=\"p-6 space-y-8\">\n              {/* معلومات أساسية */}\n              <div className=\"grid md:grid-cols-2 gap-6\">\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse\">\n                    <User className=\"w-5 h-5 text-pink-600\" />\n                    <span>\n                      {t('customer_information')}\n                      {isArabic && <span className=\"text-sm text-gray-500 mr-2\">(Customer Information)</span>}\n                    </span>\n                  </h3>\n\n                  <div className=\"space-y-3 bg-gray-50 p-4 rounded-lg\">\n                    <div className=\"flex items-center space-x-3 space-x-reverse\">\n                      <User className=\"w-4 h-4 text-gray-600\" />\n                      <span className=\"font-medium\">\n                        {t('name')}\n                        {isArabic && <span className=\"text-sm text-gray-500 mr-2\">(Name)</span>}\n                      </span>\n                      <span>{order.clientName}</span>\n                    </div>\n                    {/* تم حذف عرض رقم الهاتف للعامل */}\n                  </div>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse\">\n                    <Package className=\"w-5 h-5 text-pink-600\" />\n                    <span>\n                      {t('order_details')}\n                      {isArabic && <span className=\"text-sm text-gray-500 mr-2\">(Order Details)</span>}\n                    </span>\n                  </h3>\n\n                  <div className=\"space-y-3 bg-gray-50 p-4 rounded-lg\">\n                    <div>\n                      <span className=\"font-medium\">\n                        {t('description')}\n                        {isArabic && <span className=\"text-sm text-gray-500 mr-2\">(Description)</span>}\n                      </span>\n                      <p className=\"mt-1\">{order.description}</p>\n                    </div>\n                    {order.fabric && (\n                      <div>\n                        <span className=\"font-medium\">\n                          {t('fabric_type')}\n                          {isArabic && <span className=\"text-sm text-gray-500 mr-2\">(Fabric Type)</span>}\n                        </span>\n                        <p className=\"mt-1\">{order.fabric}</p>\n                      </div>\n                    )}\n                    {/* تم حذف عرض السعر للعامل */}\n                  </div>\n                </div>\n              </div>\n\n              {/* الحالة والتواريخ */}\n              <div className=\"grid md:grid-cols-3 gap-6\">\n                <div className=\"space-y-3\">\n                  <h4 className=\"font-bold text-gray-800\">\n                    {t('status')}\n                    {isArabic && <span className=\"text-sm text-gray-500 mr-2\">(Status)</span>}\n                  </h4>\n                  <span className={`px-3 py-2 rounded-full text-sm font-medium ${getStatusInfo(order.status).bgColor} ${getStatusInfo(order.status).color}`}>\n                    {getStatusInfo(order.status).label}\n                  </span>\n                </div>\n\n                <div className=\"space-y-3\">\n                  <h4 className=\"font-bold text-gray-800 flex items-center space-x-2 space-x-reverse\">\n                    <Calendar className=\"w-4 h-4\" />\n                    <span>\n                      {t('order_date')}\n                      {isArabic && <span className=\"text-sm text-gray-500 mr-2\">(Order Date)</span>}\n                    </span>\n                  </h4>\n                  <p>{formatDate(order.createdAt)}</p>\n                </div>\n\n                <div className=\"space-y-3\">\n                  <h4 className=\"font-bold text-gray-800 flex items-center space-x-2 space-x-reverse\">\n                    <Clock className=\"w-4 h-4\" />\n                    <span>\n                      {t('delivery_date')}\n                      {isArabic && <span className=\"text-sm text-gray-500 mr-2\">(Delivery Date)</span>}\n                    </span>\n                  </h4>\n                  <p>{formatDate(getDisplayDeliveryDate(order.dueDate))}</p>\n                </div>\n              </div>\n\n              {/* العامل المسؤول */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse\">\n                  <UserCheck className=\"w-5 h-5 text-pink-600\" />\n                  <span>\n                    {t('assigned_worker')}\n                    {isArabic && <span className=\"text-sm text-gray-500 mr-2\">(Assigned Worker)</span>}\n                  </span>\n                </h3>\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\n                  <p className=\"text-lg\">{getWorkerName(order.assignedWorker)}</p>\n                </div>\n              </div>\n\n              {/* المقاسات */}\n              {Object.values(order.measurements).some(val => val !== undefined) && (\n                <div className=\"space-y-6\">\n                  <h3 className=\"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse\">\n                    <Ruler className=\"w-5 h-5 text-pink-600\" />\n                    <span>\n                      {t('measurements_cm')}\n                      {isArabic && <span className=\"text-sm text-gray-500 mr-2\">(Measurements)</span>}\n                    </span>\n                  </h3>\n\n                  {/* المقاسات الأساسية */}\n                  {(order.measurements.shoulder || order.measurements.shoulderCircumference || order.measurements.chest || order.measurements.waist || order.measurements.hips) && (\n                    <div className=\"space-y-3\">\n                      <h4 className=\"text-md font-semibold text-gray-700 border-b border-pink-200 pb-1\">\n                        {t('basic_measurements')}\n                        {isArabic && <span className=\"text-sm text-gray-500 mr-2\">(Basic Measurements)</span>}\n                      </h4>\n                      <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                        {order.measurements.shoulder && (\n                          <div className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                            <p className=\"text-sm text-gray-600\">\n                              {t('shoulder')}\n                              {isArabic && <span className=\"text-xs text-gray-400 block\">(Shoulder)</span>}\n                            </p>\n                            <p className=\"text-lg font-bold\">{order.measurements.shoulder}</p>\n                          </div>\n                        )}\n                        {order.measurements.shoulderCircumference && (\n                          <div className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                            <p className=\"text-sm text-gray-600\">\n                              {t('shoulder_circumference')}\n                              {isArabic && <span className=\"text-xs text-gray-400 block\">(Shoulder Circumference)</span>}\n                            </p>\n                            <p className=\"text-lg font-bold\">{order.measurements.shoulderCircumference}</p>\n                          </div>\n                        )}\n                        {order.measurements.chest && (\n                          <div className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                            <p className=\"text-sm text-gray-600\">\n                              {t('chest_bust')}\n                              {isArabic && <span className=\"text-xs text-gray-400 block\">(Chest/Bust)</span>}\n                            </p>\n                            <p className=\"text-lg font-bold\">{order.measurements.chest}</p>\n                          </div>\n                        )}\n                        {order.measurements.waist && (\n                          <div className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                            <p className=\"text-sm text-gray-600\">\n                              {t('waist')}\n                              {isArabic && <span className=\"text-xs text-gray-400 block\">(Waist)</span>}\n                            </p>\n                            <p className=\"text-lg font-bold\">{order.measurements.waist}</p>\n                          </div>\n                        )}\n                        {order.measurements.hips && (\n                          <div className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                            <p className=\"text-sm text-gray-600\">\n                              {t('hips')}\n                              {isArabic && <span className=\"text-xs text-gray-400 block\">(Hips)</span>}\n                            </p>\n                            <p className=\"text-lg font-bold\">{order.measurements.hips}</p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  )}\n\n                  {/* مقاسات التفصيل المتقدمة */}\n                  {(order.measurements.dartLength || order.measurements.bodiceLength || order.measurements.neckline || order.measurements.armpit) && (\n                    <div className=\"space-y-3\">\n                      <h4 className=\"text-md font-semibold text-gray-700 border-b border-pink-200 pb-1\">\n                        {t('advanced_tailoring_measurements')}\n                        {isArabic && <span className=\"text-sm text-gray-500 mr-2\">(Advanced Tailoring)</span>}\n                      </h4>\n                      <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                        {order.measurements.dartLength && (\n                          <div className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                            <p className=\"text-sm text-gray-600\">\n                              {t('dart_length')}\n                              {isArabic && <span className=\"text-xs text-gray-400 block\">(Dart Length)</span>}\n                            </p>\n                            <p className=\"text-lg font-bold\">{order.measurements.dartLength}</p>\n                          </div>\n                        )}\n                        {order.measurements.bodiceLength && (\n                          <div className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                            <p className=\"text-sm text-gray-600\">\n                              {t('bodice_length')}\n                              {isArabic && <span className=\"text-xs text-gray-400 block\">(Bodice Length)</span>}\n                            </p>\n                            <p className=\"text-lg font-bold\">{order.measurements.bodiceLength}</p>\n                          </div>\n                        )}\n                        {order.measurements.neckline && (\n                          <div className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                            <p className=\"text-sm text-gray-600\">\n                              {t('neckline')}\n                              {isArabic && <span className=\"text-xs text-gray-400 block\">(Neckline)</span>}\n                            </p>\n                            <p className=\"text-lg font-bold\">{order.measurements.neckline}</p>\n                          </div>\n                        )}\n                        {order.measurements.armpit && (\n                          <div className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                            <p className=\"text-sm text-gray-600\">\n                              {t('armpit')}\n                              {isArabic && <span className=\"text-xs text-gray-400 block\">(Armpit)</span>}\n                            </p>\n                            <p className=\"text-lg font-bold\">{order.measurements.armpit}</p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  )}\n\n                  {/* مقاسات الأكمام */}\n                  {(order.measurements.sleeveLength || order.measurements.forearm || order.measurements.cuff) && (\n                    <div className=\"space-y-3\">\n                      <h4 className=\"text-md font-semibold text-gray-700 border-b border-pink-200 pb-1\">\n                        {t('sleeve_measurements')}\n                        {isArabic && <span className=\"text-sm text-gray-500 mr-2\">(Sleeve Measurements)</span>}\n                      </h4>\n                      <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                        {order.measurements.sleeveLength && (\n                          <div className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                            <p className=\"text-sm text-gray-600\">\n                              {t('sleeve_length')}\n                              {isArabic && <span className=\"text-xs text-gray-400 block\">(Sleeve Length)</span>}\n                            </p>\n                            <p className=\"text-lg font-bold\">{order.measurements.sleeveLength}</p>\n                          </div>\n                        )}\n                        {order.measurements.forearm && (\n                          <div className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                            <p className=\"text-sm text-gray-600\">\n                              {t('forearm')}\n                              {isArabic && <span className=\"text-xs text-gray-400 block\">(Forearm)</span>}\n                            </p>\n                            <p className=\"text-lg font-bold\">{order.measurements.forearm}</p>\n                          </div>\n                        )}\n                        {order.measurements.cuff && (\n                          <div className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                            <p className=\"text-sm text-gray-600\">\n                              {t('cuff')}\n                              {isArabic && <span className=\"text-xs text-gray-400 block\">(Cuff)</span>}\n                            </p>\n                            <p className=\"text-lg font-bold\">{order.measurements.cuff}</p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  )}\n\n                  {/* مقاسات الطول */}\n                  {(order.measurements.frontLength || order.measurements.backLength) && (\n                    <div className=\"space-y-3\">\n                      <h4 className=\"text-md font-semibold text-gray-700 border-b border-pink-200 pb-1\">\n                        {t('length_measurements')}\n                        {isArabic && <span className=\"text-sm text-gray-500 mr-2\">(Length Measurements)</span>}\n                      </h4>\n                      <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                        {order.measurements.frontLength && (\n                          <div className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                            <p className=\"text-sm text-gray-600\">\n                              {t('front_length')}\n                              {isArabic && <span className=\"text-xs text-gray-400 block\">(Front Length)</span>}\n                            </p>\n                            <p className=\"text-lg font-bold\">{order.measurements.frontLength}</p>\n                          </div>\n                        )}\n                        {order.measurements.backLength && (\n                          <div className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                            <p className=\"text-sm text-gray-600\">\n                              {t('back_length')}\n                              {isArabic && <span className=\"text-xs text-gray-400 block\">(Back Length)</span>}\n                            </p>\n                            <p className=\"text-lg font-bold\">{order.measurements.backLength}</p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  )}\n\n                  {/* المقاسات القديمة (للتوافق مع البيانات الموجودة) */}\n                  {(order.measurements.length || order.measurements.shoulders || order.measurements.sleeves) && (\n                    <div className=\"space-y-3\">\n                      <h4 className=\"text-md font-semibold text-gray-700 border-b border-gray-300 pb-1 text-gray-500\">\n                        {t('additional_measurements')}\n                        {isArabic && <span className=\"text-sm text-gray-500 mr-2\">(Additional)</span>}\n                      </h4>\n                      <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                        {order.measurements.length && (\n                          <div className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                            <p className=\"text-sm text-gray-600\">\n                              {t('dress_length')}\n                              {isArabic && <span className=\"text-xs text-gray-400 block\">(Dress Length)</span>}\n                            </p>\n                            <p className=\"text-lg font-bold\">{order.measurements.length}</p>\n                          </div>\n                        )}\n                        {order.measurements.shoulders && (\n                          <div className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                            <p className=\"text-sm text-gray-600\">\n                              {t('shoulder_width')}\n                              {isArabic && <span className=\"text-xs text-gray-400 block\">(Shoulder Width)</span>}\n                            </p>\n                            <p className=\"text-lg font-bold\">{order.measurements.shoulders}</p>\n                          </div>\n                        )}\n                        {order.measurements.sleeves && (\n                          <div className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                            <p className=\"text-sm text-gray-600\">\n                              {t('sleeve_length_old')}\n                              {isArabic && <span className=\"text-xs text-gray-400 block\">(Sleeve Length)</span>}\n                            </p>\n                            <p className=\"text-lg font-bold\">{order.measurements.sleeves}</p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              )}\n\n              {/* صور التصميم */}\n              {order.images && order.images.length > 0 && (\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse\">\n                    <ImageIcon className=\"w-5 h-5 text-pink-600\" />\n                    <span>\n                      {t('design_images')}\n                      {isArabic && <span className=\"text-sm text-gray-500 mr-2\">(Design Images)</span>}\n                    </span>\n                  </h3>\n                  <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\n                    {order.images.map((image, index) => (\n                      <div key={index} className=\"relative group\">\n                        <div className=\"aspect-square rounded-lg overflow-hidden border border-gray-200\">\n                          <img\n                            src={image}\n                            alt={`${t('design_image_alt')} ${index + 1}`}\n                            className=\"w-full h-full object-cover cursor-pointer hover:scale-105 transition-transform duration-300\"\n                            onClick={() => window.open(image, '_blank')}\n                          />\n                        </div>\n                        <div className=\"absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded\">\n                          {index + 1}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* الملاحظات */}\n              {order.notes && (\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse\">\n                    <MessageSquare className=\"w-5 h-5 text-pink-600\" />\n                    <span>\n                      {t('notes')}\n                      {isArabic && <span className=\"text-sm text-gray-500 mr-2\">(Notes)</span>}\n                    </span>\n                  </h3>\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\n                    <p>{order.notes}</p>\n                  </div>\n                </div>\n              )}\n\n              {/* الملاحظات الصوتية */}\n              {order.voiceNotes && order.voiceNotes.length > 0 && (\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse\">\n                    <MessageSquare className=\"w-5 h-5 text-pink-600\" />\n                    <span>\n                      {t('voice_notes')}\n                      {isArabic && <span className=\"text-sm text-gray-500 mr-2\">(Voice Notes)</span>}\n                    </span>\n                  </h3>\n                  <VoiceNotes\n                    voiceNotes={order.voiceNotes}\n                    onVoiceNotesChange={() => {}} // للعرض فقط\n                    disabled={true}\n                  />\n                </div>\n              )}\n\n              {/* صور العمل المكتمل - للمدراء فقط */}\n              {user?.role === 'admin' && order.completedImages && order.completedImages.length > 0 && (\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse\">\n                    <CheckCircle className=\"w-5 h-5 text-green-600\" />\n                    <span>{t('completed_work_images')}</span>\n                  </h3>\n                  <div className=\"bg-green-50 p-4 rounded-lg border border-green-200\">\n                    <div className=\"flex items-center space-x-2 space-x-reverse mb-3\">\n                      <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                      <span className=\"text-sm font-medium text-green-800\">\n                        {t('completed_work_description')}\n                      </span>\n                    </div>\n                    <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                      {order.completedImages.map((image, index) => (\n                        <div key={index} className=\"relative group\">\n                          <div className=\"aspect-square rounded-lg overflow-hidden border border-green-300\">\n                            <img\n                              src={image}\n                              alt={`${t('completed_work_image_alt')} ${index + 1}`}\n                              className=\"w-full h-full object-cover cursor-pointer hover:scale-105 transition-transform duration-300\"\n                              onClick={() => window.open(image, '_blank')}\n                            />\n                          </div>\n                          <div className=\"absolute bottom-2 left-2 bg-green-600/80 text-white text-xs px-2 py-1 rounded\">\n                            {index + 1}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* تذييل النافذة */}\n            <div className=\"sticky bottom-0 bg-white border-t border-gray-200 p-6 rounded-b-2xl\">\n              <div className=\"flex justify-end\">\n                <button\n                  onClick={onClose}\n                  className=\"btn-secondary px-6 py-2\"\n                >\n                  {t('close')}\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      )}\n    </AnimatePresence>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AACA;;;AArBA;;;;;;AA8Be,SAAS,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAmB;;IACrF,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAErC,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,UAAU,OAAO,EAAE;QACxB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,OAAO,SAAS,OAAO,SAAS,GAAG,EAAE;IACvC;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,YAAY;YAChB,SAAS;gBACP,OAAO,EAAE;gBACT,OAAO;gBACP,SAAS;YACX;YACA,aAAa;gBACX,OAAO,EAAE;gBACT,OAAO;gBACP,SAAS;YACX;YACA,WAAW;gBACT,OAAO,EAAE;gBACT,OAAO;gBACP,SAAS;YACX;YACA,WAAW;gBACT,OAAO,EAAE;gBACT,OAAO;gBACP,SAAS;YACX;YACA,WAAW;gBACT,OAAO,EAAE;gBACT,OAAO;gBACP,SAAS;YACX;QACF;QACA,OAAO,SAAS,CAAC,OAAiC,IAAI,UAAU,OAAO;IACzE;IAEA,0DAA0D;IAC1D,MAAM,yBAAyB,CAAC;QAC9B,MAAM,OAAO,IAAI,KAAK;QACtB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;QAC9B,OAAO,KAAK,WAAW;IACzB;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACzC,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACtC,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOrB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;;gEACE,EAAE;gEACF,0BAAY,6LAAC;oEAAK,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;8DAI9D,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;gEAAK,WAAU;;oEACb,EAAE;oEACF,0BAAY,6LAAC;wEAAK,WAAU;kFAA6B;;;;;;;;;;;;0EAE5D,6LAAC;0EAAM,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;sDAM7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC,2MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,6LAAC;;gEACE,EAAE;gEACF,0BAAY,6LAAC;oEAAK,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;8DAI9D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;;wEACb,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA6B;;;;;;;;;;;;8EAE5D,6LAAC;oEAAE,WAAU;8EAAQ,MAAM,WAAW;;;;;;;;;;;;wDAEvC,MAAM,MAAM,kBACX,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;;wEACb,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA6B;;;;;;;;;;;;8EAE5D,6LAAC;oEAAE,WAAU;8EAAQ,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAS3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;wDACX,EAAE;wDACF,0BAAY,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;8DAE5D,6LAAC;oDAAK,WAAW,CAAC,2CAA2C,EAAE,cAAc,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,cAAc,MAAM,MAAM,EAAE,KAAK,EAAE;8DACtI,cAAc,MAAM,MAAM,EAAE,KAAK;;;;;;;;;;;;sDAItC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;;gEACE,EAAE;gEACF,0BAAY,6LAAC;oEAAK,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;8DAG9D,6LAAC;8DAAG,WAAW,MAAM,SAAS;;;;;;;;;;;;sDAGhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;;gEACE,EAAE;gEACF,0BAAY,6LAAC;oEAAK,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;8DAG9D,6LAAC;8DAAG,WAAW,uBAAuB,MAAM,OAAO;;;;;;;;;;;;;;;;;;8CAKvD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,6LAAC;;wDACE,EAAE;wDACF,0BAAY,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;sDAG9D,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DAAW,cAAc,MAAM,cAAc;;;;;;;;;;;;;;;;;gCAK7D,OAAO,MAAM,CAAC,MAAM,YAAY,EAAE,IAAI,CAAC,CAAA,MAAO,QAAQ,4BACrD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;;wDACE,EAAE;wDACF,0BAAY,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;wCAK7D,CAAC,MAAM,YAAY,CAAC,QAAQ,IAAI,MAAM,YAAY,CAAC,qBAAqB,IAAI,MAAM,YAAY,CAAC,KAAK,IAAI,MAAM,YAAY,CAAC,KAAK,IAAI,MAAM,YAAY,CAAC,IAAI,mBAC1J,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;wDACX,EAAE;wDACF,0BAAY,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;8DAE5D,6LAAC;oDAAI,WAAU;;wDACZ,MAAM,YAAY,CAAC,QAAQ,kBAC1B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEACV,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA8B;;;;;;;;;;;;8EAE7D,6LAAC;oEAAE,WAAU;8EAAqB,MAAM,YAAY,CAAC,QAAQ;;;;;;;;;;;;wDAGhE,MAAM,YAAY,CAAC,qBAAqB,kBACvC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEACV,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA8B;;;;;;;;;;;;8EAE7D,6LAAC;oEAAE,WAAU;8EAAqB,MAAM,YAAY,CAAC,qBAAqB;;;;;;;;;;;;wDAG7E,MAAM,YAAY,CAAC,KAAK,kBACvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEACV,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA8B;;;;;;;;;;;;8EAE7D,6LAAC;oEAAE,WAAU;8EAAqB,MAAM,YAAY,CAAC,KAAK;;;;;;;;;;;;wDAG7D,MAAM,YAAY,CAAC,KAAK,kBACvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEACV,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA8B;;;;;;;;;;;;8EAE7D,6LAAC;oEAAE,WAAU;8EAAqB,MAAM,YAAY,CAAC,KAAK;;;;;;;;;;;;wDAG7D,MAAM,YAAY,CAAC,IAAI,kBACtB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEACV,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA8B;;;;;;;;;;;;8EAE7D,6LAAC;oEAAE,WAAU;8EAAqB,MAAM,YAAY,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;wCAQlE,CAAC,MAAM,YAAY,CAAC,UAAU,IAAI,MAAM,YAAY,CAAC,YAAY,IAAI,MAAM,YAAY,CAAC,QAAQ,IAAI,MAAM,YAAY,CAAC,MAAM,mBAC5H,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;wDACX,EAAE;wDACF,0BAAY,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;8DAE5D,6LAAC;oDAAI,WAAU;;wDACZ,MAAM,YAAY,CAAC,UAAU,kBAC5B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEACV,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA8B;;;;;;;;;;;;8EAE7D,6LAAC;oEAAE,WAAU;8EAAqB,MAAM,YAAY,CAAC,UAAU;;;;;;;;;;;;wDAGlE,MAAM,YAAY,CAAC,YAAY,kBAC9B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEACV,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA8B;;;;;;;;;;;;8EAE7D,6LAAC;oEAAE,WAAU;8EAAqB,MAAM,YAAY,CAAC,YAAY;;;;;;;;;;;;wDAGpE,MAAM,YAAY,CAAC,QAAQ,kBAC1B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEACV,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA8B;;;;;;;;;;;;8EAE7D,6LAAC;oEAAE,WAAU;8EAAqB,MAAM,YAAY,CAAC,QAAQ;;;;;;;;;;;;wDAGhE,MAAM,YAAY,CAAC,MAAM,kBACxB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEACV,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA8B;;;;;;;;;;;;8EAE7D,6LAAC;oEAAE,WAAU;8EAAqB,MAAM,YAAY,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;wCAQpE,CAAC,MAAM,YAAY,CAAC,YAAY,IAAI,MAAM,YAAY,CAAC,OAAO,IAAI,MAAM,YAAY,CAAC,IAAI,mBACxF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;wDACX,EAAE;wDACF,0BAAY,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;8DAE5D,6LAAC;oDAAI,WAAU;;wDACZ,MAAM,YAAY,CAAC,YAAY,kBAC9B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEACV,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA8B;;;;;;;;;;;;8EAE7D,6LAAC;oEAAE,WAAU;8EAAqB,MAAM,YAAY,CAAC,YAAY;;;;;;;;;;;;wDAGpE,MAAM,YAAY,CAAC,OAAO,kBACzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEACV,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA8B;;;;;;;;;;;;8EAE7D,6LAAC;oEAAE,WAAU;8EAAqB,MAAM,YAAY,CAAC,OAAO;;;;;;;;;;;;wDAG/D,MAAM,YAAY,CAAC,IAAI,kBACtB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEACV,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA8B;;;;;;;;;;;;8EAE7D,6LAAC;oEAAE,WAAU;8EAAqB,MAAM,YAAY,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;wCAQlE,CAAC,MAAM,YAAY,CAAC,WAAW,IAAI,MAAM,YAAY,CAAC,UAAU,mBAC/D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;wDACX,EAAE;wDACF,0BAAY,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;8DAE5D,6LAAC;oDAAI,WAAU;;wDACZ,MAAM,YAAY,CAAC,WAAW,kBAC7B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEACV,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA8B;;;;;;;;;;;;8EAE7D,6LAAC;oEAAE,WAAU;8EAAqB,MAAM,YAAY,CAAC,WAAW;;;;;;;;;;;;wDAGnE,MAAM,YAAY,CAAC,UAAU,kBAC5B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEACV,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA8B;;;;;;;;;;;;8EAE7D,6LAAC;oEAAE,WAAU;8EAAqB,MAAM,YAAY,CAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;;wCAQxE,CAAC,MAAM,YAAY,CAAC,MAAM,IAAI,MAAM,YAAY,CAAC,SAAS,IAAI,MAAM,YAAY,CAAC,OAAO,mBACvF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;wDACX,EAAE;wDACF,0BAAY,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;8DAE5D,6LAAC;oDAAI,WAAU;;wDACZ,MAAM,YAAY,CAAC,MAAM,kBACxB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEACV,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA8B;;;;;;;;;;;;8EAE7D,6LAAC;oEAAE,WAAU;8EAAqB,MAAM,YAAY,CAAC,MAAM;;;;;;;;;;;;wDAG9D,MAAM,YAAY,CAAC,SAAS,kBAC3B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEACV,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA8B;;;;;;;;;;;;8EAE7D,6LAAC;oEAAE,WAAU;8EAAqB,MAAM,YAAY,CAAC,SAAS;;;;;;;;;;;;wDAGjE,MAAM,YAAY,CAAC,OAAO,kBACzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;wEACV,EAAE;wEACF,0BAAY,6LAAC;4EAAK,WAAU;sFAA8B;;;;;;;;;;;;8EAE7D,6LAAC;oEAAE,WAAU;8EAAqB,MAAM,YAAY,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAUzE,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,MAAM,GAAG,mBACrC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,uMAAA,CAAA,QAAS;oDAAC,WAAU;;;;;;8DACrB,6LAAC;;wDACE,EAAE;wDACF,0BAAY,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;sDAG9D,6LAAC;4CAAI,WAAU;sDACZ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBACxB,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,KAAK;gEACL,KAAK,GAAG,EAAE,oBAAoB,CAAC,EAAE,QAAQ,GAAG;gEAC5C,WAAU;gEACV,SAAS,IAAM,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;sEAGtC,6LAAC;4DAAI,WAAU;sEACZ,QAAQ;;;;;;;mDAVH;;;;;;;;;;;;;;;;gCAmBjB,MAAM,KAAK,kBACV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,6LAAC;;wDACE,EAAE;wDACF,0BAAY,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;sDAG9D,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;0DAAG,MAAM,KAAK;;;;;;;;;;;;;;;;;gCAMpB,MAAM,UAAU,IAAI,MAAM,UAAU,CAAC,MAAM,GAAG,mBAC7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,6LAAC;;wDACE,EAAE;wDACF,0BAAY,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;sDAG9D,6LAAC,mIAAA,CAAA,UAAU;4CACT,YAAY,MAAM,UAAU;4CAC5B,oBAAoB,KAAO;4CAC3B,UAAU;;;;;;;;;;;;gCAMf,MAAM,SAAS,WAAW,MAAM,eAAe,IAAI,MAAM,eAAe,CAAC,MAAM,GAAG,mBACjF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC;8DAAM,EAAE;;;;;;;;;;;;sDAEX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;4DAAK,WAAU;sEACb,EAAE;;;;;;;;;;;;8DAGP,6LAAC;oDAAI,WAAU;8DACZ,MAAM,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,sBACjC,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEACC,KAAK;wEACL,KAAK,GAAG,EAAE,4BAA4B,CAAC,EAAE,QAAQ,GAAG;wEACpD,WAAU;wEACV,SAAS,IAAM,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;8EAGtC,6LAAC;oEAAI,WAAU;8EACZ,QAAQ;;;;;;;2DAVH;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAqBtB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB;GAhiBwB;;QACL,4HAAA,CAAA,eAAY;QACL,iIAAA,CAAA,iBAAc;;;KAFhB", "debugId": null}}, {"offset": {"line": 3630, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/components/ImageUpload.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Upload, X, Image as ImageIcon, Plus } from 'lucide-react'\nimport { useTranslation } from '@/hooks/useTranslation'\n\ninterface ImageUploadProps {\n  images: string[]\n  onImagesChange: (images: string[]) => void\n  maxImages?: number\n}\n\nexport default function ImageUpload({ images, onImagesChange, maxImages = 10 }: ImageUploadProps) {\n  const [dragOver, setDragOver] = useState(false)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n  const { t } = useTranslation()\n\n  const handleFileSelect = (files: FileList | null) => {\n    if (!files) return\n\n    const newImages: string[] = []\n    const remainingSlots = maxImages - images.length\n\n    Array.from(files).slice(0, remainingSlots).forEach(file => {\n      if (file.type.startsWith('image/')) {\n        const reader = new FileReader()\n        reader.onload = (e) => {\n          const result = e.target?.result as string\n          if (result) {\n            newImages.push(result)\n            if (newImages.length === Math.min(files.length, remainingSlots)) {\n              onImagesChange([...images, ...newImages])\n            }\n          }\n        }\n        reader.readAsDataURL(file)\n      }\n    })\n  }\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault()\n    setDragOver(false)\n    handleFileSelect(e.dataTransfer.files)\n  }\n\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault()\n    setDragOver(true)\n  }\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault()\n    setDragOver(false)\n  }\n\n  const removeImage = (index: number) => {\n    const newImages = images.filter((_, i) => i !== index)\n    onImagesChange(newImages)\n  }\n\n  const openFileDialog = () => {\n    fileInputRef.current?.click()\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* منطقة رفع الصور */}\n      <div\n        className={`border-2 border-dashed rounded-lg p-6 text-center transition-all duration-300 cursor-pointer ${\n          dragOver\n            ? 'border-pink-400 bg-pink-50'\n            : images.length >= maxImages\n            ? 'border-gray-200 bg-gray-50 cursor-not-allowed'\n            : 'border-gray-300 hover:border-pink-400 hover:bg-pink-50'\n        }`}\n        onDrop={handleDrop}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onClick={images.length < maxImages ? openFileDialog : undefined}\n      >\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          accept=\"image/*\"\n          multiple\n          onChange={(e) => handleFileSelect(e.target.files)}\n          className=\"hidden\"\n          disabled={images.length >= maxImages}\n        />\n\n        {images.length >= maxImages ? (\n          <div className=\"space-y-2\">\n            <ImageIcon className=\"w-12 h-12 text-gray-400 mx-auto\" />\n            <p className=\"text-gray-500\">{t('max_images_reached')} ({maxImages})</p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            <Upload className=\"w-12 h-12 text-gray-400 mx-auto\" />\n            <div>\n              <p className=\"text-lg font-medium text-gray-700\">\n                {dragOver ? t('drop_images_here') : t('click_or_drag_images')}\n              </p>\n              <p className=\"text-sm text-gray-500\">\n                {t('image_upload_format')} ({t('max_images_text')} {maxImages} {t('images_text')})\n              </p>\n              <p className=\"text-xs text-gray-400 mt-1\">\n                {images.length} {t('of')} {maxImages} {t('images_text')}\n              </p>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* معرض الصور */}\n      {images.length > 0 && (\n        <div className=\"space-y-4\">\n          <h4 className=\"font-medium text-gray-700\">الصور المرفوعة:</h4>\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4\">\n            <AnimatePresence>\n              {images.map((image, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  exit={{ opacity: 0, scale: 0.8 }}\n                  className=\"relative group\"\n                >\n                  <div className=\"aspect-square rounded-lg overflow-hidden border border-gray-200\">\n                    <img\n                      src={image}\n                      alt={`صورة ${index + 1}`}\n                      className=\"w-full h-full object-cover\"\n                    />\n                  </div>\n                  \n                  {/* زر الحذف */}\n                  <button\n                    onClick={() => removeImage(index)}\n                    className=\"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-red-600\"\n                  >\n                    <X className=\"w-3 h-3\" />\n                  </button>\n                  \n                  {/* رقم الصورة */}\n                  <div className=\"absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded\">\n                    {index + 1}\n                  </div>\n                </motion.div>\n              ))}\n              \n              {/* زر إضافة المزيد */}\n              {images.length < maxImages && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"aspect-square border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center cursor-pointer hover:border-pink-400 hover:bg-pink-50 transition-all duration-300\"\n                  onClick={openFileDialog}\n                >\n                  <div className=\"text-center\">\n                    <Plus className=\"w-8 h-8 text-gray-400 mx-auto mb-2\" />\n                    <p className=\"text-xs text-gray-500\">{t('add_image')}</p>\n                  </div>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAae,SAAS,YAAY,EAAE,MAAM,EAAE,cAAc,EAAE,YAAY,EAAE,EAAoB;;IAC9F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAE3B,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,OAAO;QAEZ,MAAM,YAAsB,EAAE;QAC9B,MAAM,iBAAiB,YAAY,OAAO,MAAM;QAEhD,MAAM,IAAI,CAAC,OAAO,KAAK,CAAC,GAAG,gBAAgB,OAAO,CAAC,CAAA;YACjD,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBAClC,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM,GAAG,CAAC;oBACf,MAAM,SAAS,EAAE,MAAM,EAAE;oBACzB,IAAI,QAAQ;wBACV,UAAU,IAAI,CAAC;wBACf,IAAI,UAAU,MAAM,KAAK,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,iBAAiB;4BAC/D,eAAe;mCAAI;mCAAW;6BAAU;wBAC1C;oBACF;gBACF;gBACA,OAAO,aAAa,CAAC;YACvB;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,YAAY;QACZ,iBAAiB,EAAE,YAAY,CAAC,KAAK;IACvC;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,YAAY;IACd;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,YAAY;IACd;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,YAAY,OAAO,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAChD,eAAe;IACjB;IAEA,MAAM,iBAAiB;QACrB,aAAa,OAAO,EAAE;IACxB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAW,CAAC,6FAA6F,EACvG,WACI,+BACA,OAAO,MAAM,IAAI,YACjB,kDACA,0DACJ;gBACF,QAAQ;gBACR,YAAY;gBACZ,aAAa;gBACb,SAAS,OAAO,MAAM,GAAG,YAAY,iBAAiB;;kCAEtD,6LAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAO;wBACP,QAAQ;wBACR,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wBAChD,WAAU;wBACV,UAAU,OAAO,MAAM,IAAI;;;;;;oBAG5B,OAAO,MAAM,IAAI,0BAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;gCAAE,WAAU;;oCAAiB,EAAE;oCAAsB;oCAAG;oCAAU;;;;;;;;;;;;6CAGrE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDACV,WAAW,EAAE,sBAAsB,EAAE;;;;;;kDAExC,6LAAC;wCAAE,WAAU;;4CACV,EAAE;4CAAuB;4CAAG,EAAE;4CAAmB;4CAAE;4CAAU;4CAAE,EAAE;4CAAe;;;;;;;kDAEnF,6LAAC;wCAAE,WAAU;;4CACV,OAAO,MAAM;4CAAC;4CAAE,EAAE;4CAAM;4CAAE;4CAAU;4CAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;YAQlD,OAAO,MAAM,GAAG,mBACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA4B;;;;;;kCAC1C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;;gCACb,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,MAAM;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAC/B,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,KAAK;oDACL,KAAK,CAAC,KAAK,EAAE,QAAQ,GAAG;oDACxB,WAAU;;;;;;;;;;;0DAKd,6LAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;0DAIf,6LAAC;gDAAI,WAAU;0DACZ,QAAQ;;;;;;;uCAxBN;;;;;gCA8BR,OAAO,MAAM,GAAG,2BACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,WAAU;oCACV,SAAS;8CAET,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAE,WAAU;0DAAyB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5D;GA/JwB;;QAGR,iIAAA,CAAA,iBAAc;;;KAHN", "debugId": null}}, {"offset": {"line": 3975, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/utils/inputValidation.ts"], "sourcesContent": ["/**\n * دوال التحقق من صحة الإدخال للحقول الرقمية\n */\n\n// التحقق من أن النص يحتوي على أرقام فقط\nexport const isNumericOnly = (value: string): boolean => {\n  return /^\\d*\\.?\\d*$/.test(value)\n}\n\n// التحقق من أن النص يحتوي على أرقام صحيحة فقط (بدون فواصل عشرية)\nexport const isIntegerOnly = (value: string): boolean => {\n  return /^\\d*$/.test(value)\n}\n\n// التحقق من صحة رقم الهاتف (أرقام فقط مع إمكانية وجود + في البداية)\nexport const isValidPhoneNumber = (value: string): boolean => {\n  return /^(\\+)?\\d*$/.test(value)\n}\n\n// تنظيف الإدخال من الأحرف غير الرقمية\nexport const cleanNumericInput = (value: string): string => {\n  return value.replace(/[^\\d.]/g, '')\n}\n\n// تنظيف الإدخال من الأحرف غير الرقمية (أرقام صحيحة فقط)\nexport const cleanIntegerInput = (value: string): string => {\n  return value.replace(/[^\\d]/g, '')\n}\n\n// تنظيف رقم الهاتف\nexport const cleanPhoneInput = (value: string): string => {\n  // السماح بـ + في البداية فقط\n  if (value.startsWith('+')) {\n    return '+' + value.slice(1).replace(/[^\\d]/g, '')\n  }\n  return value.replace(/[^\\d]/g, '')\n}\n\n// التحقق من صحة المقاس (رقم موجب)\nexport const isValidMeasurement = (value: string): boolean => {\n  const num = parseFloat(value)\n  return !isNaN(num) && num > 0\n}\n\n// التحقق من صحة السعر (رقم موجب)\nexport const isValidPrice = (value: string): boolean => {\n  const num = parseFloat(value)\n  return !isNaN(num) && num > 0\n}\n\n// رسائل الخطأ\nexport const getValidationErrorMessage = (fieldType: string, language: 'ar' | 'en' = 'ar'): string => {\n  const messages = {\n    ar: {\n      measurement: 'يرجى إدخال رقم صحيح للمقاس',\n      price: 'يرجى إدخال سعر صحيح',\n      phone: 'يرجى إدخال رقم هاتف صحيح',\n      orderNumber: 'يرجى إدخال رقم طلب صحيح',\n      numeric: 'يرجى إدخال أرقام فقط',\n      positive: 'يرجى إدخال رقم أكبر من الصفر'\n    },\n    en: {\n      measurement: 'Please enter a valid measurement',\n      price: 'Please enter a valid price',\n      phone: 'Please enter a valid phone number',\n      orderNumber: 'Please enter a valid order number',\n      numeric: 'Please enter numbers only',\n      positive: 'Please enter a number greater than zero'\n    }\n  }\n  \n  return messages[language][fieldType] || messages[language].numeric\n}\n\n// دالة للتعامل مع تغيير الإدخال في الحقول الرقمية\nexport const handleNumericInputChange = (\n  value: string,\n  fieldType: 'measurement' | 'price' | 'phone' | 'orderNumber' | 'integer' | 'decimal',\n  onChange: (value: string) => void,\n  onError?: (error: string | null) => void\n) => {\n  let cleanedValue = value\n  let isValid = true\n  let errorMessage: string | null = null\n\n  switch (fieldType) {\n    case 'measurement':\n      cleanedValue = cleanNumericInput(value)\n      isValid = isValidMeasurement(cleanedValue) || cleanedValue === ''\n      if (!isValid && cleanedValue !== '') {\n        errorMessage = getValidationErrorMessage('measurement')\n      }\n      break\n\n    case 'price':\n      cleanedValue = cleanNumericInput(value)\n      isValid = isValidPrice(cleanedValue) || cleanedValue === ''\n      if (!isValid && cleanedValue !== '') {\n        errorMessage = getValidationErrorMessage('price')\n      }\n      break\n\n    case 'phone':\n      cleanedValue = cleanPhoneInput(value)\n      isValid = isValidPhoneNumber(cleanedValue)\n      if (!isValid) {\n        errorMessage = getValidationErrorMessage('phone')\n      }\n      break\n\n    case 'orderNumber':\n      cleanedValue = cleanIntegerInput(value)\n      isValid = isIntegerOnly(cleanedValue)\n      if (!isValid) {\n        errorMessage = getValidationErrorMessage('orderNumber')\n      }\n      break\n\n    case 'integer':\n      cleanedValue = cleanIntegerInput(value)\n      isValid = isIntegerOnly(cleanedValue)\n      if (!isValid) {\n        errorMessage = getValidationErrorMessage('numeric')\n      }\n      break\n\n    case 'decimal':\n      cleanedValue = cleanNumericInput(value)\n      isValid = isNumericOnly(cleanedValue)\n      if (!isValid) {\n        errorMessage = getValidationErrorMessage('numeric')\n      }\n      break\n  }\n\n  onChange(cleanedValue)\n  if (onError) {\n    onError(errorMessage)\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,wCAAwC;;;;;;;;;;;;;AACjC,MAAM,gBAAgB,CAAC;IAC5B,OAAO,cAAc,IAAI,CAAC;AAC5B;AAGO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,QAAQ,IAAI,CAAC;AACtB;AAGO,MAAM,qBAAqB,CAAC;IACjC,OAAO,aAAa,IAAI,CAAC;AAC3B;AAGO,MAAM,oBAAoB,CAAC;IAChC,OAAO,MAAM,OAAO,CAAC,WAAW;AAClC;AAGO,MAAM,oBAAoB,CAAC;IAChC,OAAO,MAAM,OAAO,CAAC,UAAU;AACjC;AAGO,MAAM,kBAAkB,CAAC;IAC9B,6BAA6B;IAC7B,IAAI,MAAM,UAAU,CAAC,MAAM;QACzB,OAAO,MAAM,MAAM,KAAK,CAAC,GAAG,OAAO,CAAC,UAAU;IAChD;IACA,OAAO,MAAM,OAAO,CAAC,UAAU;AACjC;AAGO,MAAM,qBAAqB,CAAC;IACjC,MAAM,MAAM,WAAW;IACvB,OAAO,CAAC,MAAM,QAAQ,MAAM;AAC9B;AAGO,MAAM,eAAe,CAAC;IAC3B,MAAM,MAAM,WAAW;IACvB,OAAO,CAAC,MAAM,QAAQ,MAAM;AAC9B;AAGO,MAAM,4BAA4B,CAAC,WAAmB,WAAwB,IAAI;IACvF,MAAM,WAAW;QACf,IAAI;YACF,aAAa;YACb,OAAO;YACP,OAAO;YACP,aAAa;YACb,SAAS;YACT,UAAU;QACZ;QACA,IAAI;YACF,aAAa;YACb,OAAO;YACP,OAAO;YACP,aAAa;YACb,SAAS;YACT,UAAU;QACZ;IACF;IAEA,OAAO,QAAQ,CAAC,SAAS,CAAC,UAAU,IAAI,QAAQ,CAAC,SAAS,CAAC,OAAO;AACpE;AAGO,MAAM,2BAA2B,CACtC,OACA,WACA,UACA;IAEA,IAAI,eAAe;IACnB,IAAI,UAAU;IACd,IAAI,eAA8B;IAElC,OAAQ;QACN,KAAK;YACH,eAAe,kBAAkB;YACjC,UAAU,mBAAmB,iBAAiB,iBAAiB;YAC/D,IAAI,CAAC,WAAW,iBAAiB,IAAI;gBACnC,eAAe,0BAA0B;YAC3C;YACA;QAEF,KAAK;YACH,eAAe,kBAAkB;YACjC,UAAU,aAAa,iBAAiB,iBAAiB;YACzD,IAAI,CAAC,WAAW,iBAAiB,IAAI;gBACnC,eAAe,0BAA0B;YAC3C;YACA;QAEF,KAAK;YACH,eAAe,gBAAgB;YAC/B,UAAU,mBAAmB;YAC7B,IAAI,CAAC,SAAS;gBACZ,eAAe,0BAA0B;YAC3C;YACA;QAEF,KAAK;YACH,eAAe,kBAAkB;YACjC,UAAU,cAAc;YACxB,IAAI,CAAC,SAAS;gBACZ,eAAe,0BAA0B;YAC3C;YACA;QAEF,KAAK;YACH,eAAe,kBAAkB;YACjC,UAAU,cAAc;YACxB,IAAI,CAAC,SAAS;gBACZ,eAAe,0BAA0B;YAC3C;YACA;QAEF,KAAK;YACH,eAAe,kBAAkB;YACjC,UAAU,cAAc;YACxB,IAAI,CAAC,SAAS;gBACZ,eAAe,0BAA0B;YAC3C;YACA;IACJ;IAEA,SAAS;IACT,IAAI,SAAS;QACX,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 4103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/components/NumericInput.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { AlertCircle } from 'lucide-react'\nimport { handleNumericInputChange } from '@/utils/inputValidation'\n\ninterface NumericInputProps {\n  value: string\n  onChange: (value: string) => void\n  type: 'measurement' | 'price' | 'phone' | 'orderNumber' | 'integer' | 'decimal'\n  placeholder?: string\n  className?: string\n  disabled?: boolean\n  required?: boolean\n  label?: string\n  id?: string\n}\n\nexport default function NumericInput({\n  value,\n  onChange,\n  type,\n  placeholder,\n  className = '',\n  disabled = false,\n  required = false,\n  label,\n  id\n}: NumericInputProps) {\n  const [error, setError] = useState<string | null>(null)\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const inputValue = e.target.value\n    \n    handleNumericInputChange(\n      inputValue,\n      type,\n      onChange,\n      setError\n    )\n  }\n\n  const baseClassName = `w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors duration-200 ${\n    error ? 'border-red-500 bg-red-50' : 'border-gray-300'\n  } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''} ${className}`\n\n  return (\n    <div className=\"space-y-2\">\n      {label && (\n        <label htmlFor={id} className=\"block text-sm font-medium text-gray-700\">\n          {label}\n          {required && <span className=\"text-red-500 mr-1\">*</span>}\n        </label>\n      )}\n      \n      <div className=\"relative\">\n        <input\n          id={id}\n          type=\"text\"\n          value={value}\n          onChange={handleChange}\n          placeholder={placeholder}\n          className={baseClassName}\n          disabled={disabled}\n          required={required}\n          inputMode={type === 'phone' ? 'tel' : 'numeric'}\n          autoComplete={type === 'phone' ? 'tel' : 'off'}\n        />\n        \n        {error && (\n          <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2\">\n            <AlertCircle className=\"w-5 h-5 text-red-500\" />\n          </div>\n        )}\n      </div>\n      \n      {error && (\n        <p className=\"text-sm text-red-600 flex items-center space-x-1 space-x-reverse\">\n          <AlertCircle className=\"w-4 h-4\" />\n          <span>{error}</span>\n        </p>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAkBe,SAAS,aAAa,EACnC,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,WAAW,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,WAAW,KAAK,EAChB,KAAK,EACL,EAAE,EACgB;;IAClB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa,EAAE,MAAM,CAAC,KAAK;QAEjC,CAAA,GAAA,kIAAA,CAAA,2BAAwB,AAAD,EACrB,YACA,MACA,UACA;IAEJ;IAEA,MAAM,gBAAgB,CAAC,4HAA4H,EACjJ,QAAQ,6BAA6B,kBACtC,CAAC,EAAE,WAAW,mCAAmC,GAAG,CAAC,EAAE,WAAW;IAEnE,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,SAAS;gBAAI,WAAU;;oBAC3B;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,IAAI;wBACJ,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,aAAa;wBACb,WAAW;wBACX,UAAU;wBACV,UAAU;wBACV,WAAW,SAAS,UAAU,QAAQ;wBACtC,cAAc,SAAS,UAAU,QAAQ;;;;;;oBAG1C,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAK5B,uBACC,6LAAC;gBAAE,WAAU;;kCACX,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;kCAAM;;;;;;;;;;;;;;;;;;AAKjB;GAlEwB;KAAA", "debugId": null}}, {"offset": {"line": 4228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/components/EditOrderModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport {\n  X,\n  Save,\n  User,\n  Phone,\n  Package,\n  Ruler,\n  DollarSign,\n  MessageSquare,\n  UserCheck,\n  Calendar,\n  CheckCircle,\n  AlertCircle,\n  Image as ImageIcon\n} from 'lucide-react'\nimport ImageUpload from './ImageUpload'\nimport VoiceNotes from './VoiceNotes'\nimport NumericInput from './NumericInput'\nimport { Order, Worker } from '@/store/dataStore'\nimport { useTranslation } from '@/hooks/useTranslation'\n\ninterface EditOrderModalProps {\n  order: Order | null\n  workers: Worker[]\n  isOpen: boolean\n  onClose: () => void\n  onSave: (orderId: string, updates: Partial<Order>) => void\n}\n\nexport default function EditOrderModal({ order, workers, isOpen, onClose, onSave }: EditOrderModalProps) {\n  const { t } = useTranslation()\n  const [formData, setFormData] = useState<Partial<Order>>({})\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)\n\n  useEffect(() => {\n    if (order) {\n      setFormData({\n        clientName: order.clientName,\n        clientPhone: order.clientPhone,\n        description: order.description,\n        fabric: order.fabric,\n        price: order.price,\n        status: order.status,\n        assignedWorker: order.assignedWorker,\n        dueDate: order.dueDate,\n        notes: order.notes,\n        voiceNotes: order.voiceNotes || [],\n        images: order.images || [],\n        measurements: { ...order.measurements }\n      })\n    }\n  }, [order])\n\n  const handleInputChange = (field: string, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }))\n  }\n\n  const handleMeasurementChange = (field: string, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      measurements: {\n        ...prev.measurements,\n        [field]: value\n      }\n    }))\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!order) return\n\n    // التحقق من الحقول المطلوبة\n    if (!formData.clientName || !formData.clientPhone || !formData.description || !formData.price || !formData.dueDate) {\n      setMessage({ type: 'error', text: t('fill_required_fields') })\n      return\n    }\n\n    setIsSubmitting(true)\n    setMessage(null)\n\n    try {\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      \n      // تحويل المقاسات إلى أرقام\n      const updatedMeasurements = Object.keys(formData.measurements || {}).reduce((acc, key) => {\n        const value = (formData.measurements as any)?.[key]\n        acc[key] = value && value !== '' ? Number(value) : undefined\n        return acc\n      }, {} as any)\n\n      onSave(order.id, {\n        ...formData,\n        price: Number(formData.price),\n        measurements: updatedMeasurements,\n        updatedAt: new Date().toISOString()\n      })\n      \n      setMessage({ type: 'success', text: t('order_updated_success') })\n      \n      setTimeout(() => {\n        onClose()\n        setMessage(null)\n      }, 1500)\n      \n    } catch (error) {\n      setMessage({ type: 'error', text: t('order_update_error') })\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  if (!order) return null\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\n          {/* خلفية مظلمة */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"absolute inset-0 bg-black/50 backdrop-blur-sm\"\n            onClick={onClose}\n          />\n          \n          {/* النافذة المنبثقة */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9, y: 20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.9, y: 20 }}\n            className=\"relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\"\n          >\n            {/* رأس النافذة */}\n            <div className=\"sticky top-0 bg-white border-b border-gray-200 p-6 rounded-t-2xl\">\n              <div className=\"flex items-center justify-between\">\n                <h2 className=\"text-2xl font-bold text-gray-800\">{t('edit_order')}</h2>\n                <button\n                  onClick={onClose}\n                  className=\"p-2 text-gray-400 hover:text-gray-600 transition-colors duration-300\"\n                >\n                  <X className=\"w-6 h-6\" />\n                </button>\n              </div>\n            </div>\n\n            {/* رسالة النجاح/الخطأ */}\n            {message && (\n              <div className={`mx-6 mt-4 p-4 rounded-lg flex items-center space-x-3 space-x-reverse ${\n                message.type === 'success' \n                  ? 'bg-green-50 text-green-800 border border-green-200' \n                  : 'bg-red-50 text-red-800 border border-red-200'\n              }`}>\n                {message.type === 'success' ? (\n                  <CheckCircle className=\"w-5 h-5 text-green-600\" />\n                ) : (\n                  <AlertCircle className=\"w-5 h-5 text-red-600\" />\n                )}\n                <span>{message.text}</span>\n              </div>\n            )}\n\n            {/* محتوى النموذج */}\n            <form onSubmit={handleSubmit} className=\"p-6 space-y-8\">\n              {/* معلومات الزبون */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse\">\n                  <User className=\"w-5 h-5 text-pink-600\" />\n                  <span>{t('customer_information')}</span>\n                </h3>\n                \n                <div className=\"grid md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      {t('client_name_required')}\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.clientName || ''}\n                      onChange={(e) => handleInputChange('clientName', e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent\"\n                      required\n                    />\n                  </div>\n                  \n                  <div>\n                    <NumericInput\n                      value={formData.clientPhone || ''}\n                      onChange={(value) => handleInputChange('clientPhone', value)}\n                      type=\"phone\"\n                      label={t('phone_required')}\n                      required\n                      disabled={isSubmitting}\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* تفاصيل الطلب */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse\">\n                  <Package className=\"w-5 h-5 text-pink-600\" />\n                  <span>{t('order_details')}</span>\n                </h3>\n                \n                <div className=\"grid md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      {t('order_description_required')}\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.description || ''}\n                      onChange={(e) => handleInputChange('description', e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent\"\n                      required\n                    />\n                  </div>\n                  \n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      {t('fabric_type_optional')}\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.fabric || ''}\n                      onChange={(e) => handleInputChange('fabric', e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <NumericInput\n                      value={formData.price?.toString() || ''}\n                      onChange={(value) => handleInputChange('price', value ? Number(value) : '')}\n                      type=\"price\"\n                      label={t('price_sar_required')}\n                      required\n                      disabled={isSubmitting}\n                    />\n                  </div>\n                  \n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      {t('delivery_date_required')}\n                    </label>\n                    <input\n                      type=\"date\"\n                      value={formData.dueDate || ''}\n                      onChange={(e) => handleInputChange('dueDate', e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent\"\n                      required\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* الحالة والعامل */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse\">\n                  <UserCheck className=\"w-5 h-5 text-pink-600\" />\n                  <span>{t('status_and_worker')}</span>\n                </h3>\n                \n                <div className=\"grid md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      {t('order_status')}\n                    </label>\n                    <select\n                      value={formData.status || ''}\n                      onChange={(e) => handleInputChange('status', e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent\"\n                    >\n                      <option value=\"pending\">{t('status_pending')}</option>\n                      <option value=\"in_progress\">{t('status_in_progress')}</option>\n                      <option value=\"completed\">{t('status_completed')}</option>\n                      <option value=\"delivered\">{t('status_delivered')}</option>\n                      <option value=\"cancelled\">{t('status_cancelled')}</option>\n                    </select>\n                  </div>\n                  \n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      {t('responsible_worker')}\n                    </label>\n                    <select\n                      value={formData.assignedWorker || ''}\n                      onChange={(e) => handleInputChange('assignedWorker', e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent\"\n                    >\n                      <option value=\"\">{t('choose_worker')}</option>\n                      {workers.filter(w => w.is_active).map(worker => (\n                        <option key={worker.id} value={worker.id}>\n                          {worker.full_name} - {worker.specialty}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n              </div>\n\n              {/* صور التصميم */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse\">\n                  <ImageIcon className=\"w-5 h-5 text-pink-600\" />\n                  <span>{t('design_images')}</span>\n                </h3>\n\n                <ImageUpload\n                  images={formData.images || []}\n                  onImagesChange={(images) => handleInputChange('images', images)}\n                  maxImages={5}\n                />\n              </div>\n\n              {/* المقاسات */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse\">\n                  <Ruler className=\"w-5 h-5 text-pink-600\" />\n                  <span>{t('measurements_cm')}</span>\n                </h3>\n                \n                <div className=\"space-y-8\">\n                  {/* المقاسات الأساسية */}\n                  <div>\n                    <h4 className=\"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2\">\n                      {t('basic_measurements')}\n                    </h4>\n                    <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                      <div>\n                        <NumericInput\n                          value={formData.measurements?.shoulder?.toString() || ''}\n                          onChange={(value) => handleMeasurementChange('shoulder', value)}\n                          type=\"measurement\"\n                          label={t('shoulder')}\n                          placeholder={t('cm_placeholder')}\n                          disabled={isSubmitting}\n                        />\n                      </div>\n\n                      <div>\n                        <NumericInput\n                          value={formData.measurements?.shoulderCircumference?.toString() || ''}\n                          onChange={(value) => handleMeasurementChange('shoulderCircumference', value)}\n                          type=\"measurement\"\n                          label={t('shoulder_circumference')}\n                          placeholder={t('cm_placeholder')}\n                          disabled={isSubmitting}\n                        />\n                      </div>\n\n                      <div>\n                        <NumericInput\n                          value={formData.measurements?.chest?.toString() || ''}\n                          onChange={(value) => handleMeasurementChange('chest', value)}\n                          type=\"measurement\"\n                          label={t('chest')}\n                          placeholder={t('cm_placeholder')}\n                          disabled={isSubmitting}\n                        />\n                      </div>\n\n                      <div>\n                        <NumericInput\n                          value={formData.measurements?.waist?.toString() || ''}\n                          onChange={(value) => handleMeasurementChange('waist', value)}\n                          type=\"measurement\"\n                          label={t('waist')}\n                          placeholder={t('cm_placeholder')}\n                          disabled={isSubmitting}\n                        />\n                      </div>\n\n                      <div>\n                        <NumericInput\n                          value={formData.measurements?.hips?.toString() || ''}\n                          onChange={(value) => handleMeasurementChange('hips', value)}\n                          type=\"measurement\"\n                          label={t('hips')}\n                          placeholder={t('cm_placeholder')}\n                          disabled={isSubmitting}\n                        />\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* مقاسات التفصيل المتقدمة */}\n                  <div>\n                    <h4 className=\"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2\">\n                      {t('advanced_measurements')}\n                    </h4>\n                    <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                      <div>\n                        <NumericInput\n                          value={formData.measurements?.dartLength?.toString() || ''}\n                          onChange={(value) => handleMeasurementChange('dartLength', value)}\n                          type=\"measurement\"\n                          label={t('dart_length')}\n                          placeholder={t('cm_placeholder')}\n                          disabled={isSubmitting}\n                        />\n                      </div>\n\n                      <div>\n                        <NumericInput\n                          value={formData.measurements?.bodiceLength?.toString() || ''}\n                          onChange={(value) => handleMeasurementChange('bodiceLength', value)}\n                          type=\"measurement\"\n                          label={t('bodice_length')}\n                          placeholder={t('cm_placeholder')}\n                          disabled={isSubmitting}\n                        />\n                      </div>\n\n                      <div>\n                        <NumericInput\n                          value={formData.measurements?.neckline?.toString() || ''}\n                          onChange={(value) => handleMeasurementChange('neckline', value)}\n                          type=\"measurement\"\n                          label={t('neckline')}\n                          placeholder={t('cm_placeholder')}\n                          disabled={isSubmitting}\n                        />\n                      </div>\n\n                      <div>\n                        <NumericInput\n                          value={formData.measurements?.armpit?.toString() || ''}\n                          onChange={(value) => handleMeasurementChange('armpit', value)}\n                          type=\"measurement\"\n                          label={t('armpit')}\n                          placeholder={t('cm_placeholder')}\n                          disabled={isSubmitting}\n                        />\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* مقاسات الأكمام */}\n                  <div>\n                    <h4 className=\"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2\">\n                      {t('sleeve_measurements')}\n                    </h4>\n                    <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                      <div>\n                        <NumericInput\n                          value={formData.measurements?.sleeveLength?.toString() || ''}\n                          onChange={(value) => handleMeasurementChange('sleeveLength', value)}\n                          type=\"measurement\"\n                          label={t('sleeve_length')}\n                          placeholder={t('cm_placeholder')}\n                          disabled={isSubmitting}\n                        />\n                      </div>\n\n                      <div>\n                        <NumericInput\n                          value={formData.measurements?.forearm?.toString() || ''}\n                          onChange={(value) => handleMeasurementChange('forearm', value)}\n                          type=\"measurement\"\n                          label={t('forearm')}\n                          placeholder={t('cm_placeholder')}\n                          disabled={isSubmitting}\n                        />\n                      </div>\n\n                      <div>\n                        <NumericInput\n                          value={formData.measurements?.cuff?.toString() || ''}\n                          onChange={(value) => handleMeasurementChange('cuff', value)}\n                          type=\"measurement\"\n                          label={t('cuff')}\n                          placeholder={t('cm_placeholder')}\n                          disabled={isSubmitting}\n                        />\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* مقاسات الطول */}\n                  <div>\n                    <h4 className=\"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2\">\n                      {t('length_measurements')}\n                    </h4>\n                    <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                      <div>\n                        <NumericInput\n                          value={formData.measurements?.frontLength?.toString() || ''}\n                          onChange={(value) => handleMeasurementChange('frontLength', value)}\n                          type=\"measurement\"\n                          label={t('front_length')}\n                          placeholder={t('cm_placeholder')}\n                          disabled={isSubmitting}\n                        />\n                      </div>\n\n                      <div>\n                        <NumericInput\n                          value={formData.measurements?.backLength?.toString() || ''}\n                          onChange={(value) => handleMeasurementChange('backLength', value)}\n                          type=\"measurement\"\n                          label={t('back_length')}\n                          placeholder={t('cm_placeholder')}\n                          disabled={isSubmitting}\n                        />\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* الملاحظات */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-bold text-gray-800 flex items-center space-x-2 space-x-reverse\">\n                  <MessageSquare className=\"w-5 h-5 text-pink-600\" />\n                  <span>{t('notes_section')}</span>\n                </h3>\n                \n                <textarea\n                  value={formData.notes || ''}\n                  onChange={(e) => handleInputChange('notes', e.target.value)}\n                  rows={4}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent\"\n                  placeholder={t('additional_notes_placeholder')}\n                />\n\n                {/* الملاحظات الصوتية */}\n                <div className=\"mt-6\">\n                  <VoiceNotes\n                    voiceNotes={formData.voiceNotes || []}\n                    onVoiceNotesChange={(voiceNotes) => handleInputChange('voiceNotes', voiceNotes)}\n                    disabled={isSubmitting}\n                  />\n                </div>\n              </div>\n            </form>\n\n            {/* تذييل النافذة */}\n            <div className=\"sticky bottom-0 bg-white border-t border-gray-200 p-6 rounded-b-2xl\">\n              <div className=\"flex gap-4 justify-end\">\n                <button\n                  type=\"button\"\n                  onClick={onClose}\n                  className=\"btn-secondary px-6 py-2\"\n                  disabled={isSubmitting}\n                >\n                  {t('cancel')}\n                </button>\n                <button\n                  onClick={handleSubmit}\n                  disabled={isSubmitting}\n                  className=\"btn-primary px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 space-x-reverse\"\n                >\n                  {isSubmitting ? (\n                    <>\n                      <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                      <span>{t('saving')}</span>\n                    </>\n                  ) : (\n                    <>\n                      <Save className=\"w-4 h-4\" />\n                      <span>{t('save_changes')}</span>\n                    </>\n                  )}\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      )}\n    </AnimatePresence>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AACA;AAEA;;;AAvBA;;;;;;;;AAiCe,SAAS,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAuB;;IACrG,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,CAAC;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsD;IAE3F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,OAAO;gBACT,YAAY;oBACV,YAAY,MAAM,UAAU;oBAC5B,aAAa,MAAM,WAAW;oBAC9B,aAAa,MAAM,WAAW;oBAC9B,QAAQ,MAAM,MAAM;oBACpB,OAAO,MAAM,KAAK;oBAClB,QAAQ,MAAM,MAAM;oBACpB,gBAAgB,MAAM,cAAc;oBACpC,SAAS,MAAM,OAAO;oBACtB,OAAO,MAAM,KAAK;oBAClB,YAAY,MAAM,UAAU,IAAI,EAAE;oBAClC,QAAQ,MAAM,MAAM,IAAI,EAAE;oBAC1B,cAAc;wBAAE,GAAG,MAAM,YAAY;oBAAC;gBACxC;YACF;QACF;mCAAG;QAAC;KAAM;IAEV,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,0BAA0B,CAAC,OAAe;QAC9C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,cAAc;oBACZ,GAAG,KAAK,YAAY;oBACpB,CAAC,MAAM,EAAE;gBACX;YACF,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,OAAO;QAEZ,4BAA4B;QAC5B,IAAI,CAAC,SAAS,UAAU,IAAI,CAAC,SAAS,WAAW,IAAI,CAAC,SAAS,WAAW,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,OAAO,EAAE;YAClH,WAAW;gBAAE,MAAM;gBAAS,MAAM,EAAE;YAAwB;YAC5D;QACF;QAEA,gBAAgB;QAChB,WAAW;QAEX,IAAI;YACF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,2BAA2B;YAC3B,MAAM,sBAAsB,OAAO,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK;gBAChF,MAAM,QAAS,SAAS,YAAY,EAAU,CAAC,IAAI;gBACnD,GAAG,CAAC,IAAI,GAAG,SAAS,UAAU,KAAK,OAAO,SAAS;gBACnD,OAAO;YACT,GAAG,CAAC;YAEJ,OAAO,MAAM,EAAE,EAAE;gBACf,GAAG,QAAQ;gBACX,OAAO,OAAO,SAAS,KAAK;gBAC5B,cAAc;gBACd,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,WAAW;gBAAE,MAAM;gBAAW,MAAM,EAAE;YAAyB;YAE/D,WAAW;gBACT;gBACA,WAAW;YACb,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,WAAW;gBAAE,MAAM;gBAAS,MAAM,EAAE;YAAsB;QAC5D,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,CAAC,OAAO,OAAO;IAEnB,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACzC,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACtC,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoC,EAAE;;;;;;kDACpD,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;wBAMlB,yBACC,6LAAC;4BAAI,WAAW,CAAC,qEAAqE,EACpF,QAAQ,IAAI,KAAK,YACb,uDACA,gDACJ;;gCACC,QAAQ,IAAI,KAAK,0BAChB,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;yDAEvB,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CAEzB,6LAAC;8CAAM,QAAQ,IAAI;;;;;;;;;;;;sCAKvB,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAM,EAAE;;;;;;;;;;;;sDAGX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEACd,EAAE;;;;;;sEAEL,6LAAC;4DACC,MAAK;4DACL,OAAO,SAAS,UAAU,IAAI;4DAC9B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC/D,WAAU;4DACV,QAAQ;;;;;;;;;;;;8DAIZ,6LAAC;8DACC,cAAA,6LAAC,qIAAA,CAAA,UAAY;wDACX,OAAO,SAAS,WAAW,IAAI;wDAC/B,UAAU,CAAC,QAAU,kBAAkB,eAAe;wDACtD,MAAK;wDACL,OAAO,EAAE;wDACT,QAAQ;wDACR,UAAU;;;;;;;;;;;;;;;;;;;;;;;8CAOlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC;8DAAM,EAAE;;;;;;;;;;;;sDAGX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEACd,EAAE;;;;;;sEAEL,6LAAC;4DACC,MAAK;4DACL,OAAO,SAAS,WAAW,IAAI;4DAC/B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4DAChE,WAAU;4DACV,QAAQ;;;;;;;;;;;;8DAIZ,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEACd,EAAE;;;;;;sEAEL,6LAAC;4DACC,MAAK;4DACL,OAAO,SAAS,MAAM,IAAI;4DAC1B,UAAU,CAAC,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,KAAK;4DAC3D,WAAU;;;;;;;;;;;;8DAId,6LAAC;8DACC,cAAA,6LAAC,qIAAA,CAAA,UAAY;wDACX,OAAO,SAAS,KAAK,EAAE,cAAc;wDACrC,UAAU,CAAC,QAAU,kBAAkB,SAAS,QAAQ,OAAO,SAAS;wDACxE,MAAK;wDACL,OAAO,EAAE;wDACT,QAAQ;wDACR,UAAU;;;;;;;;;;;8DAId,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEACd,EAAE;;;;;;sEAEL,6LAAC;4DACC,MAAK;4DACL,OAAO,SAAS,OAAO,IAAI;4DAC3B,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;4DAC5D,WAAU;4DACV,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;8CAOhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,6LAAC;8DAAM,EAAE;;;;;;;;;;;;sDAGX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEACd,EAAE;;;;;;sEAEL,6LAAC;4DACC,OAAO,SAAS,MAAM,IAAI;4DAC1B,UAAU,CAAC,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,KAAK;4DAC3D,WAAU;;8EAEV,6LAAC;oEAAO,OAAM;8EAAW,EAAE;;;;;;8EAC3B,6LAAC;oEAAO,OAAM;8EAAe,EAAE;;;;;;8EAC/B,6LAAC;oEAAO,OAAM;8EAAa,EAAE;;;;;;8EAC7B,6LAAC;oEAAO,OAAM;8EAAa,EAAE;;;;;;8EAC7B,6LAAC;oEAAO,OAAM;8EAAa,EAAE;;;;;;;;;;;;;;;;;;8DAIjC,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEACd,EAAE;;;;;;sEAEL,6LAAC;4DACC,OAAO,SAAS,cAAc,IAAI;4DAClC,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;4DACnE,WAAU;;8EAEV,6LAAC;oEAAO,OAAM;8EAAI,EAAE;;;;;;gEACnB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,GAAG,CAAC,CAAA,uBACpC,6LAAC;wEAAuB,OAAO,OAAO,EAAE;;4EACrC,OAAO,SAAS;4EAAC;4EAAI,OAAO,SAAS;;uEAD3B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAUhC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,uMAAA,CAAA,QAAS;oDAAC,WAAU;;;;;;8DACrB,6LAAC;8DAAM,EAAE;;;;;;;;;;;;sDAGX,6LAAC,oIAAA,CAAA,UAAW;4CACV,QAAQ,SAAS,MAAM,IAAI,EAAE;4CAC7B,gBAAgB,CAAC,SAAW,kBAAkB,UAAU;4CACxD,WAAW;;;;;;;;;;;;8CAKf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;8DAAM,EAAE;;;;;;;;;;;;sDAGX,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,EAAE;;;;;;sEAEL,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EACC,cAAA,6LAAC,qIAAA,CAAA,UAAY;wEACX,OAAO,SAAS,YAAY,EAAE,UAAU,cAAc;wEACtD,UAAU,CAAC,QAAU,wBAAwB,YAAY;wEACzD,MAAK;wEACL,OAAO,EAAE;wEACT,aAAa,EAAE;wEACf,UAAU;;;;;;;;;;;8EAId,6LAAC;8EACC,cAAA,6LAAC,qIAAA,CAAA,UAAY;wEACX,OAAO,SAAS,YAAY,EAAE,uBAAuB,cAAc;wEACnE,UAAU,CAAC,QAAU,wBAAwB,yBAAyB;wEACtE,MAAK;wEACL,OAAO,EAAE;wEACT,aAAa,EAAE;wEACf,UAAU;;;;;;;;;;;8EAId,6LAAC;8EACC,cAAA,6LAAC,qIAAA,CAAA,UAAY;wEACX,OAAO,SAAS,YAAY,EAAE,OAAO,cAAc;wEACnD,UAAU,CAAC,QAAU,wBAAwB,SAAS;wEACtD,MAAK;wEACL,OAAO,EAAE;wEACT,aAAa,EAAE;wEACf,UAAU;;;;;;;;;;;8EAId,6LAAC;8EACC,cAAA,6LAAC,qIAAA,CAAA,UAAY;wEACX,OAAO,SAAS,YAAY,EAAE,OAAO,cAAc;wEACnD,UAAU,CAAC,QAAU,wBAAwB,SAAS;wEACtD,MAAK;wEACL,OAAO,EAAE;wEACT,aAAa,EAAE;wEACf,UAAU;;;;;;;;;;;8EAId,6LAAC;8EACC,cAAA,6LAAC,qIAAA,CAAA,UAAY;wEACX,OAAO,SAAS,YAAY,EAAE,MAAM,cAAc;wEAClD,UAAU,CAAC,QAAU,wBAAwB,QAAQ;wEACrD,MAAK;wEACL,OAAO,EAAE;wEACT,aAAa,EAAE;wEACf,UAAU;;;;;;;;;;;;;;;;;;;;;;;8DAOlB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,EAAE;;;;;;sEAEL,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EACC,cAAA,6LAAC,qIAAA,CAAA,UAAY;wEACX,OAAO,SAAS,YAAY,EAAE,YAAY,cAAc;wEACxD,UAAU,CAAC,QAAU,wBAAwB,cAAc;wEAC3D,MAAK;wEACL,OAAO,EAAE;wEACT,aAAa,EAAE;wEACf,UAAU;;;;;;;;;;;8EAId,6LAAC;8EACC,cAAA,6LAAC,qIAAA,CAAA,UAAY;wEACX,OAAO,SAAS,YAAY,EAAE,cAAc,cAAc;wEAC1D,UAAU,CAAC,QAAU,wBAAwB,gBAAgB;wEAC7D,MAAK;wEACL,OAAO,EAAE;wEACT,aAAa,EAAE;wEACf,UAAU;;;;;;;;;;;8EAId,6LAAC;8EACC,cAAA,6LAAC,qIAAA,CAAA,UAAY;wEACX,OAAO,SAAS,YAAY,EAAE,UAAU,cAAc;wEACtD,UAAU,CAAC,QAAU,wBAAwB,YAAY;wEACzD,MAAK;wEACL,OAAO,EAAE;wEACT,aAAa,EAAE;wEACf,UAAU;;;;;;;;;;;8EAId,6LAAC;8EACC,cAAA,6LAAC,qIAAA,CAAA,UAAY;wEACX,OAAO,SAAS,YAAY,EAAE,QAAQ,cAAc;wEACpD,UAAU,CAAC,QAAU,wBAAwB,UAAU;wEACvD,MAAK;wEACL,OAAO,EAAE;wEACT,aAAa,EAAE;wEACf,UAAU;;;;;;;;;;;;;;;;;;;;;;;8DAOlB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,EAAE;;;;;;sEAEL,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EACC,cAAA,6LAAC,qIAAA,CAAA,UAAY;wEACX,OAAO,SAAS,YAAY,EAAE,cAAc,cAAc;wEAC1D,UAAU,CAAC,QAAU,wBAAwB,gBAAgB;wEAC7D,MAAK;wEACL,OAAO,EAAE;wEACT,aAAa,EAAE;wEACf,UAAU;;;;;;;;;;;8EAId,6LAAC;8EACC,cAAA,6LAAC,qIAAA,CAAA,UAAY;wEACX,OAAO,SAAS,YAAY,EAAE,SAAS,cAAc;wEACrD,UAAU,CAAC,QAAU,wBAAwB,WAAW;wEACxD,MAAK;wEACL,OAAO,EAAE;wEACT,aAAa,EAAE;wEACf,UAAU;;;;;;;;;;;8EAId,6LAAC;8EACC,cAAA,6LAAC,qIAAA,CAAA,UAAY;wEACX,OAAO,SAAS,YAAY,EAAE,MAAM,cAAc;wEAClD,UAAU,CAAC,QAAU,wBAAwB,QAAQ;wEACrD,MAAK;wEACL,OAAO,EAAE;wEACT,aAAa,EAAE;wEACf,UAAU;;;;;;;;;;;;;;;;;;;;;;;8DAOlB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,EAAE;;;;;;sEAEL,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EACC,cAAA,6LAAC,qIAAA,CAAA,UAAY;wEACX,OAAO,SAAS,YAAY,EAAE,aAAa,cAAc;wEACzD,UAAU,CAAC,QAAU,wBAAwB,eAAe;wEAC5D,MAAK;wEACL,OAAO,EAAE;wEACT,aAAa,EAAE;wEACf,UAAU;;;;;;;;;;;8EAId,6LAAC;8EACC,cAAA,6LAAC,qIAAA,CAAA,UAAY;wEACX,OAAO,SAAS,YAAY,EAAE,YAAY,cAAc;wEACxD,UAAU,CAAC,QAAU,wBAAwB,cAAc;wEAC3D,MAAK;wEACL,OAAO,EAAE;wEACT,aAAa,EAAE;wEACf,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAStB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,6LAAC;8DAAM,EAAE;;;;;;;;;;;;sDAGX,6LAAC;4CACC,OAAO,SAAS,KAAK,IAAI;4CACzB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;4CAC1D,MAAM;4CACN,WAAU;4CACV,aAAa,EAAE;;;;;;sDAIjB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mIAAA,CAAA,UAAU;gDACT,YAAY,SAAS,UAAU,IAAI,EAAE;gDACrC,oBAAoB,CAAC,aAAe,kBAAkB,cAAc;gDACpE,UAAU;;;;;;;;;;;;;;;;;;;;;;;sCAOlB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;wCACV,UAAU;kDAET,EAAE;;;;;;kDAEL,6LAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,6BACC;;8DACE,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAM,EAAE;;;;;;;yEAGX;;8DACE,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW/B;GAriBwB;;QACR,iIAAA,CAAA,iBAAc;;;KADN", "debugId": null}}, {"offset": {"line": 5445, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/components/CompletedWorkUpload.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef } from 'react'\nimport { motion } from 'framer-motion'\nimport { Upload, X, Image as ImageIcon, Camera } from 'lucide-react'\n\ninterface CompletedWorkUploadProps {\n  onImagesChange: (images: string[]) => void\n  maxImages?: number\n  disabled?: boolean\n}\n\nexport default function CompletedWorkUpload({ \n  onImagesChange, \n  maxImages = 3, \n  disabled = false \n}: CompletedWorkUploadProps) {\n  const [images, setImages] = useState<string[]>([])\n  const [isDragging, setIsDragging] = useState(false)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  const handleFileSelect = (files: FileList | null) => {\n    if (!files || disabled) return\n\n    const newImages: string[] = []\n    const remainingSlots = maxImages - images.length\n\n    Array.from(files).slice(0, remainingSlots).forEach(file => {\n      if (file.type.startsWith('image/')) {\n        const reader = new FileReader()\n        reader.onload = (e) => {\n          const base64 = e.target?.result as string\n          newImages.push(base64)\n          \n          if (newImages.length === Math.min(files.length, remainingSlots)) {\n            const updatedImages = [...images, ...newImages]\n            setImages(updatedImages)\n            onImagesChange(updatedImages)\n          }\n        }\n        reader.readAsDataURL(file)\n      }\n    })\n  }\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault()\n    setIsDragging(false)\n    handleFileSelect(e.dataTransfer.files)\n  }\n\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault()\n    if (!disabled) {\n      setIsDragging(true)\n    }\n  }\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault()\n    setIsDragging(false)\n  }\n\n  const removeImage = (index: number) => {\n    if (disabled) return\n    const updatedImages = images.filter((_, i) => i !== index)\n    setImages(updatedImages)\n    onImagesChange(updatedImages)\n  }\n\n  const openFileDialog = () => {\n    if (!disabled && fileInputRef.current) {\n      fileInputRef.current.click()\n    }\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"flex items-center justify-between\">\n        <label className=\"block text-sm font-medium text-gray-700\">\n          صور العمل المكتمل\n        </label>\n        <span className=\"text-xs text-gray-500\">\n          {images.length}/{maxImages} صور\n        </span>\n      </div>\n\n      {/* منطقة رفع الصور */}\n      <div\n        onDrop={handleDrop}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onClick={openFileDialog}\n        className={`\n          relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-300\n          ${isDragging \n            ? 'border-pink-400 bg-pink-50' \n            : 'border-gray-300 hover:border-pink-400 hover:bg-pink-50'\n          }\n          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}\n          ${images.length >= maxImages ? 'opacity-50 cursor-not-allowed' : ''}\n        `}\n      >\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          multiple\n          accept=\"image/*\"\n          onChange={(e) => handleFileSelect(e.target.files)}\n          className=\"hidden\"\n          disabled={disabled || images.length >= maxImages}\n        />\n\n        <div className=\"space-y-2\">\n          <Camera className=\"w-12 h-12 text-gray-400 mx-auto\" />\n          <div>\n            <p className=\"text-sm font-medium text-gray-700\">\n              {images.length >= maxImages \n                ? 'تم الوصول للحد الأقصى من الصور'\n                : 'اضغط لرفع صور العمل المكتمل'\n              }\n            </p>\n            {images.length < maxImages && (\n              <p className=\"text-xs text-gray-500 mt-1\">\n                أو اسحب الصور هنا • JPG, PNG, GIF\n              </p>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* عرض الصور المرفوعة */}\n      {images.length > 0 && (\n        <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n          {images.map((image, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              exit={{ opacity: 0, scale: 0.8 }}\n              className=\"relative group\"\n            >\n              <div className=\"aspect-square rounded-lg overflow-hidden bg-gray-100\">\n                <img\n                  src={image}\n                  alt={`صورة العمل ${index + 1}`}\n                  className=\"w-full h-full object-cover\"\n                />\n              </div>\n              \n              {!disabled && (\n                <button\n                  onClick={(e) => {\n                    e.stopPropagation()\n                    removeImage(index)\n                  }}\n                  className=\"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-red-600\"\n                >\n                  <X className=\"w-3 h-3\" />\n                </button>\n              )}\n              \n              <div className=\"absolute bottom-2 left-2 right-2\">\n                <div className=\"bg-black/50 text-white text-xs px-2 py-1 rounded backdrop-blur-sm\">\n                  صورة {index + 1}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      )}\n\n      {images.length > 0 && (\n        <div className=\"text-xs text-gray-500 bg-blue-50 p-3 rounded-lg border border-blue-200\">\n          <div className=\"flex items-start space-x-2 space-x-reverse\">\n            <ImageIcon className=\"w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0\" />\n            <div>\n              <p className=\"font-medium text-blue-800 mb-1\">ملاحظة مهمة:</p>\n              <p className=\"text-blue-700\">\n                هذه الصور ستكون مرئية للمدير فقط ولن تظهر للعملاء في صفحة تتبع الطلبات.\n                تُستخدم لتوثيق جودة العمل المكتمل.\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAYe,SAAS,oBAAoB,EAC1C,cAAc,EACd,YAAY,CAAC,EACb,WAAW,KAAK,EACS;;IACzB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,SAAS,UAAU;QAExB,MAAM,YAAsB,EAAE;QAC9B,MAAM,iBAAiB,YAAY,OAAO,MAAM;QAEhD,MAAM,IAAI,CAAC,OAAO,KAAK,CAAC,GAAG,gBAAgB,OAAO,CAAC,CAAA;YACjD,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBAClC,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM,GAAG,CAAC;oBACf,MAAM,SAAS,EAAE,MAAM,EAAE;oBACzB,UAAU,IAAI,CAAC;oBAEf,IAAI,UAAU,MAAM,KAAK,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,iBAAiB;wBAC/D,MAAM,gBAAgB;+BAAI;+BAAW;yBAAU;wBAC/C,UAAU;wBACV,eAAe;oBACjB;gBACF;gBACA,OAAO,aAAa,CAAC;YACvB;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,cAAc;QACd,iBAAiB,EAAE,YAAY,CAAC,KAAK;IACvC;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,IAAI,CAAC,UAAU;YACb,cAAc;QAChB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,UAAU;QACd,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACpD,UAAU;QACV,eAAe;IACjB;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,YAAY,aAAa,OAAO,EAAE;YACrC,aAAa,OAAO,CAAC,KAAK;QAC5B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA0C;;;;;;kCAG3D,6LAAC;wBAAK,WAAU;;4BACb,OAAO,MAAM;4BAAC;4BAAE;4BAAU;;;;;;;;;;;;;0BAK/B,6LAAC;gBACC,QAAQ;gBACR,YAAY;gBACZ,aAAa;gBACb,SAAS;gBACT,WAAW,CAAC;;UAEV,EAAE,aACE,+BACA,yDACH;UACD,EAAE,WAAW,kCAAkC,GAAG;UAClD,EAAE,OAAO,MAAM,IAAI,YAAY,kCAAkC,GAAG;QACtE,CAAC;;kCAED,6LAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,QAAO;wBACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wBAChD,WAAU;wBACV,UAAU,YAAY,OAAO,MAAM,IAAI;;;;;;kCAGzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDACV,OAAO,MAAM,IAAI,YACd,mCACA;;;;;;oCAGL,OAAO,MAAM,GAAG,2BACf,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;YASjD,OAAO,MAAM,GAAG,mBACf,6LAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,MAAM;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAC/B,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,KAAK;oCACL,KAAK,CAAC,WAAW,EAAE,QAAQ,GAAG;oCAC9B,WAAU;;;;;;;;;;;4BAIb,CAAC,0BACA,6LAAC;gCACC,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB,YAAY;gCACd;gCACA,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;wCAAoE;wCAC3E,QAAQ;;;;;;;;;;;;;uBA5Bb;;;;;;;;;;YAoCZ,OAAO,MAAM,GAAG,mBACf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uMAAA,CAAA,QAAS;4BAAC,WAAU;;;;;;sCACrB,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAAiC;;;;;;8CAC9C,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3C;GAhLwB;KAAA", "debugId": null}}, {"offset": {"line": 5765, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/components/DeleteOrderModal.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { X, AlertTriangle, Trash2, Eye, EyeOff } from 'lucide-react'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { useAuthStore } from '@/store/authStore'\n\ninterface DeleteOrderModalProps {\n  isOpen: boolean\n  onClose: () => void\n  onConfirm: () => void\n  orderInfo: {\n    id: string\n    clientName: string\n    description: string\n  }\n}\n\nexport default function DeleteOrderModal({ isOpen, onClose, onConfirm, orderInfo }: DeleteOrderModalProps) {\n  const { t } = useTranslation()\n  const { user } = useAuthStore()\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [showPassword, setShowPassword] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setError('')\n    setIsLoading(true)\n\n    // التحقق من صحة البيانات\n    if (!email || !password) {\n      setError(t('please_fill_all_fields'))\n      setIsLoading(false)\n      return\n    }\n\n    // التحقق من أن البريد الإلكتروني يطابق بريد المدير المسجل\n    if (email !== user?.email) {\n      setError(t('email_does_not_match'))\n      setIsLoading(false)\n      return\n    }\n\n    // محاكاة التحقق من كلمة المرور (في التطبيق الحقيقي، يجب التحقق من الخادم)\n    // هنا نفترض أن كلمة المرور الصحيحة هي \"admin123\" للمحاكاة\n    if (password !== 'admin123') {\n      setError(t('incorrect_password'))\n      setIsLoading(false)\n      return\n    }\n\n    // محاكاة تأخير الشبكة\n    await new Promise(resolve => setTimeout(resolve, 1000))\n\n    setIsLoading(false)\n    onConfirm()\n    handleClose()\n  }\n\n  const handleClose = () => {\n    setEmail('')\n    setPassword('')\n    setError('')\n    setShowPassword(false)\n    onClose()\n  }\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\n          {/* خلفية مظلمة */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"absolute inset-0 bg-black/50 backdrop-blur-sm\"\n            onClick={handleClose}\n          />\n\n          {/* المودال */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9, y: 20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.9, y: 20 }}\n            className=\"relative bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto\"\n          >\n            {/* الهيدر */}\n            <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n              <div className=\"flex items-center space-x-3 space-x-reverse\">\n                <div className=\"p-2 bg-red-100 rounded-full\">\n                  <AlertTriangle className=\"w-6 h-6 text-red-600\" />\n                </div>\n                <h2 className=\"text-xl font-bold text-gray-800\">\n                  {t('confirm_delete_order')}\n                </h2>\n              </div>\n              <button\n                onClick={handleClose}\n                className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n              >\n                <X className=\"w-5 h-5 text-gray-500\" />\n              </button>\n            </div>\n\n            {/* المحتوى */}\n            <div className=\"p-6\">\n              {/* تحذير */}\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\">\n                <div className=\"flex items-start space-x-3 space-x-reverse\">\n                  <Trash2 className=\"w-5 h-5 text-red-600 mt-0.5 flex-shrink-0\" />\n                  <div>\n                    <h3 className=\"font-medium text-red-800 mb-2\">\n                      {t('warning_delete_order')}\n                    </h3>\n                    <p className=\"text-sm text-red-700\">\n                      {t('delete_order_warning_message')}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* معلومات الطلب */}\n              <div className=\"bg-gray-50 rounded-lg p-4 mb-6\">\n                <h4 className=\"font-medium text-gray-800 mb-2\">{t('order_details')}</h4>\n                <div className=\"space-y-1 text-sm text-gray-600\">\n                  <p><span className=\"font-medium\">{t('order_id')}:</span> #{orderInfo.id}</p>\n                  <p><span className=\"font-medium\">{t('client_name')}:</span> {orderInfo.clientName}</p>\n                  <p><span className=\"font-medium\">{t('description')}:</span> {orderInfo.description}</p>\n                </div>\n              </div>\n\n              {/* نموذج التحقق */}\n              <form onSubmit={handleSubmit} className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    {t('admin_email')}\n                  </label>\n                  <input\n                    type=\"email\"\n                    value={email}\n                    onChange={(e) => setEmail(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent\"\n                    placeholder={t('enter_admin_email')}\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    {t('admin_password')}\n                  </label>\n                  <div className=\"relative\">\n                    <input\n                      type={showPassword ? 'text' : 'password'}\n                      value={password}\n                      onChange={(e) => setPassword(e.target.value)}\n                      className=\"w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent\"\n                      placeholder={t('enter_admin_password')}\n                      required\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={() => setShowPassword(!showPassword)}\n                      className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n                    >\n                      {showPassword ? <EyeOff className=\"w-4 h-4\" /> : <Eye className=\"w-4 h-4\" />}\n                    </button>\n                  </div>\n                </div>\n\n                {error && (\n                  <div className=\"bg-red-50 border border-red-200 rounded-lg p-3\">\n                    <p className=\"text-sm text-red-700\">{error}</p>\n                  </div>\n                )}\n\n                {/* الأزرار */}\n                <div className=\"flex space-x-3 space-x-reverse pt-4\">\n                  <button\n                    type=\"button\"\n                    onClick={handleClose}\n                    className=\"flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors\"\n                  >\n                    {t('cancel')}\n                  </button>\n                  <button\n                    type=\"submit\"\n                    disabled={isLoading}\n                    className=\"flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 space-x-reverse\"\n                  >\n                    {isLoading ? (\n                      <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\" />\n                    ) : (\n                      <>\n                        <Trash2 className=\"w-4 h-4\" />\n                        <span>{t('confirm_delete')}</span>\n                      </>\n                    )}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </motion.div>\n        </div>\n      )}\n    </AnimatePresence>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;;AAmBe,SAAS,iBAAiB,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAyB;;IACvG,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,aAAa;QAEb,yBAAyB;QACzB,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,SAAS,EAAE;YACX,aAAa;YACb;QACF;QAEA,0DAA0D;QAC1D,IAAI,UAAU,MAAM,OAAO;YACzB,SAAS,EAAE;YACX,aAAa;YACb;QACF;QAEA,0EAA0E;QAC1E,0DAA0D;QAC1D,IAAI,aAAa,YAAY;YAC3B,SAAS,EAAE;YACX,aAAa;YACb;QACF;QAEA,sBAAsB;QACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,aAAa;QACb;QACA;IACF;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,YAAY;QACZ,SAAS;QACT,gBAAgB;QAChB;IACF;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACzC,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACtC,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAE3B,6LAAC;4CAAG,WAAU;sDACX,EAAE;;;;;;;;;;;;8CAGP,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEACX,EAAE;;;;;;kEAEL,6LAAC;wDAAE,WAAU;kEACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;8CAOX,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAkC,EAAE;;;;;;sDAClD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEAAE,6LAAC;4DAAK,WAAU;;gEAAe,EAAE;gEAAY;;;;;;;wDAAQ;wDAAG,UAAU,EAAE;;;;;;;8DACvE,6LAAC;;sEAAE,6LAAC;4DAAK,WAAU;;gEAAe,EAAE;gEAAe;;;;;;;wDAAQ;wDAAE,UAAU,UAAU;;;;;;;8DACjF,6LAAC;;sEAAE,6LAAC;4DAAK,WAAU;;gEAAe,EAAE;gEAAe;;;;;;;wDAAQ;wDAAE,UAAU,WAAW;;;;;;;;;;;;;;;;;;;8CAKtF,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DACd,EAAE;;;;;;8DAEL,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,WAAU;oDACV,aAAa,EAAE;oDACf,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DACd,EAAE;;;;;;8DAEL,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAM,eAAe,SAAS;4DAC9B,OAAO;4DACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4DAC3C,WAAU;4DACV,aAAa,EAAE;4DACf,QAAQ;;;;;;sEAEV,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,gBAAgB,CAAC;4DAChC,WAAU;sEAET,6BAAe,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;qFAAe,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;wCAKrE,uBACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;sDAKzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;8DAET,EAAE;;;;;;8DAEL,6LAAC;oDACC,MAAK;oDACL,UAAU;oDACV,WAAU;8DAET,0BACC,6LAAC;wDAAI,WAAU;;;;;6EAEf;;0EACE,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;0EAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYjC;GAjMwB;;QACR,iIAAA,CAAA,iBAAc;QACX,4HAAA,CAAA,eAAY;;;KAFP", "debugId": null}}, {"offset": {"line": 6271, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-al<PERSON>/src/app/dashboard/orders/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion } from 'framer-motion'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { useAuthStore } from '@/store/authStore'\nimport { useDataStore } from '@/store/dataStore'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport OrderModal from '@/components/OrderModal'\nimport EditOrderModal from '@/components/EditOrderModal'\nimport CompletedWorkUpload from '@/components/CompletedWorkUpload'\nimport DeleteOrderModal from '@/components/DeleteOrderModal'\nimport NumericInput from '@/components/NumericInput'\nimport {\n  ArrowRight,\n  Package,\n  Search,\n  Filter,\n  Eye,\n  Edit,\n  Clock,\n  CheckCircle,\n  AlertCircle,\n  Plus,\n  Calendar,\n  User,\n  X,\n  Languages,\n  Trash2\n} from 'lucide-react'\n\nexport default function OrdersPage() {\n  const { user } = useAuthStore()\n  const { orders, workers, updateOrder, deleteOrder, startOrderWork, completeOrder } = useDataStore()\n  const { t, language, changeLanguage, isArabic } = useTranslation()\n  const router = useRouter()\n\n  // التحقق من الصلاحيات\n  useEffect(() => {\n    if (!user) {\n      router.push('/login')\n    }\n  }, [user, router])\n\n  const [searchTerm, setSearchTerm] = useState('')\n  const [searchType, setSearchType] = useState<'text' | 'orderNumber'>('text')\n  const [statusFilter, setStatusFilter] = useState('all')\n  const [selectedOrder, setSelectedOrder] = useState<any>(null)\n  const [showViewModal, setShowViewModal] = useState(false)\n  const [showEditModal, setShowEditModal] = useState(false)\n  const [showCompleteModal, setShowCompleteModal] = useState(false)\n  const [completedImages, setCompletedImages] = useState<string[]>([])\n  const [isProcessing, setIsProcessing] = useState(false)\n\n  // حالات modal حذف الطلب\n  const [deleteModalOpen, setDeleteModalOpen] = useState(false)\n  const [orderToDelete, setOrderToDelete] = useState<any>(null)\n  const [deleteSuccess, setDeleteSuccess] = useState(false)\n\n  const getStatusInfo = (status: string) => {\n    const statusMap = {\n      pending: { label: t('pending'), color: 'text-yellow-600', bgColor: 'bg-yellow-100', icon: Clock },\n      in_progress: { label: t('in_progress'), color: 'text-blue-600', bgColor: 'bg-blue-100', icon: Package },\n      completed: { label: t('completed'), color: 'text-green-600', bgColor: 'bg-green-100', icon: CheckCircle },\n      delivered: { label: t('delivered'), color: 'text-purple-600', bgColor: 'bg-purple-100', icon: CheckCircle },\n      cancelled: { label: t('cancelled'), color: 'text-red-600', bgColor: 'bg-red-100', icon: AlertCircle }\n    }\n    return statusMap[status as keyof typeof statusMap] || statusMap.pending\n  }\n\n  // الحصول على اسم العامل\n  const getWorkerName = (workerId?: string) => {\n    if (!workerId) return null\n    const worker = workers.find(w => w.id === workerId)\n    return worker ? worker.full_name : null\n  }\n\n  // فتح نافذة العرض\n  const handleViewOrder = (order: any) => {\n    setSelectedOrder(order)\n    setShowViewModal(true)\n  }\n\n  // فتح نافذة التعديل\n  const handleEditOrder = (order: any) => {\n    setSelectedOrder(order)\n    setShowEditModal(true)\n  }\n\n  // حفظ التعديلات\n  const handleSaveOrder = (orderId: string, updates: any) => {\n    updateOrder(orderId, updates)\n    setShowEditModal(false)\n    setSelectedOrder(null)\n  }\n\n  // إغلاق النوافذ\n  const handleCloseModals = () => {\n    setShowViewModal(false)\n    setShowEditModal(false)\n    setShowCompleteModal(false)\n    setSelectedOrder(null)\n    setCompletedImages([])\n  }\n\n  // فتح modal حذف الطلب\n  const handleDeleteOrder = (order: any) => {\n    setOrderToDelete(order)\n    setDeleteModalOpen(true)\n  }\n\n  // تأكيد حذف الطلب\n  const confirmDeleteOrder = () => {\n    if (orderToDelete) {\n      deleteOrder(orderToDelete.id)\n      setDeleteModalOpen(false)\n      setOrderToDelete(null)\n      setDeleteSuccess(true)\n\n      // إخفاء رسالة النجاح بعد 3 ثوان\n      setTimeout(() => {\n        setDeleteSuccess(false)\n      }, 3000)\n    }\n  }\n\n  // إغلاق modal حذف الطلب\n  const closeDeleteModal = () => {\n    setDeleteModalOpen(false)\n    setOrderToDelete(null)\n  }\n\n  // بدء العمل في الطلب (للعمال)\n  const handleStartWork = async (orderId: string) => {\n    if (!user || user.role !== 'worker') return\n\n    setIsProcessing(true)\n    try {\n      await new Promise(resolve => setTimeout(resolve, 500))\n      startOrderWork(orderId, user.id)\n    } finally {\n      setIsProcessing(false)\n    }\n  }\n\n  // فتح نافذة إنهاء الطلب\n  const handleOpenCompleteModal = (order: any) => {\n    setSelectedOrder(order)\n    setShowCompleteModal(true)\n  }\n\n  // إنهاء الطلب (للعمال)\n  const handleCompleteWork = async () => {\n    if (!selectedOrder || !user || user.role !== 'worker') return\n\n    setIsProcessing(true)\n    try {\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      completeOrder(selectedOrder.id, user.id, completedImages)\n      setShowCompleteModal(false)\n      setSelectedOrder(null)\n      setCompletedImages([])\n    } finally {\n      setIsProcessing(false)\n    }\n  }\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString)\n    return date.toLocaleDateString('ar-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      calendar: 'gregory'\n    })\n  }\n\n  const filteredOrders = orders.filter(order => {\n    // فلترة حسب الدور - العمال يرون طلباتهم فقط\n    const matchesRole = user.role === 'admin' || order.assignedWorker === user.id\n\n    const matchesSearch = searchType === 'orderNumber'\n      ? order.id.toLowerCase().includes(searchTerm.toLowerCase())\n      : order.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        order.description.toLowerCase().includes(searchTerm.toLowerCase())\n\n    const matchesStatus = statusFilter === 'all' || order.status === statusFilter\n\n    return matchesRole && matchesSearch && matchesStatus\n  })\n\n  if (!user) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">{t('loading')}</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* التنقل */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"mb-8\"\n        >\n          <Link\n            href=\"/dashboard\"\n            className=\"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300\"\n          >\n            <ArrowRight className=\"w-4 h-4\" />\n            <span>{t('back_to_dashboard')} {t('dashboard')}</span>\n          </Link>\n        </motion.div>\n\n        {/* العنوان والأزرار */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8\"\n        >\n          <div>\n            <h1 className=\"text-3xl sm:text-4xl font-bold mb-2\">\n              <span className=\"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\">\n                {t('orders')}\n              </span>\n            </h1>\n            <p className=\"text-lg text-gray-600\">\n              {t('view_manage_orders')}\n            </p>\n          </div>\n\n          <div className=\"flex items-center gap-3\">\n            {user.role === 'admin' && (\n              <Link\n                href=\"/dashboard/add-order\"\n                className=\"btn-primary inline-flex items-center justify-center space-x-2 space-x-reverse px-6 py-3 group\"\n              >\n                <Plus className=\"w-5 h-5 group-hover:scale-110 transition-transform duration-300\" />\n                <span>{t('add_new_order')}</span>\n              </Link>\n            )}\n          </div>\n        </motion.div>\n\n        {/* البحث والفلاتر */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 mb-8\"\n        >\n          <div className=\"grid md:grid-cols-2 gap-4\">\n            {/* البحث */}\n            <div className=\"space-y-3\">\n              {/* أزرار تبديل نوع البحث */}\n              <div className=\"flex space-x-2 space-x-reverse\">\n                <button\n                  onClick={() => {\n                    setSearchType('text')\n                    setSearchTerm('')\n                  }}\n                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\n                    searchType === 'text'\n                      ? 'bg-pink-500 text-white'\n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n                  }`}\n                >\n                  {t('search_by_text')}\n                </button>\n                <button\n                  onClick={() => {\n                    setSearchType('orderNumber')\n                    setSearchTerm('')\n                  }}\n                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\n                    searchType === 'orderNumber'\n                      ? 'bg-pink-500 text-white'\n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n                  }`}\n                >\n                  {t('search_by_order_number')}\n                </button>\n              </div>\n\n              {/* حقل البحث */}\n              <div className=\"relative\">\n                <Search className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n                {searchType === 'orderNumber' ? (\n                  <NumericInput\n                    value={searchTerm}\n                    onChange={setSearchTerm}\n                    type=\"orderNumber\"\n                    placeholder={t('enter_order_number')}\n                    className=\"pr-10\"\n                  />\n                ) : (\n                  <input\n                    type=\"text\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    className=\"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300\"\n                    placeholder={t('search_placeholder')}\n                  />\n                )}\n              </div>\n            </div>\n\n            {/* فلتر الحالة */}\n            <div className=\"relative\">\n              <Filter className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n              <select\n                value={statusFilter}\n                onChange={(e) => setStatusFilter(e.target.value)}\n                className=\"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300\"\n              >\n                <option value=\"all\">{t('all_orders')}</option>\n                <option value=\"pending\">{t('pending')}</option>\n                <option value=\"in_progress\">{t('in_progress')}</option>\n                <option value=\"completed\">{t('completed')}</option>\n                <option value=\"delivered\">{t('delivered')}</option>\n              </select>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* قائمة الطلبات */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          className=\"space-y-6\"\n        >\n          {filteredOrders.length === 0 ? (\n            <div className=\"text-center py-12 bg-white/80 backdrop-blur-sm rounded-2xl border border-pink-100\">\n              <Package className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-medium text-gray-600 mb-2\">\n                {user.role === 'worker' ? t('no_orders_assigned') : t('no_orders_found')}\n              </h3>\n              <p className=\"text-gray-500\">\n                {user.role === 'worker'\n                  ? t('no_orders_assigned_desc')\n                  : t('no_orders_found_desc')\n                }\n              </p>\n            </div>\n          ) : (\n            filteredOrders.map((order, index) => (\n              <motion.div\n                key={order.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n                className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 hover:shadow-lg transition-all duration-300\"\n              >\n                <div className=\"grid lg:grid-cols-4 gap-6\">\n                  {/* معلومات الطلب */}\n                  <div className=\"lg:col-span-2\">\n                    <div className=\"flex items-start justify-between mb-4\">\n                      <div>\n                        <h3 className=\"text-xl font-bold text-gray-800 mb-1\">\n                          {order.clientName}\n                        </h3>\n                        <p className=\"text-pink-600 font-medium\">{order.description}</p>\n                        <p className=\"text-sm text-gray-500\">#{order.id}</p>\n                      </div>\n                      \n                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusInfo(order.status).bgColor} ${getStatusInfo(order.status).color}`}>\n                        {getStatusInfo(order.status).label}\n                      </span>\n                    </div>\n\n                    <div className=\"space-y-2 text-sm text-gray-600\">\n                      <div className=\"flex items-center space-x-2 space-x-reverse\">\n                        <Calendar className=\"w-4 h-4\" />\n                        <span>{t('order_date_label')}: {formatDate(order.createdAt)}</span>\n                      </div>\n                      <div className=\"flex items-center space-x-2 space-x-reverse\">\n                        <Clock className=\"w-4 h-4\" />\n                        <span>{t('delivery_date_label')}: {formatDate(order.dueDate)}</span>\n                      </div>\n                      {order.assignedWorker && (\n                        <div className=\"flex items-center space-x-2 space-x-reverse\">\n                          <User className=\"w-4 h-4\" />\n                          <span>{t('worker_label')}: {getWorkerName(order.assignedWorker)}</span>\n                        </div>\n                      )}\n                      {order.fabric && (\n                        <div className=\"flex items-center space-x-2 space-x-reverse\">\n                          <Package className=\"w-4 h-4\" />\n                          <span>{t('fabric_label')} {order.fabric}</span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* السعر والحالة */}\n                  <div className=\"space-y-4\">\n                    <div className=\"text-center\">\n                      <p className=\"text-sm text-gray-600\">{t('price_label')}</p>\n                      <p className=\"text-lg font-bold text-green-600\">{order.price} {t('sar')}</p>\n                    </div>\n\n                    {order.notes && (\n                      <div>\n                        <p className=\"text-sm font-medium text-gray-700 mb-1\">{t('notes_label')}</p>\n                        <p className=\"text-sm text-gray-600 bg-gray-50 p-2 rounded\">{order.notes}</p>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* الإجراءات */}\n                  <div className=\"flex flex-col gap-2\">\n                    <button\n                      onClick={() => handleViewOrder(order)}\n                      className=\"btn-secondary py-2 px-4 text-sm inline-flex items-center justify-center space-x-1 space-x-reverse\"\n                    >\n                      <Eye className=\"w-4 h-4\" />\n                      <span>{t('view')}</span>\n                    </button>\n\n                    {user.role === 'admin' && (\n                      <>\n                        <button\n                          onClick={() => handleEditOrder(order)}\n                          className=\"btn-primary py-2 px-4 text-sm inline-flex items-center justify-center space-x-1 space-x-reverse\"\n                        >\n                          <Edit className=\"w-4 h-4\" />\n                          <span>{t('edit')}</span>\n                        </button>\n\n                        <button\n                          onClick={() => handleDeleteOrder(order)}\n                          className=\"bg-red-500 hover:bg-red-600 text-white py-2 px-4 text-sm inline-flex items-center justify-center space-x-1 space-x-reverse rounded-lg transition-colors duration-200\"\n                        >\n                          <Trash2 className=\"w-4 h-4\" />\n                          <span>{t('delete')}</span>\n                        </button>\n                      </>\n                    )}\n\n                    {/* أزرار العامل */}\n                    {user.role === 'worker' && order.assignedWorker === user.id && (\n                      <>\n                        {order.status === 'pending' && (\n                          <button\n                            onClick={() => handleStartWork(order.id)}\n                            disabled={isProcessing}\n                            className=\"bg-blue-600 text-white py-2 px-4 text-sm rounded-lg hover:bg-blue-700 transition-colors duration-300 inline-flex items-center justify-center space-x-1 space-x-reverse disabled:opacity-50\"\n                          >\n                            <Package className=\"w-4 h-4\" />\n                            <span>{t('start_work')}</span>\n                          </button>\n                        )}\n\n                        {order.status === 'in_progress' && (\n                          <button\n                            onClick={() => handleOpenCompleteModal(order)}\n                            disabled={isProcessing}\n                            className=\"bg-green-600 text-white py-2 px-4 text-sm rounded-lg hover:bg-green-700 transition-colors duration-300 inline-flex items-center justify-center space-x-1 space-x-reverse disabled:opacity-50\"\n                          >\n                            <CheckCircle className=\"w-4 h-4\" />\n                            <span>{t('complete_order')}</span>\n                          </button>\n                        )}\n                      </>\n                    )}\n                  </div>\n                </div>\n              </motion.div>\n            ))\n          )}\n        </motion.div>\n\n        {/* إحصائيات سريعة */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          className=\"mt-12 grid grid-cols-2 lg:grid-cols-4 gap-6\"\n        >\n          <div className=\"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100\">\n            <div className=\"text-2xl font-bold text-yellow-600 mb-1\">\n              {orders.filter(o => {\n                const matchesRole = user.role === 'admin' || o.assignedWorker === user.id\n                return matchesRole && o.status === 'pending'\n              }).length}\n            </div>\n            <div className=\"text-sm text-gray-600\">{t('pending')}</div>\n          </div>\n\n          <div className=\"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100\">\n            <div className=\"text-2xl font-bold text-blue-600 mb-1\">\n              {orders.filter(o => {\n                const matchesRole = user.role === 'admin' || o.assignedWorker === user.id\n                return matchesRole && o.status === 'in_progress'\n              }).length}\n            </div>\n            <div className=\"text-sm text-gray-600\">{t('in_progress')}</div>\n          </div>\n\n          <div className=\"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100\">\n            <div className=\"text-2xl font-bold text-green-600 mb-1\">\n              {orders.filter(o => {\n                const matchesRole = user.role === 'admin' || o.assignedWorker === user.id\n                return matchesRole && o.status === 'completed'\n              }).length}\n            </div>\n            <div className=\"text-sm text-gray-600\">{t('completed')}</div>\n          </div>\n\n          <div className=\"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100\">\n            <div className=\"text-2xl font-bold text-purple-600 mb-1\">\n              {orders.filter(o => {\n                const matchesRole = user.role === 'admin' || o.assignedWorker === user.id\n                return matchesRole && o.status === 'delivered'\n              }).length}\n            </div>\n            <div className=\"text-sm text-gray-600\">{t('delivered')}</div>\n          </div>\n        </motion.div>\n\n        {/* النوافذ المنبثقة */}\n        <OrderModal\n          order={selectedOrder}\n          workers={workers}\n          isOpen={showViewModal}\n          onClose={handleCloseModals}\n        />\n\n        <EditOrderModal\n          order={selectedOrder}\n          workers={workers}\n          isOpen={showEditModal}\n          onClose={handleCloseModals}\n          onSave={handleSaveOrder}\n        />\n\n        {/* نافذة إنهاء الطلب */}\n        {showCompleteModal && selectedOrder && (\n          <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\n            <div className=\"absolute inset-0 bg-black/50 backdrop-blur-sm\" onClick={handleCloseModals} />\n\n            <motion.div\n              initial={{ opacity: 0, scale: 0.9, y: 20 }}\n              animate={{ opacity: 1, scale: 1, y: 0 }}\n              className=\"relative bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\"\n            >\n              <div className=\"sticky top-0 bg-white border-b border-gray-200 p-6 rounded-t-2xl\">\n                <div className=\"flex items-center justify-between\">\n                  <h3 className=\"text-xl font-bold text-gray-800\">{t('complete_order_modal_title')}</h3>\n                  <button\n                    onClick={handleCloseModals}\n                    className=\"text-gray-400 hover:text-gray-600\"\n                  >\n                    <X className=\"w-6 h-6\" />\n                  </button>\n                </div>\n              </div>\n\n              <div className=\"p-6 space-y-6\">\n                <div className=\"text-center\">\n                  <h4 className=\"text-lg font-medium text-gray-800 mb-2\">\n                    {t('order_label')} {selectedOrder.description}\n                  </h4>\n                  <p className=\"text-gray-600\">\n                    {t('for_client')} {selectedOrder.clientName}\n                  </p>\n                </div>\n\n                <CompletedWorkUpload\n                  onImagesChange={setCompletedImages}\n                  maxImages={3}\n                  disabled={isProcessing}\n                />\n\n                <div className=\"bg-yellow-50 p-4 rounded-lg border border-yellow-200\">\n                  <div className=\"flex items-start space-x-3 space-x-reverse\">\n                    <AlertCircle className=\"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0\" />\n                    <div>\n                      <p className=\"font-medium text-yellow-800 mb-1\">{t('important_warning')}</p>\n                      <p className=\"text-yellow-700 text-sm\">\n                        {t('complete_order_warning')}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex gap-4 justify-end\">\n                  <button\n                    onClick={handleCloseModals}\n                    disabled={isProcessing}\n                    className=\"btn-secondary px-6 py-2\"\n                  >\n                    {t('cancel')}\n                  </button>\n                  <button\n                    onClick={handleCompleteWork}\n                    disabled={isProcessing}\n                    className=\"bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 space-x-reverse\"\n                  >\n                    {isProcessing ? (\n                      <>\n                        <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                        <span>{t('completing')}</span>\n                      </>\n                    ) : (\n                      <>\n                        <CheckCircle className=\"w-4 h-4\" />\n                        <span>{t('complete_order')}</span>\n                      </>\n                    )}\n                  </button>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        )}\n\n        {/* نافذة حذف الطلب */}\n        <DeleteOrderModal\n          isOpen={deleteModalOpen}\n          onClose={closeDeleteModal}\n          onConfirm={confirmDeleteOrder}\n          orderInfo={orderToDelete}\n        />\n\n        {/* رسالة نجاح الحذف */}\n        {deleteSuccess && (\n          <div className=\"fixed top-4 right-4 z-50\">\n            <motion.div\n              initial={{ opacity: 0, x: 100 }}\n              animate={{ opacity: 1, x: 0 }}\n              exit={{ opacity: 0, x: 100 }}\n              className=\"bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center space-x-2 space-x-reverse\"\n            >\n              <CheckCircle className=\"w-5 h-5\" />\n              <span>{t('order_deleted_successfully')}</span>\n            </motion.div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAdA;;;;;;;;;;;;;;AAgCe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAChG,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAC/D,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;YACd;QACF;+BAAG;QAAC;QAAM;KAAO;IAEjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACxD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,wBAAwB;IACxB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACxD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,gBAAgB,CAAC;QACrB,MAAM,YAAY;YAChB,SAAS;gBAAE,OAAO,EAAE;gBAAY,OAAO;gBAAmB,SAAS;gBAAiB,MAAM,uMAAA,CAAA,QAAK;YAAC;YAChG,aAAa;gBAAE,OAAO,EAAE;gBAAgB,OAAO;gBAAiB,SAAS;gBAAe,MAAM,2MAAA,CAAA,UAAO;YAAC;YACtG,WAAW;gBAAE,OAAO,EAAE;gBAAc,OAAO;gBAAkB,SAAS;gBAAgB,MAAM,8NAAA,CAAA,cAAW;YAAC;YACxG,WAAW;gBAAE,OAAO,EAAE;gBAAc,OAAO;gBAAmB,SAAS;gBAAiB,MAAM,8NAAA,CAAA,cAAW;YAAC;YAC1G,WAAW;gBAAE,OAAO,EAAE;gBAAc,OAAO;gBAAgB,SAAS;gBAAc,MAAM,uNAAA,CAAA,cAAW;YAAC;QACtG;QACA,OAAO,SAAS,CAAC,OAAiC,IAAI,UAAU,OAAO;IACzE;IAEA,wBAAwB;IACxB,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,UAAU,OAAO;QACtB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,OAAO,SAAS,OAAO,SAAS,GAAG;IACrC;IAEA,kBAAkB;IAClB,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;QACjB,iBAAiB;IACnB;IAEA,oBAAoB;IACpB,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;QACjB,iBAAiB;IACnB;IAEA,gBAAgB;IAChB,MAAM,kBAAkB,CAAC,SAAiB;QACxC,YAAY,SAAS;QACrB,iBAAiB;QACjB,iBAAiB;IACnB;IAEA,gBAAgB;IAChB,MAAM,oBAAoB;QACxB,iBAAiB;QACjB,iBAAiB;QACjB,qBAAqB;QACrB,iBAAiB;QACjB,mBAAmB,EAAE;IACvB;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,mBAAmB;IACrB;IAEA,kBAAkB;IAClB,MAAM,qBAAqB;QACzB,IAAI,eAAe;YACjB,YAAY,cAAc,EAAE;YAC5B,mBAAmB;YACnB,iBAAiB;YACjB,iBAAiB;YAEjB,gCAAgC;YAChC,WAAW;gBACT,iBAAiB;YACnB,GAAG;QACL;IACF;IAEA,wBAAwB;IACxB,MAAM,mBAAmB;QACvB,mBAAmB;QACnB,iBAAiB;IACnB;IAEA,8BAA8B;IAC9B,MAAM,kBAAkB,OAAO;QAC7B,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,UAAU;QAErC,gBAAgB;QAChB,IAAI;YACF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,eAAe,SAAS,KAAK,EAAE;QACjC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,wBAAwB;IACxB,MAAM,0BAA0B,CAAC;QAC/B,iBAAiB;QACjB,qBAAqB;IACvB;IAEA,uBAAuB;IACvB,MAAM,qBAAqB;QACzB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,KAAK,IAAI,KAAK,UAAU;QAEvD,gBAAgB;QAChB,IAAI;YACF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,cAAc,cAAc,EAAE,EAAE,KAAK,EAAE,EAAE;YACzC,qBAAqB;YACrB,iBAAiB;YACjB,mBAAmB,EAAE;QACvB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;YACL,UAAU;QACZ;IACF;IAEA,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA;QACnC,4CAA4C;QAC5C,MAAM,cAAc,KAAK,IAAI,KAAK,WAAW,MAAM,cAAc,KAAK,KAAK,EAAE;QAE7E,MAAM,gBAAgB,eAAe,gBACjC,MAAM,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,MACtD,MAAM,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,MAAM,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACtD,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEnE,MAAM,gBAAgB,iBAAiB,SAAS,MAAM,MAAM,KAAK;QAEjE,OAAO,eAAe,iBAAiB;IACzC;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAiB,EAAE;;;;;;;;;;;;;;;;;IAIxC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,6LAAC;;oCAAM,EAAE;oCAAqB;oCAAE,EAAE;;;;;;;;;;;;;;;;;;8BAKtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CACZ,cAAA,6LAAC;wCAAK,WAAU;kDACb,EAAE;;;;;;;;;;;8CAGP,6LAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;sCAIP,6LAAC;4BAAI,WAAU;sCACZ,KAAK,IAAI,KAAK,yBACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;8BAOjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS;oDACP,cAAc;oDACd,cAAc;gDAChB;gDACA,WAAW,CAAC,qEAAqE,EAC/E,eAAe,SACX,2BACA,+CACJ;0DAED,EAAE;;;;;;0DAEL,6LAAC;gDACC,SAAS;oDACP,cAAc;oDACd,cAAc;gDAChB;gDACA,WAAW,CAAC,qEAAqE,EAC/E,eAAe,gBACX,2BACA,+CACJ;0DAED,EAAE;;;;;;;;;;;;kDAKP,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CACjB,eAAe,8BACd,6LAAC,qIAAA,CAAA,UAAY;gDACX,OAAO;gDACP,UAAU;gDACV,MAAK;gDACL,aAAa,EAAE;gDACf,WAAU;;;;;qEAGZ,6LAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;gDACV,aAAa,EAAE;;;;;;;;;;;;;;;;;;0CAOvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAO,EAAE;;;;;;0DACvB,6LAAC;gDAAO,OAAM;0DAAW,EAAE;;;;;;0DAC3B,6LAAC;gDAAO,OAAM;0DAAe,EAAE;;;;;;0DAC/B,6LAAC;gDAAO,OAAM;0DAAa,EAAE;;;;;;0DAC7B,6LAAC;gDAAO,OAAM;0DAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAET,eAAe,MAAM,KAAK,kBACzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;gCAAG,WAAU;0CACX,KAAK,IAAI,KAAK,WAAW,EAAE,wBAAwB,EAAE;;;;;;0CAExD,6LAAC;gCAAE,WAAU;0CACV,KAAK,IAAI,KAAK,WACX,EAAE,6BACF,EAAE;;;;;;;;;;;+BAKV,eAAe,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EACX,MAAM,UAAU;;;;;;0EAEnB,6LAAC;gEAAE,WAAU;0EAA6B,MAAM,WAAW;;;;;;0EAC3D,6LAAC;gEAAE,WAAU;;oEAAwB;oEAAE,MAAM,EAAE;;;;;;;;;;;;;kEAGjD,6LAAC;wDAAK,WAAW,CAAC,2CAA2C,EAAE,cAAc,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,cAAc,MAAM,MAAM,EAAE,KAAK,EAAE;kEACtI,cAAc,MAAM,MAAM,EAAE,KAAK;;;;;;;;;;;;0DAItC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;;oEAAM,EAAE;oEAAoB;oEAAG,WAAW,MAAM,SAAS;;;;;;;;;;;;;kEAE5D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;;oEAAM,EAAE;oEAAuB;oEAAG,WAAW,MAAM,OAAO;;;;;;;;;;;;;oDAE5D,MAAM,cAAc,kBACnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;;oEAAM,EAAE;oEAAgB;oEAAG,cAAc,MAAM,cAAc;;;;;;;;;;;;;oDAGjE,MAAM,MAAM,kBACX,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,2MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,6LAAC;;oEAAM,EAAE;oEAAgB;oEAAE,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;kDAO/C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyB,EAAE;;;;;;kEACxC,6LAAC;wDAAE,WAAU;;4DAAoC,MAAM,KAAK;4DAAC;4DAAE,EAAE;;;;;;;;;;;;;4CAGlE,MAAM,KAAK,kBACV,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAA0C,EAAE;;;;;;kEACzD,6LAAC;wDAAE,WAAU;kEAAgD,MAAM,KAAK;;;;;;;;;;;;;;;;;;kDAM9E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,gBAAgB;gDAC/B,WAAU;;kEAEV,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,6LAAC;kEAAM,EAAE;;;;;;;;;;;;4CAGV,KAAK,IAAI,KAAK,yBACb;;kEACE,6LAAC;wDACC,SAAS,IAAM,gBAAgB;wDAC/B,WAAU;;0EAEV,6LAAC,8MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;0EAAM,EAAE;;;;;;;;;;;;kEAGX,6LAAC;wDACC,SAAS,IAAM,kBAAkB;wDACjC,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;0EAAM,EAAE;;;;;;;;;;;;;;4CAMd,KAAK,IAAI,KAAK,YAAY,MAAM,cAAc,KAAK,KAAK,EAAE,kBACzD;;oDACG,MAAM,MAAM,KAAK,2BAChB,6LAAC;wDACC,SAAS,IAAM,gBAAgB,MAAM,EAAE;wDACvC,UAAU;wDACV,WAAU;;0EAEV,6LAAC,2MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,6LAAC;0EAAM,EAAE;;;;;;;;;;;;oDAIZ,MAAM,MAAM,KAAK,+BAChB,6LAAC;wDACC,SAAS,IAAM,wBAAwB;wDACvC,UAAU;wDACV,WAAU;;0EAEV,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,6LAAC;0EAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;2BAjHhB,MAAM,EAAE;;;;;;;;;;8BA8HrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,OAAO,MAAM,CAAC,CAAA;wCACb,MAAM,cAAc,KAAK,IAAI,KAAK,WAAW,EAAE,cAAc,KAAK,KAAK,EAAE;wCACzE,OAAO,eAAe,EAAE,MAAM,KAAK;oCACrC,GAAG,MAAM;;;;;;8CAEX,6LAAC;oCAAI,WAAU;8CAAyB,EAAE;;;;;;;;;;;;sCAG5C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,OAAO,MAAM,CAAC,CAAA;wCACb,MAAM,cAAc,KAAK,IAAI,KAAK,WAAW,EAAE,cAAc,KAAK,KAAK,EAAE;wCACzE,OAAO,eAAe,EAAE,MAAM,KAAK;oCACrC,GAAG,MAAM;;;;;;8CAEX,6LAAC;oCAAI,WAAU;8CAAyB,EAAE;;;;;;;;;;;;sCAG5C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,OAAO,MAAM,CAAC,CAAA;wCACb,MAAM,cAAc,KAAK,IAAI,KAAK,WAAW,EAAE,cAAc,KAAK,KAAK,EAAE;wCACzE,OAAO,eAAe,EAAE,MAAM,KAAK;oCACrC,GAAG,MAAM;;;;;;8CAEX,6LAAC;oCAAI,WAAU;8CAAyB,EAAE;;;;;;;;;;;;sCAG5C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,OAAO,MAAM,CAAC,CAAA;wCACb,MAAM,cAAc,KAAK,IAAI,KAAK,WAAW,EAAE,cAAc,KAAK,KAAK,EAAE;wCACzE,OAAO,eAAe,EAAE,MAAM,KAAK;oCACrC,GAAG,MAAM;;;;;;8CAEX,6LAAC;oCAAI,WAAU;8CAAyB,EAAE;;;;;;;;;;;;;;;;;;8BAK9C,6LAAC,mIAAA,CAAA,UAAU;oBACT,OAAO;oBACP,SAAS;oBACT,QAAQ;oBACR,SAAS;;;;;;8BAGX,6LAAC,uIAAA,CAAA,UAAc;oBACb,OAAO;oBACP,SAAS;oBACT,QAAQ;oBACR,SAAS;oBACT,QAAQ;;;;;;gBAIT,qBAAqB,+BACpB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;4BAAgD,SAAS;;;;;;sCAExE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;gCAAK,GAAG;4BAAG;4BACzC,SAAS;gCAAE,SAAS;gCAAG,OAAO;gCAAG,GAAG;4BAAE;4BACtC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmC,EAAE;;;;;;0DACnD,6LAAC;gDACC,SAAS;gDACT,WAAU;0DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAKnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;wDACX,EAAE;wDAAe;wDAAE,cAAc,WAAW;;;;;;;8DAE/C,6LAAC;oDAAE,WAAU;;wDACV,EAAE;wDAAc;wDAAE,cAAc,UAAU;;;;;;;;;;;;;sDAI/C,6LAAC,4IAAA,CAAA,UAAmB;4CAClB,gBAAgB;4CAChB,WAAW;4CACX,UAAU;;;;;;sDAGZ,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAoC,EAAE;;;;;;0EACnD,6LAAC;gEAAE,WAAU;0EACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;sDAMX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAU;8DAET,EAAE;;;;;;8DAEL,6LAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAU;8DAET,6BACC;;0EACE,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;0EAAM,EAAE;;;;;;;qFAGX;;0EACE,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,6LAAC;0EAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWzB,6LAAC,yIAAA,CAAA,UAAgB;oBACf,QAAQ;oBACR,SAAS;oBACT,WAAW;oBACX,WAAW;;;;;;gBAIZ,+BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAI;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAI;wBAC3B,WAAU;;0CAEV,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;0CAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvB;GA7mBwB;;QACL,4HAAA,CAAA,eAAY;QACwD,4HAAA,CAAA,eAAY;QAC/C,iIAAA,CAAA,iBAAc;QACjD,qIAAA,CAAA,YAAS;;;KAJF", "debugId": null}}]}