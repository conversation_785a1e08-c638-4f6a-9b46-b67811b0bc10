/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Kufi_Arabic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-kufi%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoKufi%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Kufi_Arabic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-kufi%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoKufi%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2toYWxlJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1lBU01JTiUyMEFMU0hBTSU1QyU1Q3lhc21pbi1hbHNoYW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQStJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxraGFsZVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxZQVNNSU4gQUxTSEFNXFxcXHlhc21pbi1hbHNoYW1cXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca2hhbGVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcWUFTTUlOIEFMU0hBTVxceWFzbWluLWFsc2hhbVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b8e683c6119e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGtoYWxlXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFlBU01JTiBBTFNIQU1cXHlhc21pbi1hbHNoYW1cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImI4ZTY4M2M2MTE5ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_variable_font_cairo_subsets_arabic_latin_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"variable\":\"--font-cairo\",\"subsets\":[\"arabic\",\"latin\"],\"display\":\"swap\"}],\"variableName\":\"cairo\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Cairo\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-cairo\\\",\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"cairo\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_variable_font_cairo_subsets_arabic_latin_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_variable_font_cairo_subsets_arabic_latin_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Kufi_Arabic_arguments_variable_font_noto_kufi_subsets_arabic_display_swap_variableName_notoKufi___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Noto_Kufi_Arabic\",\"arguments\":[{\"variable\":\"--font-noto-kufi\",\"subsets\":[\"arabic\"],\"display\":\"swap\"}],\"variableName\":\"notoKufi\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Noto_Kufi_Arabic\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-noto-kufi\\\",\\\"subsets\\\":[\\\"arabic\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoKufi\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Kufi_Arabic_arguments_variable_font_noto_kufi_subsets_arabic_display_swap_variableName_notoKufi___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Kufi_Arabic_arguments_variable_font_noto_kufi_subsets_arabic_display_swap_variableName_notoKufi___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"ياسمين الشام - تفصيل فساتين حسب الطلب\",\n    description: \"محل ياسمين الشام لتفصيل الفساتين النسائية بأناقة دمشقية. حجز مواعيد، استعلام عن الطلبات، وأفضل الأقمشة.\",\n    keywords: \"تفصيل فساتين، خياطة نسائية، ياسمين الشام، فساتين دمشقية، حجز موعد\",\n    authors: [\n        {\n            name: \"ياسمين الشام\"\n        }\n    ],\n    openGraph: {\n        title: \"ياسمين الشام - تفصيل فساتين حسب الطلب\",\n        description: \"محل ياسمين الشام لتفصيل الفساتين النسائية بأناقة دمشقية\",\n        type: \"website\",\n        locale: \"ar_SA\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_variable_font_cairo_subsets_arabic_latin_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Kufi_Arabic_arguments_variable_font_noto_kufi_subsets_arabic_display_swap_variableName_notoKufi___WEBPACK_IMPORTED_MODULE_3___default().variable)} font-cairo antialiased bg-gradient-to-br from-rose-50 to-pink-50 min-h-screen`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Kufi_Arabic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-kufi%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoKufi%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Kufi_Arabic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-kufi%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoKufi%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2toYWxlJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1lBU01JTiUyMEFMU0hBTSU1QyU1Q3lhc21pbi1hbHNoYW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQStJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxraGFsZVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxZQVNNSU4gQUxTSEFNXFxcXHlhc21pbi1hbHNoYW1cXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/authStore */ \"(ssr)/./src/store/authStore.ts\");\n/* harmony import */ var _store_dataStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/dataStore */ \"(ssr)/./src/store/dataStore.ts\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(ssr)/./src/hooks/useTranslation.ts\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(ssr)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Calendar,CheckCircle,Clock,LogOut,Package,Plus,Settings,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Calendar,CheckCircle,Clock,LogOut,Package,Plus,Settings,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Calendar,CheckCircle,Clock,LogOut,Package,Plus,Settings,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Calendar,CheckCircle,Clock,LogOut,Package,Plus,Settings,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Calendar,CheckCircle,Clock,LogOut,Package,Plus,Settings,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Calendar,CheckCircle,Clock,LogOut,Package,Plus,Settings,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Calendar,CheckCircle,Clock,LogOut,Package,Plus,Settings,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Calendar,CheckCircle,Clock,LogOut,Package,Plus,Settings,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Calendar,CheckCircle,Clock,LogOut,Package,Plus,Settings,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Calendar,CheckCircle,Clock,LogOut,Package,Plus,Settings,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Calendar,CheckCircle,Clock,LogOut,Package,Plus,Settings,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction DashboardContent() {\n    const { user, signOut } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_4__.useAuthStore)();\n    const { orders, appointments, getStats } = (0,_store_dataStore__WEBPACK_IMPORTED_MODULE_5__.useDataStore)();\n    const { t, language, changeLanguage, isArabic } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleSignOut = async ()=>{\n        await signOut();\n        router.push('/');\n    };\n    // حساب الإحصائيات الحقيقية\n    const realStats = getStats();\n    // حساب المواعيد اليوم\n    const todayAppointments = appointments.filter((appointment)=>{\n        const today = new Date().toISOString().split('T')[0];\n        return appointment.appointmentDate === today && appointment.status !== 'cancelled';\n    }).length;\n    // الإحصائيات حسب الدور\n    const getStatsForRole = ()=>{\n        if (user?.role === 'worker') {\n            // إحصائيات العامل - طلباته فقط\n            const workerOrders = orders.filter((order)=>order.assignedWorker === user?.id);\n            const workerCompletedOrders = workerOrders.filter((order)=>order.status === 'completed');\n            const workerActiveOrders = workerOrders.filter((order)=>[\n                    'pending',\n                    'in_progress'\n                ].includes(order.status));\n            return [\n                {\n                    title: t('my_active_orders'),\n                    value: workerActiveOrders.length.toString(),\n                    change: '+0%',\n                    icon: _barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    color: 'from-blue-400 to-blue-600'\n                },\n                {\n                    title: t('my_completed_orders'),\n                    value: workerCompletedOrders.length.toString(),\n                    change: '+0%',\n                    icon: _barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    color: 'from-green-400 to-green-600'\n                },\n                {\n                    title: t('my_total_orders'),\n                    value: workerOrders.length.toString(),\n                    change: '+0%',\n                    icon: _barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    color: 'from-purple-400 to-purple-600'\n                }\n            ];\n        } else {\n            // إحصائيات المدير - جميع البيانات\n            return [\n                {\n                    title: t('active_orders'),\n                    value: realStats.activeOrders.toString(),\n                    change: '+0%',\n                    icon: _barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    color: 'from-blue-400 to-blue-600'\n                },\n                {\n                    title: t('today_appointments'),\n                    value: todayAppointments.toString(),\n                    change: '+0',\n                    icon: _barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                    color: 'from-green-400 to-green-600'\n                },\n                {\n                    title: t('completed_orders'),\n                    value: realStats.completedOrders.toString(),\n                    change: '+0%',\n                    icon: _barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    color: 'from-purple-400 to-purple-600'\n                },\n                {\n                    title: t('total_orders'),\n                    value: realStats.totalOrders.toString(),\n                    change: '+0%',\n                    icon: _barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    color: 'from-pink-400 to-pink-600'\n                }\n            ];\n        }\n    };\n    const stats = getStatsForRole();\n    // أحدث الطلبات (آخر 3 طلبات) - مفلترة حسب الدور\n    const recentOrders = orders.filter((order)=>{\n        // إذا كان المستخدم عامل، اعرض طلباته فقط\n        if (user?.role === 'worker') {\n            return order.assignedWorker === user?.id;\n        }\n        // إذا كان مدير، اعرض جميع الطلبات\n        return true;\n    }).sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()).slice(0, 3).map((order)=>({\n            id: order.id,\n            client: order.clientName,\n            type: order.description,\n            status: order.status,\n            dueDate: order.dueDate\n        }));\n    const getStatusColor = (status)=>{\n        const colors = {\n            pending: 'bg-yellow-100 text-yellow-800',\n            in_progress: 'bg-blue-100 text-blue-800',\n            completed: 'bg-green-100 text-green-800',\n            delivered: 'bg-purple-100 text-purple-800'\n        };\n        return colors[status] || 'bg-gray-100 text-gray-800';\n    };\n    const getStatusLabel = (status)=>{\n        const labels = {\n            pending: t('pending'),\n            in_progress: t('in_progress'),\n            completed: t('completed'),\n            delivered: t('delivered')\n        };\n        return labels[status] || status;\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleDateString('ar-SA', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric'\n        });\n    };\n    const getStatusInfo = (status)=>{\n        const statusMap = {\n            pending: {\n                label: t('pending'),\n                color: 'text-yellow-600',\n                bgColor: 'bg-yellow-100'\n            },\n            in_progress: {\n                label: t('in_progress'),\n                color: 'text-blue-600',\n                bgColor: 'bg-blue-100'\n            },\n            completed: {\n                label: t('completed'),\n                color: 'text-green-600',\n                bgColor: 'bg-green-100'\n            },\n            delivered: {\n                label: t('delivered'),\n                color: 'text-purple-600',\n                bgColor: 'bg-purple-100'\n            },\n            cancelled: {\n                label: t('cancelled'),\n                color: 'text-red-600',\n                bgColor: 'bg-red-100'\n            }\n        };\n        return statusMap[status] || statusMap.pending;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"f02afbb3bd588b5f\",\n                children: 'button[title*=\"تغيير اللغة\"],button[title*=\"Change Language\"]{display:block!important;visibility:visible!important;opacity:1!important;position:relative!important;z-index:99999!important;-webkit-flex-shrink:0!important;-ms-flex-negative:0!important;flex-shrink:0!important}@media(min-width:1920px){button[title*=\"تغيير اللغة\"],button[title*=\"Change Language\"]{display:block!important;visibility:visible!important;opacity:1!important}}@media(min-width:2560px){button[title*=\"تغيير اللغة\"],button[title*=\"Change Language\"]{display:block!important;visibility:visible!important;opacity:1!important}}@media(display-mode:fullscreen){button[title*=\"تغيير اللغة\"],button[title*=\"Change Language\"]{display:block!important;visibility:visible!important;opacity:1!important}}.language-btn-container{overflow:visible!important}'\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"bg-white/80 backdrop-blur-md border-b border-pink-100 shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"w-full max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"hidden lg:flex items-center justify-between h-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"flex items-center space-x-4 space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/\",\n                                                    className: \"text-pink-600 hover:text-pink-700 transition-colors duration-300 group flex items-center space-x-2 space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"w-5 h-5 group-hover:translate-x-1 transition-transform duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-sm font-medium\",\n                                                            children: t('homepage')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"w-px h-6 bg-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"max-w-md\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-xl xl:text-2xl font-bold text-gray-800 truncate\",\n                                                            children: [\n                                                                t('welcome_back'),\n                                                                \"، \",\n                                                                user?.full_name || user?.email\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-gray-600 text-sm\",\n                                                            children: user?.role === 'admin' ? t('admin_dashboard') : t('worker_dashboard')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"px-3 py-1 bg-gradient-to-r from-pink-100 to-rose-100 text-pink-700 rounded-full text-sm font-medium whitespace-nowrap\",\n                                                    children: user?.role === 'admin' ? t('admin') : t('worker')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"flex items-center space-x-4 space-x-reverse language-btn-container\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"flex items-center space-x-3 space-x-reverse language-btn-container\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-right max-w-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"font-medium text-gray-800 truncate\",\n                                                                children: user?.full_name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-sm text-gray-600 truncate\",\n                                                                children: user?.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>changeLanguage(language === 'ar' ? 'en' : 'ar'),\n                                                        title: t('change_language'),\n                                                        style: {\n                                                            display: 'block',\n                                                            visibility: 'visible',\n                                                            position: 'relative',\n                                                            zIndex: 99999,\n                                                            minWidth: '70px',\n                                                            flexShrink: 0,\n                                                            opacity: 1\n                                                        },\n                                                        className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"px-3 py-1.5 text-sm text-gray-600 hover:text-pink-600 bg-gray-100 hover:bg-pink-50 rounded-full transition-all duration-300 font-medium min-w-[70px] text-center\",\n                                                        children: language === 'ar' ? 'English' : 'عربي'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleSignOut,\n                                                        title: t('logout'),\n                                                        className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"p-2 text-gray-600 hover:text-red-600 transition-colors duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"lg:hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"flex items-center justify-between h-14 sm:h-16\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"flex items-center space-x-2 space-x-reverse min-w-0 flex-1 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                            href: \"/\",\n                                                            className: \"text-pink-600 hover:text-pink-700 transition-colors duration-300 group flex items-center space-x-1 space-x-reverse flex-shrink-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-4 h-4 sm:w-5 sm:h-5 group-hover:translate-x-1 transition-transform duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-xs sm:text-sm font-medium hidden sm:inline whitespace-nowrap\",\n                                                                    children: t('homepage')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-xs font-medium sm:hidden whitespace-nowrap\",\n                                                                    children: t('home')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"w-px h-4 sm:h-6 bg-gray-300 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"min-w-0 flex-1 overflow-hidden\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-sm sm:text-lg md:text-xl font-bold text-gray-800 truncate\",\n                                                                children: [\n                                                                    t('welcome_back'),\n                                                                    \"، \",\n                                                                    user?.full_name || user?.email\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"flex items-center space-x-1 sm:space-x-2 space-x-reverse flex-shrink-0 min-w-0 language-btn-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"px-2 sm:px-3 py-1 bg-gradient-to-r from-pink-100 to-rose-100 text-pink-700 rounded-full text-xs sm:text-sm font-medium whitespace-nowrap\",\n                                                            children: t(user?.role === 'admin' ? 'admin' : 'worker')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>changeLanguage(language === 'ar' ? 'en' : 'ar'),\n                                                            title: t('change_language'),\n                                                            style: {\n                                                                display: 'block',\n                                                                visibility: 'visible',\n                                                                position: 'relative',\n                                                                zIndex: 99999,\n                                                                minWidth: '60px',\n                                                                flexShrink: 0,\n                                                                opacity: 1\n                                                            },\n                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"px-2 sm:px-3 py-1 text-xs sm:text-sm text-gray-600 hover:text-pink-600 bg-gray-100 hover:bg-pink-50 rounded-full transition-all duration-300 flex-shrink-0 font-medium min-w-[60px] sm:min-w-[70px] text-center\",\n                                                            children: language === 'ar' ? 'English' : 'عربي'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleSignOut,\n                                                            title: t('logout'),\n                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"p-1.5 sm:p-2 text-gray-600 hover:text-red-600 transition-colors duration-300 flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"pb-3 sm:pb-4 border-t border-gray-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"pt-2 sm:pt-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-xs sm:text-sm text-gray-600 mb-1\",\n                                                        children: [\n                                                            t('dashboard'),\n                                                            \" - \",\n                                                            t(user?.role === 'admin' ? 'admin' : 'worker')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"flex items-center justify-between\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"min-w-0 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"font-medium text-gray-800 text-sm sm:text-base truncate\",\n                                                                    children: user?.full_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-xs sm:text-sm text-gray-600 truncate\",\n                                                                    children: user?.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"w-full max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                className: \"mb-6 sm:mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"flex flex-col gap-4 sm:gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-center sm:text-right overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-gray-800 mb-2 sm:mb-3 break-words\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"block sm:hidden\",\n                                                            children: t('welcome_back')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"hidden sm:inline\",\n                                                            children: [\n                                                                t('welcome_back'),\n                                                                \"، \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-pink-600 break-words\",\n                                                            children: user?.full_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-sm sm:text-base md:text-lg text-gray-600 max-w-2xl mx-auto sm:mx-0 break-words\",\n                                                    children: t('overview_today')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 13\n                                        }, this),\n                                        user?.role === 'admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center sm:justify-start w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/dashboard/add-order\",\n                                                    className: \"btn-primary inline-flex items-center justify-center space-x-2 space-x-reverse px-4 sm:px-6 py-3 sm:py-4 group text-sm sm:text-base w-full sm:w-auto min-w-0 flex-shrink-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4 sm:w-5 sm:h-5 group-hover:scale-110 transition-transform duration-300 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"whitespace-nowrap\",\n                                                            children: t('add_new_order')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/book-appointment\",\n                                                    className: \"btn-secondary inline-flex items-center justify-center space-x-2 space-x-reverse px-4 sm:px-6 py-3 sm:py-4 group text-sm sm:text-base w-full sm:w-auto min-w-0 flex-shrink-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-4 h-4 sm:w-5 sm:h-5 group-hover:scale-110 transition-transform duration-300 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"whitespace-nowrap\",\n                                                            children: t('book_appointment')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this),\n                                        user?.role === 'worker' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg sm:rounded-xl p-4 sm:p-6 text-center sm:text-right overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-lg sm:text-xl font-semibold text-blue-800 mb-2 break-words\",\n                                                    children: t('welcome_worker')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-sm sm:text-base text-blue-600 break-words\",\n                                                    children: t('worker_description')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                className: \"hidden lg:grid lg:grid-cols-4 gap-6 mb-8\",\n                                children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.3 + index * 0.1\n                                        },\n                                        className: \"bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-100 hover:shadow-lg transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-f02afbb3bd588b5f\" + \" \" + `w-12 h-12 bg-gradient-to-r ${stat.color} rounded-lg flex items-center justify-center`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-sm font-medium text-green-600 flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-f02afbb3bd588b5f\",\n                                                                children: stat.change\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-2xl font-bold text-gray-800 mb-1\",\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-gray-600 text-sm\",\n                                                children: stat.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 13\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                className: \"block lg:hidden mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-pink-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2 space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"w-5 h-5 text-pink-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-f02afbb3bd588b5f\",\n                                                    children: t('statistics')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"grid grid-cols-3 gap-3\",\n                                            children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        scale: 0.8\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        scale: 1\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5,\n                                                        delay: 0.3 + index * 0.1\n                                                    },\n                                                    className: \"text-center p-3 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + `w-8 h-8 bg-gradient-to-r ${stat.color} rounded-lg flex items-center justify-center mx-auto mb-2`,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                                className: \"w-4 h-4 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-lg font-bold text-gray-800 mb-1\",\n                                                            children: stat.value\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-xs text-gray-600 leading-tight\",\n                                                            children: stat.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"block lg:hidden space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.4\n                                        },\n                                        className: \"bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-5 h-5 text-pink-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-f02afbb3bd588b5f\",\n                                                        children: t('recent_orders')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"space-y-4\",\n                                                children: [\n                                                    recentOrders.map((order, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                                            initial: {\n                                                                opacity: 0,\n                                                                y: 20\n                                                            },\n                                                            animate: {\n                                                                opacity: 1,\n                                                                y: 0\n                                                            },\n                                                            transition: {\n                                                                duration: 0.4,\n                                                                delay: 0.5 + index * 0.1\n                                                            },\n                                                            className: \"flex items-center justify-between p-4 bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg border border-pink-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-f02afbb3bd588b5f\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"font-medium text-gray-800\",\n                                                                            children: order.client\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 510,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-sm text-gray-600\",\n                                                                            children: order.type\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 511,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"#\",\n                                                                                order.id\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 512,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-right flex items-center space-x-2 space-x-reverse\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-f02afbb3bd588b5f\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + `px-2 py-1 rounded-full text-xs font-medium ${getStatusInfo(order.status).bgColor} ${getStatusInfo(order.status).color}`,\n                                                                                children: getStatusInfo(order.status).label\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 517,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-xs text-gray-500 mt-1\",\n                                                                                children: formatDate(order.dueDate)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 520,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 516,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 515,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, order.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 17\n                                                        }, this)),\n                                                    recentOrders.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-gray-500\",\n                                                                children: t('no_orders_found')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 531,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/dashboard/orders\",\n                                                className: \"w-full mt-4 btn-secondary py-2 text-sm inline-flex items-center justify-center\",\n                                                children: [\n                                                    t('view_all'),\n                                                    \" \",\n                                                    t('orders')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 11\n                                    }, this),\n                                    user?.role === 'admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.6\n                                        },\n                                        className: \"bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-5 h-5 text-pink-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-f02afbb3bd588b5f\",\n                                                        children: t('quick_actions')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"grid gap-4 grid-cols-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/dashboard/add-order\",\n                                                        className: \"p-4 bg-gradient-to-r from-pink-50 to-pink-100 rounded-lg border border-pink-200 hover:shadow-md transition-all duration-300 text-center block\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-6 h-6 text-pink-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-sm font-medium text-pink-800\",\n                                                                children: t('add_new_order')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/dashboard/workers\",\n                                                        className: \"p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200 hover:shadow-md transition-all duration-300 text-center block\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-6 h-6 text-blue-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-sm font-medium text-blue-800\",\n                                                                children: t('worker_management')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/dashboard/appointments\",\n                                                        className: \"p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border border-purple-200 hover:shadow-md transition-all duration-300 text-center block col-span-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"w-6 h-6 text-purple-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 580,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-sm font-medium text-purple-800\",\n                                                                children: t('appointments')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"font-medium text-gray-800 mb-2\",\n                                                        children: t('reminder')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-sm text-gray-600\",\n                                                        children: [\n                                                            t('you_have'),\n                                                            \" \",\n                                                            todayAppointments,\n                                                            \" \",\n                                                            t('today_appointments_reminder'),\n                                                            \" \",\n                                                            t('and'),\n                                                            \" \",\n                                                            realStats.activeOrders,\n                                                            \" \",\n                                                            t('orders_need_follow')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 13\n                                    }, this),\n                                    user?.role === 'admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.8\n                                        },\n                                        className: \"bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-f02afbb3bd588b5f\",\n                                                        children: t('reports')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/dashboard/reports\",\n                                                className: \"w-full p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200 hover:shadow-md transition-all duration-300 text-center block\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-8 h-8 text-green-600 mx-auto mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-base font-medium text-green-800\",\n                                                        children: t('detailed_reports')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"hidden lg:grid lg:grid-cols-2 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -30\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.4\n                                        },\n                                        className: \"bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-5 h-5 text-pink-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-f02afbb3bd588b5f\",\n                                                        children: t('recent_orders')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 629,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"space-y-4\",\n                                                children: recentOrders.map((order, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 20\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.4,\n                                                            delay: 0.6 + index * 0.1\n                                                        },\n                                                        className: \"flex items-center justify-between p-4 bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg border border-pink-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-f02afbb3bd588b5f\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"font-medium text-gray-800\",\n                                                                        children: order.client\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 644,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-sm text-gray-600\",\n                                                                        children: order.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 645,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"#\",\n                                                                            order.id\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 646,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-right flex items-center space-x-2 space-x-reverse\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-f02afbb3bd588b5f\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + `px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`,\n                                                                            children: getStatusLabel(order.status)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 651,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-xs text-gray-500 mt-1 flex items-center space-x-1 space-x-reverse\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"w-3 h-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 655,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-f02afbb3bd588b5f\",\n                                                                                    children: order.dueDate\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 656,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 654,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 650,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 649,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, order.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 17\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/dashboard/orders\",\n                                                className: \"w-full mt-4 btn-secondary py-2 text-sm inline-flex items-center justify-center\",\n                                                children: [\n                                                    t('view_all'),\n                                                    \" \",\n                                                    t('orders')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 11\n                                    }, this),\n                                    user?.role === 'admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 30\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.6\n                                        },\n                                        className: \"bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-5 h-5 text-pink-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-f02afbb3bd588b5f\",\n                                                        children: t('quick_actions')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"grid gap-4 grid-cols-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/dashboard/add-order\",\n                                                        className: \"p-4 bg-gradient-to-r from-pink-50 to-pink-100 rounded-lg border border-pink-200 hover:shadow-md transition-all duration-300 text-center block\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-6 h-6 text-pink-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 692,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-sm font-medium text-pink-800\",\n                                                                children: t('add_new_order')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/dashboard/workers\",\n                                                        className: \"p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200 hover:shadow-md transition-all duration-300 text-center block\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-6 h-6 text-blue-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-sm font-medium text-blue-800\",\n                                                                children: t('worker_management')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 701,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 696,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/dashboard/reports\",\n                                                        className: \"p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200 hover:shadow-md transition-all duration-300 text-center block\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"w-6 h-6 text-green-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 708,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-sm font-medium text-green-800\",\n                                                                children: t('reports')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/dashboard/appointments\",\n                                                        className: \"p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border border-purple-200 hover:shadow-md transition-all duration-300 text-center block\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Calendar_CheckCircle_Clock_LogOut_Package_Plus_Settings_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"w-6 h-6 text-purple-600 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 716,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-sm font-medium text-purple-800\",\n                                                                children: t('appointments')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 717,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 712,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"font-medium text-gray-800 mb-2\",\n                                                        children: t('reminder')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-f02afbb3bd588b5f\" + \" \" + \"text-sm text-gray-600\",\n                                                        children: [\n                                                            t('you_have'),\n                                                            \" \",\n                                                            todayAppointments,\n                                                            \" \",\n                                                            t('today_appointments_reminder'),\n                                                            \" \",\n                                                            t('and'),\n                                                            \" \",\n                                                            realStats.activeOrders,\n                                                            \" \",\n                                                            t('orders_need_follow')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\nfunction DashboardPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 739,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 738,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Rhc2hib2FyZC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUdzQztBQUNLO0FBQ2Y7QUFDb0I7QUFDQTtBQUNPO0FBQ0M7QUFjbkM7QUFFckIsU0FBU2tCO0lBQ1AsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLE9BQU8sRUFBRSxHQUFHakIsOERBQVlBO0lBQ3RDLE1BQU0sRUFBRWtCLE1BQU0sRUFBRUMsWUFBWSxFQUFFQyxRQUFRLEVBQUUsR0FBR25CLDhEQUFZQTtJQUN2RCxNQUFNLEVBQUVvQixDQUFDLEVBQUVDLFFBQVEsRUFBRUMsY0FBYyxFQUFFQyxRQUFRLEVBQUUsR0FBR3RCLHFFQUFjQTtJQUNoRSxNQUFNdUIsU0FBUzNCLDBEQUFTQTtJQUl4QixNQUFNNEIsZ0JBQWdCO1FBQ3BCLE1BQU1UO1FBQ05RLE9BQU9FLElBQUksQ0FBQztJQUNkO0lBSUEsMkJBQTJCO0lBQzNCLE1BQU1DLFlBQVlSO0lBRWxCLHNCQUFzQjtJQUN0QixNQUFNUyxvQkFBb0JWLGFBQWFXLE1BQU0sQ0FBQ0MsQ0FBQUE7UUFDNUMsTUFBTUMsUUFBUSxJQUFJQyxPQUFPQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtRQUNwRCxPQUFPSixZQUFZSyxlQUFlLEtBQUtKLFNBQVNELFlBQVlNLE1BQU0sS0FBSztJQUN6RSxHQUFHQyxNQUFNO0lBRVQsdUJBQXVCO0lBQ3ZCLE1BQU1DLGtCQUFrQjtRQUN0QixJQUFJdkIsTUFBTXdCLFNBQVMsVUFBVTtZQUMzQiwrQkFBK0I7WUFDL0IsTUFBTUMsZUFBZXZCLE9BQU9ZLE1BQU0sQ0FBQ1ksQ0FBQUEsUUFBU0EsTUFBTUMsY0FBYyxLQUFLM0IsTUFBTTRCO1lBQzNFLE1BQU1DLHdCQUF3QkosYUFBYVgsTUFBTSxDQUFDWSxDQUFBQSxRQUFTQSxNQUFNTCxNQUFNLEtBQUs7WUFDNUUsTUFBTVMscUJBQXFCTCxhQUFhWCxNQUFNLENBQUNZLENBQUFBLFFBQVM7b0JBQUM7b0JBQVc7aUJBQWMsQ0FBQ0ssUUFBUSxDQUFDTCxNQUFNTCxNQUFNO1lBRXhHLE9BQU87Z0JBQ0w7b0JBQ0VXLE9BQU8zQixFQUFFO29CQUNUNEIsT0FBT0gsbUJBQW1CUixNQUFNLENBQUNZLFFBQVE7b0JBQ3pDQyxRQUFRO29CQUNSQyxNQUFNN0MseUtBQU9BO29CQUNiOEMsT0FBTztnQkFDVDtnQkFDQTtvQkFDRUwsT0FBTzNCLEVBQUU7b0JBQ1Q0QixPQUFPSixzQkFBc0JQLE1BQU0sQ0FBQ1ksUUFBUTtvQkFDNUNDLFFBQVE7b0JBQ1JDLE1BQU14Qyx5S0FBV0E7b0JBQ2pCeUMsT0FBTztnQkFDVDtnQkFDQTtvQkFDRUwsT0FBTzNCLEVBQUU7b0JBQ1Q0QixPQUFPUixhQUFhSCxNQUFNLENBQUNZLFFBQVE7b0JBQ25DQyxRQUFRO29CQUNSQyxNQUFNL0MsMEtBQUtBO29CQUNYZ0QsT0FBTztnQkFDVDthQUNEO1FBQ0gsT0FBTztZQUNMLGtDQUFrQztZQUNsQyxPQUFPO2dCQUNMO29CQUNFTCxPQUFPM0IsRUFBRTtvQkFDVDRCLE9BQU9yQixVQUFVMEIsWUFBWSxDQUFDSixRQUFRO29CQUN0Q0MsUUFBUTtvQkFDUkMsTUFBTTdDLHlLQUFPQTtvQkFDYjhDLE9BQU87Z0JBQ1Q7Z0JBQ0E7b0JBQ0VMLE9BQU8zQixFQUFFO29CQUNUNEIsT0FBT3BCLGtCQUFrQnFCLFFBQVE7b0JBQ2pDQyxRQUFRO29CQUNSQyxNQUFNOUMsMEtBQVFBO29CQUNkK0MsT0FBTztnQkFDVDtnQkFDQTtvQkFDRUwsT0FBTzNCLEVBQUU7b0JBQ1Q0QixPQUFPckIsVUFBVTJCLGVBQWUsQ0FBQ0wsUUFBUTtvQkFDekNDLFFBQVE7b0JBQ1JDLE1BQU14Qyx5S0FBV0E7b0JBQ2pCeUMsT0FBTztnQkFDVDtnQkFDQTtvQkFDRUwsT0FBTzNCLEVBQUU7b0JBQ1Q0QixPQUFPckIsVUFBVTRCLFdBQVcsQ0FBQ04sUUFBUTtvQkFDckNDLFFBQVE7b0JBQ1JDLE1BQU0vQywwS0FBS0E7b0JBQ1hnRCxPQUFPO2dCQUNUO2FBQ0Q7UUFDSDtJQUNGO0lBRUEsTUFBTUksUUFBUWxCO0lBRWQsZ0RBQWdEO0lBQ2hELE1BQU1tQixlQUFleEMsT0FDbEJZLE1BQU0sQ0FBQ1ksQ0FBQUE7UUFDTix5Q0FBeUM7UUFDekMsSUFBSTFCLE1BQU13QixTQUFTLFVBQVU7WUFDM0IsT0FBT0UsTUFBTUMsY0FBYyxLQUFLM0IsTUFBTTRCO1FBQ3hDO1FBQ0Esa0NBQWtDO1FBQ2xDLE9BQU87SUFDVCxHQUNDZSxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTSxJQUFJNUIsS0FBSzRCLEVBQUVDLFNBQVMsRUFBRUMsT0FBTyxLQUFLLElBQUk5QixLQUFLMkIsRUFBRUUsU0FBUyxFQUFFQyxPQUFPLElBQzlFQyxLQUFLLENBQUMsR0FBRyxHQUNUQyxHQUFHLENBQUN2QixDQUFBQSxRQUFVO1lBQ2JFLElBQUlGLE1BQU1FLEVBQUU7WUFDWnNCLFFBQVF4QixNQUFNeUIsVUFBVTtZQUN4QkMsTUFBTTFCLE1BQU0yQixXQUFXO1lBQ3ZCaEMsUUFBUUssTUFBTUwsTUFBTTtZQUNwQmlDLFNBQVM1QixNQUFNNEIsT0FBTztRQUN4QjtJQUVGLE1BQU1DLGlCQUFpQixDQUFDbEM7UUFDdEIsTUFBTW1DLFNBQVM7WUFDYkMsU0FBUztZQUNUQyxhQUFhO1lBQ2JDLFdBQVc7WUFDWEMsV0FBVztRQUNiO1FBQ0EsT0FBT0osTUFBTSxDQUFDbkMsT0FBOEIsSUFBSTtJQUNsRDtJQUVBLE1BQU13QyxpQkFBaUIsQ0FBQ3hDO1FBQ3RCLE1BQU15QyxTQUFTO1lBQ2JMLFNBQVNwRCxFQUFFO1lBQ1hxRCxhQUFhckQsRUFBRTtZQUNmc0QsV0FBV3RELEVBQUU7WUFDYnVELFdBQVd2RCxFQUFFO1FBQ2Y7UUFDQSxPQUFPeUQsTUFBTSxDQUFDekMsT0FBOEIsSUFBSUE7SUFDbEQ7SUFFQSxNQUFNMEMsYUFBYSxDQUFDQztRQUNsQixNQUFNQyxPQUFPLElBQUloRCxLQUFLK0M7UUFDdEIsT0FBT0MsS0FBS0Msa0JBQWtCLENBQUMsU0FBUztZQUN0Q0MsTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLEtBQUs7UUFDUDtJQUNGO0lBRUEsTUFBTUMsZ0JBQWdCLENBQUNqRDtRQUNyQixNQUFNa0QsWUFBWTtZQUNoQmQsU0FBUztnQkFBRWUsT0FBT25FLEVBQUU7Z0JBQVlnQyxPQUFPO2dCQUFtQm9DLFNBQVM7WUFBZ0I7WUFDbkZmLGFBQWE7Z0JBQUVjLE9BQU9uRSxFQUFFO2dCQUFnQmdDLE9BQU87Z0JBQWlCb0MsU0FBUztZQUFjO1lBQ3ZGZCxXQUFXO2dCQUFFYSxPQUFPbkUsRUFBRTtnQkFBY2dDLE9BQU87Z0JBQWtCb0MsU0FBUztZQUFlO1lBQ3JGYixXQUFXO2dCQUFFWSxPQUFPbkUsRUFBRTtnQkFBY2dDLE9BQU87Z0JBQW1Cb0MsU0FBUztZQUFnQjtZQUN2RkMsV0FBVztnQkFBRUYsT0FBT25FLEVBQUU7Z0JBQWNnQyxPQUFPO2dCQUFnQm9DLFNBQVM7WUFBYTtRQUNuRjtRQUNBLE9BQU9GLFNBQVMsQ0FBQ2xELE9BQWlDLElBQUlrRCxVQUFVZCxPQUFPO0lBQ3pFO0lBSUEscUJBQ0U7Ozs7OzswQkFnREUsOERBQUNrQjswREFBYzs7a0NBRWYsOERBQUNDO2tFQUFpQjtrQ0FDaEIsNEVBQUNEO3NFQUFjOzs4Q0FFYiw4REFBQ0E7OEVBQWM7O3NEQUNiLDhEQUFDQTtzRkFBYzs7OERBQ2IsOERBQUM1RixrREFBSUE7b0RBQ0g4RixNQUFLO29EQUNMQyxXQUFVOztzRUFFViw4REFBQ2hGLDBLQUFVQTs0REFBQ2dGLFdBQVU7Ozs7OztzRUFDdEIsOERBQUNDO3NHQUFlO3NFQUF1QjFFLEVBQUU7Ozs7Ozs7Ozs7Ozs4REFFM0MsOERBQUNzRTs4RkFBYzs7Ozs7OzhEQUNmLDhEQUFDQTs4RkFBYzs7c0VBQ2IsOERBQUNLO3NHQUFhOztnRUFDWDNFLEVBQUU7Z0VBQWdCO2dFQUFHTCxNQUFNaUYsYUFBYWpGLE1BQU1rRjs7Ozs7OztzRUFFakQsOERBQUNDO3NHQUFZO3NFQUNWbkYsTUFBTXdCLFNBQVMsVUFBVW5CLEVBQUUscUJBQXFCQSxFQUFFOzs7Ozs7Ozs7Ozs7OERBR3ZELDhEQUFDMEU7OEZBQWU7OERBQ2IvRSxNQUFNd0IsU0FBUyxVQUFVbkIsRUFBRSxXQUFXQSxFQUFFOzs7Ozs7Ozs7Ozs7c0RBSTdDLDhEQUFDc0U7c0ZBQWM7c0RBQ2IsNEVBQUNBOzBGQUFjOztrRUFDYiw4REFBQ0E7a0dBQWM7OzBFQUNiLDhEQUFDUTswR0FBWTswRUFBc0NuRixNQUFNaUY7Ozs7OzswRUFDekQsOERBQUNFOzBHQUFZOzBFQUFrQ25GLE1BQU1rRjs7Ozs7Ozs7Ozs7O2tFQUl2RCw4REFBQ0U7d0RBQ0NDLFNBQVMsSUFBTTlFLGVBQWVELGFBQWEsT0FBTyxPQUFPO3dEQUV6RDBCLE9BQU8zQixFQUFFO3dEQUNUaUYsT0FBTzs0REFDTEMsU0FBUzs0REFDVEMsWUFBWTs0REFDWkMsVUFBVTs0REFDVkMsUUFBUTs0REFDUkMsVUFBVTs0REFDVkMsWUFBWTs0REFDWkMsU0FBUzt3REFDWDtrR0FWVTtrRUFZVHZGLGFBQWEsT0FBTyxZQUFZOzs7Ozs7a0VBR25DLDhEQUFDOEU7d0RBQ0NDLFNBQVMzRTt3REFFVHNCLE9BQU8zQixFQUFFO2tHQURDO2tFQUdWLDRFQUFDWiwwS0FBTUE7NERBQUNxRixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU8xQiw4REFBQ0g7OEVBQWM7O3NEQUViLDhEQUFDQTtzRkFBYzs7OERBQ2IsOERBQUNBOzhGQUFjOztzRUFDYiw4REFBQzVGLGtEQUFJQTs0REFDSDhGLE1BQUs7NERBQ0xDLFdBQVU7OzhFQUVWLDhEQUFDaEYsMEtBQVVBO29FQUFDZ0YsV0FBVTs7Ozs7OzhFQUN0Qiw4REFBQ0M7OEdBQWU7OEVBQXFFMUUsRUFBRTs7Ozs7OzhFQUN2Riw4REFBQzBFOzhHQUFlOzhFQUFtRDFFLEVBQUU7Ozs7Ozs7Ozs7OztzRUFFdkUsOERBQUNzRTtzR0FBYzs7Ozs7O3NFQUNmLDhEQUFDQTtzR0FBYztzRUFDYiw0RUFBQ0s7MEdBQWE7O29FQUNYM0UsRUFBRTtvRUFBZ0I7b0VBQUdMLE1BQU1pRixhQUFhakYsTUFBTWtGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBS3JELDhEQUFDUDs4RkFBYzs7c0VBQ2IsOERBQUNJO3NHQUFlO3NFQUNiMUUsRUFBRUwsTUFBTXdCLFNBQVMsVUFBVSxVQUFVOzs7Ozs7c0VBRXhDLDhEQUFDNEQ7NERBQ0NDLFNBQVMsSUFBTTlFLGVBQWVELGFBQWEsT0FBTyxPQUFPOzREQUV6RDBCLE9BQU8zQixFQUFFOzREQUNUaUYsT0FBTztnRUFDTEMsU0FBUztnRUFDVEMsWUFBWTtnRUFDWkMsVUFBVTtnRUFDVkMsUUFBUTtnRUFDUkMsVUFBVTtnRUFDVkMsWUFBWTtnRUFDWkMsU0FBUzs0REFDWDtzR0FWVTtzRUFZVHZGLGFBQWEsT0FBTyxZQUFZOzs7Ozs7c0VBR25DLDhEQUFDOEU7NERBQ0NDLFNBQVMzRTs0REFFVHNCLE9BQU8zQixFQUFFO3NHQURDO3NFQUdWLDRFQUFDWiwwS0FBTUE7Z0VBQUNxRixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFNeEIsOERBQUNIO3NGQUFjO3NEQUNiLDRFQUFDQTswRkFBYzs7a0VBQ2IsOERBQUNRO2tHQUFZOzs0REFDVjlFLEVBQUU7NERBQWE7NERBQUlBLEVBQUVMLE1BQU13QixTQUFTLFVBQVUsVUFBVTs7Ozs7OztrRUFFM0QsOERBQUNtRDtrR0FBYztrRUFDYiw0RUFBQ0E7c0dBQWM7OzhFQUNiLDhEQUFDUTs4R0FBWTs4RUFBMkRuRixNQUFNaUY7Ozs7Ozs4RUFDOUUsOERBQUNFOzhHQUFZOzhFQUE2Q25GLE1BQU1rRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVM5RSw4REFBQ1A7a0VBQWM7OzBDQUViLDhEQUFDOUYsa0RBQU1BLENBQUM4RixHQUFHO2dDQUNUbUIsU0FBUztvQ0FBRUQsU0FBUztvQ0FBR0UsR0FBRztnQ0FBRztnQ0FDN0JDLFNBQVM7b0NBQUVILFNBQVM7b0NBQUdFLEdBQUc7Z0NBQUU7Z0NBQzVCRSxZQUFZO29DQUFFQyxVQUFVO2dDQUFJO2dDQUM1QnBCLFdBQVU7MENBRVYsNEVBQUNIOzhFQUFjOztzREFFYiw4REFBQ0E7c0ZBQWM7OzhEQUNiLDhEQUFDd0I7OEZBQWE7O3NFQUNaLDhEQUFDcEI7c0dBQWU7c0VBQW1CMUUsRUFBRTs7Ozs7O3NFQUNyQyw4REFBQzBFO3NHQUFlOztnRUFBb0IxRSxFQUFFO2dFQUFnQjs7Ozs7OztzRUFDdEQsOERBQUMwRTtzR0FBZTtzRUFBNkIvRSxNQUFNaUY7Ozs7Ozs7Ozs7Ozs4REFFckQsOERBQUNFOzhGQUFZOzhEQUNWOUUsRUFBRTs7Ozs7Ozs7Ozs7O3dDQUtOTCxNQUFNd0IsU0FBUyx5QkFDZCw4REFBQ21EO3NGQUFjOzs4REFDYiw4REFBQzVGLGtEQUFJQTtvREFDSDhGLE1BQUs7b0RBQ0xDLFdBQVU7O3NFQUVWLDhEQUFDakYsMEtBQUlBOzREQUFDaUYsV0FBVTs7Ozs7O3NFQUNoQiw4REFBQ0M7c0dBQWU7c0VBQXFCMUUsRUFBRTs7Ozs7Ozs7Ozs7OzhEQUd6Qyw4REFBQ3RCLGtEQUFJQTtvREFDSDhGLE1BQUs7b0RBQ0xDLFdBQVU7O3NFQUVWLDhEQUFDeEYsMEtBQVFBOzREQUFDd0YsV0FBVTs7Ozs7O3NFQUNwQiw4REFBQ0M7c0dBQWU7c0VBQXFCMUUsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dDQU01Q0wsTUFBTXdCLFNBQVMsMEJBQ2QsOERBQUNtRDtzRkFBYzs7OERBQ2IsOERBQUN5Qjs4RkFBYTs4REFDWC9GLEVBQUU7Ozs7Ozs4REFFTCw4REFBQzhFOzhGQUFZOzhEQUNWOUUsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBVWIsOERBQUN4QixrREFBTUEsQ0FBQzhGLEdBQUc7Z0NBQ1RtQixTQUFTO29DQUFFRCxTQUFTO29DQUFHRSxHQUFHO2dDQUFHO2dDQUM3QkMsU0FBUztvQ0FBRUgsU0FBUztvQ0FBR0UsR0FBRztnQ0FBRTtnQ0FDNUJFLFlBQVk7b0NBQUVDLFVBQVU7b0NBQUtHLE9BQU87Z0NBQUk7Z0NBQ3hDdkIsV0FBVTswQ0FFVHJDLE1BQU1RLEdBQUcsQ0FBQyxDQUFDcUQsTUFBTUMsc0JBQ2hCLDhEQUFDMUgsa0RBQU1BLENBQUM4RixHQUFHO3dDQUVUbUIsU0FBUzs0Q0FBRUQsU0FBUzs0Q0FBR1csT0FBTzt3Q0FBSTt3Q0FDbENSLFNBQVM7NENBQUVILFNBQVM7NENBQUdXLE9BQU87d0NBQUU7d0NBQ2hDUCxZQUFZOzRDQUFFQyxVQUFVOzRDQUFLRyxPQUFPLE1BQU1FLFFBQVE7d0NBQUk7d0NBQ3REekIsV0FBVTs7MERBRVYsOERBQUNIOzBGQUFjOztrRUFDYiw4REFBQ0E7a0dBQWUsQ0FBQywyQkFBMkIsRUFBRTJCLEtBQUtqRSxLQUFLLENBQUMsNENBQTRDLENBQUM7a0VBQ3BHLDRFQUFDaUUsS0FBS2xFLElBQUk7NERBQUMwQyxXQUFVOzs7Ozs7Ozs7OztrRUFFdkIsOERBQUNDO2tHQUFlOzswRUFDZCw4REFBQ3JGLDBLQUFVQTtnRUFBQ29GLFdBQVU7Ozs7OzswRUFDdEIsOERBQUNDOzswRUFBTXVCLEtBQUtuRSxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBSXRCLDhEQUFDaUU7MEZBQWE7MERBQXlDRSxLQUFLckUsS0FBSzs7Ozs7OzBEQUNqRSw4REFBQ2tEOzBGQUFZOzBEQUF5Qm1CLEtBQUt0RSxLQUFLOzs7Ozs7O3VDQWpCM0N1RTs7Ozs7Ozs7OzswQ0F1QlgsOERBQUMxSCxrREFBTUEsQ0FBQzhGLEdBQUc7Z0NBQ1RtQixTQUFTO29DQUFFRCxTQUFTO29DQUFHRSxHQUFHO2dDQUFHO2dDQUM3QkMsU0FBUztvQ0FBRUgsU0FBUztvQ0FBR0UsR0FBRztnQ0FBRTtnQ0FDNUJFLFlBQVk7b0NBQUVDLFVBQVU7b0NBQUtHLE9BQU87Z0NBQUk7Z0NBQ3hDdkIsV0FBVTswQ0FFViw0RUFBQ0g7OEVBQWM7O3NEQUNiLDhEQUFDeUI7c0ZBQWE7OzhEQUNaLDhEQUFDaEgsMEtBQVNBO29EQUFDMEYsV0FBVTs7Ozs7OzhEQUNyQiw4REFBQ0M7OzhEQUFNMUUsRUFBRTs7Ozs7Ozs7Ozs7O3NEQUdYLDhEQUFDc0U7c0ZBQWM7c0RBQ1psQyxNQUFNUSxHQUFHLENBQUMsQ0FBQ3FELE1BQU1DLHNCQUNoQiw4REFBQzFILGtEQUFNQSxDQUFDOEYsR0FBRztvREFFVG1CLFNBQVM7d0RBQUVELFNBQVM7d0RBQUdXLE9BQU87b0RBQUk7b0RBQ2xDUixTQUFTO3dEQUFFSCxTQUFTO3dEQUFHVyxPQUFPO29EQUFFO29EQUNoQ1AsWUFBWTt3REFBRUMsVUFBVTt3REFBS0csT0FBTyxNQUFNRSxRQUFRO29EQUFJO29EQUN0RHpCLFdBQVU7O3NFQUVWLDhEQUFDSDtzR0FBZSxDQUFDLHlCQUF5QixFQUFFMkIsS0FBS2pFLEtBQUssQ0FBQyx5REFBeUQsQ0FBQztzRUFDL0csNEVBQUNpRSxLQUFLbEUsSUFBSTtnRUFBQzBDLFdBQVU7Ozs7Ozs7Ozs7O3NFQUd2Qiw4REFBQzJCO3NHQUFhO3NFQUF3Q0gsS0FBS3JFLEtBQUs7Ozs7OztzRUFDaEUsOERBQUNrRDtzR0FBWTtzRUFBdUNtQixLQUFLdEUsS0FBSzs7Ozs7OzttREFYekR1RTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQW1CZiw4REFBQzVCOzBFQUFjOztrREFFYiw4REFBQzlGLGtEQUFNQSxDQUFDOEYsR0FBRzt3Q0FDVG1CLFNBQVM7NENBQUVELFNBQVM7NENBQUdFLEdBQUc7d0NBQUc7d0NBQzdCQyxTQUFTOzRDQUFFSCxTQUFTOzRDQUFHRSxHQUFHO3dDQUFFO3dDQUM1QkUsWUFBWTs0Q0FBRUMsVUFBVTs0Q0FBS0csT0FBTzt3Q0FBSTt3Q0FDeEN2QixXQUFVOzswREFFViw4REFBQ3NCOzBGQUFhOztrRUFDWiw4REFBQzdHLHlLQUFPQTt3REFBQ3VGLFdBQVU7Ozs7OztrRUFDbkIsOERBQUNDOztrRUFBTTFFLEVBQUU7Ozs7Ozs7Ozs7OzswREFHWCw4REFBQ3NFOzBGQUFjOztvREFDWmpDLGFBQWFPLEdBQUcsQ0FBQyxDQUFDdkIsT0FBTzZFLHNCQUN4Qiw4REFBQzFILGtEQUFNQSxDQUFDOEYsR0FBRzs0REFFVG1CLFNBQVM7Z0VBQUVELFNBQVM7Z0VBQUdFLEdBQUc7NERBQUc7NERBQzdCQyxTQUFTO2dFQUFFSCxTQUFTO2dFQUFHRSxHQUFHOzREQUFFOzREQUM1QkUsWUFBWTtnRUFBRUMsVUFBVTtnRUFBS0csT0FBTyxNQUFNRSxRQUFROzREQUFJOzREQUN0RHpCLFdBQVU7OzhFQUVWLDhEQUFDSDs7O3NGQUNDLDhEQUFDOEI7c0hBQWE7c0ZBQTZCL0UsTUFBTXdCLE1BQU07Ozs7OztzRkFDdkQsOERBQUNpQztzSEFBWTtzRkFBeUJ6RCxNQUFNMEIsSUFBSTs7Ozs7O3NGQUNoRCw4REFBQytCO3NIQUFZOztnRkFBd0I7Z0ZBQUV6RCxNQUFNRSxFQUFFOzs7Ozs7Ozs7Ozs7OzhFQUdqRCw4REFBQytDOzhHQUFjOzhFQUNiLDRFQUFDQTs7OzBGQUNDLDhEQUFDSTswSEFBZ0IsQ0FBQywyQ0FBMkMsRUFBRVQsY0FBYzVDLE1BQU1MLE1BQU0sRUFBRW9ELE9BQU8sQ0FBQyxDQUFDLEVBQUVILGNBQWM1QyxNQUFNTCxNQUFNLEVBQUVnQixLQUFLLEVBQUU7MEZBQ3RJaUMsY0FBYzVDLE1BQU1MLE1BQU0sRUFBRW1ELEtBQUs7Ozs7OzswRkFFcEMsOERBQUNXOzBIQUFZOzBGQUE4QnBCLFdBQVdyQyxNQUFNNEIsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzJEQWpCbEU1QixNQUFNRSxFQUFFOzs7OztvREF5QmhCYyxhQUFhcEIsTUFBTSxLQUFLLG1CQUN2Qiw4REFBQ3FEO2tHQUFjOzswRUFDYiw4REFBQ3BGLHlLQUFPQTtnRUFBQ3VGLFdBQVU7Ozs7OzswRUFDbkIsOERBQUNLOzBHQUFZOzBFQUFpQjlFLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFLdEMsOERBQUN0QixrREFBSUE7Z0RBQ0g4RixNQUFLO2dEQUNMQyxXQUFVOztvREFFVHpFLEVBQUU7b0RBQVk7b0RBQUVBLEVBQUU7Ozs7Ozs7Ozs7Ozs7b0NBT3RCTCxNQUFNd0IsU0FBUyx5QkFDZCw4REFBQzNDLGtEQUFNQSxDQUFDOEYsR0FBRzt3Q0FDVG1CLFNBQVM7NENBQUVELFNBQVM7NENBQUdFLEdBQUc7d0NBQUc7d0NBQzdCQyxTQUFTOzRDQUFFSCxTQUFTOzRDQUFHRSxHQUFHO3dDQUFFO3dDQUM1QkUsWUFBWTs0Q0FBRUMsVUFBVTs0Q0FBS0csT0FBTzt3Q0FBSTt3Q0FDeEN2QixXQUFVOzswREFFViw4REFBQ3NCOzBGQUFhOztrRUFDWiw4REFBQzVHLDBLQUFRQTt3REFBQ3NGLFdBQVU7Ozs7OztrRUFDcEIsOERBQUNDOztrRUFBTTFFLEVBQUU7Ozs7Ozs7Ozs7OzswREFHWCw4REFBQ3NFOzBGQUFjOztrRUFDYiw4REFBQzVGLGtEQUFJQTt3REFDSDhGLE1BQUs7d0RBQ0xDLFdBQVU7OzBFQUVWLDhEQUFDakYsMEtBQUlBO2dFQUFDaUYsV0FBVTs7Ozs7OzBFQUNoQiw4REFBQ0M7MEdBQWU7MEVBQXFDMUUsRUFBRTs7Ozs7Ozs7Ozs7O2tFQUd6RCw4REFBQ3RCLGtEQUFJQTt3REFDSDhGLE1BQUs7d0RBQ0xDLFdBQVU7OzBFQUVWLDhEQUFDekYsMEtBQUtBO2dFQUFDeUYsV0FBVTs7Ozs7OzBFQUNqQiw4REFBQ0M7MEdBQWU7MEVBQXFDMUUsRUFBRTs7Ozs7Ozs7Ozs7O2tFQUd6RCw4REFBQ3RCLGtEQUFJQTt3REFDSDhGLE1BQUs7d0RBQ0xDLFdBQVU7OzBFQUVWLDhEQUFDeEYsMEtBQVFBO2dFQUFDd0YsV0FBVTs7Ozs7OzBFQUNwQiw4REFBQ0M7MEdBQWU7MEVBQXVDMUUsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUk3RCw4REFBQ3NFOzBGQUFjOztrRUFDYiw4REFBQzhCO2tHQUFhO2tFQUFrQ3BHLEVBQUU7Ozs7OztrRUFDbEQsOERBQUM4RTtrR0FBWTs7NERBQ1Y5RSxFQUFFOzREQUFZOzREQUFFUTs0REFBa0I7NERBQUVSLEVBQUU7NERBQStCOzREQUFFQSxFQUFFOzREQUFPOzREQUFFTyxVQUFVMEIsWUFBWTs0REFBQzs0REFBRWpDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBU3JITCxNQUFNd0IsU0FBUyx5QkFDZCw4REFBQzNDLGtEQUFNQSxDQUFDOEYsR0FBRzt3Q0FDVG1CLFNBQVM7NENBQUVELFNBQVM7NENBQUdFLEdBQUc7d0NBQUc7d0NBQzdCQyxTQUFTOzRDQUFFSCxTQUFTOzRDQUFHRSxHQUFHO3dDQUFFO3dDQUM1QkUsWUFBWTs0Q0FBRUMsVUFBVTs0Q0FBS0csT0FBTzt3Q0FBSTt3Q0FDeEN2QixXQUFVOzswREFFViw4REFBQ3NCOzBGQUFhOztrRUFDWiw4REFBQ2hILDBLQUFTQTt3REFBQzBGLFdBQVU7Ozs7OztrRUFDckIsOERBQUNDOztrRUFBTTFFLEVBQUU7Ozs7Ozs7Ozs7OzswREFHWCw4REFBQ3RCLGtEQUFJQTtnREFDSDhGLE1BQUs7Z0RBQ0xDLFdBQVU7O2tFQUVWLDhEQUFDMUYsMEtBQVNBO3dEQUFDMEYsV0FBVTs7Ozs7O2tFQUNyQiw4REFBQ0M7a0dBQWU7a0VBQXdDMUUsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU9sRSw4REFBQ3NFOzBFQUFjOztrREFFYiw4REFBQzlGLGtEQUFNQSxDQUFDOEYsR0FBRzt3Q0FDVG1CLFNBQVM7NENBQUVELFNBQVM7NENBQUdhLEdBQUcsQ0FBQzt3Q0FBRzt3Q0FDOUJWLFNBQVM7NENBQUVILFNBQVM7NENBQUdhLEdBQUc7d0NBQUU7d0NBQzVCVCxZQUFZOzRDQUFFQyxVQUFVOzRDQUFLRyxPQUFPO3dDQUFJO3dDQUN4Q3ZCLFdBQVU7OzBEQUVWLDhEQUFDc0I7MEZBQWE7O2tFQUNaLDhEQUFDN0cseUtBQU9BO3dEQUFDdUYsV0FBVTs7Ozs7O2tFQUNuQiw4REFBQ0M7O2tFQUFNMUUsRUFBRTs7Ozs7Ozs7Ozs7OzBEQUdYLDhEQUFDc0U7MEZBQWM7MERBQ1pqQyxhQUFhTyxHQUFHLENBQUMsQ0FBQ3ZCLE9BQU82RSxzQkFDeEIsOERBQUMxSCxrREFBTUEsQ0FBQzhGLEdBQUc7d0RBRVRtQixTQUFTOzREQUFFRCxTQUFTOzREQUFHRSxHQUFHO3dEQUFHO3dEQUM3QkMsU0FBUzs0REFBRUgsU0FBUzs0REFBR0UsR0FBRzt3REFBRTt3REFDNUJFLFlBQVk7NERBQUVDLFVBQVU7NERBQUtHLE9BQU8sTUFBTUUsUUFBUTt3REFBSTt3REFDdER6QixXQUFVOzswRUFFViw4REFBQ0g7OztrRkFDQyw4REFBQzhCO2tIQUFhO2tGQUE2Qi9FLE1BQU13QixNQUFNOzs7Ozs7a0ZBQ3ZELDhEQUFDaUM7a0hBQVk7a0ZBQXlCekQsTUFBTTBCLElBQUk7Ozs7OztrRkFDaEQsOERBQUMrQjtrSEFBWTs7NEVBQXdCOzRFQUFFekQsTUFBTUUsRUFBRTs7Ozs7Ozs7Ozs7OzswRUFHakQsOERBQUMrQzswR0FBYzswRUFDYiw0RUFBQ0E7OztzRkFDQyw4REFBQ0k7c0hBQWdCLENBQUMsMkNBQTJDLEVBQUV4QixlQUFlN0IsTUFBTUwsTUFBTSxHQUFHO3NGQUMxRndDLGVBQWVuQyxNQUFNTCxNQUFNOzs7Ozs7c0ZBRTlCLDhEQUFDOEQ7c0hBQVk7OzhGQUNYLDhEQUFDeEYsMEtBQUtBO29GQUFDbUYsV0FBVTs7Ozs7OzhGQUNqQiw4REFBQ0M7OzhGQUFNckQsTUFBTTRCLE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt1REFuQnJCNUIsTUFBTUUsRUFBRTs7Ozs7Ozs7OzswREE2Qm5CLDhEQUFDN0Msa0RBQUlBO2dEQUNIOEYsTUFBSztnREFDTEMsV0FBVTs7b0RBRVR6RSxFQUFFO29EQUFZO29EQUFFQSxFQUFFOzs7Ozs7Ozs7Ozs7O29DQUt0QkwsTUFBTXdCLFNBQVMseUJBQ2QsOERBQUMzQyxrREFBTUEsQ0FBQzhGLEdBQUc7d0NBQ1RtQixTQUFTOzRDQUFFRCxTQUFTOzRDQUFHYSxHQUFHO3dDQUFHO3dDQUM3QlYsU0FBUzs0Q0FBRUgsU0FBUzs0Q0FBR2EsR0FBRzt3Q0FBRTt3Q0FDNUJULFlBQVk7NENBQUVDLFVBQVU7NENBQUtHLE9BQU87d0NBQUk7d0NBQ3hDdkIsV0FBVTs7MERBRVYsOERBQUNzQjswRkFBYTs7a0VBQ1osOERBQUM1RywwS0FBUUE7d0RBQUNzRixXQUFVOzs7Ozs7a0VBQ3BCLDhEQUFDQzs7a0VBQU0xRSxFQUFFOzs7Ozs7Ozs7Ozs7MERBR1gsOERBQUNzRTswRkFBYzs7a0VBQ2IsOERBQUM1RixrREFBSUE7d0RBQ0g4RixNQUFLO3dEQUNMQyxXQUFVOzswRUFFViw4REFBQ2pGLDBLQUFJQTtnRUFBQ2lGLFdBQVU7Ozs7OzswRUFDaEIsOERBQUNDOzBHQUFlOzBFQUFxQzFFLEVBQUU7Ozs7Ozs7Ozs7OztrRUFHekQsOERBQUN0QixrREFBSUE7d0RBQ0g4RixNQUFLO3dEQUNMQyxXQUFVOzswRUFFViw4REFBQ3pGLDBLQUFLQTtnRUFBQ3lGLFdBQVU7Ozs7OzswRUFDakIsOERBQUNDOzBHQUFlOzBFQUFxQzFFLEVBQUU7Ozs7Ozs7Ozs7OztrRUFHekQsOERBQUN0QixrREFBSUE7d0RBQ0g4RixNQUFLO3dEQUNMQyxXQUFVOzswRUFFViw4REFBQzFGLDBLQUFTQTtnRUFBQzBGLFdBQVU7Ozs7OzswRUFDckIsOERBQUNDOzBHQUFlOzBFQUFzQzFFLEVBQUU7Ozs7Ozs7Ozs7OztrRUFHMUQsOERBQUN0QixrREFBSUE7d0RBQ0g4RixNQUFLO3dEQUNMQyxXQUFVOzswRUFFViw4REFBQ3hGLDBLQUFRQTtnRUFBQ3dGLFdBQVU7Ozs7OzswRUFDcEIsOERBQUNDOzBHQUFlOzBFQUF1QzFFLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFJN0QsOERBQUNzRTswRkFBYzs7a0VBQ2IsOERBQUM4QjtrR0FBYTtrRUFBa0NwRyxFQUFFOzs7Ozs7a0VBQ2xELDhEQUFDOEU7a0dBQVk7OzREQUNWOUUsRUFBRTs0REFBWTs0REFBRVE7NERBQWtCOzREQUFFUixFQUFFOzREQUErQjs0REFBRUEsRUFBRTs0REFBTzs0REFBRU8sVUFBVTBCLFlBQVk7NERBQUM7NERBQUVqQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVaEk7QUFFZSxTQUFTc0c7SUFDdEIscUJBQ0UsOERBQUN4SCxrRUFBY0E7a0JBQ2IsNEVBQUNZOzs7Ozs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca2hhbGVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcWUFTTUlOIEFMU0hBTVxceWFzbWluLWFsc2hhbVxcc3JjXFxhcHBcXGRhc2hib2FyZFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXHJcblxyXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXHJcbmltcG9ydCB7IG1vdGlvbiB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nXHJcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcclxuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xyXG5pbXBvcnQgeyB1c2VBdXRoU3RvcmUgfSBmcm9tICdAL3N0b3JlL2F1dGhTdG9yZSdcclxuaW1wb3J0IHsgdXNlRGF0YVN0b3JlIH0gZnJvbSAnQC9zdG9yZS9kYXRhU3RvcmUnXHJcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSAnQC9ob29rcy91c2VUcmFuc2xhdGlvbidcclxuaW1wb3J0IFByb3RlY3RlZFJvdXRlIGZyb20gJ0AvY29tcG9uZW50cy9Qcm90ZWN0ZWRSb3V0ZSdcclxuaW1wb3J0IHtcclxuICBCYXJDaGFydDMsXHJcbiAgVXNlcnMsXHJcbiAgQ2FsZW5kYXIsXHJcbiAgUGFja2FnZSxcclxuICBTZXR0aW5ncyxcclxuICBMb2dPdXQsXHJcbiAgVHJlbmRpbmdVcCxcclxuICBDbG9jayxcclxuICBDaGVja0NpcmNsZSxcclxuICBQbHVzLFxyXG4gIEFycm93UmlnaHQsXHJcbiAgTGFuZ3VhZ2VzXHJcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xyXG5cclxuZnVuY3Rpb24gRGFzaGJvYXJkQ29udGVudCgpIHtcclxuICBjb25zdCB7IHVzZXIsIHNpZ25PdXQgfSA9IHVzZUF1dGhTdG9yZSgpXHJcbiAgY29uc3QgeyBvcmRlcnMsIGFwcG9pbnRtZW50cywgZ2V0U3RhdHMgfSA9IHVzZURhdGFTdG9yZSgpXHJcbiAgY29uc3QgeyB0LCBsYW5ndWFnZSwgY2hhbmdlTGFuZ3VhZ2UsIGlzQXJhYmljIH0gPSB1c2VUcmFuc2xhdGlvbigpXHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcclxuXHJcblxyXG5cclxuICBjb25zdCBoYW5kbGVTaWduT3V0ID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgYXdhaXQgc2lnbk91dCgpXHJcbiAgICByb3V0ZXIucHVzaCgnLycpXHJcbiAgfVxyXG5cclxuXHJcblxyXG4gIC8vINit2LPYp9ioINin2YTYpdit2LXYp9im2YrYp9iqINin2YTYrdmC2YrZgtmK2KlcclxuICBjb25zdCByZWFsU3RhdHMgPSBnZXRTdGF0cygpXHJcblxyXG4gIC8vINit2LPYp9ioINin2YTZhdmI2KfYudmK2K8g2KfZhNmK2YjZhVxyXG4gIGNvbnN0IHRvZGF5QXBwb2ludG1lbnRzID0gYXBwb2ludG1lbnRzLmZpbHRlcihhcHBvaW50bWVudCA9PiB7XHJcbiAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdXHJcbiAgICByZXR1cm4gYXBwb2ludG1lbnQuYXBwb2ludG1lbnREYXRlID09PSB0b2RheSAmJiBhcHBvaW50bWVudC5zdGF0dXMgIT09ICdjYW5jZWxsZWQnXHJcbiAgfSkubGVuZ3RoXHJcblxyXG4gIC8vINin2YTYpdit2LXYp9im2YrYp9iqINit2LPYqCDYp9mE2K/ZiNixXHJcbiAgY29uc3QgZ2V0U3RhdHNGb3JSb2xlID0gKCkgPT4ge1xyXG4gICAgaWYgKHVzZXI/LnJvbGUgPT09ICd3b3JrZXInKSB7XHJcbiAgICAgIC8vINil2K3Ytdin2KbZitin2Kog2KfZhNi52KfZhdmEIC0g2LfZhNio2KfYqtmHINmB2YLYt1xyXG4gICAgICBjb25zdCB3b3JrZXJPcmRlcnMgPSBvcmRlcnMuZmlsdGVyKG9yZGVyID0+IG9yZGVyLmFzc2lnbmVkV29ya2VyID09PSB1c2VyPy5pZClcclxuICAgICAgY29uc3Qgd29ya2VyQ29tcGxldGVkT3JkZXJzID0gd29ya2VyT3JkZXJzLmZpbHRlcihvcmRlciA9PiBvcmRlci5zdGF0dXMgPT09ICdjb21wbGV0ZWQnKVxyXG4gICAgICBjb25zdCB3b3JrZXJBY3RpdmVPcmRlcnMgPSB3b3JrZXJPcmRlcnMuZmlsdGVyKG9yZGVyID0+IFsncGVuZGluZycsICdpbl9wcm9ncmVzcyddLmluY2x1ZGVzKG9yZGVyLnN0YXR1cykpXHJcblxyXG4gICAgICByZXR1cm4gW1xyXG4gICAgICAgIHtcclxuICAgICAgICAgIHRpdGxlOiB0KCdteV9hY3RpdmVfb3JkZXJzJyksXHJcbiAgICAgICAgICB2YWx1ZTogd29ya2VyQWN0aXZlT3JkZXJzLmxlbmd0aC50b1N0cmluZygpLFxyXG4gICAgICAgICAgY2hhbmdlOiAnKzAlJyxcclxuICAgICAgICAgIGljb246IFBhY2thZ2UsXHJcbiAgICAgICAgICBjb2xvcjogJ2Zyb20tYmx1ZS00MDAgdG8tYmx1ZS02MDAnXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICB0aXRsZTogdCgnbXlfY29tcGxldGVkX29yZGVycycpLFxyXG4gICAgICAgICAgdmFsdWU6IHdvcmtlckNvbXBsZXRlZE9yZGVycy5sZW5ndGgudG9TdHJpbmcoKSxcclxuICAgICAgICAgIGNoYW5nZTogJyswJScsXHJcbiAgICAgICAgICBpY29uOiBDaGVja0NpcmNsZSxcclxuICAgICAgICAgIGNvbG9yOiAnZnJvbS1ncmVlbi00MDAgdG8tZ3JlZW4tNjAwJ1xyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgdGl0bGU6IHQoJ215X3RvdGFsX29yZGVycycpLFxyXG4gICAgICAgICAgdmFsdWU6IHdvcmtlck9yZGVycy5sZW5ndGgudG9TdHJpbmcoKSxcclxuICAgICAgICAgIGNoYW5nZTogJyswJScsXHJcbiAgICAgICAgICBpY29uOiBVc2VycyxcclxuICAgICAgICAgIGNvbG9yOiAnZnJvbS1wdXJwbGUtNDAwIHRvLXB1cnBsZS02MDAnXHJcbiAgICAgICAgfVxyXG4gICAgICBdXHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAvLyDYpdit2LXYp9im2YrYp9iqINin2YTZhdiv2YrYsSAtINis2YXZiti5INin2YTYqNmK2KfZhtin2KpcclxuICAgICAgcmV0dXJuIFtcclxuICAgICAgICB7XHJcbiAgICAgICAgICB0aXRsZTogdCgnYWN0aXZlX29yZGVycycpLFxyXG4gICAgICAgICAgdmFsdWU6IHJlYWxTdGF0cy5hY3RpdmVPcmRlcnMudG9TdHJpbmcoKSxcclxuICAgICAgICAgIGNoYW5nZTogJyswJScsXHJcbiAgICAgICAgICBpY29uOiBQYWNrYWdlLFxyXG4gICAgICAgICAgY29sb3I6ICdmcm9tLWJsdWUtNDAwIHRvLWJsdWUtNjAwJ1xyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgdGl0bGU6IHQoJ3RvZGF5X2FwcG9pbnRtZW50cycpLFxyXG4gICAgICAgICAgdmFsdWU6IHRvZGF5QXBwb2ludG1lbnRzLnRvU3RyaW5nKCksXHJcbiAgICAgICAgICBjaGFuZ2U6ICcrMCcsXHJcbiAgICAgICAgICBpY29uOiBDYWxlbmRhcixcclxuICAgICAgICAgIGNvbG9yOiAnZnJvbS1ncmVlbi00MDAgdG8tZ3JlZW4tNjAwJ1xyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgdGl0bGU6IHQoJ2NvbXBsZXRlZF9vcmRlcnMnKSxcclxuICAgICAgICAgIHZhbHVlOiByZWFsU3RhdHMuY29tcGxldGVkT3JkZXJzLnRvU3RyaW5nKCksXHJcbiAgICAgICAgICBjaGFuZ2U6ICcrMCUnLFxyXG4gICAgICAgICAgaWNvbjogQ2hlY2tDaXJjbGUsXHJcbiAgICAgICAgICBjb2xvcjogJ2Zyb20tcHVycGxlLTQwMCB0by1wdXJwbGUtNjAwJ1xyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgdGl0bGU6IHQoJ3RvdGFsX29yZGVycycpLFxyXG4gICAgICAgICAgdmFsdWU6IHJlYWxTdGF0cy50b3RhbE9yZGVycy50b1N0cmluZygpLFxyXG4gICAgICAgICAgY2hhbmdlOiAnKzAlJyxcclxuICAgICAgICAgIGljb246IFVzZXJzLFxyXG4gICAgICAgICAgY29sb3I6ICdmcm9tLXBpbmstNDAwIHRvLXBpbmstNjAwJ1xyXG4gICAgICAgIH1cclxuICAgICAgXVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgY29uc3Qgc3RhdHMgPSBnZXRTdGF0c0ZvclJvbGUoKVxyXG5cclxuICAvLyDYo9it2K/YqyDYp9mE2LfZhNio2KfYqiAo2KLYrtixIDMg2LfZhNio2KfYqikgLSDZhdmB2YTYqtix2Kkg2K3Ys9ioINin2YTYr9mI2LFcclxuICBjb25zdCByZWNlbnRPcmRlcnMgPSBvcmRlcnNcclxuICAgIC5maWx0ZXIob3JkZXIgPT4ge1xyXG4gICAgICAvLyDYpdiw2Kcg2YPYp9mGINin2YTZhdiz2KrYrtiv2YUg2LnYp9mF2YTYjCDYp9i52LHYtiDYt9mE2KjYp9iq2Ycg2YHZgti3XHJcbiAgICAgIGlmICh1c2VyPy5yb2xlID09PSAnd29ya2VyJykge1xyXG4gICAgICAgIHJldHVybiBvcmRlci5hc3NpZ25lZFdvcmtlciA9PT0gdXNlcj8uaWRcclxuICAgICAgfVxyXG4gICAgICAvLyDYpdiw2Kcg2YPYp9mGINmF2K/Zitix2Iwg2KfYudix2LYg2KzZhdmK2Lkg2KfZhNi32YTYqNin2KpcclxuICAgICAgcmV0dXJuIHRydWVcclxuICAgIH0pXHJcbiAgICAuc29ydCgoYSwgYikgPT4gbmV3IERhdGUoYi5jcmVhdGVkQXQpLmdldFRpbWUoKSAtIG5ldyBEYXRlKGEuY3JlYXRlZEF0KS5nZXRUaW1lKCkpXHJcbiAgICAuc2xpY2UoMCwgMylcclxuICAgIC5tYXAob3JkZXIgPT4gKHtcclxuICAgICAgaWQ6IG9yZGVyLmlkLFxyXG4gICAgICBjbGllbnQ6IG9yZGVyLmNsaWVudE5hbWUsXHJcbiAgICAgIHR5cGU6IG9yZGVyLmRlc2NyaXB0aW9uLFxyXG4gICAgICBzdGF0dXM6IG9yZGVyLnN0YXR1cyxcclxuICAgICAgZHVlRGF0ZTogb3JkZXIuZHVlRGF0ZVxyXG4gICAgfSkpXHJcblxyXG4gIGNvbnN0IGdldFN0YXR1c0NvbG9yID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zdCBjb2xvcnMgPSB7XHJcbiAgICAgIHBlbmRpbmc6ICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCcsXHJcbiAgICAgIGluX3Byb2dyZXNzOiAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCcsXHJcbiAgICAgIGNvbXBsZXRlZDogJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCcsXHJcbiAgICAgIGRlbGl2ZXJlZDogJ2JnLXB1cnBsZS0xMDAgdGV4dC1wdXJwbGUtODAwJ1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIGNvbG9yc1tzdGF0dXMgYXMga2V5b2YgdHlwZW9mIGNvbG9yc10gfHwgJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnXHJcbiAgfVxyXG5cclxuICBjb25zdCBnZXRTdGF0dXNMYWJlbCA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xyXG4gICAgY29uc3QgbGFiZWxzID0ge1xyXG4gICAgICBwZW5kaW5nOiB0KCdwZW5kaW5nJyksXHJcbiAgICAgIGluX3Byb2dyZXNzOiB0KCdpbl9wcm9ncmVzcycpLFxyXG4gICAgICBjb21wbGV0ZWQ6IHQoJ2NvbXBsZXRlZCcpLFxyXG4gICAgICBkZWxpdmVyZWQ6IHQoJ2RlbGl2ZXJlZCcpXHJcbiAgICB9XHJcbiAgICByZXR1cm4gbGFiZWxzW3N0YXR1cyBhcyBrZXlvZiB0eXBlb2YgbGFiZWxzXSB8fCBzdGF0dXNcclxuICB9XHJcblxyXG4gIGNvbnN0IGZvcm1hdERhdGUgPSAoZGF0ZVN0cmluZzogc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoZGF0ZVN0cmluZylcclxuICAgIHJldHVybiBkYXRlLnRvTG9jYWxlRGF0ZVN0cmluZygnYXItU0EnLCB7XHJcbiAgICAgIHllYXI6ICdudW1lcmljJyxcclxuICAgICAgbW9udGg6ICdzaG9ydCcsXHJcbiAgICAgIGRheTogJ251bWVyaWMnXHJcbiAgICB9KVxyXG4gIH1cclxuXHJcbiAgY29uc3QgZ2V0U3RhdHVzSW5mbyA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xyXG4gICAgY29uc3Qgc3RhdHVzTWFwID0ge1xyXG4gICAgICBwZW5kaW5nOiB7IGxhYmVsOiB0KCdwZW5kaW5nJyksIGNvbG9yOiAndGV4dC15ZWxsb3ctNjAwJywgYmdDb2xvcjogJ2JnLXllbGxvdy0xMDAnIH0sXHJcbiAgICAgIGluX3Byb2dyZXNzOiB7IGxhYmVsOiB0KCdpbl9wcm9ncmVzcycpLCBjb2xvcjogJ3RleHQtYmx1ZS02MDAnLCBiZ0NvbG9yOiAnYmctYmx1ZS0xMDAnIH0sXHJcbiAgICAgIGNvbXBsZXRlZDogeyBsYWJlbDogdCgnY29tcGxldGVkJyksIGNvbG9yOiAndGV4dC1ncmVlbi02MDAnLCBiZ0NvbG9yOiAnYmctZ3JlZW4tMTAwJyB9LFxyXG4gICAgICBkZWxpdmVyZWQ6IHsgbGFiZWw6IHQoJ2RlbGl2ZXJlZCcpLCBjb2xvcjogJ3RleHQtcHVycGxlLTYwMCcsIGJnQ29sb3I6ICdiZy1wdXJwbGUtMTAwJyB9LFxyXG4gICAgICBjYW5jZWxsZWQ6IHsgbGFiZWw6IHQoJ2NhbmNlbGxlZCcpLCBjb2xvcjogJ3RleHQtcmVkLTYwMCcsIGJnQ29sb3I6ICdiZy1yZWQtMTAwJyB9XHJcbiAgICB9XHJcbiAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1cyBhcyBrZXlvZiB0eXBlb2Ygc3RhdHVzTWFwXSB8fCBzdGF0dXNNYXAucGVuZGluZ1xyXG4gIH1cclxuXHJcblxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPD5cclxuICAgICAgPHN0eWxlIGpzeCBnbG9iYWw+e2BcclxuICAgICAgICAvKiDYttmF2KfZhiDYuNmH2YjYsSDYstixINiq2LrZitmK2LEg2KfZhNmE2LrYqSDZgdmKINis2YXZiti5INin2YTYo9mI2LbYp9i5ICovXHJcbiAgICAgICAgYnV0dG9uW3RpdGxlKj1cItiq2LrZitmK2LEg2KfZhNmE2LrYqVwiXSxcclxuICAgICAgICBidXR0b25bdGl0bGUqPVwiQ2hhbmdlIExhbmd1YWdlXCJdIHtcclxuICAgICAgICAgIGRpc3BsYXk6IGJsb2NrICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICB2aXNpYmlsaXR5OiB2aXNpYmxlICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBvcGFjaXR5OiAxICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmUgIWltcG9ydGFudDtcclxuICAgICAgICAgIHotaW5kZXg6IDk5OTk5ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBmbGV4LXNocmluazogMCAhaW1wb3J0YW50O1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLyog2YTZhNi02KfYtNin2Kog2KfZhNmD2KjZitix2Kkg2KzYr9in2YsgKi9cclxuICAgICAgICBAbWVkaWEgKG1pbi13aWR0aDogMTkyMHB4KSB7XHJcbiAgICAgICAgICBidXR0b25bdGl0bGUqPVwi2KrYutmK2YrYsSDYp9mE2YTYutipXCJdLFxyXG4gICAgICAgICAgYnV0dG9uW3RpdGxlKj1cIkNoYW5nZSBMYW5ndWFnZVwiXSB7XHJcbiAgICAgICAgICAgIGRpc3BsYXk6IGJsb2NrICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgIHZpc2liaWxpdHk6IHZpc2libGUgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgb3BhY2l0eTogMSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLyog2YTZhNi02KfYtNin2KogNEsg2YjZhdinINmB2YjZgiAqL1xyXG4gICAgICAgIEBtZWRpYSAobWluLXdpZHRoOiAyNTYwcHgpIHtcclxuICAgICAgICAgIGJ1dHRvblt0aXRsZSo9XCLYqti62YrZitixINin2YTZhNi62KlcIl0sXHJcbiAgICAgICAgICBidXR0b25bdGl0bGUqPVwiQ2hhbmdlIExhbmd1YWdlXCJdIHtcclxuICAgICAgICAgICAgZGlzcGxheTogYmxvY2sgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgdmlzaWJpbGl0eTogdmlzaWJsZSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICBvcGFjaXR5OiAxICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvKiDZiNi22Lkg2KfZhNi02KfYtNipINin2YTZg9in2YXZhNipICovXHJcbiAgICAgICAgQG1lZGlhIChkaXNwbGF5LW1vZGU6IGZ1bGxzY3JlZW4pIHtcclxuICAgICAgICAgIGJ1dHRvblt0aXRsZSo9XCLYqti62YrZitixINin2YTZhNi62KlcIl0sXHJcbiAgICAgICAgICBidXR0b25bdGl0bGUqPVwiQ2hhbmdlIExhbmd1YWdlXCJdIHtcclxuICAgICAgICAgICAgZGlzcGxheTogYmxvY2sgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgdmlzaWJpbGl0eTogdmlzaWJsZSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICBvcGFjaXR5OiAxICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvKiDZhNmE2KrYo9mD2K8g2YXZhiDYudiv2YUg2KXYrtmB2KfYoSDYp9mE2LLYsSDYqNmI2KfYs9i32Kkgb3ZlcmZsb3cgKi9cclxuICAgICAgICAubGFuZ3VhZ2UtYnRuLWNvbnRhaW5lciB7XHJcbiAgICAgICAgICBvdmVyZmxvdzogdmlzaWJsZSAhaW1wb3J0YW50O1xyXG4gICAgICAgIH1cclxuICAgICAgYH08L3N0eWxlPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLXJvc2UtNTAgdmlhLXBpbmstNTAgdG8tcHVycGxlLTUwXCI+XHJcbiAgICAgICAgey8qINin2YTZh9mK2K/YsSDYp9mE2YXYrdiz2YYgKi99XHJcbiAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiYmctd2hpdGUvODAgYmFja2Ryb3AtYmx1ci1tZCBib3JkZXItYiBib3JkZXItcGluay0xMDAgc2hhZG93LXNtXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbWF4LXctN3hsIG14LWF1dG8gcHgtMyBzbTpweC00IG1kOnB4LTYgbGc6cHgtOFwiPlxyXG4gICAgICAgICAgey8qINmH2YrYr9ixINmE2YTYtNin2LTYp9iqINin2YTZg9io2YrYsdipICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbGc6ZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGgtMTZcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTQgc3BhY2UteC1yZXZlcnNlXCI+XHJcbiAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgIGhyZWY9XCIvXCJcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcGluay02MDAgaG92ZXI6dGV4dC1waW5rLTcwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDAgZ3JvdXAgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHNwYWNlLXgtcmV2ZXJzZVwiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPEFycm93UmlnaHQgY2xhc3NOYW1lPVwidy01IGgtNSBncm91cC1ob3Zlcjp0cmFuc2xhdGUteC0xIHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+e3QoJ2hvbWVwYWdlJyl9PC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctcHggaC02IGJnLWdyYXktMzAwXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy1tZFwiPlxyXG4gICAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQteGwgeGw6dGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS04MDAgdHJ1bmNhdGVcIj5cclxuICAgICAgICAgICAgICAgICAge3QoJ3dlbGNvbWVfYmFjaycpfdiMIHt1c2VyPy5mdWxsX25hbWUgfHwgdXNlcj8uZW1haWx9XHJcbiAgICAgICAgICAgICAgICA8L2gxPlxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAgICAgIHt1c2VyPy5yb2xlID09PSAnYWRtaW4nID8gdCgnYWRtaW5fZGFzaGJvYXJkJykgOiB0KCd3b3JrZXJfZGFzaGJvYXJkJyl9XHJcbiAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwicHgtMyBweS0xIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1waW5rLTEwMCB0by1yb3NlLTEwMCB0ZXh0LXBpbmstNzAwIHJvdW5kZWQtZnVsbCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHdoaXRlc3BhY2Utbm93cmFwXCI+XHJcbiAgICAgICAgICAgICAgICB7dXNlcj8ucm9sZSA9PT0gJ2FkbWluJyA/IHQoJ2FkbWluJykgOiB0KCd3b3JrZXInKX1cclxuICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTQgc3BhY2UteC1yZXZlcnNlIGxhbmd1YWdlLWJ0bi1jb250YWluZXJcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBzcGFjZS14LXJldmVyc2UgbGFuZ3VhZ2UtYnRuLWNvbnRhaW5lclwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0IG1heC13LXhzXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS04MDAgdHJ1bmNhdGVcIj57dXNlcj8uZnVsbF9uYW1lfTwvcD5cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIHRydW5jYXRlXCI+e3VzZXI/LmVtYWlsfTwvcD5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgIHsvKiDYstixINiq2LrZitmK2LEg2KfZhNmE2LrYqSDZhNmE2LTYp9i02KfYqiDYp9mE2YPYqNmK2LHYqSAqL31cclxuICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gY2hhbmdlTGFuZ3VhZ2UobGFuZ3VhZ2UgPT09ICdhcicgPyAnZW4nIDogJ2FyJyl9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMS41IHRleHQtc20gdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LXBpbmstNjAwIGJnLWdyYXktMTAwIGhvdmVyOmJnLXBpbmstNTAgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBmb250LW1lZGl1bSBtaW4tdy1bNzBweF0gdGV4dC1jZW50ZXJcIlxyXG4gICAgICAgICAgICAgICAgICB0aXRsZT17dCgnY2hhbmdlX2xhbmd1YWdlJyl9XHJcbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogJ2Jsb2NrJyxcclxuICAgICAgICAgICAgICAgICAgICB2aXNpYmlsaXR5OiAndmlzaWJsZScsXHJcbiAgICAgICAgICAgICAgICAgICAgcG9zaXRpb246ICdyZWxhdGl2ZScsXHJcbiAgICAgICAgICAgICAgICAgICAgekluZGV4OiA5OTk5OSxcclxuICAgICAgICAgICAgICAgICAgICBtaW5XaWR0aDogJzcwcHgnLFxyXG4gICAgICAgICAgICAgICAgICAgIGZsZXhTaHJpbms6IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgb3BhY2l0eTogMVxyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICB7bGFuZ3VhZ2UgPT09ICdhcicgPyAnRW5nbGlzaCcgOiAn2LnYsdio2YonfVxyXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XHJcblxyXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTaWduT3V0fVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LXJlZC02MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwXCJcclxuICAgICAgICAgICAgICAgICAgdGl0bGU9e3QoJ2xvZ291dCcpfVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8TG9nT3V0IGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgey8qINmH2YrYr9ixINmE2YTYtNin2LTYp9iqINin2YTYtdi62YrYsdipINmI2KfZhNmF2KrZiNiz2LfYqSAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6aGlkZGVuXCI+XHJcbiAgICAgICAgICAgIHsvKiDYp9mE2LXZgSDYp9mE2KPZiNmEICovfVxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBoLTE0IHNtOmgtMTZcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBzcGFjZS14LXJldmVyc2UgbWluLXctMCBmbGV4LTEgb3ZlcmZsb3ctaGlkZGVuXCI+XHJcbiAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICBocmVmPVwiL1wiXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcGluay02MDAgaG92ZXI6dGV4dC1waW5rLTcwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDAgZ3JvdXAgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xIHNwYWNlLXgtcmV2ZXJzZSBmbGV4LXNocmluay0wXCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPEFycm93UmlnaHQgY2xhc3NOYW1lPVwidy00IGgtNCBzbTp3LTUgc206aC01IGdyb3VwLWhvdmVyOnRyYW5zbGF0ZS14LTEgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBzbTp0ZXh0LXNtIGZvbnQtbWVkaXVtIGhpZGRlbiBzbTppbmxpbmUgd2hpdGVzcGFjZS1ub3dyYXBcIj57dCgnaG9tZXBhZ2UnKX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gc206aGlkZGVuIHdoaXRlc3BhY2Utbm93cmFwXCI+e3QoJ2hvbWUnKX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctcHggaC00IHNtOmgtNiBiZy1ncmF5LTMwMCBmbGV4LXNocmluay0wXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi13LTAgZmxleC0xIG92ZXJmbG93LWhpZGRlblwiPlxyXG4gICAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC1zbSBzbTp0ZXh0LWxnIG1kOnRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JheS04MDAgdHJ1bmNhdGVcIj5cclxuICAgICAgICAgICAgICAgICAgICB7dCgnd2VsY29tZV9iYWNrJyl92Iwge3VzZXI/LmZ1bGxfbmFtZSB8fCB1c2VyPy5lbWFpbH1cclxuICAgICAgICAgICAgICAgICAgPC9oMT5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSBzbTpzcGFjZS14LTIgc3BhY2UteC1yZXZlcnNlIGZsZXgtc2hyaW5rLTAgbWluLXctMCBsYW5ndWFnZS1idG4tY29udGFpbmVyXCI+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJweC0yIHNtOnB4LTMgcHktMSBiZy1ncmFkaWVudC10by1yIGZyb20tcGluay0xMDAgdG8tcm9zZS0xMDAgdGV4dC1waW5rLTcwMCByb3VuZGVkLWZ1bGwgdGV4dC14cyBzbTp0ZXh0LXNtIGZvbnQtbWVkaXVtIHdoaXRlc3BhY2Utbm93cmFwXCI+XHJcbiAgICAgICAgICAgICAgICAgIHt0KHVzZXI/LnJvbGUgPT09ICdhZG1pbicgPyAnYWRtaW4nIDogJ3dvcmtlcicpfVxyXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBjaGFuZ2VMYW5ndWFnZShsYW5ndWFnZSA9PT0gJ2FyJyA/ICdlbicgOiAnYXInKX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMiBzbTpweC0zIHB5LTEgdGV4dC14cyBzbTp0ZXh0LXNtIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1waW5rLTYwMCBiZy1ncmF5LTEwMCBob3ZlcjpiZy1waW5rLTUwIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZmxleC1zaHJpbmstMCBmb250LW1lZGl1bSBtaW4tdy1bNjBweF0gc206bWluLXctWzcwcHhdIHRleHQtY2VudGVyXCJcclxuICAgICAgICAgICAgICAgICAgdGl0bGU9e3QoJ2NoYW5nZV9sYW5ndWFnZScpfVxyXG4gICAgICAgICAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdibG9jaycsXHJcbiAgICAgICAgICAgICAgICAgICAgdmlzaWJpbGl0eTogJ3Zpc2libGUnLFxyXG4gICAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiAncmVsYXRpdmUnLFxyXG4gICAgICAgICAgICAgICAgICAgIHpJbmRleDogOTk5OTksXHJcbiAgICAgICAgICAgICAgICAgICAgbWluV2lkdGg6ICc2MHB4JyxcclxuICAgICAgICAgICAgICAgICAgICBmbGV4U2hyaW5rOiAwLFxyXG4gICAgICAgICAgICAgICAgICAgIG9wYWNpdHk6IDFcclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAge2xhbmd1YWdlID09PSAnYXInID8gJ0VuZ2xpc2gnIDogJ9i52LHYqNmKJ31cclxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG5cclxuICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU2lnbk91dH1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0xLjUgc206cC0yIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1yZWQtNjAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMCBmbGV4LXNocmluay0wXCJcclxuICAgICAgICAgICAgICAgICAgdGl0bGU9e3QoJ2xvZ291dCcpfVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8TG9nT3V0IGNsYXNzTmFtZT1cInctNCBoLTQgc206dy01IHNtOmgtNVwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICB7Lyog2KfZhNi12YEg2KfZhNir2KfZhtmKIC0g2YXYudmE2YjZhdin2Kog2KfZhNmF2LPYqtiu2K/ZhSAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwYi0zIHNtOnBiLTQgYm9yZGVyLXQgYm9yZGVyLWdyYXktMTAwXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwdC0yIHNtOnB0LTNcIj5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgc206dGV4dC1zbSB0ZXh0LWdyYXktNjAwIG1iLTFcIj5cclxuICAgICAgICAgICAgICAgICAge3QoJ2Rhc2hib2FyZCcpfSAtIHt0KHVzZXI/LnJvbGUgPT09ICdhZG1pbicgPyAnYWRtaW4nIDogJ3dvcmtlcicpfVxyXG4gICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4tdy0wIGZsZXgtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS04MDAgdGV4dC1zbSBzbTp0ZXh0LWJhc2UgdHJ1bmNhdGVcIj57dXNlcj8uZnVsbF9uYW1lfTwvcD5cclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHNtOnRleHQtc20gdGV4dC1ncmF5LTYwMCB0cnVuY2F0ZVwiPnt1c2VyPy5lbWFpbH08L3A+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvaGVhZGVyPlxyXG5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbWF4LXctN3hsIG14LWF1dG8gcHgtMyBzbTpweC00IG1kOnB4LTYgbGc6cHgtOCBweS00IHNtOnB5LTYgbGc6cHktOFwiPlxyXG4gICAgICAgIHsvKiDYp9mE2KrYsdit2YrYqCDZiNij2LLYsdin2LEg2KfZhNi52YXZhCDYp9mE2YXYrdiz2YYgKi99XHJcbiAgICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cclxuICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxyXG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC42IH19XHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJtYi02IHNtOm1iLThcIlxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBnYXAtNCBzbTpnYXAtNlwiPlxyXG4gICAgICAgICAgICB7Lyog2YLYs9mFINin2YTYqtix2K3ZitioICovfVxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHNtOnRleHQtcmlnaHQgb3ZlcmZsb3ctaGlkZGVuXCI+XHJcbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgc206dGV4dC0yeGwgbWQ6dGV4dC0zeGwgbGc6dGV4dC00eGwgZm9udC1ib2xkIHRleHQtZ3JheS04MDAgbWItMiBzbTptYi0zIGJyZWFrLXdvcmRzXCI+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJibG9jayBzbTpoaWRkZW5cIj57dCgnd2VsY29tZV9iYWNrJyl9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaGlkZGVuIHNtOmlubGluZVwiPnt0KCd3ZWxjb21lX2JhY2snKX3YjCA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXBpbmstNjAwIGJyZWFrLXdvcmRzXCI+e3VzZXI/LmZ1bGxfbmFtZX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgPC9oMj5cclxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHNtOnRleHQtYmFzZSBtZDp0ZXh0LWxnIHRleHQtZ3JheS02MDAgbWF4LXctMnhsIG14LWF1dG8gc206bXgtMCBicmVhay13b3Jkc1wiPlxyXG4gICAgICAgICAgICAgICAge3QoJ292ZXJ2aWV3X3RvZGF5Jyl9XHJcbiAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIHsvKiDYo9iy2LHYp9ixINin2YTYudmF2YQg2YTZhNmF2K/ZitixICovfVxyXG4gICAgICAgICAgICB7dXNlcj8ucm9sZSA9PT0gJ2FkbWluJyAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGdhcC0zIHNtOmdhcC00IGp1c3RpZnktY2VudGVyIHNtOmp1c3RpZnktc3RhcnQgdy1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICBocmVmPVwiL2Rhc2hib2FyZC9hZGQtb3JkZXJcIlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tcHJpbWFyeSBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC0yIHNwYWNlLXgtcmV2ZXJzZSBweC00IHNtOnB4LTYgcHktMyBzbTpweS00IGdyb3VwIHRleHQtc20gc206dGV4dC1iYXNlIHctZnVsbCBzbTp3LWF1dG8gbWluLXctMCBmbGV4LXNocmluay0wXCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwidy00IGgtNCBzbTp3LTUgc206aC01IGdyb3VwLWhvdmVyOnNjYWxlLTExMCB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDAgZmxleC1zaHJpbmstMFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIndoaXRlc3BhY2Utbm93cmFwXCI+e3QoJ2FkZF9uZXdfb3JkZXInKX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L0xpbms+XHJcblxyXG4gICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgaHJlZj1cIi9ib29rLWFwcG9pbnRtZW50XCJcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLXNlY29uZGFyeSBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC0yIHNwYWNlLXgtcmV2ZXJzZSBweC00IHNtOnB4LTYgcHktMyBzbTpweS00IGdyb3VwIHRleHQtc20gc206dGV4dC1iYXNlIHctZnVsbCBzbTp3LWF1dG8gbWluLXctMCBmbGV4LXNocmluay0wXCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cInctNCBoLTQgc206dy01IHNtOmgtNSBncm91cC1ob3ZlcjpzY2FsZS0xMTAgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwIGZsZXgtc2hyaW5rLTBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ3aGl0ZXNwYWNlLW5vd3JhcFwiPnt0KCdib29rX2FwcG9pbnRtZW50Jyl9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgey8qINix2LPYp9mE2Kkg2KrYsdit2YrYqCDZhNmE2LnYp9mF2YQgKi99XHJcbiAgICAgICAgICAgIHt1c2VyPy5yb2xlID09PSAnd29ya2VyJyAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MCB0by1pbmRpZ28tNTAgYm9yZGVyIGJvcmRlci1ibHVlLTIwMCByb3VuZGVkLWxnIHNtOnJvdW5kZWQteGwgcC00IHNtOnAtNiB0ZXh0LWNlbnRlciBzbTp0ZXh0LXJpZ2h0IG92ZXJmbG93LWhpZGRlblwiPlxyXG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgc206dGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtYmx1ZS04MDAgbWItMiBicmVhay13b3Jkc1wiPlxyXG4gICAgICAgICAgICAgICAgICB7dCgnd2VsY29tZV93b3JrZXInKX1cclxuICAgICAgICAgICAgICAgIDwvaDM+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHNtOnRleHQtYmFzZSB0ZXh0LWJsdWUtNjAwIGJyZWFrLXdvcmRzXCI+XHJcbiAgICAgICAgICAgICAgICAgIHt0KCd3b3JrZXJfZGVzY3JpcHRpb24nKX1cclxuICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvbW90aW9uLmRpdj5cclxuXHJcbiAgICAgICAgey8qINin2YTYpdit2LXYp9im2YrYp9iqIC0g2KrYtdmF2YrZhSDZhdiu2KrZhNmBINmE2YTYtNin2LTYp9iqINin2YTYtdi62YrYsdipINmI2KfZhNmD2KjZitix2KkgKi99XHJcblxyXG4gICAgICAgIHsvKiDYp9mE2KXYrdi12KfYptmK2KfYqiDZhNmE2LTYp9i02KfYqiDYp9mE2YPYqNmK2LHYqSAqL31cclxuICAgICAgICA8bW90aW9uLmRpdlxyXG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAzMCB9fVxyXG4gICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XHJcbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYsIGRlbGF5OiAwLjIgfX1cclxuICAgICAgICAgIGNsYXNzTmFtZT1cImhpZGRlbiBsZzpncmlkIGxnOmdyaWQtY29scy00IGdhcC02IG1iLThcIlxyXG4gICAgICAgID5cclxuICAgICAgICAgIHtzdGF0cy5tYXAoKHN0YXQsIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgICAgICAga2V5PXtpbmRleH1cclxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHNjYWxlOiAwLjggfX1cclxuICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHNjYWxlOiAxIH19XHJcbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC41LCBkZWxheTogMC4zICsgaW5kZXggKiAwLjEgfX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZS84MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQteGwgcC02IGJvcmRlciBib3JkZXItcGluay0xMDAgaG92ZXI6c2hhZG93LWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctMTIgaC0xMiBiZy1ncmFkaWVudC10by1yICR7c3RhdC5jb2xvcn0gcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlcmB9PlxyXG4gICAgICAgICAgICAgICAgICA8c3RhdC5pY29uIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC13aGl0ZVwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmVlbi02MDAgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxUcmVuZGluZ1VwIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8c3Bhbj57c3RhdC5jaGFuZ2V9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS04MDAgbWItMVwiPntzdGF0LnZhbHVlfTwvaDM+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LXNtXCI+e3N0YXQudGl0bGV9PC9wPlxyXG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XHJcbiAgICAgICAgICApKX1cclxuICAgICAgICA8L21vdGlvbi5kaXY+XHJcblxyXG4gICAgICAgIHsvKiDYp9mE2KXYrdi12KfYptmK2KfYqiDZhNmE2LTYp9i02KfYqiDYp9mE2LXYutmK2LHYqSAtINiq2LXZhdmK2YUg2YXYtti62YjYtyAqL31cclxuICAgICAgICA8bW90aW9uLmRpdlxyXG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAzMCB9fVxyXG4gICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XHJcbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYsIGRlbGF5OiAwLjIgfX1cclxuICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIGxnOmhpZGRlbiBtYi04XCJcclxuICAgICAgICA+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzgwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC14bCBwLTQgYm9yZGVyIGJvcmRlci1waW5rLTEwMFwiPlxyXG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC1ncmF5LTgwMCBtYi00IGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBzcGFjZS14LXJldmVyc2VcIj5cclxuICAgICAgICAgICAgICA8QmFyQ2hhcnQzIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1waW5rLTYwMFwiIC8+XHJcbiAgICAgICAgICAgICAgPHNwYW4+e3QoJ3N0YXRpc3RpY3MnKX08L3NwYW4+XHJcbiAgICAgICAgICAgIDwvaDM+XHJcblxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTMgZ2FwLTNcIj5cclxuICAgICAgICAgICAgICB7c3RhdHMubWFwKChzdGF0LCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICAgICAgICAgICAga2V5PXtpbmRleH1cclxuICAgICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCBzY2FsZTogMC44IH19XHJcbiAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgc2NhbGU6IDEgfX1cclxuICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC41LCBkZWxheTogMC4zICsgaW5kZXggKiAwLjEgfX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcC0zIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tZ3JheS01MCB0by1ncmF5LTEwMCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDBcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctOCBoLTggYmctZ3JhZGllbnQtdG8tciAke3N0YXQuY29sb3J9IHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi0yYH0+XHJcbiAgICAgICAgICAgICAgICAgICAgPHN0YXQuaWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtd2hpdGVcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LWdyYXktODAwIG1iLTFcIj57c3RhdC52YWx1ZX08L2g0PlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS02MDAgbGVhZGluZy10aWdodFwiPntzdGF0LnRpdGxlfTwvcD5cclxuICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cclxuICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L21vdGlvbi5kaXY+XHJcblxyXG4gICAgICAgIHsvKiDYqtix2KrZitioINmF2K7YqtmE2YEg2YTZhNi02KfYtNin2Kog2KfZhNi12LrZitix2Kkg2YjYp9mE2YPYqNmK2LHYqSAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJsb2NrIGxnOmhpZGRlbiBzcGFjZS15LThcIj5cclxuICAgICAgICAgIHsvKiDYp9mE2LfZhNio2KfYqiDYp9mE2K3Yr9mK2KvYqSAtINmE2YTYtNin2LTYp9iqINin2YTYtdi62YrYsdipICjZgdmKINin2YTYo9i52YTZiSkgKi99XHJcbiAgICAgICAgICA8bW90aW9uLmRpdlxyXG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDMwIH19XHJcbiAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxyXG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYsIGRlbGF5OiAwLjQgfX1cclxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUvODAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLXhsIHAtNiBib3JkZXIgYm9yZGVyLXBpbmstMTAwXCJcclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JheS04MDAgbWItNiBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgc3BhY2UteC1yZXZlcnNlXCI+XHJcbiAgICAgICAgICAgICAgPFBhY2thZ2UgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXBpbmstNjAwXCIgLz5cclxuICAgICAgICAgICAgICA8c3Bhbj57dCgncmVjZW50X29yZGVycycpfTwvc3Bhbj5cclxuICAgICAgICAgICAgPC9oMz5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAge3JlY2VudE9yZGVycy5tYXAoKG9yZGVyLCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICAgICAgICAgICAga2V5PXtvcmRlci5pZH1cclxuICAgICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxyXG4gICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cclxuICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC40LCBkZWxheTogMC41ICsgaW5kZXggKiAwLjEgfX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNCBiZy1ncmFkaWVudC10by1yIGZyb20tcGluay01MCB0by1yb3NlLTUwIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1waW5rLTIwMFwiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS04MDBcIj57b3JkZXIuY2xpZW50fTwvaDQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+e29yZGVyLnR5cGV9PC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPiN7b3JkZXIuaWR9PC9wPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgc3BhY2UteC1yZXZlcnNlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSAke2dldFN0YXR1c0luZm8ob3JkZXIuc3RhdHVzKS5iZ0NvbG9yfSAke2dldFN0YXR1c0luZm8ob3JkZXIuc3RhdHVzKS5jb2xvcn1gfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2dldFN0YXR1c0luZm8ob3JkZXIuc3RhdHVzKS5sYWJlbH1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC0xXCI+e2Zvcm1hdERhdGUob3JkZXIuZHVlRGF0ZSl9PC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuXHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxyXG4gICAgICAgICAgICAgICkpfVxyXG5cclxuICAgICAgICAgICAgICB7cmVjZW50T3JkZXJzLmxlbmd0aCA9PT0gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LThcIj5cclxuICAgICAgICAgICAgICAgICAgPFBhY2thZ2UgY2xhc3NOYW1lPVwidy0xMiBoLTEyIHRleHQtZ3JheS00MDAgbXgtYXV0byBtYi00XCIgLz5cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPnt0KCdub19vcmRlcnNfZm91bmQnKX08L3A+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgaHJlZj1cIi9kYXNoYm9hcmQvb3JkZXJzXCJcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgbXQtNCBidG4tc2Vjb25kYXJ5IHB5LTIgdGV4dC1zbSBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAge3QoJ3ZpZXdfYWxsJyl9IHt0KCdvcmRlcnMnKX1cclxuICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxyXG5cclxuXHJcblxyXG4gICAgICAgICAgey8qINin2YTYpdis2LHYp9ih2KfYqiDYp9mE2LPYsdmK2LnYqSAtINmE2YTZhdiv2YrYsSDZgdmC2Lcg2YHZiiDYp9mE2LTYp9i02KfYqiDYp9mE2LXYutmK2LHYqSAqL31cclxuICAgICAgICAgIHt1c2VyPy5yb2xlID09PSAnYWRtaW4nICYmIChcclxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcclxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDMwIH19XHJcbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XHJcbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC42LCBkZWxheTogMC42IH19XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUvODAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLXhsIHAtNiBib3JkZXIgYm9yZGVyLXBpbmstMTAwXCJcclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktODAwIG1iLTYgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHNwYWNlLXgtcmV2ZXJzZVwiPlxyXG4gICAgICAgICAgICAgICAgPFNldHRpbmdzIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1waW5rLTYwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICA8c3Bhbj57dCgncXVpY2tfYWN0aW9ucycpfTwvc3Bhbj5cclxuICAgICAgICAgICAgICA8L2gzPlxyXG5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ2FwLTQgZ3JpZC1jb2xzLTJcIj5cclxuICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgIGhyZWY9XCIvZGFzaGJvYXJkL2FkZC1vcmRlclwiXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtNCBiZy1ncmFkaWVudC10by1yIGZyb20tcGluay01MCB0by1waW5rLTEwMCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItcGluay0yMDAgaG92ZXI6c2hhZG93LW1kIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCB0ZXh0LWNlbnRlciBibG9ja1wiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1waW5rLTYwMCBteC1hdXRvIG1iLTJcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtcGluay04MDBcIj57dCgnYWRkX25ld19vcmRlcicpfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvTGluaz5cclxuXHJcbiAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICBocmVmPVwiL2Rhc2hib2FyZC93b3JrZXJzXCJcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC00IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwIHRvLWJsdWUtMTAwIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ibHVlLTIwMCBob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIHRleHQtY2VudGVyIGJsb2NrXCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPFVzZXJzIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1ibHVlLTYwMCBteC1hdXRvIG1iLTJcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS04MDBcIj57dCgnd29ya2VyX21hbmFnZW1lbnQnKX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L0xpbms+XHJcblxyXG4gICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgaHJlZj1cIi9kYXNoYm9hcmQvYXBwb2ludG1lbnRzXCJcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC00IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1wdXJwbGUtNTAgdG8tcHVycGxlLTEwMCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItcHVycGxlLTIwMCBob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIHRleHQtY2VudGVyIGJsb2NrIGNvbC1zcGFuLTJcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8Q2FsZW5kYXIgY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LXB1cnBsZS02MDAgbXgtYXV0byBtYi0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXB1cnBsZS04MDBcIj57dCgnYXBwb2ludG1lbnRzJyl9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgcC00IGJnLWdyYWRpZW50LXRvLXIgZnJvbS15ZWxsb3ctNTAgdG8tb3JhbmdlLTUwIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci15ZWxsb3ctMjAwXCI+XHJcbiAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTgwMCBtYi0yXCI+e3QoJ3JlbWluZGVyJyl9PC9oND5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxyXG4gICAgICAgICAgICAgICAgICB7dCgneW91X2hhdmUnKX0ge3RvZGF5QXBwb2ludG1lbnRzfSB7dCgndG9kYXlfYXBwb2ludG1lbnRzX3JlbWluZGVyJyl9IHt0KCdhbmQnKX0ge3JlYWxTdGF0cy5hY3RpdmVPcmRlcnN9IHt0KCdvcmRlcnNfbmVlZF9mb2xsb3cnKX1cclxuICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxyXG4gICAgICAgICAgKX1cclxuXHJcblxyXG5cclxuICAgICAgICAgIHsvKiDYp9mE2KrZgtin2LHZitixIC0g2YTZhNi02KfYtNin2Kog2KfZhNi12LrZitix2Kkg2YHZiiDYp9mE2KPYs9mB2YQgKi99XHJcbiAgICAgICAgICB7dXNlcj8ucm9sZSA9PT0gJ2FkbWluJyAmJiAoXHJcbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAzMCB9fVxyXG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxyXG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNiwgZGVsYXk6IDAuOCB9fVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXdoaXRlLzgwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC14bCBwLTYgYm9yZGVyIGJvcmRlci1waW5rLTEwMFwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ncmF5LTgwMCBtYi02IGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBzcGFjZS14LXJldmVyc2VcIj5cclxuICAgICAgICAgICAgICAgIDxCYXJDaGFydDMgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LWdyZWVuLTYwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICA8c3Bhbj57dCgncmVwb3J0cycpfTwvc3Bhbj5cclxuICAgICAgICAgICAgICA8L2gzPlxyXG5cclxuICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgaHJlZj1cIi9kYXNoYm9hcmQvcmVwb3J0c1wiXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcC00IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmVlbi01MCB0by1ncmVlbi0xMDAgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCBob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIHRleHQtY2VudGVyIGJsb2NrXCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8QmFyQ2hhcnQzIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC1ncmVlbi02MDAgbXgtYXV0byBtYi0yXCIgLz5cclxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmFzZSBmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTgwMFwiPnt0KCdkZXRhaWxlZF9yZXBvcnRzJyl9PC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgey8qINin2YTYqtiu2LfZiti3INin2YTYo9i12YTZiiDZhNmE2LTYp9i02KfYqiDYp9mE2YPYqNmK2LHYqSAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBsZzpncmlkIGxnOmdyaWQtY29scy0yIGdhcC04XCI+XHJcbiAgICAgICAgICB7Lyog2KfZhNi32YTYqNin2Kog2KfZhNit2K/Zitir2KkgKi99XHJcbiAgICAgICAgICA8bW90aW9uLmRpdlxyXG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IC0zMCB9fVxyXG4gICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cclxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC42LCBkZWxheTogMC40IH19XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXdoaXRlLzgwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC14bCBwLTYgYm9yZGVyIGJvcmRlci1waW5rLTEwMFwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktODAwIG1iLTYgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHNwYWNlLXgtcmV2ZXJzZVwiPlxyXG4gICAgICAgICAgICAgIDxQYWNrYWdlIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1waW5rLTYwMFwiIC8+XHJcbiAgICAgICAgICAgICAgPHNwYW4+e3QoJ3JlY2VudF9vcmRlcnMnKX08L3NwYW4+XHJcbiAgICAgICAgICAgIDwvaDM+XHJcblxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxyXG4gICAgICAgICAgICAgIHtyZWNlbnRPcmRlcnMubWFwKChvcmRlciwgaW5kZXgpID0+IChcclxuICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgICAgICAgICAgIGtleT17b3JkZXIuaWR9XHJcbiAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cclxuICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XHJcbiAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNCwgZGVsYXk6IDAuNiArIGluZGV4ICogMC4xIH19XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTQgYmctZ3JhZGllbnQtdG8tciBmcm9tLXBpbmstNTAgdG8tcm9zZS01MCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItcGluay0yMDBcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktODAwXCI+e29yZGVyLmNsaWVudH08L2g0PlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPntvcmRlci50eXBlfTwvcD5cclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj4je29yZGVyLmlkfTwvcD5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHQgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHNwYWNlLXgtcmV2ZXJzZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BweC0yIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gJHtnZXRTdGF0dXNDb2xvcihvcmRlci5zdGF0dXMpfWB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7Z2V0U3RhdHVzTGFiZWwob3JkZXIuc3RhdHVzKX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC0xIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSBzcGFjZS14LXJldmVyc2VcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPENsb2NrIGNsYXNzTmFtZT1cInctMyBoLTNcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57b3JkZXIuZHVlRGF0ZX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XHJcbiAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICBocmVmPVwiL2Rhc2hib2FyZC9vcmRlcnNcIlxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBtdC00IGJ0bi1zZWNvbmRhcnkgcHktMiB0ZXh0LXNtIGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7dCgndmlld19hbGwnKX0ge3QoJ29yZGVycycpfVxyXG4gICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICA8L21vdGlvbi5kaXY+XHJcblxyXG4gICAgICAgICAgey8qINin2YTYpdis2LHYp9ih2KfYqiDYp9mE2LPYsdmK2LnYqSAtINmE2YTZhdiv2YrYsSDZgdmC2LcgKi99XHJcbiAgICAgICAgICB7dXNlcj8ucm9sZSA9PT0gJ2FkbWluJyAmJiAoXHJcbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB4OiAzMCB9fVxyXG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxyXG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNiwgZGVsYXk6IDAuNiB9fVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXdoaXRlLzgwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC14bCBwLTYgYm9yZGVyIGJvcmRlci1waW5rLTEwMFwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ncmF5LTgwMCBtYi02IGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBzcGFjZS14LXJldmVyc2VcIj5cclxuICAgICAgICAgICAgICAgIDxTZXR0aW5ncyBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtcGluay02MDBcIiAvPlxyXG4gICAgICAgICAgICAgICAgPHNwYW4+e3QoJ3F1aWNrX2FjdGlvbnMnKX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgPC9oMz5cclxuXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdhcC00IGdyaWQtY29scy0yXCI+XHJcbiAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICBocmVmPVwiL2Rhc2hib2FyZC9hZGQtb3JkZXJcIlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTQgYmctZ3JhZGllbnQtdG8tciBmcm9tLXBpbmstNTAgdG8tcGluay0xMDAgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLXBpbmstMjAwIGhvdmVyOnNoYWRvdy1tZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgdGV4dC1jZW50ZXIgYmxvY2tcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtcGluay02MDAgbXgtYXV0byBtYi0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXBpbmstODAwXCI+e3QoJ2FkZF9uZXdfb3JkZXInKX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L0xpbms+XHJcblxyXG4gICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgaHJlZj1cIi9kYXNoYm9hcmQvd29ya2Vyc1wiXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtNCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MCB0by1ibHVlLTEwMCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItYmx1ZS0yMDAgaG92ZXI6c2hhZG93LW1kIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCB0ZXh0LWNlbnRlciBibG9ja1wiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxVc2VycyBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtYmx1ZS02MDAgbXgtYXV0byBtYi0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWJsdWUtODAwXCI+e3QoJ3dvcmtlcl9tYW5hZ2VtZW50Jyl9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxyXG5cclxuICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgIGhyZWY9XCIvZGFzaGJvYXJkL3JlcG9ydHNcIlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTQgYmctZ3JhZGllbnQtdG8tciBmcm9tLWdyZWVuLTUwIHRvLWdyZWVuLTEwMCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwIGhvdmVyOnNoYWRvdy1tZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgdGV4dC1jZW50ZXIgYmxvY2tcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8QmFyQ2hhcnQzIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1ncmVlbi02MDAgbXgtYXV0byBtYi0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTgwMFwiPnt0KCdyZXBvcnRzJyl9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxyXG5cclxuICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgIGhyZWY9XCIvZGFzaGJvYXJkL2FwcG9pbnRtZW50c1wiXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtNCBiZy1ncmFkaWVudC10by1yIGZyb20tcHVycGxlLTUwIHRvLXB1cnBsZS0xMDAgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLXB1cnBsZS0yMDAgaG92ZXI6c2hhZG93LW1kIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCB0ZXh0LWNlbnRlciBibG9ja1wiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxDYWxlbmRhciBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtcHVycGxlLTYwMCBteC1hdXRvIG1iLTJcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtcHVycGxlLTgwMFwiPnt0KCdhcHBvaW50bWVudHMnKX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiBwLTQgYmctZ3JhZGllbnQtdG8tciBmcm9tLXllbGxvdy01MCB0by1vcmFuZ2UtNTAgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLXllbGxvdy0yMDBcIj5cclxuICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktODAwIG1iLTJcIj57dCgncmVtaW5kZXInKX08L2g0PlxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XHJcbiAgICAgICAgICAgICAgICAgIHt0KCd5b3VfaGF2ZScpfSB7dG9kYXlBcHBvaW50bWVudHN9IHt0KCd0b2RheV9hcHBvaW50bWVudHNfcmVtaW5kZXInKX0ge3QoJ2FuZCcpfSB7cmVhbFN0YXRzLmFjdGl2ZU9yZGVyc30ge3QoJ29yZGVyc19uZWVkX2ZvbGxvdycpfVxyXG4gICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gICAgPC8+XHJcbiAgKVxyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEYXNoYm9hcmRQYWdlKCkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8UHJvdGVjdGVkUm91dGU+XHJcbiAgICAgIDxEYXNoYm9hcmRDb250ZW50IC8+XHJcbiAgICA8L1Byb3RlY3RlZFJvdXRlPlxyXG4gIClcclxufVxyXG4iXSwibmFtZXMiOlsibW90aW9uIiwidXNlUm91dGVyIiwiTGluayIsInVzZUF1dGhTdG9yZSIsInVzZURhdGFTdG9yZSIsInVzZVRyYW5zbGF0aW9uIiwiUHJvdGVjdGVkUm91dGUiLCJCYXJDaGFydDMiLCJVc2VycyIsIkNhbGVuZGFyIiwiUGFja2FnZSIsIlNldHRpbmdzIiwiTG9nT3V0IiwiVHJlbmRpbmdVcCIsIkNsb2NrIiwiQ2hlY2tDaXJjbGUiLCJQbHVzIiwiQXJyb3dSaWdodCIsIkRhc2hib2FyZENvbnRlbnQiLCJ1c2VyIiwic2lnbk91dCIsIm9yZGVycyIsImFwcG9pbnRtZW50cyIsImdldFN0YXRzIiwidCIsImxhbmd1YWdlIiwiY2hhbmdlTGFuZ3VhZ2UiLCJpc0FyYWJpYyIsInJvdXRlciIsImhhbmRsZVNpZ25PdXQiLCJwdXNoIiwicmVhbFN0YXRzIiwidG9kYXlBcHBvaW50bWVudHMiLCJmaWx0ZXIiLCJhcHBvaW50bWVudCIsInRvZGF5IiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJhcHBvaW50bWVudERhdGUiLCJzdGF0dXMiLCJsZW5ndGgiLCJnZXRTdGF0c0ZvclJvbGUiLCJyb2xlIiwid29ya2VyT3JkZXJzIiwib3JkZXIiLCJhc3NpZ25lZFdvcmtlciIsImlkIiwid29ya2VyQ29tcGxldGVkT3JkZXJzIiwid29ya2VyQWN0aXZlT3JkZXJzIiwiaW5jbHVkZXMiLCJ0aXRsZSIsInZhbHVlIiwidG9TdHJpbmciLCJjaGFuZ2UiLCJpY29uIiwiY29sb3IiLCJhY3RpdmVPcmRlcnMiLCJjb21wbGV0ZWRPcmRlcnMiLCJ0b3RhbE9yZGVycyIsInN0YXRzIiwicmVjZW50T3JkZXJzIiwic29ydCIsImEiLCJiIiwiY3JlYXRlZEF0IiwiZ2V0VGltZSIsInNsaWNlIiwibWFwIiwiY2xpZW50IiwiY2xpZW50TmFtZSIsInR5cGUiLCJkZXNjcmlwdGlvbiIsImR1ZURhdGUiLCJnZXRTdGF0dXNDb2xvciIsImNvbG9ycyIsInBlbmRpbmciLCJpbl9wcm9ncmVzcyIsImNvbXBsZXRlZCIsImRlbGl2ZXJlZCIsImdldFN0YXR1c0xhYmVsIiwibGFiZWxzIiwiZm9ybWF0RGF0ZSIsImRhdGVTdHJpbmciLCJkYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwieWVhciIsIm1vbnRoIiwiZGF5IiwiZ2V0U3RhdHVzSW5mbyIsInN0YXR1c01hcCIsImxhYmVsIiwiYmdDb2xvciIsImNhbmNlbGxlZCIsImRpdiIsImhlYWRlciIsImhyZWYiLCJjbGFzc05hbWUiLCJzcGFuIiwiaDEiLCJmdWxsX25hbWUiLCJlbWFpbCIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwic3R5bGUiLCJkaXNwbGF5IiwidmlzaWJpbGl0eSIsInBvc2l0aW9uIiwiekluZGV4IiwibWluV2lkdGgiLCJmbGV4U2hyaW5rIiwib3BhY2l0eSIsImluaXRpYWwiLCJ5IiwiYW5pbWF0ZSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsImgyIiwiaDMiLCJkZWxheSIsInN0YXQiLCJpbmRleCIsInNjYWxlIiwiaDQiLCJ4IiwiRGFzaGJvYXJkUGFnZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProtectedRoute.tsx":
/*!*******************************************!*\
  !*** ./src/components/ProtectedRoute.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/authStore */ \"(ssr)/./src/store/authStore.ts\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction ProtectedRoute({ children, requiredRole, redirectTo = '/login' }) {\n    const { user, isLoading, checkAuth } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            const initAuth = {\n                \"ProtectedRoute.useEffect.initAuth\": async ()=>{\n                    await checkAuth();\n                    setIsChecking(false);\n                }\n            }[\"ProtectedRoute.useEffect.initAuth\"];\n            initAuth();\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        checkAuth\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            if (!isChecking && !isLoading) {\n                if (!user) {\n                    router.push(redirectTo);\n                    return;\n                }\n                if (requiredRole && user.role !== requiredRole) {\n                    router.push('/dashboard');\n                    return;\n                }\n                if (!user.is_active) {\n                    router.push('/login');\n                    return;\n                }\n            }\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        user,\n        isChecking,\n        isLoading,\n        requiredRole,\n        router,\n        redirectTo\n    ]);\n    // عرض شاشة التحميل أثناء التحقق من المصادقة\n    if (isChecking || isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    scale: 0.9\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                transition: {\n                    duration: 0.5\n                },\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-8 h-8 text-white animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 font-medium\",\n                        children: \"جاري التحقق من الصلاحيات...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this);\n    }\n    // عرض رسالة خطأ إذا لم يكن المستخدم مخول\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6\n                },\n                className: \"text-center max-w-md mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-8 h-8 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-800 mb-4\",\n                        children: \"غير مخول للوصول\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"يجب تسجيل الدخول للوصول إلى هذه الصفحة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/login'),\n                        className: \"btn-primary px-6 py-3\",\n                        children: \"تسجيل الدخول\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this);\n    }\n    if (requiredRole && user.role !== requiredRole) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6\n                },\n                className: \"text-center max-w-md mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-8 h-8 text-yellow-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-800 mb-4\",\n                        children: \"صلاحيات غير كافية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/dashboard'),\n                        className: \"btn-primary px-6 py-3\",\n                        children: \"العودة إلى لوحة التحكم\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user.is_active) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6\n                },\n                className: \"text-center max-w-md mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-8 h-8 text-gray-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-800 mb-4\",\n                        children: \"حساب غير نشط\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"تم إلغاء تفعيل حسابك. يرجى التواصل مع المدير.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/login'),\n                        className: \"btn-primary px-6 py-3\",\n                        children: \"تسجيل الدخول\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n            lineNumber: 129,\n            columnNumber: 7\n        }, this);\n    }\n    // عرض المحتوى إذا كان كل شيء صحيح\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useTranslation.ts":
/*!*************************************!*\
  !*** ./src/hooks/useTranslation.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useTranslation auto */ \n// الترجمات العربية\nconst arTranslations = {\n    // التنقل والعناوين الرئيسية\n    'dashboard': 'لوحة التحكم',\n    'orders': 'الطلبات',\n    'appointments': 'المواعيد',\n    'settings': 'الإعدادات',\n    'workers': 'العمال',\n    'reports': 'التقارير',\n    'logout': 'تسجيل الخروج',\n    'welcome': 'مرحباً',\n    'welcome_back': 'مرحباً بعودتك',\n    // الأزرار والإجراءات\n    'add_new_order': 'إضافة طلب جديد',\n    'book_appointment': 'حجز موعد',\n    'view_details': 'عرض التفاصيل',\n    'edit': 'تعديل',\n    'delete': 'حذف',\n    'save': 'حفظ',\n    'cancel': 'إلغاء',\n    'submit': 'إرسال',\n    'search': 'بحث',\n    'filter': 'تصفية',\n    'export': 'تصدير',\n    'print': 'طباعة',\n    'back': 'رجوع',\n    'next': 'التالي',\n    'previous': 'السابق',\n    'close': 'إغلاق',\n    'confirm': 'تأكيد',\n    'loading': 'جاري التحميل...',\n    'saving': 'جاري الحفظ...',\n    // حالات الطلبات\n    'pending': 'في الانتظار',\n    'in_progress': 'قيد التنفيذ',\n    'completed': 'مكتمل',\n    'delivered': 'تم التسليم',\n    'cancelled': 'ملغي',\n    // نصوص عامة\n    'name': 'الاسم',\n    'email': 'البريد الإلكتروني',\n    'phone': 'رقم الهاتف',\n    'address': 'العنوان',\n    'date': 'التاريخ',\n    'time': 'الوقت',\n    'status': 'الحالة',\n    'price': 'السعر',\n    'total': 'المجموع',\n    'description': 'الوصف',\n    'notes': 'ملاحظات',\n    'client_name': 'اسم الزبونة',\n    'client_phone': 'رقم هاتف الزبونة',\n    // رسائل النجاح والخطأ\n    'success': 'نجح',\n    'error': 'خطأ',\n    'warning': 'تحذير',\n    'info': 'معلومات',\n    'order_added_success': 'تم إضافة الطلب بنجاح',\n    'order_updated_success': 'تم تحديث الطلب بنجاح',\n    'order_deleted_success': 'تم حذف الطلب بنجاح',\n    'fill_required_fields': 'يرجى ملء جميع الحقول المطلوبة',\n    // نصوص إضافية مطلوبة\n    'admin_dashboard': 'لوحة تحكم المدير',\n    'worker_dashboard': 'لوحة تحكم العامل',\n    'admin': 'مدير',\n    'worker': 'عامل',\n    'change_language': 'تغيير اللغة',\n    'my_active_orders': 'طلباتي النشطة',\n    'completed_orders': 'الطلبات المكتملة',\n    'total_orders': 'إجمالي الطلبات',\n    'total_revenue': 'إجمالي الإيرادات',\n    'recent_orders': 'الطلبات الحديثة',\n    'quick_actions': 'إجراءات سريعة',\n    'view_all_orders': 'عرض جميع الطلبات',\n    'add_order': 'إضافة طلب',\n    'manage_workers': 'إدارة العمال',\n    'view_reports': 'عرض التقارير',\n    'client_name_required': 'اسم الزبونة *',\n    'phone_required': 'رقم الهاتف *',\n    'order_description_required': 'وصف الطلب *',\n    'delivery_date_required': 'موعد التسليم *',\n    'price_sar': 'السعر (ريال سعودي)',\n    'measurements_cm': 'المقاسات (بالسنتيمتر)',\n    'additional_notes': 'ملاحظات إضافية',\n    'voice_notes_optional': 'ملاحظات صوتية (اختيارية)',\n    'design_images': 'صور التصميم',\n    'fabric_type': 'نوع القماش',\n    'fabric_type_optional': 'نوع القماش',\n    'responsible_worker': 'العامل المسؤول',\n    'choose_worker': 'اختر العامل المسؤول',\n    'status_and_worker': 'الحالة والعامل',\n    'order_status': 'حالة الطلب',\n    'additional_notes_placeholder': 'أي ملاحظات أو تفاصيل إضافية...',\n    'not_specified': 'غير محدد',\n    'back_to_dashboard': 'العودة إلى لوحة التحكم',\n    'overview_today': 'نظرة عامة على أنشطة اليوم',\n    'welcome_worker': 'مرحباً بك في مساحة العمل',\n    // مفاتيح مفقودة من لوحة التحكم\n    'homepage': 'الصفحة الرئيسية',\n    'my_completed_orders': 'طلباتي المكتملة',\n    'my_total_orders': 'إجمالي طلباتي',\n    'active_orders': 'الطلبات النشطة',\n    'today_appointments': 'مواعيد اليوم',\n    'statistics': 'الإحصائيات',\n    'no_orders_found': 'لا توجد طلبات',\n    'view_all': 'عرض الكل',\n    'worker_management': 'إدارة العمال',\n    'reminder': 'تذكير',\n    'you_have': 'لديك',\n    'today_appointments_reminder': 'موعد اليوم',\n    'and': 'و',\n    'orders_need_follow': 'طلبات تحتاج متابعة',\n    'detailed_reports': 'تقارير مفصلة',\n    'worker_description': 'يمكنك هنا متابعة طلباتك المخصصة لك وتحديث حالتها',\n    // مفاتيح صفحة إضافة الطلبات\n    'order_add_error': 'حدث خطأ أثناء إضافة الطلب',\n    'cm_placeholder': 'سم',\n    'shoulder': 'الكتف',\n    'shoulder_circumference': 'محيط الكتف',\n    'chest': 'الصدر',\n    'waist': 'الخصر',\n    'hips': 'الأرداف',\n    'dart_length': 'طول الخياطة',\n    'bodice_length': 'طول الجسم',\n    'neckline': 'خط الرقبة',\n    'armpit': 'الإبط',\n    'sleeve_length': 'طول الكم',\n    'forearm': 'الساعد',\n    'cuff': 'الكم',\n    'front_length': 'الطول الأمامي',\n    'back_length': 'الطول الخلفي',\n    // مفاتيح صفحة الطلبات\n    'enter_order_number': 'أدخل رقم الطلب',\n    'search_placeholder': 'البحث بالاسم أو رقم الطلب أو الوصف...',\n    'search_by_text': 'البحث بالنص',\n    'search_by_order_number': 'البحث برقم الطلب',\n    'all_orders': 'جميع الطلبات',\n    'no_orders_assigned': 'لا توجد طلبات مخصصة لك',\n    'no_orders_assigned_desc': 'لم يتم تخصيص أي طلبات لك بعد',\n    'no_orders_found_desc': 'لا توجد طلبات مطابقة لمعايير البحث',\n    'price_label': 'السعر',\n    'sar': 'ريال',\n    'view': 'عرض',\n    'completing': 'جاري الإنهاء...',\n    'start_work': 'بدء العمل',\n    'complete_order': 'إنهاء الطلب',\n    'complete_order_modal_title': 'إنهاء الطلب وتحميل صور العمل المكتمل',\n    'important_warning': 'تحذير مهم',\n    'complete_order_warning': 'بمجرد إنهاء الطلب، لن تتمكن من تعديل حالته مرة أخرى. تأكد من تحميل جميع صور العمل المكتمل قبل المتابعة.',\n    'order_deleted_successfully': 'تم حذف الطلب بنجاح',\n    // مفاتيح مكون حذف الطلب\n    'confirm_delete_order': 'تأكيد حذف الطلب',\n    'warning_delete_order': 'تحذير: حذف الطلب',\n    'delete_order_warning_message': 'لا يمكن التراجع عن هذا الإجراء. سيتم حذف الطلب وجميع البيانات المرتبطة به نهائياً.',\n    'admin_email': 'بريد المدير الإلكتروني',\n    'admin_password': 'كلمة مرور المدير',\n    'enter_admin_email': 'أدخل بريد المدير الإلكتروني',\n    'enter_admin_password': 'أدخل كلمة مرور المدير',\n    'please_fill_all_fields': 'يرجى ملء جميع الحقول',\n    'email_does_not_match': 'البريد الإلكتروني لا يطابق بريد المدير المسجل',\n    'incorrect_password': 'كلمة المرور غير صحيحة',\n    'confirm_delete': 'تأكيد الحذف',\n    // مفاتيح مكون الملاحظات الصوتية\n    'start_recording': 'بدء التسجيل',\n    'stop_recording': 'إيقاف التسجيل',\n    'click_to_record_voice_note': 'انقر لتسجيل ملاحظة صوتية',\n    'voice_notes': 'الملاحظات الصوتية',\n    'voice_note': 'ملاحظة صوتية',\n    'microphone_access_error': 'خطأ في الوصول للميكروفون',\n    // مفاتيح صفحة العمال\n    'worker_added_success': 'تم إضافة العامل بنجاح',\n    'error_adding_worker': 'حدث خطأ أثناء إضافة العامل',\n    'worker_updated_success': 'تم تحديث العامل بنجاح',\n    'error_updating_worker': 'حدث خطأ أثناء تحديث العامل',\n    'worker_deleted_success': 'تم حذف العامل بنجاح',\n    'worker_deactivated': 'تم إلغاء تفعيل العامل',\n    'worker_activated': 'تم تفعيل العامل',\n    'adding': 'جاري الإضافة...',\n    'add_worker': 'إضافة عامل',\n    'active': 'نشط',\n    'inactive': 'غير نشط',\n    'save_changes': 'حفظ التغييرات',\n    'search_workers_placeholder': 'البحث عن العمال...',\n    'workers_management': 'إدارة العمال',\n    'view_manage_team': 'عرض وإدارة فريق العمل في ورشة التفصيل',\n    'add_new_worker': 'إضافة عامل جديد',\n    'add_new_worker_form': 'إضافة عامل جديد',\n    'full_name_required': 'الاسم الكامل *',\n    'email_required': 'البريد الإلكتروني *',\n    'password_required': 'كلمة المرور *',\n    'phone_required_worker': 'رقم الهاتف *',\n    'enter_full_name': 'أدخل الاسم الكامل',\n    'enter_email': 'أدخل البريد الإلكتروني',\n    'enter_password': 'أدخل كلمة المرور',\n    'enter_phone': 'أدخل رقم الهاتف',\n    'specialty_required': 'التخصص *',\n    'specialty_example': 'مثال: خياطة فساتين السهرة',\n    'edit_worker': 'تعديل العامل',\n    'new_password': 'كلمة المرور الجديدة',\n    'leave_empty_no_change': 'اتركه فارغاً إذا لم ترد تغييره',\n    'no_workers': 'لا يوجد عمال',\n    'no_workers_found': 'لا يوجد عمال مطابقين لمعايير البحث',\n    'joined_on': 'انضم في',\n    'total_workers': 'إجمالي العمال',\n    'active_workers': 'العمال النشطون',\n    'total_completed_orders': 'إجمالي الطلبات المكتملة',\n    'confirm_delete_worker': 'هل أنت متأكد من حذف هذا العامل؟',\n    // مفاتيح صفحة التقارير\n    'engagement_dress': 'فستان خطوبة',\n    'casual_dress': 'فستان يومي',\n    'other': 'أخرى',\n    'this_week': 'هذا الأسبوع',\n    'this_month': 'هذا الشهر',\n    'this_quarter': 'هذا الربع',\n    'this_year': 'هذا العام',\n    'reports_analytics': 'التقارير والتحليلات',\n    'monthly_trend': 'الاتجاه الشهري',\n    'revenue_label': 'الإيرادات',\n    // مفاتيح صفحة المواعيد\n    'confirmed': 'مؤكد',\n    'pm': 'مساءً',\n    'am': 'صباحاً',\n    'all_statuses': 'جميع الحالات',\n    'all_dates': 'جميع التواريخ',\n    'today': 'اليوم',\n    'tomorrow': 'غداً',\n    'appointments_management': 'إدارة المواعيد',\n    'view_manage_appointments': 'عرض وإدارة جميع مواعيد التفصيل',\n    'book_new_appointment': 'حجز موعد جديد',\n    'search_appointments_placeholder': 'البحث في المواعيد...',\n    'no_appointments': 'لا توجد مواعيد',\n    'no_appointments_found': 'لا توجد مواعيد مطابقة لمعايير البحث',\n    'created_on': 'تم الإنشاء في',\n    'confirm_appointment': 'تأكيد الموعد',\n    'cancel_appointment': 'إلغاء الموعد',\n    // مفاتيح مكونات إضافية\n    'of': 'من',\n    'images_text': 'صور',\n    // مفاتيح مكونات الصور والتحميل\n    'max_images_reached': 'تم الوصول للحد الأقصى من الصور',\n    'drop_images_here': 'اسقط الصور هنا',\n    'click_or_drag_images': 'انقر أو اسحب الصور هنا',\n    'image_upload_format': 'PNG, JPG, JPEG حتى 5MB',\n    'max_images_text': 'الحد الأقصى',\n    'order_label': 'الطلب',\n    'for_client': 'للزبونة',\n    // مفاتيح مكون تعديل الطلب\n    'order_update_error': 'حدث خطأ أثناء تحديث الطلب',\n    'price_sar_required': 'السعر (ريال سعودي) *',\n    'status_pending': 'في الانتظار',\n    'status_in_progress': 'قيد التنفيذ',\n    'status_completed': 'مكتمل',\n    'status_delivered': 'تم التسليم',\n    'status_cancelled': 'ملغي',\n    // نصوص الفوتر\n    'home': 'الرئيسية',\n    'track_order': 'استعلام عن الطلب',\n    'fabrics': 'الأقمشة',\n    'contact_us': 'تواصلي معنا',\n    'yasmin_alsham': 'ياسمين الشام',\n    'custom_dress_tailoring': 'تفصيل فساتين حسب الطلب'\n};\n// الترجمات الإنجليزية\nconst enTranslations = {\n    // التنقل والعناوين الرئيسية\n    'dashboard': 'Dashboard',\n    'orders': 'Orders',\n    'appointments': 'Appointments',\n    'settings': 'Settings',\n    'workers': 'Workers',\n    'reports': 'Reports',\n    'logout': 'Logout',\n    'welcome': 'Welcome',\n    'welcome_back': 'Welcome Back',\n    // الأزرار والإجراءات\n    'add_new_order': 'Add New Order',\n    'book_appointment': 'Book Appointment',\n    'view_details': 'View Details',\n    'edit': 'Edit',\n    'delete': 'Delete',\n    'save': 'Save',\n    'cancel': 'Cancel',\n    'submit': 'Submit',\n    'search': 'Search',\n    'filter': 'Filter',\n    'export': 'Export',\n    'print': 'Print',\n    'back': 'Back',\n    'next': 'Next',\n    'previous': 'Previous',\n    'close': 'Close',\n    'confirm': 'Confirm',\n    'loading': 'Loading...',\n    'saving': 'Saving...',\n    // حالات الطلبات\n    'pending': 'Pending',\n    'in_progress': 'In Progress',\n    'completed': 'Completed',\n    'delivered': 'Delivered',\n    'cancelled': 'Cancelled',\n    // نصوص عامة\n    'name': 'Name',\n    'email': 'Email',\n    'phone': 'Phone',\n    'address': 'Address',\n    'date': 'Date',\n    'time': 'Time',\n    'status': 'Status',\n    'price': 'Price',\n    'total': 'Total',\n    'description': 'Description',\n    'notes': 'Notes',\n    'client_name': 'Client Name',\n    'client_phone': 'Client Phone',\n    // رسائل النجاح والخطأ\n    'success': 'Success',\n    'error': 'Error',\n    'warning': 'Warning',\n    'info': 'Info',\n    'order_added_success': 'Order added successfully',\n    'order_updated_success': 'Order updated successfully',\n    'order_deleted_success': 'Order deleted successfully',\n    'fill_required_fields': 'Please fill all required fields',\n    // نصوص إضافية مطلوبة\n    'admin_dashboard': 'Admin Dashboard',\n    'worker_dashboard': 'Worker Dashboard',\n    'admin': 'Admin',\n    'worker': 'Worker',\n    'change_language': 'Change Language',\n    'my_active_orders': 'My Active Orders',\n    'completed_orders': 'Completed Orders',\n    'total_orders': 'Total Orders',\n    'total_revenue': 'Total Revenue',\n    'recent_orders': 'Recent Orders',\n    'quick_actions': 'Quick Actions',\n    'view_all_orders': 'View All Orders',\n    'add_order': 'Add Order',\n    'manage_workers': 'Manage Workers',\n    'view_reports': 'View Reports',\n    'client_name_required': 'Client Name *',\n    'phone_required': 'Phone Number *',\n    'order_description_required': 'Order Description *',\n    'delivery_date_required': 'Delivery Date *',\n    'price_sar': 'Price (SAR)',\n    'measurements_cm': 'Measurements (cm)',\n    'additional_notes': 'Additional Notes',\n    'voice_notes_optional': 'Voice Notes (Optional)',\n    'design_images': 'Design Images',\n    'fabric_type': 'Fabric Type',\n    'fabric_type_optional': 'Fabric Type',\n    'responsible_worker': 'Responsible Worker',\n    'choose_worker': 'Choose Responsible Worker',\n    'status_and_worker': 'Status and Worker',\n    'order_status': 'Order Status',\n    'additional_notes_placeholder': 'Any additional notes or details...',\n    'not_specified': 'Not Specified',\n    'back_to_dashboard': 'Back to Dashboard',\n    'overview_today': 'Overview of today\\'s activities',\n    'welcome_worker': 'Welcome to your workspace',\n    // مفاتيح مفقودة من لوحة التحكم\n    'homepage': 'Homepage',\n    'my_completed_orders': 'My Completed Orders',\n    'my_total_orders': 'My Total Orders',\n    'active_orders': 'Active Orders',\n    'today_appointments': 'Today\\'s Appointments',\n    'statistics': 'Statistics',\n    'no_orders_found': 'No orders found',\n    'view_all': 'View All',\n    'worker_management': 'Worker Management',\n    'reminder': 'Reminder',\n    'you_have': 'You have',\n    'today_appointments_reminder': 'appointments today',\n    'and': 'and',\n    'orders_need_follow': 'orders that need follow-up',\n    'detailed_reports': 'Detailed Reports',\n    'worker_description': 'Here you can track your assigned orders and update their status',\n    // مفاتيح صفحة إضافة الطلبات\n    'order_add_error': 'An error occurred while adding the order',\n    'cm_placeholder': 'cm',\n    'shoulder': 'Shoulder',\n    'shoulder_circumference': 'Shoulder Circumference',\n    'chest': 'Chest',\n    'waist': 'Waist',\n    'hips': 'Hips',\n    'dart_length': 'Dart Length',\n    'bodice_length': 'Bodice Length',\n    'neckline': 'Neckline',\n    'armpit': 'Armpit',\n    'sleeve_length': 'Sleeve Length',\n    'forearm': 'Forearm',\n    'cuff': 'Cuff',\n    'front_length': 'Front Length',\n    'back_length': 'Back Length',\n    // مفاتيح صفحة الطلبات\n    'enter_order_number': 'Enter order number',\n    'search_placeholder': 'Search by name, order number, or description...',\n    'search_by_text': 'Search by Text',\n    'search_by_order_number': 'Search by Order Number',\n    'all_orders': 'All Orders',\n    'no_orders_assigned': 'No orders assigned to you',\n    'no_orders_assigned_desc': 'No orders have been assigned to you yet',\n    'no_orders_found_desc': 'No orders found matching the search criteria',\n    'price_label': 'Price',\n    'sar': 'SAR',\n    'view': 'View',\n    'completing': 'Completing...',\n    'start_work': 'Start Work',\n    'complete_order': 'Complete Order',\n    'complete_order_modal_title': 'Complete Order and Upload Finished Work Images',\n    'important_warning': 'Important Warning',\n    'complete_order_warning': 'Once you complete the order, you will not be able to modify its status again. Make sure to upload all finished work images before proceeding.',\n    'order_deleted_successfully': 'Order deleted successfully',\n    // مفاتيح مكون حذف الطلب\n    'confirm_delete_order': 'Confirm Delete Order',\n    'warning_delete_order': 'Warning: Delete Order',\n    'delete_order_warning_message': 'This action cannot be undone. The order and all associated data will be permanently deleted.',\n    'admin_email': 'Admin Email',\n    'admin_password': 'Admin Password',\n    'enter_admin_email': 'Enter admin email',\n    'enter_admin_password': 'Enter admin password',\n    'please_fill_all_fields': 'Please fill all fields',\n    'email_does_not_match': 'Email does not match the registered admin email',\n    'incorrect_password': 'Incorrect password',\n    'confirm_delete': 'Confirm Delete',\n    // مفاتيح مكون الملاحظات الصوتية\n    'start_recording': 'Start Recording',\n    'stop_recording': 'Stop Recording',\n    'click_to_record_voice_note': 'Click to record a voice note',\n    'voice_notes': 'Voice Notes',\n    'voice_note': 'Voice Note',\n    'microphone_access_error': 'Microphone access error',\n    // مفاتيح صفحة العمال\n    'worker_added_success': 'Worker added successfully',\n    'error_adding_worker': 'Error adding worker',\n    'worker_updated_success': 'Worker updated successfully',\n    'error_updating_worker': 'Error updating worker',\n    'worker_deleted_success': 'Worker deleted successfully',\n    'worker_deactivated': 'Worker deactivated',\n    'worker_activated': 'Worker activated',\n    'adding': 'Adding...',\n    'add_worker': 'Add Worker',\n    'active': 'Active',\n    'inactive': 'Inactive',\n    'save_changes': 'Save Changes',\n    'search_workers_placeholder': 'Search workers...',\n    'workers_management': 'Workers Management',\n    'view_manage_team': 'View and manage the tailoring workshop team',\n    'add_new_worker': 'Add New Worker',\n    'add_new_worker_form': 'Add New Worker',\n    'full_name_required': 'Full Name *',\n    'email_required': 'Email *',\n    'password_required': 'Password *',\n    'phone_required_worker': 'Phone Number *',\n    'enter_full_name': 'Enter full name',\n    'enter_email': 'Enter email',\n    'enter_password': 'Enter password',\n    'enter_phone': 'Enter phone number',\n    'specialty_required': 'Specialty *',\n    'specialty_example': 'Example: Evening dress tailoring',\n    'edit_worker': 'Edit Worker',\n    'new_password': 'New Password',\n    'leave_empty_no_change': 'Leave empty if you don\\'t want to change it',\n    'no_workers': 'No workers',\n    'no_workers_found': 'No workers found matching the search criteria',\n    'joined_on': 'Joined on',\n    'total_workers': 'Total Workers',\n    'active_workers': 'Active Workers',\n    'total_completed_orders': 'Total Completed Orders',\n    'confirm_delete_worker': 'Are you sure you want to delete this worker?',\n    // مفاتيح صفحة التقارير\n    'engagement_dress': 'Engagement Dress',\n    'casual_dress': 'Casual Dress',\n    'other': 'Other',\n    'this_week': 'This Week',\n    'this_month': 'This Month',\n    'this_quarter': 'This Quarter',\n    'this_year': 'This Year',\n    'reports_analytics': 'Reports & Analytics',\n    'monthly_trend': 'Monthly Trend',\n    'revenue_label': 'Revenue',\n    // مفاتيح صفحة المواعيد\n    'confirmed': 'Confirmed',\n    'pm': 'PM',\n    'am': 'AM',\n    'all_statuses': 'All Statuses',\n    'all_dates': 'All Dates',\n    'today': 'Today',\n    'tomorrow': 'Tomorrow',\n    'appointments_management': 'Appointments Management',\n    'view_manage_appointments': 'View and manage all tailoring appointments',\n    'book_new_appointment': 'Book New Appointment',\n    'search_appointments_placeholder': 'Search appointments...',\n    'no_appointments': 'No appointments',\n    'no_appointments_found': 'No appointments found matching the search criteria',\n    'created_on': 'Created on',\n    'confirm_appointment': 'Confirm Appointment',\n    'cancel_appointment': 'Cancel Appointment',\n    // مفاتيح مكونات إضافية\n    'of': 'of',\n    'images_text': 'images',\n    // مفاتيح مكونات الصور والتحميل\n    'max_images_reached': 'Maximum images reached',\n    'drop_images_here': 'Drop images here',\n    'click_or_drag_images': 'Click or drag images here',\n    'image_upload_format': 'PNG, JPG, JPEG up to 5MB',\n    'max_images_text': 'Maximum',\n    'order_label': 'Order',\n    'for_client': 'For client',\n    // مفاتيح مكون تعديل الطلب\n    'order_update_error': 'Error updating order',\n    'price_sar_required': 'Price (SAR) *',\n    'status_pending': 'Pending',\n    'status_in_progress': 'In Progress',\n    'status_completed': 'Completed',\n    'status_delivered': 'Delivered',\n    'status_cancelled': 'Cancelled',\n    // نصوص الفوتر\n    'home': 'Home',\n    'track_order': 'Track Order',\n    'fabrics': 'Fabrics',\n    'contact_us': 'Contact Us',\n    'yasmin_alsham': 'Yasmin Alsham',\n    'custom_dress_tailoring': 'Custom Dress Tailoring'\n};\n// Hook للترجمة\nfunction useTranslation() {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('ar');\n    // تحميل اللغة المحفوظة عند بدء التطبيق\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTranslation.useEffect\": ()=>{\n            const savedLanguage = localStorage.getItem('dashboard-language');\n            if (savedLanguage) {\n                setLanguage(savedLanguage);\n            }\n        }\n    }[\"useTranslation.useEffect\"], []);\n    // حفظ اللغة عند تغييرها\n    const changeLanguage = (newLanguage)=>{\n        setLanguage(newLanguage);\n        localStorage.setItem('dashboard-language', newLanguage);\n    };\n    // دالة الترجمة\n    const t = (key)=>{\n        const translations = language === 'ar' ? arTranslations : enTranslations;\n        const translation = translations[key];\n        if (typeof translation === 'string') {\n            return translation;\n        }\n        // إذا لم توجد الترجمة، أرجع المفتاح نفسه\n        return key;\n    };\n    // التحقق من اللغة الحالية\n    const isArabic = language === 'ar';\n    const isEnglish = language === 'en';\n    return {\n        language,\n        changeLanguage,\n        t,\n        isArabic,\n        isEnglish\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlVHJhbnNsYXRpb24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O29FQUUyQztBQU8zQyxtQkFBbUI7QUFDbkIsTUFBTUUsaUJBQStCO0lBQ25DLDRCQUE0QjtJQUM1QixhQUFhO0lBQ2IsVUFBVTtJQUNWLGdCQUFnQjtJQUNoQixZQUFZO0lBQ1osV0FBVztJQUNYLFdBQVc7SUFDWCxVQUFVO0lBQ1YsV0FBVztJQUNYLGdCQUFnQjtJQUVoQixxQkFBcUI7SUFDckIsaUJBQWlCO0lBQ2pCLG9CQUFvQjtJQUNwQixnQkFBZ0I7SUFDaEIsUUFBUTtJQUNSLFVBQVU7SUFDVixRQUFRO0lBQ1IsVUFBVTtJQUNWLFVBQVU7SUFDVixVQUFVO0lBQ1YsVUFBVTtJQUNWLFVBQVU7SUFDVixTQUFTO0lBQ1QsUUFBUTtJQUNSLFFBQVE7SUFDUixZQUFZO0lBQ1osU0FBUztJQUNULFdBQVc7SUFDWCxXQUFXO0lBQ1gsVUFBVTtJQUVWLGdCQUFnQjtJQUNoQixXQUFXO0lBQ1gsZUFBZTtJQUNmLGFBQWE7SUFDYixhQUFhO0lBQ2IsYUFBYTtJQUViLFlBQVk7SUFDWixRQUFRO0lBQ1IsU0FBUztJQUNULFNBQVM7SUFDVCxXQUFXO0lBQ1gsUUFBUTtJQUNSLFFBQVE7SUFDUixVQUFVO0lBQ1YsU0FBUztJQUNULFNBQVM7SUFDVCxlQUFlO0lBQ2YsU0FBUztJQUNULGVBQWU7SUFDZixnQkFBZ0I7SUFFaEIsc0JBQXNCO0lBQ3RCLFdBQVc7SUFDWCxTQUFTO0lBQ1QsV0FBVztJQUNYLFFBQVE7SUFDUix1QkFBdUI7SUFDdkIseUJBQXlCO0lBQ3pCLHlCQUF5QjtJQUN6Qix3QkFBd0I7SUFFeEIscUJBQXFCO0lBQ3JCLG1CQUFtQjtJQUNuQixvQkFBb0I7SUFDcEIsU0FBUztJQUNULFVBQVU7SUFDVixtQkFBbUI7SUFDbkIsb0JBQW9CO0lBQ3BCLG9CQUFvQjtJQUNwQixnQkFBZ0I7SUFDaEIsaUJBQWlCO0lBQ2pCLGlCQUFpQjtJQUNqQixpQkFBaUI7SUFDakIsbUJBQW1CO0lBQ25CLGFBQWE7SUFDYixrQkFBa0I7SUFDbEIsZ0JBQWdCO0lBQ2hCLHdCQUF3QjtJQUN4QixrQkFBa0I7SUFDbEIsOEJBQThCO0lBQzlCLDBCQUEwQjtJQUMxQixhQUFhO0lBQ2IsbUJBQW1CO0lBQ25CLG9CQUFvQjtJQUNwQix3QkFBd0I7SUFDeEIsaUJBQWlCO0lBQ2pCLGVBQWU7SUFDZix3QkFBd0I7SUFDeEIsc0JBQXNCO0lBQ3RCLGlCQUFpQjtJQUNqQixxQkFBcUI7SUFDckIsZ0JBQWdCO0lBQ2hCLGdDQUFnQztJQUNoQyxpQkFBaUI7SUFDakIscUJBQXFCO0lBQ3JCLGtCQUFrQjtJQUNsQixrQkFBa0I7SUFFbEIsK0JBQStCO0lBQy9CLFlBQVk7SUFDWix1QkFBdUI7SUFDdkIsbUJBQW1CO0lBQ25CLGlCQUFpQjtJQUNqQixzQkFBc0I7SUFDdEIsY0FBYztJQUNkLG1CQUFtQjtJQUNuQixZQUFZO0lBQ1oscUJBQXFCO0lBQ3JCLFlBQVk7SUFDWixZQUFZO0lBQ1osK0JBQStCO0lBQy9CLE9BQU87SUFDUCxzQkFBc0I7SUFDdEIsb0JBQW9CO0lBQ3BCLHNCQUFzQjtJQUV0Qiw0QkFBNEI7SUFDNUIsbUJBQW1CO0lBQ25CLGtCQUFrQjtJQUNsQixZQUFZO0lBQ1osMEJBQTBCO0lBQzFCLFNBQVM7SUFDVCxTQUFTO0lBQ1QsUUFBUTtJQUNSLGVBQWU7SUFDZixpQkFBaUI7SUFDakIsWUFBWTtJQUNaLFVBQVU7SUFDVixpQkFBaUI7SUFDakIsV0FBVztJQUNYLFFBQVE7SUFDUixnQkFBZ0I7SUFDaEIsZUFBZTtJQUVmLHNCQUFzQjtJQUN0QixzQkFBc0I7SUFDdEIsc0JBQXNCO0lBQ3RCLGtCQUFrQjtJQUNsQiwwQkFBMEI7SUFDMUIsY0FBYztJQUNkLHNCQUFzQjtJQUN0QiwyQkFBMkI7SUFDM0Isd0JBQXdCO0lBQ3hCLGVBQWU7SUFDZixPQUFPO0lBQ1AsUUFBUTtJQUNSLGNBQWM7SUFDZCxjQUFjO0lBQ2Qsa0JBQWtCO0lBQ2xCLDhCQUE4QjtJQUM5QixxQkFBcUI7SUFDckIsMEJBQTBCO0lBQzFCLDhCQUE4QjtJQUU5Qix3QkFBd0I7SUFDeEIsd0JBQXdCO0lBQ3hCLHdCQUF3QjtJQUN4QixnQ0FBZ0M7SUFDaEMsZUFBZTtJQUNmLGtCQUFrQjtJQUNsQixxQkFBcUI7SUFDckIsd0JBQXdCO0lBQ3hCLDBCQUEwQjtJQUMxQix3QkFBd0I7SUFDeEIsc0JBQXNCO0lBQ3RCLGtCQUFrQjtJQUVsQixnQ0FBZ0M7SUFDaEMsbUJBQW1CO0lBQ25CLGtCQUFrQjtJQUNsQiw4QkFBOEI7SUFDOUIsZUFBZTtJQUNmLGNBQWM7SUFDZCwyQkFBMkI7SUFFM0IscUJBQXFCO0lBQ3JCLHdCQUF3QjtJQUN4Qix1QkFBdUI7SUFDdkIsMEJBQTBCO0lBQzFCLHlCQUF5QjtJQUN6QiwwQkFBMEI7SUFDMUIsc0JBQXNCO0lBQ3RCLG9CQUFvQjtJQUNwQixVQUFVO0lBQ1YsY0FBYztJQUNkLFVBQVU7SUFDVixZQUFZO0lBQ1osZ0JBQWdCO0lBQ2hCLDhCQUE4QjtJQUM5QixzQkFBc0I7SUFDdEIsb0JBQW9CO0lBQ3BCLGtCQUFrQjtJQUNsQix1QkFBdUI7SUFDdkIsc0JBQXNCO0lBQ3RCLGtCQUFrQjtJQUNsQixxQkFBcUI7SUFDckIseUJBQXlCO0lBQ3pCLG1CQUFtQjtJQUNuQixlQUFlO0lBQ2Ysa0JBQWtCO0lBQ2xCLGVBQWU7SUFDZixzQkFBc0I7SUFDdEIscUJBQXFCO0lBQ3JCLGVBQWU7SUFDZixnQkFBZ0I7SUFDaEIseUJBQXlCO0lBQ3pCLGNBQWM7SUFDZCxvQkFBb0I7SUFDcEIsYUFBYTtJQUNiLGlCQUFpQjtJQUNqQixrQkFBa0I7SUFDbEIsMEJBQTBCO0lBQzFCLHlCQUF5QjtJQUV6Qix1QkFBdUI7SUFDdkIsb0JBQW9CO0lBQ3BCLGdCQUFnQjtJQUNoQixTQUFTO0lBQ1QsYUFBYTtJQUNiLGNBQWM7SUFDZCxnQkFBZ0I7SUFDaEIsYUFBYTtJQUNiLHFCQUFxQjtJQUNyQixpQkFBaUI7SUFDakIsaUJBQWlCO0lBRWpCLHVCQUF1QjtJQUN2QixhQUFhO0lBQ2IsTUFBTTtJQUNOLE1BQU07SUFDTixnQkFBZ0I7SUFDaEIsYUFBYTtJQUNiLFNBQVM7SUFDVCxZQUFZO0lBQ1osMkJBQTJCO0lBQzNCLDRCQUE0QjtJQUM1Qix3QkFBd0I7SUFDeEIsbUNBQW1DO0lBQ25DLG1CQUFtQjtJQUNuQix5QkFBeUI7SUFDekIsY0FBYztJQUNkLHVCQUF1QjtJQUN2QixzQkFBc0I7SUFFdEIsdUJBQXVCO0lBQ3ZCLE1BQU07SUFDTixlQUFlO0lBRWYsK0JBQStCO0lBQy9CLHNCQUFzQjtJQUN0QixvQkFBb0I7SUFDcEIsd0JBQXdCO0lBQ3hCLHVCQUF1QjtJQUN2QixtQkFBbUI7SUFDbkIsZUFBZTtJQUNmLGNBQWM7SUFFZCwwQkFBMEI7SUFDMUIsc0JBQXNCO0lBQ3RCLHNCQUFzQjtJQUN0QixrQkFBa0I7SUFDbEIsc0JBQXNCO0lBQ3RCLG9CQUFvQjtJQUNwQixvQkFBb0I7SUFDcEIsb0JBQW9CO0lBRXBCLGNBQWM7SUFDZCxRQUFRO0lBQ1IsZUFBZTtJQUNmLFdBQVc7SUFDWCxjQUFjO0lBQ2QsaUJBQWlCO0lBQ2pCLDBCQUEwQjtBQUM1QjtBQUVBLHNCQUFzQjtBQUN0QixNQUFNQyxpQkFBK0I7SUFDbkMsNEJBQTRCO0lBQzVCLGFBQWE7SUFDYixVQUFVO0lBQ1YsZ0JBQWdCO0lBQ2hCLFlBQVk7SUFDWixXQUFXO0lBQ1gsV0FBVztJQUNYLFVBQVU7SUFDVixXQUFXO0lBQ1gsZ0JBQWdCO0lBRWhCLHFCQUFxQjtJQUNyQixpQkFBaUI7SUFDakIsb0JBQW9CO0lBQ3BCLGdCQUFnQjtJQUNoQixRQUFRO0lBQ1IsVUFBVTtJQUNWLFFBQVE7SUFDUixVQUFVO0lBQ1YsVUFBVTtJQUNWLFVBQVU7SUFDVixVQUFVO0lBQ1YsVUFBVTtJQUNWLFNBQVM7SUFDVCxRQUFRO0lBQ1IsUUFBUTtJQUNSLFlBQVk7SUFDWixTQUFTO0lBQ1QsV0FBVztJQUNYLFdBQVc7SUFDWCxVQUFVO0lBRVYsZ0JBQWdCO0lBQ2hCLFdBQVc7SUFDWCxlQUFlO0lBQ2YsYUFBYTtJQUNiLGFBQWE7SUFDYixhQUFhO0lBRWIsWUFBWTtJQUNaLFFBQVE7SUFDUixTQUFTO0lBQ1QsU0FBUztJQUNULFdBQVc7SUFDWCxRQUFRO0lBQ1IsUUFBUTtJQUNSLFVBQVU7SUFDVixTQUFTO0lBQ1QsU0FBUztJQUNULGVBQWU7SUFDZixTQUFTO0lBQ1QsZUFBZTtJQUNmLGdCQUFnQjtJQUVoQixzQkFBc0I7SUFDdEIsV0FBVztJQUNYLFNBQVM7SUFDVCxXQUFXO0lBQ1gsUUFBUTtJQUNSLHVCQUF1QjtJQUN2Qix5QkFBeUI7SUFDekIseUJBQXlCO0lBQ3pCLHdCQUF3QjtJQUV4QixxQkFBcUI7SUFDckIsbUJBQW1CO0lBQ25CLG9CQUFvQjtJQUNwQixTQUFTO0lBQ1QsVUFBVTtJQUNWLG1CQUFtQjtJQUNuQixvQkFBb0I7SUFDcEIsb0JBQW9CO0lBQ3BCLGdCQUFnQjtJQUNoQixpQkFBaUI7SUFDakIsaUJBQWlCO0lBQ2pCLGlCQUFpQjtJQUNqQixtQkFBbUI7SUFDbkIsYUFBYTtJQUNiLGtCQUFrQjtJQUNsQixnQkFBZ0I7SUFDaEIsd0JBQXdCO0lBQ3hCLGtCQUFrQjtJQUNsQiw4QkFBOEI7SUFDOUIsMEJBQTBCO0lBQzFCLGFBQWE7SUFDYixtQkFBbUI7SUFDbkIsb0JBQW9CO0lBQ3BCLHdCQUF3QjtJQUN4QixpQkFBaUI7SUFDakIsZUFBZTtJQUNmLHdCQUF3QjtJQUN4QixzQkFBc0I7SUFDdEIsaUJBQWlCO0lBQ2pCLHFCQUFxQjtJQUNyQixnQkFBZ0I7SUFDaEIsZ0NBQWdDO0lBQ2hDLGlCQUFpQjtJQUNqQixxQkFBcUI7SUFDckIsa0JBQWtCO0lBQ2xCLGtCQUFrQjtJQUVsQiwrQkFBK0I7SUFDL0IsWUFBWTtJQUNaLHVCQUF1QjtJQUN2QixtQkFBbUI7SUFDbkIsaUJBQWlCO0lBQ2pCLHNCQUFzQjtJQUN0QixjQUFjO0lBQ2QsbUJBQW1CO0lBQ25CLFlBQVk7SUFDWixxQkFBcUI7SUFDckIsWUFBWTtJQUNaLFlBQVk7SUFDWiwrQkFBK0I7SUFDL0IsT0FBTztJQUNQLHNCQUFzQjtJQUN0QixvQkFBb0I7SUFDcEIsc0JBQXNCO0lBRXRCLDRCQUE0QjtJQUM1QixtQkFBbUI7SUFDbkIsa0JBQWtCO0lBQ2xCLFlBQVk7SUFDWiwwQkFBMEI7SUFDMUIsU0FBUztJQUNULFNBQVM7SUFDVCxRQUFRO0lBQ1IsZUFBZTtJQUNmLGlCQUFpQjtJQUNqQixZQUFZO0lBQ1osVUFBVTtJQUNWLGlCQUFpQjtJQUNqQixXQUFXO0lBQ1gsUUFBUTtJQUNSLGdCQUFnQjtJQUNoQixlQUFlO0lBRWYsc0JBQXNCO0lBQ3RCLHNCQUFzQjtJQUN0QixzQkFBc0I7SUFDdEIsa0JBQWtCO0lBQ2xCLDBCQUEwQjtJQUMxQixjQUFjO0lBQ2Qsc0JBQXNCO0lBQ3RCLDJCQUEyQjtJQUMzQix3QkFBd0I7SUFDeEIsZUFBZTtJQUNmLE9BQU87SUFDUCxRQUFRO0lBQ1IsY0FBYztJQUNkLGNBQWM7SUFDZCxrQkFBa0I7SUFDbEIsOEJBQThCO0lBQzlCLHFCQUFxQjtJQUNyQiwwQkFBMEI7SUFDMUIsOEJBQThCO0lBRTlCLHdCQUF3QjtJQUN4Qix3QkFBd0I7SUFDeEIsd0JBQXdCO0lBQ3hCLGdDQUFnQztJQUNoQyxlQUFlO0lBQ2Ysa0JBQWtCO0lBQ2xCLHFCQUFxQjtJQUNyQix3QkFBd0I7SUFDeEIsMEJBQTBCO0lBQzFCLHdCQUF3QjtJQUN4QixzQkFBc0I7SUFDdEIsa0JBQWtCO0lBRWxCLGdDQUFnQztJQUNoQyxtQkFBbUI7SUFDbkIsa0JBQWtCO0lBQ2xCLDhCQUE4QjtJQUM5QixlQUFlO0lBQ2YsY0FBYztJQUNkLDJCQUEyQjtJQUUzQixxQkFBcUI7SUFDckIsd0JBQXdCO0lBQ3hCLHVCQUF1QjtJQUN2QiwwQkFBMEI7SUFDMUIseUJBQXlCO0lBQ3pCLDBCQUEwQjtJQUMxQixzQkFBc0I7SUFDdEIsb0JBQW9CO0lBQ3BCLFVBQVU7SUFDVixjQUFjO0lBQ2QsVUFBVTtJQUNWLFlBQVk7SUFDWixnQkFBZ0I7SUFDaEIsOEJBQThCO0lBQzlCLHNCQUFzQjtJQUN0QixvQkFBb0I7SUFDcEIsa0JBQWtCO0lBQ2xCLHVCQUF1QjtJQUN2QixzQkFBc0I7SUFDdEIsa0JBQWtCO0lBQ2xCLHFCQUFxQjtJQUNyQix5QkFBeUI7SUFDekIsbUJBQW1CO0lBQ25CLGVBQWU7SUFDZixrQkFBa0I7SUFDbEIsZUFBZTtJQUNmLHNCQUFzQjtJQUN0QixxQkFBcUI7SUFDckIsZUFBZTtJQUNmLGdCQUFnQjtJQUNoQix5QkFBeUI7SUFDekIsY0FBYztJQUNkLG9CQUFvQjtJQUNwQixhQUFhO0lBQ2IsaUJBQWlCO0lBQ2pCLGtCQUFrQjtJQUNsQiwwQkFBMEI7SUFDMUIseUJBQXlCO0lBRXpCLHVCQUF1QjtJQUN2QixvQkFBb0I7SUFDcEIsZ0JBQWdCO0lBQ2hCLFNBQVM7SUFDVCxhQUFhO0lBQ2IsY0FBYztJQUNkLGdCQUFnQjtJQUNoQixhQUFhO0lBQ2IscUJBQXFCO0lBQ3JCLGlCQUFpQjtJQUNqQixpQkFBaUI7SUFFakIsdUJBQXVCO0lBQ3ZCLGFBQWE7SUFDYixNQUFNO0lBQ04sTUFBTTtJQUNOLGdCQUFnQjtJQUNoQixhQUFhO0lBQ2IsU0FBUztJQUNULFlBQVk7SUFDWiwyQkFBMkI7SUFDM0IsNEJBQTRCO0lBQzVCLHdCQUF3QjtJQUN4QixtQ0FBbUM7SUFDbkMsbUJBQW1CO0lBQ25CLHlCQUF5QjtJQUN6QixjQUFjO0lBQ2QsdUJBQXVCO0lBQ3ZCLHNCQUFzQjtJQUV0Qix1QkFBdUI7SUFDdkIsTUFBTTtJQUNOLGVBQWU7SUFFZiwrQkFBK0I7SUFDL0Isc0JBQXNCO0lBQ3RCLG9CQUFvQjtJQUNwQix3QkFBd0I7SUFDeEIsdUJBQXVCO0lBQ3ZCLG1CQUFtQjtJQUNuQixlQUFlO0lBQ2YsY0FBYztJQUVkLDBCQUEwQjtJQUMxQixzQkFBc0I7SUFDdEIsc0JBQXNCO0lBQ3RCLGtCQUFrQjtJQUNsQixzQkFBc0I7SUFDdEIsb0JBQW9CO0lBQ3BCLG9CQUFvQjtJQUNwQixvQkFBb0I7SUFFcEIsY0FBYztJQUNkLFFBQVE7SUFDUixlQUFlO0lBQ2YsV0FBVztJQUNYLGNBQWM7SUFDZCxpQkFBaUI7SUFDakIsMEJBQTBCO0FBQzVCO0FBRUEsZUFBZTtBQUNSLFNBQVNDO0lBQ2QsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdOLCtDQUFRQSxDQUFjO0lBRXRELHVDQUF1QztJQUN2Q0MsZ0RBQVNBO29DQUFDO1lBQ1IsTUFBTU0sZ0JBQWdCQyxhQUFhQyxPQUFPLENBQUM7WUFDM0MsSUFBSUYsZUFBZTtnQkFDakJELFlBQVlDO1lBQ2Q7UUFDRjttQ0FBRyxFQUFFO0lBRUwsd0JBQXdCO0lBQ3hCLE1BQU1HLGlCQUFpQixDQUFDQztRQUN0QkwsWUFBWUs7UUFDWkgsYUFBYUksT0FBTyxDQUFDLHNCQUFzQkQ7SUFDN0M7SUFFQSxlQUFlO0lBQ2YsTUFBTUUsSUFBSSxDQUFDQztRQUNULE1BQU1DLGVBQWVWLGFBQWEsT0FBT0gsaUJBQWlCQztRQUMxRCxNQUFNYSxjQUFjRCxZQUFZLENBQUNELElBQUk7UUFFckMsSUFBSSxPQUFPRSxnQkFBZ0IsVUFBVTtZQUNuQyxPQUFPQTtRQUNUO1FBRUEseUNBQXlDO1FBQ3pDLE9BQU9GO0lBQ1Q7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTUcsV0FBV1osYUFBYTtJQUM5QixNQUFNYSxZQUFZYixhQUFhO0lBRS9CLE9BQU87UUFDTEE7UUFDQUs7UUFDQUc7UUFDQUk7UUFDQUM7SUFDRjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGtoYWxlXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFlBU01JTiBBTFNIQU1cXHlhc21pbi1hbHNoYW1cXHNyY1xcaG9va3NcXHVzZVRyYW5zbGF0aW9uLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xyXG5cclxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xyXG5cclxuLy8g2YbZiNi5INin2YTYqNmK2KfZhtin2Kog2YTZhNiq2LHYrNmF2KfYqlxyXG50eXBlIFRyYW5zbGF0aW9uS2V5ID0gc3RyaW5nXHJcbnR5cGUgVHJhbnNsYXRpb25WYWx1ZSA9IHN0cmluZyB8IHsgW2tleTogc3RyaW5nXTogc3RyaW5nIH1cclxudHlwZSBUcmFuc2xhdGlvbnMgPSB7IFtrZXk6IHN0cmluZ106IFRyYW5zbGF0aW9uVmFsdWUgfVxyXG5cclxuLy8g2KfZhNiq2LHYrNmF2KfYqiDYp9mE2LnYsdio2YrYqVxyXG5jb25zdCBhclRyYW5zbGF0aW9uczogVHJhbnNsYXRpb25zID0ge1xyXG4gIC8vINin2YTYqtmG2YLZhCDZiNin2YTYudmG2KfZiNmK2YYg2KfZhNix2KbZitiz2YrYqVxyXG4gICdkYXNoYm9hcmQnOiAn2YTZiNit2Kkg2KfZhNiq2K3Zg9mFJyxcclxuICAnb3JkZXJzJzogJ9in2YTYt9mE2KjYp9iqJyxcclxuICAnYXBwb2ludG1lbnRzJzogJ9in2YTZhdmI2KfYudmK2K8nLFxyXG4gICdzZXR0aW5ncyc6ICfYp9mE2KXYudiv2KfYr9in2KonLFxyXG4gICd3b3JrZXJzJzogJ9in2YTYudmF2KfZhCcsXHJcbiAgJ3JlcG9ydHMnOiAn2KfZhNiq2YLYp9ix2YrYsScsXHJcbiAgJ2xvZ291dCc6ICfYqtiz2KzZitmEINin2YTYrtix2YjYrCcsXHJcbiAgJ3dlbGNvbWUnOiAn2YXYsdit2KjYp9mLJyxcclxuICAnd2VsY29tZV9iYWNrJzogJ9mF2LHYrdio2KfZiyDYqNi52YjYr9iq2YMnLFxyXG4gIFxyXG4gIC8vINin2YTYo9iy2LHYp9ixINmI2KfZhNil2KzYsdin2KHYp9iqXHJcbiAgJ2FkZF9uZXdfb3JkZXInOiAn2KXYttin2YHYqSDYt9mE2Kgg2KzYr9mK2K8nLFxyXG4gICdib29rX2FwcG9pbnRtZW50JzogJ9it2KzYsiDZhdmI2LnYrycsXHJcbiAgJ3ZpZXdfZGV0YWlscyc6ICfYudix2LYg2KfZhNiq2YHYp9i12YrZhCcsXHJcbiAgJ2VkaXQnOiAn2KrYudiv2YrZhCcsXHJcbiAgJ2RlbGV0ZSc6ICfYrdiw2YEnLFxyXG4gICdzYXZlJzogJ9it2YHYuCcsXHJcbiAgJ2NhbmNlbCc6ICfYpdmE2LrYp9ihJyxcclxuICAnc3VibWl0JzogJ9il2LHYs9in2YQnLFxyXG4gICdzZWFyY2gnOiAn2KjYrdirJyxcclxuICAnZmlsdGVyJzogJ9iq2LXZgdmK2KknLFxyXG4gICdleHBvcnQnOiAn2KrYtdiv2YrYsScsXHJcbiAgJ3ByaW50JzogJ9i32KjYp9i52KknLFxyXG4gICdiYWNrJzogJ9ix2KzZiNi5JyxcclxuICAnbmV4dCc6ICfYp9mE2KrYp9mE2YonLFxyXG4gICdwcmV2aW91cyc6ICfYp9mE2LPYp9io2YInLFxyXG4gICdjbG9zZSc6ICfYpdi62YTYp9mCJyxcclxuICAnY29uZmlybSc6ICfYqtij2YPZitivJyxcclxuICAnbG9hZGluZyc6ICfYrNin2LHZiiDYp9mE2KrYrdmF2YrZhC4uLicsXHJcbiAgJ3NhdmluZyc6ICfYrNin2LHZiiDYp9mE2K3Zgdi4Li4uJyxcclxuICBcclxuICAvLyDYrdin2YTYp9iqINin2YTYt9mE2KjYp9iqXHJcbiAgJ3BlbmRpbmcnOiAn2YHZiiDYp9mE2KfZhtiq2LjYp9ixJyxcclxuICAnaW5fcHJvZ3Jlc3MnOiAn2YLZitivINin2YTYqtmG2YHZitiwJyxcclxuICAnY29tcGxldGVkJzogJ9mF2YPYqtmF2YQnLFxyXG4gICdkZWxpdmVyZWQnOiAn2KrZhSDYp9mE2KrYs9mE2YrZhScsXHJcbiAgJ2NhbmNlbGxlZCc6ICfZhdmE2LrZiicsXHJcbiAgXHJcbiAgLy8g2YbYtdmI2LUg2LnYp9mF2KlcclxuICAnbmFtZSc6ICfYp9mE2KfYs9mFJyxcclxuICAnZW1haWwnOiAn2KfZhNio2LHZitivINin2YTYpdmE2YPYqtix2YjZhtmKJyxcclxuICAncGhvbmUnOiAn2LHZgtmFINin2YTZh9in2KrZgScsXHJcbiAgJ2FkZHJlc3MnOiAn2KfZhNi52YbZiNin2YYnLFxyXG4gICdkYXRlJzogJ9in2YTYqtin2LHZitiuJyxcclxuICAndGltZSc6ICfYp9mE2YjZgtiqJyxcclxuICAnc3RhdHVzJzogJ9in2YTYrdin2YTYqScsXHJcbiAgJ3ByaWNlJzogJ9in2YTYs9i52LEnLFxyXG4gICd0b3RhbCc6ICfYp9mE2YXYrNmF2YjYuScsXHJcbiAgJ2Rlc2NyaXB0aW9uJzogJ9in2YTZiNi12YEnLFxyXG4gICdub3Rlcyc6ICfZhdmE2KfYrdi42KfYqicsXHJcbiAgJ2NsaWVudF9uYW1lJzogJ9in2LPZhSDYp9mE2LLYqNmI2YbYqScsXHJcbiAgJ2NsaWVudF9waG9uZSc6ICfYsdmC2YUg2YfYp9iq2YEg2KfZhNiy2KjZiNmG2KknLFxyXG4gIFxyXG4gIC8vINix2LPYp9im2YQg2KfZhNmG2KzYp9itINmI2KfZhNiu2LfYo1xyXG4gICdzdWNjZXNzJzogJ9mG2KzYrScsXHJcbiAgJ2Vycm9yJzogJ9iu2LfYoycsXHJcbiAgJ3dhcm5pbmcnOiAn2KrYrdiw2YrYsScsXHJcbiAgJ2luZm8nOiAn2YXYudmE2YjZhdin2KonLFxyXG4gICdvcmRlcl9hZGRlZF9zdWNjZXNzJzogJ9iq2YUg2KXYttin2YHYqSDYp9mE2LfZhNioINio2YbYrNin2K0nLFxyXG4gICdvcmRlcl91cGRhdGVkX3N1Y2Nlc3MnOiAn2KrZhSDYqtit2K/ZitirINin2YTYt9mE2Kgg2KjZhtis2KfYrScsXHJcbiAgJ29yZGVyX2RlbGV0ZWRfc3VjY2Vzcyc6ICfYqtmFINit2LDZgSDYp9mE2LfZhNioINio2YbYrNin2K0nLFxyXG4gICdmaWxsX3JlcXVpcmVkX2ZpZWxkcyc6ICfZitix2KzZiSDZhdmE2KEg2KzZhdmK2Lkg2KfZhNit2YLZiNmEINin2YTZhdi32YTZiNio2KknLFxyXG5cclxuICAvLyDZhti12YjYtSDYpdi22KfZgdmK2Kkg2YXYt9mE2YjYqNipXHJcbiAgJ2FkbWluX2Rhc2hib2FyZCc6ICfZhNmI2K3YqSDYqtit2YPZhSDYp9mE2YXYr9mK2LEnLFxyXG4gICd3b3JrZXJfZGFzaGJvYXJkJzogJ9mE2YjYrdipINiq2K3Zg9mFINin2YTYudin2YXZhCcsXHJcbiAgJ2FkbWluJzogJ9mF2K/ZitixJyxcclxuICAnd29ya2VyJzogJ9i52KfZhdmEJyxcclxuICAnY2hhbmdlX2xhbmd1YWdlJzogJ9iq2LrZitmK2LEg2KfZhNmE2LrYqScsXHJcbiAgJ215X2FjdGl2ZV9vcmRlcnMnOiAn2LfZhNio2KfYqtmKINin2YTZhti02LfYqScsXHJcbiAgJ2NvbXBsZXRlZF9vcmRlcnMnOiAn2KfZhNi32YTYqNin2Kog2KfZhNmF2YPYqtmF2YTYqScsXHJcbiAgJ3RvdGFsX29yZGVycyc6ICfYpdis2YXYp9mE2Yog2KfZhNi32YTYqNin2KonLFxyXG4gICd0b3RhbF9yZXZlbnVlJzogJ9il2KzZhdin2YTZiiDYp9mE2KXZitix2KfYr9in2KonLFxyXG4gICdyZWNlbnRfb3JkZXJzJzogJ9in2YTYt9mE2KjYp9iqINin2YTYrdiv2YrYq9ipJyxcclxuICAncXVpY2tfYWN0aW9ucyc6ICfYpdis2LHYp9ih2KfYqiDYs9ix2YrYudipJyxcclxuICAndmlld19hbGxfb3JkZXJzJzogJ9i52LHYtiDYrNmF2YrYuSDYp9mE2LfZhNio2KfYqicsXHJcbiAgJ2FkZF9vcmRlcic6ICfYpdi22KfZgdipINi32YTYqCcsXHJcbiAgJ21hbmFnZV93b3JrZXJzJzogJ9il2K/Yp9ix2Kkg2KfZhNi52YXYp9mEJyxcclxuICAndmlld19yZXBvcnRzJzogJ9i52LHYtiDYp9mE2KrZgtin2LHZitixJyxcclxuICAnY2xpZW50X25hbWVfcmVxdWlyZWQnOiAn2KfYs9mFINin2YTYstio2YjZhtipIConLFxyXG4gICdwaG9uZV9yZXF1aXJlZCc6ICfYsdmC2YUg2KfZhNmH2KfYqtmBIConLFxyXG4gICdvcmRlcl9kZXNjcmlwdGlvbl9yZXF1aXJlZCc6ICfZiNi12YEg2KfZhNi32YTYqCAqJyxcclxuICAnZGVsaXZlcnlfZGF0ZV9yZXF1aXJlZCc6ICfZhdmI2LnYryDYp9mE2KrYs9mE2YrZhSAqJyxcclxuICAncHJpY2Vfc2FyJzogJ9in2YTYs9i52LEgKNix2YrYp9mEINiz2LnZiNiv2YopJyxcclxuICAnbWVhc3VyZW1lbnRzX2NtJzogJ9in2YTZhdmC2KfYs9in2KogKNio2KfZhNiz2YbYqtmK2YXYqtixKScsXHJcbiAgJ2FkZGl0aW9uYWxfbm90ZXMnOiAn2YXZhNin2K3YuNin2Kog2KXYttin2YHZitipJyxcclxuICAndm9pY2Vfbm90ZXNfb3B0aW9uYWwnOiAn2YXZhNin2K3YuNin2Kog2LXZiNiq2YrYqSAo2KfYrtiq2YrYp9ix2YrYqSknLFxyXG4gICdkZXNpZ25faW1hZ2VzJzogJ9i12YjYsSDYp9mE2KrYtdmF2YrZhScsXHJcbiAgJ2ZhYnJpY190eXBlJzogJ9mG2YjYuSDYp9mE2YLZhdin2LQnLFxyXG4gICdmYWJyaWNfdHlwZV9vcHRpb25hbCc6ICfZhtmI2Lkg2KfZhNmC2YXYp9i0JyxcclxuICAncmVzcG9uc2libGVfd29ya2VyJzogJ9in2YTYudin2YXZhCDYp9mE2YXYs9ik2YjZhCcsXHJcbiAgJ2Nob29zZV93b3JrZXInOiAn2KfYrtiq2LEg2KfZhNi52KfZhdmEINin2YTZhdiz2KTZiNmEJyxcclxuICAnc3RhdHVzX2FuZF93b3JrZXInOiAn2KfZhNit2KfZhNipINmI2KfZhNi52KfZhdmEJyxcclxuICAnb3JkZXJfc3RhdHVzJzogJ9it2KfZhNipINin2YTYt9mE2KgnLFxyXG4gICdhZGRpdGlvbmFsX25vdGVzX3BsYWNlaG9sZGVyJzogJ9ij2Yog2YXZhNin2K3YuNin2Kog2KPZiCDYqtmB2KfYtdmK2YQg2KXYttin2YHZitipLi4uJyxcclxuICAnbm90X3NwZWNpZmllZCc6ICfYutmK2LEg2YXYrdiv2K8nLFxyXG4gICdiYWNrX3RvX2Rhc2hib2FyZCc6ICfYp9mE2LnZiNiv2Kkg2KXZhNmJINmE2YjYrdipINin2YTYqtit2YPZhScsXHJcbiAgJ292ZXJ2aWV3X3RvZGF5JzogJ9mG2LjYsdipINi52KfZhdipINi52YTZiSDYo9mG2LTYt9ipINin2YTZitmI2YUnLFxyXG4gICd3ZWxjb21lX3dvcmtlcic6ICfZhdix2K3YqNin2Ysg2KjZgyDZgdmKINmF2LPYp9it2Kkg2KfZhNi52YXZhCcsXHJcblxyXG4gIC8vINmF2YHYp9iq2YrYrSDZhdmB2YLZiNiv2Kkg2YXZhiDZhNmI2K3YqSDYp9mE2KrYrdmD2YVcclxuICAnaG9tZXBhZ2UnOiAn2KfZhNi12YHYrdipINin2YTYsdim2YrYs9mK2KknLFxyXG4gICdteV9jb21wbGV0ZWRfb3JkZXJzJzogJ9i32YTYqNin2KrZiiDYp9mE2YXZg9iq2YXZhNipJyxcclxuICAnbXlfdG90YWxfb3JkZXJzJzogJ9il2KzZhdin2YTZiiDYt9mE2KjYp9iq2YonLFxyXG4gICdhY3RpdmVfb3JkZXJzJzogJ9in2YTYt9mE2KjYp9iqINin2YTZhti02LfYqScsXHJcbiAgJ3RvZGF5X2FwcG9pbnRtZW50cyc6ICfZhdmI2KfYudmK2K8g2KfZhNmK2YjZhScsXHJcbiAgJ3N0YXRpc3RpY3MnOiAn2KfZhNil2K3Ytdin2KbZitin2KonLFxyXG4gICdub19vcmRlcnNfZm91bmQnOiAn2YTYpyDYqtmI2KzYryDYt9mE2KjYp9iqJyxcclxuICAndmlld19hbGwnOiAn2LnYsdi2INin2YTZg9mEJyxcclxuICAnd29ya2VyX21hbmFnZW1lbnQnOiAn2KXYr9in2LHYqSDYp9mE2LnZhdin2YQnLFxyXG4gICdyZW1pbmRlcic6ICfYqtiw2YPZitixJyxcclxuICAneW91X2hhdmUnOiAn2YTYr9mK2YMnLFxyXG4gICd0b2RheV9hcHBvaW50bWVudHNfcmVtaW5kZXInOiAn2YXZiNi52K8g2KfZhNmK2YjZhScsXHJcbiAgJ2FuZCc6ICfZiCcsXHJcbiAgJ29yZGVyc19uZWVkX2ZvbGxvdyc6ICfYt9mE2KjYp9iqINiq2K3Yqtin2Kwg2YXYqtin2KjYudipJyxcclxuICAnZGV0YWlsZWRfcmVwb3J0cyc6ICfYqtmC2KfYsdmK2LEg2YXZgdi12YTYqScsXHJcbiAgJ3dvcmtlcl9kZXNjcmlwdGlvbic6ICfZitmF2YPZhtmDINmH2YbYpyDZhdiq2KfYqNi52Kkg2LfZhNio2KfYqtmDINin2YTZhdiu2LXYtdipINmE2YMg2YjYqtit2K/ZitirINit2KfZhNiq2YfYpycsXHJcblxyXG4gIC8vINmF2YHYp9iq2YrYrSDYtdmB2K3YqSDYpdi22KfZgdipINin2YTYt9mE2KjYp9iqXHJcbiAgJ29yZGVyX2FkZF9lcnJvcic6ICfYrdiv2Ksg2K7Yt9ijINij2KvZhtin2KEg2KXYttin2YHYqSDYp9mE2LfZhNioJyxcclxuICAnY21fcGxhY2Vob2xkZXInOiAn2LPZhScsXHJcbiAgJ3Nob3VsZGVyJzogJ9in2YTZg9iq2YEnLFxyXG4gICdzaG91bGRlcl9jaXJjdW1mZXJlbmNlJzogJ9mF2K3Ziti3INin2YTZg9iq2YEnLFxyXG4gICdjaGVzdCc6ICfYp9mE2LXYr9ixJyxcclxuICAnd2Fpc3QnOiAn2KfZhNiu2LXYsScsXHJcbiAgJ2hpcHMnOiAn2KfZhNij2LHYr9in2YEnLFxyXG4gICdkYXJ0X2xlbmd0aCc6ICfYt9mI2YQg2KfZhNiu2YrYp9i32KknLFxyXG4gICdib2RpY2VfbGVuZ3RoJzogJ9i32YjZhCDYp9mE2KzYs9mFJyxcclxuICAnbmVja2xpbmUnOiAn2K7YtyDYp9mE2LHZgtio2KknLFxyXG4gICdhcm1waXQnOiAn2KfZhNil2KjYtycsXHJcbiAgJ3NsZWV2ZV9sZW5ndGgnOiAn2LfZiNmEINin2YTZg9mFJyxcclxuICAnZm9yZWFybSc6ICfYp9mE2LPYp9i52K8nLFxyXG4gICdjdWZmJzogJ9in2YTZg9mFJyxcclxuICAnZnJvbnRfbGVuZ3RoJzogJ9in2YTYt9mI2YQg2KfZhNij2YXYp9mF2YonLFxyXG4gICdiYWNrX2xlbmd0aCc6ICfYp9mE2LfZiNmEINin2YTYrtmE2YHZiicsXHJcblxyXG4gIC8vINmF2YHYp9iq2YrYrSDYtdmB2K3YqSDYp9mE2LfZhNio2KfYqlxyXG4gICdlbnRlcl9vcmRlcl9udW1iZXInOiAn2KPYr9iu2YQg2LHZgtmFINin2YTYt9mE2KgnLFxyXG4gICdzZWFyY2hfcGxhY2Vob2xkZXInOiAn2KfZhNio2K3YqyDYqNin2YTYp9iz2YUg2KPZiCDYsdmC2YUg2KfZhNi32YTYqCDYo9mIINin2YTZiNi12YEuLi4nLFxyXG4gICdzZWFyY2hfYnlfdGV4dCc6ICfYp9mE2KjYrdirINio2KfZhNmG2LUnLFxyXG4gICdzZWFyY2hfYnlfb3JkZXJfbnVtYmVyJzogJ9in2YTYqNit2Ksg2KjYsdmC2YUg2KfZhNi32YTYqCcsXHJcbiAgJ2FsbF9vcmRlcnMnOiAn2KzZhdmK2Lkg2KfZhNi32YTYqNin2KonLFxyXG4gICdub19vcmRlcnNfYXNzaWduZWQnOiAn2YTYpyDYqtmI2KzYryDYt9mE2KjYp9iqINmF2K7Ytdi12Kkg2YTZgycsXHJcbiAgJ25vX29yZGVyc19hc3NpZ25lZF9kZXNjJzogJ9mE2YUg2YrYqtmFINiq2K7YtdmK2LUg2KPZiiDYt9mE2KjYp9iqINmE2YMg2KjYudivJyxcclxuICAnbm9fb3JkZXJzX2ZvdW5kX2Rlc2MnOiAn2YTYpyDYqtmI2KzYryDYt9mE2KjYp9iqINmF2LfYp9io2YLYqSDZhNmF2LnYp9mK2YrYsSDYp9mE2KjYrdirJyxcclxuICAncHJpY2VfbGFiZWwnOiAn2KfZhNiz2LnYsScsXHJcbiAgJ3Nhcic6ICfYsdmK2KfZhCcsXHJcbiAgJ3ZpZXcnOiAn2LnYsdi2JyxcclxuICAnY29tcGxldGluZyc6ICfYrNin2LHZiiDYp9mE2KXZhtmH2KfYoS4uLicsXHJcbiAgJ3N0YXJ0X3dvcmsnOiAn2KjYr9ihINin2YTYudmF2YQnLFxyXG4gICdjb21wbGV0ZV9vcmRlcic6ICfYpdmG2YfYp9ihINin2YTYt9mE2KgnLFxyXG4gICdjb21wbGV0ZV9vcmRlcl9tb2RhbF90aXRsZSc6ICfYpdmG2YfYp9ihINin2YTYt9mE2Kgg2YjYqtit2YXZitmEINi12YjYsSDYp9mE2LnZhdmEINin2YTZhdmD2KrZhdmEJyxcclxuICAnaW1wb3J0YW50X3dhcm5pbmcnOiAn2KrYrdiw2YrYsSDZhdmH2YUnLFxyXG4gICdjb21wbGV0ZV9vcmRlcl93YXJuaW5nJzogJ9io2YXYrNix2K8g2KXZhtmH2KfYoSDYp9mE2LfZhNio2Iwg2YTZhiDYqtiq2YXZg9mGINmF2YYg2KrYudiv2YrZhCDYrdin2YTYqtmHINmF2LHYqSDYo9iu2LHZiS4g2KrYo9mD2K8g2YXZhiDYqtit2YXZitmEINis2YXZiti5INi12YjYsSDYp9mE2LnZhdmEINin2YTZhdmD2KrZhdmEINmC2KjZhCDYp9mE2YXYqtin2KjYudipLicsXHJcbiAgJ29yZGVyX2RlbGV0ZWRfc3VjY2Vzc2Z1bGx5JzogJ9iq2YUg2K3YsNmBINin2YTYt9mE2Kgg2KjZhtis2KfYrScsXHJcblxyXG4gIC8vINmF2YHYp9iq2YrYrSDZhdmD2YjZhiDYrdiw2YEg2KfZhNi32YTYqFxyXG4gICdjb25maXJtX2RlbGV0ZV9vcmRlcic6ICfYqtij2YPZitivINit2LDZgSDYp9mE2LfZhNioJyxcclxuICAnd2FybmluZ19kZWxldGVfb3JkZXInOiAn2KrYrdiw2YrYsTog2K3YsNmBINin2YTYt9mE2KgnLFxyXG4gICdkZWxldGVfb3JkZXJfd2FybmluZ19tZXNzYWdlJzogJ9mE2Kcg2YrZhdmD2YYg2KfZhNiq2LHYp9is2Lkg2LnZhiDZh9iw2Kcg2KfZhNil2KzYsdin2KEuINiz2YrYqtmFINit2LDZgSDYp9mE2LfZhNioINmI2KzZhdmK2Lkg2KfZhNio2YrYp9mG2KfYqiDYp9mE2YXYsdiq2KjYt9ipINio2Ycg2YbZh9in2KbZitin2YsuJyxcclxuICAnYWRtaW5fZW1haWwnOiAn2KjYsdmK2K8g2KfZhNmF2K/ZitixINin2YTYpdmE2YPYqtix2YjZhtmKJyxcclxuICAnYWRtaW5fcGFzc3dvcmQnOiAn2YPZhNmF2Kkg2YXYsdmI2LEg2KfZhNmF2K/ZitixJyxcclxuICAnZW50ZXJfYWRtaW5fZW1haWwnOiAn2KPYr9iu2YQg2KjYsdmK2K8g2KfZhNmF2K/ZitixINin2YTYpdmE2YPYqtix2YjZhtmKJyxcclxuICAnZW50ZXJfYWRtaW5fcGFzc3dvcmQnOiAn2KPYr9iu2YQg2YPZhNmF2Kkg2YXYsdmI2LEg2KfZhNmF2K/ZitixJyxcclxuICAncGxlYXNlX2ZpbGxfYWxsX2ZpZWxkcyc6ICfZitix2KzZiSDZhdmE2KEg2KzZhdmK2Lkg2KfZhNit2YLZiNmEJyxcclxuICAnZW1haWxfZG9lc19ub3RfbWF0Y2gnOiAn2KfZhNio2LHZitivINin2YTYpdmE2YPYqtix2YjZhtmKINmE2Kcg2YrYt9in2KjZgiDYqNix2YrYryDYp9mE2YXYr9mK2LEg2KfZhNmF2LPYrNmEJyxcclxuICAnaW5jb3JyZWN0X3Bhc3N3b3JkJzogJ9mD2YTZhdipINin2YTZhdix2YjYsSDYutmK2LEg2LXYrdmK2K3YqScsXHJcbiAgJ2NvbmZpcm1fZGVsZXRlJzogJ9iq2KPZg9mK2K8g2KfZhNit2LDZgScsXHJcblxyXG4gIC8vINmF2YHYp9iq2YrYrSDZhdmD2YjZhiDYp9mE2YXZhNin2K3YuNin2Kog2KfZhNi12YjYqtmK2KlcclxuICAnc3RhcnRfcmVjb3JkaW5nJzogJ9io2K/YoSDYp9mE2KrYs9is2YrZhCcsXHJcbiAgJ3N0b3BfcmVjb3JkaW5nJzogJ9il2YrZgtin2YEg2KfZhNiq2LPYrNmK2YQnLFxyXG4gICdjbGlja190b19yZWNvcmRfdm9pY2Vfbm90ZSc6ICfYp9mG2YLYsSDZhNiq2LPYrNmK2YQg2YXZhNin2K3YuNipINi12YjYqtmK2KknLFxyXG4gICd2b2ljZV9ub3Rlcyc6ICfYp9mE2YXZhNin2K3YuNin2Kog2KfZhNi12YjYqtmK2KknLFxyXG4gICd2b2ljZV9ub3RlJzogJ9mF2YTYp9it2LjYqSDYtdmI2KrZitipJyxcclxuICAnbWljcm9waG9uZV9hY2Nlc3NfZXJyb3InOiAn2K7Yt9ijINmB2Yog2KfZhNmI2LXZiNmEINmE2YTZhdmK2YPYsdmI2YHZiNmGJyxcclxuXHJcbiAgLy8g2YXZgdin2KrZititINi12YHYrdipINin2YTYudmF2KfZhFxyXG4gICd3b3JrZXJfYWRkZWRfc3VjY2Vzcyc6ICfYqtmFINil2LbYp9mB2Kkg2KfZhNi52KfZhdmEINio2YbYrNin2K0nLFxyXG4gICdlcnJvcl9hZGRpbmdfd29ya2VyJzogJ9it2K/YqyDYrti32KMg2KPYq9mG2KfYoSDYpdi22KfZgdipINin2YTYudin2YXZhCcsXHJcbiAgJ3dvcmtlcl91cGRhdGVkX3N1Y2Nlc3MnOiAn2KrZhSDYqtit2K/ZitirINin2YTYudin2YXZhCDYqNmG2KzYp9itJyxcclxuICAnZXJyb3JfdXBkYXRpbmdfd29ya2VyJzogJ9it2K/YqyDYrti32KMg2KPYq9mG2KfYoSDYqtit2K/ZitirINin2YTYudin2YXZhCcsXHJcbiAgJ3dvcmtlcl9kZWxldGVkX3N1Y2Nlc3MnOiAn2KrZhSDYrdiw2YEg2KfZhNi52KfZhdmEINio2YbYrNin2K0nLFxyXG4gICd3b3JrZXJfZGVhY3RpdmF0ZWQnOiAn2KrZhSDYpdmE2LrYp9ihINiq2YHYudmK2YQg2KfZhNi52KfZhdmEJyxcclxuICAnd29ya2VyX2FjdGl2YXRlZCc6ICfYqtmFINiq2YHYudmK2YQg2KfZhNi52KfZhdmEJyxcclxuICAnYWRkaW5nJzogJ9is2KfYsdmKINin2YTYpdi22KfZgdipLi4uJyxcclxuICAnYWRkX3dvcmtlcic6ICfYpdi22KfZgdipINi52KfZhdmEJyxcclxuICAnYWN0aXZlJzogJ9mG2LTYtycsXHJcbiAgJ2luYWN0aXZlJzogJ9i62YrYsSDZhti02LcnLFxyXG4gICdzYXZlX2NoYW5nZXMnOiAn2K3Zgdi4INin2YTYqti62YrZitix2KfYqicsXHJcbiAgJ3NlYXJjaF93b3JrZXJzX3BsYWNlaG9sZGVyJzogJ9in2YTYqNit2Ksg2LnZhiDYp9mE2LnZhdin2YQuLi4nLFxyXG4gICd3b3JrZXJzX21hbmFnZW1lbnQnOiAn2KXYr9in2LHYqSDYp9mE2LnZhdin2YQnLFxyXG4gICd2aWV3X21hbmFnZV90ZWFtJzogJ9i52LHYtiDZiNil2K/Yp9ix2Kkg2YHYsdmK2YIg2KfZhNi52YXZhCDZgdmKINmI2LHYtNipINin2YTYqtmB2LXZitmEJyxcclxuICAnYWRkX25ld193b3JrZXInOiAn2KXYttin2YHYqSDYudin2YXZhCDYrNiv2YrYrycsXHJcbiAgJ2FkZF9uZXdfd29ya2VyX2Zvcm0nOiAn2KXYttin2YHYqSDYudin2YXZhCDYrNiv2YrYrycsXHJcbiAgJ2Z1bGxfbmFtZV9yZXF1aXJlZCc6ICfYp9mE2KfYs9mFINin2YTZg9in2YXZhCAqJyxcclxuICAnZW1haWxfcmVxdWlyZWQnOiAn2KfZhNio2LHZitivINin2YTYpdmE2YPYqtix2YjZhtmKIConLFxyXG4gICdwYXNzd29yZF9yZXF1aXJlZCc6ICfZg9mE2YXYqSDYp9mE2YXYsdmI2LEgKicsXHJcbiAgJ3Bob25lX3JlcXVpcmVkX3dvcmtlcic6ICfYsdmC2YUg2KfZhNmH2KfYqtmBIConLFxyXG4gICdlbnRlcl9mdWxsX25hbWUnOiAn2KPYr9iu2YQg2KfZhNin2LPZhSDYp9mE2YPYp9mF2YQnLFxyXG4gICdlbnRlcl9lbWFpbCc6ICfYo9iv2K7ZhCDYp9mE2KjYsdmK2K8g2KfZhNil2YTZg9iq2LHZiNmG2YonLFxyXG4gICdlbnRlcl9wYXNzd29yZCc6ICfYo9iv2K7ZhCDZg9mE2YXYqSDYp9mE2YXYsdmI2LEnLFxyXG4gICdlbnRlcl9waG9uZSc6ICfYo9iv2K7ZhCDYsdmC2YUg2KfZhNmH2KfYqtmBJyxcclxuICAnc3BlY2lhbHR5X3JlcXVpcmVkJzogJ9in2YTYqtiu2LXYtSAqJyxcclxuICAnc3BlY2lhbHR5X2V4YW1wbGUnOiAn2YXYq9in2YQ6INiu2YrYp9i32Kkg2YHYs9in2KrZitmGINin2YTYs9mH2LHYqScsXHJcbiAgJ2VkaXRfd29ya2VyJzogJ9iq2LnYr9mK2YQg2KfZhNi52KfZhdmEJyxcclxuICAnbmV3X3Bhc3N3b3JkJzogJ9mD2YTZhdipINin2YTZhdix2YjYsSDYp9mE2KzYr9mK2K/YqScsXHJcbiAgJ2xlYXZlX2VtcHR5X25vX2NoYW5nZSc6ICfYp9iq2LHZg9mHINmB2KfYsdi62KfZiyDYpdiw2Kcg2YTZhSDYqtix2K8g2KrYutmK2YrYsdmHJyxcclxuICAnbm9fd29ya2Vycyc6ICfZhNinINmK2YjYrNivINi52YXYp9mEJyxcclxuICAnbm9fd29ya2Vyc19mb3VuZCc6ICfZhNinINmK2YjYrNivINi52YXYp9mEINmF2LfYp9io2YLZitmGINmE2YXYudin2YrZitixINin2YTYqNit2KsnLFxyXG4gICdqb2luZWRfb24nOiAn2KfZhti22YUg2YHZiicsXHJcbiAgJ3RvdGFsX3dvcmtlcnMnOiAn2KXYrNmF2KfZhNmKINin2YTYudmF2KfZhCcsXHJcbiAgJ2FjdGl2ZV93b3JrZXJzJzogJ9in2YTYudmF2KfZhCDYp9mE2YbYtNi32YjZhicsXHJcbiAgJ3RvdGFsX2NvbXBsZXRlZF9vcmRlcnMnOiAn2KXYrNmF2KfZhNmKINin2YTYt9mE2KjYp9iqINin2YTZhdmD2KrZhdmE2KknLFxyXG4gICdjb25maXJtX2RlbGV0ZV93b3JrZXInOiAn2YfZhCDYo9mG2Kog2YXYqtij2YPYryDZhdmGINit2LDZgSDZh9iw2Kcg2KfZhNi52KfZhdmE2J8nLFxyXG5cclxuICAvLyDZhdmB2KfYqtmK2K0g2LXZgdit2Kkg2KfZhNiq2YLYp9ix2YrYsVxyXG4gICdlbmdhZ2VtZW50X2RyZXNzJzogJ9mB2LPYqtin2YYg2K7Yt9mI2KjYqScsXHJcbiAgJ2Nhc3VhbF9kcmVzcyc6ICfZgdiz2KrYp9mGINmK2YjZhdmKJyxcclxuICAnb3RoZXInOiAn2KPYrtix2YknLFxyXG4gICd0aGlzX3dlZWsnOiAn2YfYsNinINin2YTYo9iz2KjZiNi5JyxcclxuICAndGhpc19tb250aCc6ICfZh9iw2Kcg2KfZhNi02YfYsScsXHJcbiAgJ3RoaXNfcXVhcnRlcic6ICfZh9iw2Kcg2KfZhNix2KjYuScsXHJcbiAgJ3RoaXNfeWVhcic6ICfZh9iw2Kcg2KfZhNi52KfZhScsXHJcbiAgJ3JlcG9ydHNfYW5hbHl0aWNzJzogJ9in2YTYqtmC2KfYsdmK2LEg2YjYp9mE2KrYrdmE2YrZhNin2KonLFxyXG4gICdtb250aGx5X3RyZW5kJzogJ9in2YTYp9iq2KzYp9mHINin2YTYtNmH2LHZiicsXHJcbiAgJ3JldmVudWVfbGFiZWwnOiAn2KfZhNil2YrYsdin2K/Yp9iqJyxcclxuXHJcbiAgLy8g2YXZgdin2KrZititINi12YHYrdipINin2YTZhdmI2KfYudmK2K9cclxuICAnY29uZmlybWVkJzogJ9mF2KTZg9ivJyxcclxuICAncG0nOiAn2YXYs9in2KHZiycsXHJcbiAgJ2FtJzogJ9i12KjYp9it2KfZiycsXHJcbiAgJ2FsbF9zdGF0dXNlcyc6ICfYrNmF2YrYuSDYp9mE2K3Yp9mE2KfYqicsXHJcbiAgJ2FsbF9kYXRlcyc6ICfYrNmF2YrYuSDYp9mE2KrZiNin2LHZitiuJyxcclxuICAndG9kYXknOiAn2KfZhNmK2YjZhScsXHJcbiAgJ3RvbW9ycm93JzogJ9i62K/Yp9mLJyxcclxuICAnYXBwb2ludG1lbnRzX21hbmFnZW1lbnQnOiAn2KXYr9in2LHYqSDYp9mE2YXZiNin2LnZitivJyxcclxuICAndmlld19tYW5hZ2VfYXBwb2ludG1lbnRzJzogJ9i52LHYtiDZiNil2K/Yp9ix2Kkg2KzZhdmK2Lkg2YXZiNin2LnZitivINin2YTYqtmB2LXZitmEJyxcclxuICAnYm9va19uZXdfYXBwb2ludG1lbnQnOiAn2K3YrNiyINmF2YjYudivINis2K/ZitivJyxcclxuICAnc2VhcmNoX2FwcG9pbnRtZW50c19wbGFjZWhvbGRlcic6ICfYp9mE2KjYrdirINmB2Yog2KfZhNmF2YjYp9i52YrYry4uLicsXHJcbiAgJ25vX2FwcG9pbnRtZW50cyc6ICfZhNinINiq2YjYrNivINmF2YjYp9i52YrYrycsXHJcbiAgJ25vX2FwcG9pbnRtZW50c19mb3VuZCc6ICfZhNinINiq2YjYrNivINmF2YjYp9i52YrYryDZhdi32KfYqNmC2Kkg2YTZhdi52KfZitmK2LEg2KfZhNio2K3YqycsXHJcbiAgJ2NyZWF0ZWRfb24nOiAn2KrZhSDYp9mE2KXZhti02KfYoSDZgdmKJyxcclxuICAnY29uZmlybV9hcHBvaW50bWVudCc6ICfYqtij2YPZitivINin2YTZhdmI2LnYrycsXHJcbiAgJ2NhbmNlbF9hcHBvaW50bWVudCc6ICfYpdmE2LrYp9ihINin2YTZhdmI2LnYrycsXHJcblxyXG4gIC8vINmF2YHYp9iq2YrYrSDZhdmD2YjZhtin2Kog2KXYttin2YHZitipXHJcbiAgJ29mJzogJ9mF2YYnLFxyXG4gICdpbWFnZXNfdGV4dCc6ICfYtdmI2LEnLFxyXG5cclxuICAvLyDZhdmB2KfYqtmK2K0g2YXZg9mI2YbYp9iqINin2YTYtdmI2LEg2YjYp9mE2KrYrdmF2YrZhFxyXG4gICdtYXhfaW1hZ2VzX3JlYWNoZWQnOiAn2KrZhSDYp9mE2YjYtdmI2YQg2YTZhNit2K8g2KfZhNij2YLYtdmJINmF2YYg2KfZhNi12YjYsScsXHJcbiAgJ2Ryb3BfaW1hZ2VzX2hlcmUnOiAn2KfYs9mC2Lcg2KfZhNi12YjYsSDZh9mG2KcnLFxyXG4gICdjbGlja19vcl9kcmFnX2ltYWdlcyc6ICfYp9mG2YLYsSDYo9mIINin2LPYrdioINin2YTYtdmI2LEg2YfZhtinJyxcclxuICAnaW1hZ2VfdXBsb2FkX2Zvcm1hdCc6ICdQTkcsIEpQRywgSlBFRyDYrdiq2YkgNU1CJyxcclxuICAnbWF4X2ltYWdlc190ZXh0JzogJ9in2YTYrdivINin2YTYo9mC2LXZiScsXHJcbiAgJ29yZGVyX2xhYmVsJzogJ9in2YTYt9mE2KgnLFxyXG4gICdmb3JfY2xpZW50JzogJ9mE2YTYstio2YjZhtipJyxcclxuXHJcbiAgLy8g2YXZgdin2KrZititINmF2YPZiNmGINiq2LnYr9mK2YQg2KfZhNi32YTYqFxyXG4gICdvcmRlcl91cGRhdGVfZXJyb3InOiAn2K3Yr9irINiu2LfYoyDYo9ir2YbYp9ihINiq2K3Yr9mK2Ksg2KfZhNi32YTYqCcsXHJcbiAgJ3ByaWNlX3Nhcl9yZXF1aXJlZCc6ICfYp9mE2LPYudixICjYsdmK2KfZhCDYs9i52YjYr9mKKSAqJyxcclxuICAnc3RhdHVzX3BlbmRpbmcnOiAn2YHZiiDYp9mE2KfZhtiq2LjYp9ixJyxcclxuICAnc3RhdHVzX2luX3Byb2dyZXNzJzogJ9mC2YrYryDYp9mE2KrZhtmB2YrYsCcsXHJcbiAgJ3N0YXR1c19jb21wbGV0ZWQnOiAn2YXZg9iq2YXZhCcsXHJcbiAgJ3N0YXR1c19kZWxpdmVyZWQnOiAn2KrZhSDYp9mE2KrYs9mE2YrZhScsXHJcbiAgJ3N0YXR1c19jYW5jZWxsZWQnOiAn2YXZhNi62YonLFxyXG5cclxuICAvLyDZhti12YjYtSDYp9mE2YHZiNiq2LFcclxuICAnaG9tZSc6ICfYp9mE2LHYptmK2LPZitipJyxcclxuICAndHJhY2tfb3JkZXInOiAn2KfYs9iq2LnZhNin2YUg2LnZhiDYp9mE2LfZhNioJyxcclxuICAnZmFicmljcyc6ICfYp9mE2KPZgtmF2LTYqScsXHJcbiAgJ2NvbnRhY3RfdXMnOiAn2KrZiNin2LXZhNmKINmF2LnZhtinJyxcclxuICAneWFzbWluX2Fsc2hhbSc6ICfZitin2LPZhdmK2YYg2KfZhNi02KfZhScsXHJcbiAgJ2N1c3RvbV9kcmVzc190YWlsb3JpbmcnOiAn2KrZgdi12YrZhCDZgdiz2KfYqtmK2YYg2K3Ys9ioINin2YTYt9mE2KgnXHJcbn1cclxuXHJcbi8vINin2YTYqtix2KzZhdin2Kog2KfZhNil2YbYrNmE2YrYstmK2KlcclxuY29uc3QgZW5UcmFuc2xhdGlvbnM6IFRyYW5zbGF0aW9ucyA9IHtcclxuICAvLyDYp9mE2KrZhtmC2YQg2YjYp9mE2LnZhtin2YjZitmGINin2YTYsdim2YrYs9mK2KlcclxuICAnZGFzaGJvYXJkJzogJ0Rhc2hib2FyZCcsXHJcbiAgJ29yZGVycyc6ICdPcmRlcnMnLFxyXG4gICdhcHBvaW50bWVudHMnOiAnQXBwb2ludG1lbnRzJyxcclxuICAnc2V0dGluZ3MnOiAnU2V0dGluZ3MnLFxyXG4gICd3b3JrZXJzJzogJ1dvcmtlcnMnLFxyXG4gICdyZXBvcnRzJzogJ1JlcG9ydHMnLFxyXG4gICdsb2dvdXQnOiAnTG9nb3V0JyxcclxuICAnd2VsY29tZSc6ICdXZWxjb21lJyxcclxuICAnd2VsY29tZV9iYWNrJzogJ1dlbGNvbWUgQmFjaycsXHJcbiAgXHJcbiAgLy8g2KfZhNij2LLYsdin2LEg2YjYp9mE2KXYrNix2KfYodin2KpcclxuICAnYWRkX25ld19vcmRlcic6ICdBZGQgTmV3IE9yZGVyJyxcclxuICAnYm9va19hcHBvaW50bWVudCc6ICdCb29rIEFwcG9pbnRtZW50JyxcclxuICAndmlld19kZXRhaWxzJzogJ1ZpZXcgRGV0YWlscycsXHJcbiAgJ2VkaXQnOiAnRWRpdCcsXHJcbiAgJ2RlbGV0ZSc6ICdEZWxldGUnLFxyXG4gICdzYXZlJzogJ1NhdmUnLFxyXG4gICdjYW5jZWwnOiAnQ2FuY2VsJyxcclxuICAnc3VibWl0JzogJ1N1Ym1pdCcsXHJcbiAgJ3NlYXJjaCc6ICdTZWFyY2gnLFxyXG4gICdmaWx0ZXInOiAnRmlsdGVyJyxcclxuICAnZXhwb3J0JzogJ0V4cG9ydCcsXHJcbiAgJ3ByaW50JzogJ1ByaW50JyxcclxuICAnYmFjayc6ICdCYWNrJyxcclxuICAnbmV4dCc6ICdOZXh0JyxcclxuICAncHJldmlvdXMnOiAnUHJldmlvdXMnLFxyXG4gICdjbG9zZSc6ICdDbG9zZScsXHJcbiAgJ2NvbmZpcm0nOiAnQ29uZmlybScsXHJcbiAgJ2xvYWRpbmcnOiAnTG9hZGluZy4uLicsXHJcbiAgJ3NhdmluZyc6ICdTYXZpbmcuLi4nLFxyXG4gIFxyXG4gIC8vINit2KfZhNin2Kog2KfZhNi32YTYqNin2KpcclxuICAncGVuZGluZyc6ICdQZW5kaW5nJyxcclxuICAnaW5fcHJvZ3Jlc3MnOiAnSW4gUHJvZ3Jlc3MnLFxyXG4gICdjb21wbGV0ZWQnOiAnQ29tcGxldGVkJyxcclxuICAnZGVsaXZlcmVkJzogJ0RlbGl2ZXJlZCcsXHJcbiAgJ2NhbmNlbGxlZCc6ICdDYW5jZWxsZWQnLFxyXG4gIFxyXG4gIC8vINmG2LXZiNi1INi52KfZhdipXHJcbiAgJ25hbWUnOiAnTmFtZScsXHJcbiAgJ2VtYWlsJzogJ0VtYWlsJyxcclxuICAncGhvbmUnOiAnUGhvbmUnLFxyXG4gICdhZGRyZXNzJzogJ0FkZHJlc3MnLFxyXG4gICdkYXRlJzogJ0RhdGUnLFxyXG4gICd0aW1lJzogJ1RpbWUnLFxyXG4gICdzdGF0dXMnOiAnU3RhdHVzJyxcclxuICAncHJpY2UnOiAnUHJpY2UnLFxyXG4gICd0b3RhbCc6ICdUb3RhbCcsXHJcbiAgJ2Rlc2NyaXB0aW9uJzogJ0Rlc2NyaXB0aW9uJyxcclxuICAnbm90ZXMnOiAnTm90ZXMnLFxyXG4gICdjbGllbnRfbmFtZSc6ICdDbGllbnQgTmFtZScsXHJcbiAgJ2NsaWVudF9waG9uZSc6ICdDbGllbnQgUGhvbmUnLFxyXG4gIFxyXG4gIC8vINix2LPYp9im2YQg2KfZhNmG2KzYp9itINmI2KfZhNiu2LfYo1xyXG4gICdzdWNjZXNzJzogJ1N1Y2Nlc3MnLFxyXG4gICdlcnJvcic6ICdFcnJvcicsXHJcbiAgJ3dhcm5pbmcnOiAnV2FybmluZycsXHJcbiAgJ2luZm8nOiAnSW5mbycsXHJcbiAgJ29yZGVyX2FkZGVkX3N1Y2Nlc3MnOiAnT3JkZXIgYWRkZWQgc3VjY2Vzc2Z1bGx5JyxcclxuICAnb3JkZXJfdXBkYXRlZF9zdWNjZXNzJzogJ09yZGVyIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5JyxcclxuICAnb3JkZXJfZGVsZXRlZF9zdWNjZXNzJzogJ09yZGVyIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5JyxcclxuICAnZmlsbF9yZXF1aXJlZF9maWVsZHMnOiAnUGxlYXNlIGZpbGwgYWxsIHJlcXVpcmVkIGZpZWxkcycsXHJcblxyXG4gIC8vINmG2LXZiNi1INil2LbYp9mB2YrYqSDZhdi32YTZiNio2KlcclxuICAnYWRtaW5fZGFzaGJvYXJkJzogJ0FkbWluIERhc2hib2FyZCcsXHJcbiAgJ3dvcmtlcl9kYXNoYm9hcmQnOiAnV29ya2VyIERhc2hib2FyZCcsXHJcbiAgJ2FkbWluJzogJ0FkbWluJyxcclxuICAnd29ya2VyJzogJ1dvcmtlcicsXHJcbiAgJ2NoYW5nZV9sYW5ndWFnZSc6ICdDaGFuZ2UgTGFuZ3VhZ2UnLFxyXG4gICdteV9hY3RpdmVfb3JkZXJzJzogJ015IEFjdGl2ZSBPcmRlcnMnLFxyXG4gICdjb21wbGV0ZWRfb3JkZXJzJzogJ0NvbXBsZXRlZCBPcmRlcnMnLFxyXG4gICd0b3RhbF9vcmRlcnMnOiAnVG90YWwgT3JkZXJzJyxcclxuICAndG90YWxfcmV2ZW51ZSc6ICdUb3RhbCBSZXZlbnVlJyxcclxuICAncmVjZW50X29yZGVycyc6ICdSZWNlbnQgT3JkZXJzJyxcclxuICAncXVpY2tfYWN0aW9ucyc6ICdRdWljayBBY3Rpb25zJyxcclxuICAndmlld19hbGxfb3JkZXJzJzogJ1ZpZXcgQWxsIE9yZGVycycsXHJcbiAgJ2FkZF9vcmRlcic6ICdBZGQgT3JkZXInLFxyXG4gICdtYW5hZ2Vfd29ya2Vycyc6ICdNYW5hZ2UgV29ya2VycycsXHJcbiAgJ3ZpZXdfcmVwb3J0cyc6ICdWaWV3IFJlcG9ydHMnLFxyXG4gICdjbGllbnRfbmFtZV9yZXF1aXJlZCc6ICdDbGllbnQgTmFtZSAqJyxcclxuICAncGhvbmVfcmVxdWlyZWQnOiAnUGhvbmUgTnVtYmVyIConLFxyXG4gICdvcmRlcl9kZXNjcmlwdGlvbl9yZXF1aXJlZCc6ICdPcmRlciBEZXNjcmlwdGlvbiAqJyxcclxuICAnZGVsaXZlcnlfZGF0ZV9yZXF1aXJlZCc6ICdEZWxpdmVyeSBEYXRlIConLFxyXG4gICdwcmljZV9zYXInOiAnUHJpY2UgKFNBUiknLFxyXG4gICdtZWFzdXJlbWVudHNfY20nOiAnTWVhc3VyZW1lbnRzIChjbSknLFxyXG4gICdhZGRpdGlvbmFsX25vdGVzJzogJ0FkZGl0aW9uYWwgTm90ZXMnLFxyXG4gICd2b2ljZV9ub3Rlc19vcHRpb25hbCc6ICdWb2ljZSBOb3RlcyAoT3B0aW9uYWwpJyxcclxuICAnZGVzaWduX2ltYWdlcyc6ICdEZXNpZ24gSW1hZ2VzJyxcclxuICAnZmFicmljX3R5cGUnOiAnRmFicmljIFR5cGUnLFxyXG4gICdmYWJyaWNfdHlwZV9vcHRpb25hbCc6ICdGYWJyaWMgVHlwZScsXHJcbiAgJ3Jlc3BvbnNpYmxlX3dvcmtlcic6ICdSZXNwb25zaWJsZSBXb3JrZXInLFxyXG4gICdjaG9vc2Vfd29ya2VyJzogJ0Nob29zZSBSZXNwb25zaWJsZSBXb3JrZXInLFxyXG4gICdzdGF0dXNfYW5kX3dvcmtlcic6ICdTdGF0dXMgYW5kIFdvcmtlcicsXHJcbiAgJ29yZGVyX3N0YXR1cyc6ICdPcmRlciBTdGF0dXMnLFxyXG4gICdhZGRpdGlvbmFsX25vdGVzX3BsYWNlaG9sZGVyJzogJ0FueSBhZGRpdGlvbmFsIG5vdGVzIG9yIGRldGFpbHMuLi4nLFxyXG4gICdub3Rfc3BlY2lmaWVkJzogJ05vdCBTcGVjaWZpZWQnLFxyXG4gICdiYWNrX3RvX2Rhc2hib2FyZCc6ICdCYWNrIHRvIERhc2hib2FyZCcsXHJcbiAgJ292ZXJ2aWV3X3RvZGF5JzogJ092ZXJ2aWV3IG9mIHRvZGF5XFwncyBhY3Rpdml0aWVzJyxcclxuICAnd2VsY29tZV93b3JrZXInOiAnV2VsY29tZSB0byB5b3VyIHdvcmtzcGFjZScsXHJcblxyXG4gIC8vINmF2YHYp9iq2YrYrSDZhdmB2YLZiNiv2Kkg2YXZhiDZhNmI2K3YqSDYp9mE2KrYrdmD2YVcclxuICAnaG9tZXBhZ2UnOiAnSG9tZXBhZ2UnLFxyXG4gICdteV9jb21wbGV0ZWRfb3JkZXJzJzogJ015IENvbXBsZXRlZCBPcmRlcnMnLFxyXG4gICdteV90b3RhbF9vcmRlcnMnOiAnTXkgVG90YWwgT3JkZXJzJyxcclxuICAnYWN0aXZlX29yZGVycyc6ICdBY3RpdmUgT3JkZXJzJyxcclxuICAndG9kYXlfYXBwb2ludG1lbnRzJzogJ1RvZGF5XFwncyBBcHBvaW50bWVudHMnLFxyXG4gICdzdGF0aXN0aWNzJzogJ1N0YXRpc3RpY3MnLFxyXG4gICdub19vcmRlcnNfZm91bmQnOiAnTm8gb3JkZXJzIGZvdW5kJyxcclxuICAndmlld19hbGwnOiAnVmlldyBBbGwnLFxyXG4gICd3b3JrZXJfbWFuYWdlbWVudCc6ICdXb3JrZXIgTWFuYWdlbWVudCcsXHJcbiAgJ3JlbWluZGVyJzogJ1JlbWluZGVyJyxcclxuICAneW91X2hhdmUnOiAnWW91IGhhdmUnLFxyXG4gICd0b2RheV9hcHBvaW50bWVudHNfcmVtaW5kZXInOiAnYXBwb2ludG1lbnRzIHRvZGF5JyxcclxuICAnYW5kJzogJ2FuZCcsXHJcbiAgJ29yZGVyc19uZWVkX2ZvbGxvdyc6ICdvcmRlcnMgdGhhdCBuZWVkIGZvbGxvdy11cCcsXHJcbiAgJ2RldGFpbGVkX3JlcG9ydHMnOiAnRGV0YWlsZWQgUmVwb3J0cycsXHJcbiAgJ3dvcmtlcl9kZXNjcmlwdGlvbic6ICdIZXJlIHlvdSBjYW4gdHJhY2sgeW91ciBhc3NpZ25lZCBvcmRlcnMgYW5kIHVwZGF0ZSB0aGVpciBzdGF0dXMnLFxyXG5cclxuICAvLyDZhdmB2KfYqtmK2K0g2LXZgdit2Kkg2KXYttin2YHYqSDYp9mE2LfZhNio2KfYqlxyXG4gICdvcmRlcl9hZGRfZXJyb3InOiAnQW4gZXJyb3Igb2NjdXJyZWQgd2hpbGUgYWRkaW5nIHRoZSBvcmRlcicsXHJcbiAgJ2NtX3BsYWNlaG9sZGVyJzogJ2NtJyxcclxuICAnc2hvdWxkZXInOiAnU2hvdWxkZXInLFxyXG4gICdzaG91bGRlcl9jaXJjdW1mZXJlbmNlJzogJ1Nob3VsZGVyIENpcmN1bWZlcmVuY2UnLFxyXG4gICdjaGVzdCc6ICdDaGVzdCcsXHJcbiAgJ3dhaXN0JzogJ1dhaXN0JyxcclxuICAnaGlwcyc6ICdIaXBzJyxcclxuICAnZGFydF9sZW5ndGgnOiAnRGFydCBMZW5ndGgnLFxyXG4gICdib2RpY2VfbGVuZ3RoJzogJ0JvZGljZSBMZW5ndGgnLFxyXG4gICduZWNrbGluZSc6ICdOZWNrbGluZScsXHJcbiAgJ2FybXBpdCc6ICdBcm1waXQnLFxyXG4gICdzbGVldmVfbGVuZ3RoJzogJ1NsZWV2ZSBMZW5ndGgnLFxyXG4gICdmb3JlYXJtJzogJ0ZvcmVhcm0nLFxyXG4gICdjdWZmJzogJ0N1ZmYnLFxyXG4gICdmcm9udF9sZW5ndGgnOiAnRnJvbnQgTGVuZ3RoJyxcclxuICAnYmFja19sZW5ndGgnOiAnQmFjayBMZW5ndGgnLFxyXG5cclxuICAvLyDZhdmB2KfYqtmK2K0g2LXZgdit2Kkg2KfZhNi32YTYqNin2KpcclxuICAnZW50ZXJfb3JkZXJfbnVtYmVyJzogJ0VudGVyIG9yZGVyIG51bWJlcicsXHJcbiAgJ3NlYXJjaF9wbGFjZWhvbGRlcic6ICdTZWFyY2ggYnkgbmFtZSwgb3JkZXIgbnVtYmVyLCBvciBkZXNjcmlwdGlvbi4uLicsXHJcbiAgJ3NlYXJjaF9ieV90ZXh0JzogJ1NlYXJjaCBieSBUZXh0JyxcclxuICAnc2VhcmNoX2J5X29yZGVyX251bWJlcic6ICdTZWFyY2ggYnkgT3JkZXIgTnVtYmVyJyxcclxuICAnYWxsX29yZGVycyc6ICdBbGwgT3JkZXJzJyxcclxuICAnbm9fb3JkZXJzX2Fzc2lnbmVkJzogJ05vIG9yZGVycyBhc3NpZ25lZCB0byB5b3UnLFxyXG4gICdub19vcmRlcnNfYXNzaWduZWRfZGVzYyc6ICdObyBvcmRlcnMgaGF2ZSBiZWVuIGFzc2lnbmVkIHRvIHlvdSB5ZXQnLFxyXG4gICdub19vcmRlcnNfZm91bmRfZGVzYyc6ICdObyBvcmRlcnMgZm91bmQgbWF0Y2hpbmcgdGhlIHNlYXJjaCBjcml0ZXJpYScsXHJcbiAgJ3ByaWNlX2xhYmVsJzogJ1ByaWNlJyxcclxuICAnc2FyJzogJ1NBUicsXHJcbiAgJ3ZpZXcnOiAnVmlldycsXHJcbiAgJ2NvbXBsZXRpbmcnOiAnQ29tcGxldGluZy4uLicsXHJcbiAgJ3N0YXJ0X3dvcmsnOiAnU3RhcnQgV29yaycsXHJcbiAgJ2NvbXBsZXRlX29yZGVyJzogJ0NvbXBsZXRlIE9yZGVyJyxcclxuICAnY29tcGxldGVfb3JkZXJfbW9kYWxfdGl0bGUnOiAnQ29tcGxldGUgT3JkZXIgYW5kIFVwbG9hZCBGaW5pc2hlZCBXb3JrIEltYWdlcycsXHJcbiAgJ2ltcG9ydGFudF93YXJuaW5nJzogJ0ltcG9ydGFudCBXYXJuaW5nJyxcclxuICAnY29tcGxldGVfb3JkZXJfd2FybmluZyc6ICdPbmNlIHlvdSBjb21wbGV0ZSB0aGUgb3JkZXIsIHlvdSB3aWxsIG5vdCBiZSBhYmxlIHRvIG1vZGlmeSBpdHMgc3RhdHVzIGFnYWluLiBNYWtlIHN1cmUgdG8gdXBsb2FkIGFsbCBmaW5pc2hlZCB3b3JrIGltYWdlcyBiZWZvcmUgcHJvY2VlZGluZy4nLFxyXG4gICdvcmRlcl9kZWxldGVkX3N1Y2Nlc3NmdWxseSc6ICdPcmRlciBkZWxldGVkIHN1Y2Nlc3NmdWxseScsXHJcblxyXG4gIC8vINmF2YHYp9iq2YrYrSDZhdmD2YjZhiDYrdiw2YEg2KfZhNi32YTYqFxyXG4gICdjb25maXJtX2RlbGV0ZV9vcmRlcic6ICdDb25maXJtIERlbGV0ZSBPcmRlcicsXHJcbiAgJ3dhcm5pbmdfZGVsZXRlX29yZGVyJzogJ1dhcm5pbmc6IERlbGV0ZSBPcmRlcicsXHJcbiAgJ2RlbGV0ZV9vcmRlcl93YXJuaW5nX21lc3NhZ2UnOiAnVGhpcyBhY3Rpb24gY2Fubm90IGJlIHVuZG9uZS4gVGhlIG9yZGVyIGFuZCBhbGwgYXNzb2NpYXRlZCBkYXRhIHdpbGwgYmUgcGVybWFuZW50bHkgZGVsZXRlZC4nLFxyXG4gICdhZG1pbl9lbWFpbCc6ICdBZG1pbiBFbWFpbCcsXHJcbiAgJ2FkbWluX3Bhc3N3b3JkJzogJ0FkbWluIFBhc3N3b3JkJyxcclxuICAnZW50ZXJfYWRtaW5fZW1haWwnOiAnRW50ZXIgYWRtaW4gZW1haWwnLFxyXG4gICdlbnRlcl9hZG1pbl9wYXNzd29yZCc6ICdFbnRlciBhZG1pbiBwYXNzd29yZCcsXHJcbiAgJ3BsZWFzZV9maWxsX2FsbF9maWVsZHMnOiAnUGxlYXNlIGZpbGwgYWxsIGZpZWxkcycsXHJcbiAgJ2VtYWlsX2RvZXNfbm90X21hdGNoJzogJ0VtYWlsIGRvZXMgbm90IG1hdGNoIHRoZSByZWdpc3RlcmVkIGFkbWluIGVtYWlsJyxcclxuICAnaW5jb3JyZWN0X3Bhc3N3b3JkJzogJ0luY29ycmVjdCBwYXNzd29yZCcsXHJcbiAgJ2NvbmZpcm1fZGVsZXRlJzogJ0NvbmZpcm0gRGVsZXRlJyxcclxuXHJcbiAgLy8g2YXZgdin2KrZititINmF2YPZiNmGINin2YTZhdmE2KfYrdi42KfYqiDYp9mE2LXZiNiq2YrYqVxyXG4gICdzdGFydF9yZWNvcmRpbmcnOiAnU3RhcnQgUmVjb3JkaW5nJyxcclxuICAnc3RvcF9yZWNvcmRpbmcnOiAnU3RvcCBSZWNvcmRpbmcnLFxyXG4gICdjbGlja190b19yZWNvcmRfdm9pY2Vfbm90ZSc6ICdDbGljayB0byByZWNvcmQgYSB2b2ljZSBub3RlJyxcclxuICAndm9pY2Vfbm90ZXMnOiAnVm9pY2UgTm90ZXMnLFxyXG4gICd2b2ljZV9ub3RlJzogJ1ZvaWNlIE5vdGUnLFxyXG4gICdtaWNyb3Bob25lX2FjY2Vzc19lcnJvcic6ICdNaWNyb3Bob25lIGFjY2VzcyBlcnJvcicsXHJcblxyXG4gIC8vINmF2YHYp9iq2YrYrSDYtdmB2K3YqSDYp9mE2LnZhdin2YRcclxuICAnd29ya2VyX2FkZGVkX3N1Y2Nlc3MnOiAnV29ya2VyIGFkZGVkIHN1Y2Nlc3NmdWxseScsXHJcbiAgJ2Vycm9yX2FkZGluZ193b3JrZXInOiAnRXJyb3IgYWRkaW5nIHdvcmtlcicsXHJcbiAgJ3dvcmtlcl91cGRhdGVkX3N1Y2Nlc3MnOiAnV29ya2VyIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5JyxcclxuICAnZXJyb3JfdXBkYXRpbmdfd29ya2VyJzogJ0Vycm9yIHVwZGF0aW5nIHdvcmtlcicsXHJcbiAgJ3dvcmtlcl9kZWxldGVkX3N1Y2Nlc3MnOiAnV29ya2VyIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5JyxcclxuICAnd29ya2VyX2RlYWN0aXZhdGVkJzogJ1dvcmtlciBkZWFjdGl2YXRlZCcsXHJcbiAgJ3dvcmtlcl9hY3RpdmF0ZWQnOiAnV29ya2VyIGFjdGl2YXRlZCcsXHJcbiAgJ2FkZGluZyc6ICdBZGRpbmcuLi4nLFxyXG4gICdhZGRfd29ya2VyJzogJ0FkZCBXb3JrZXInLFxyXG4gICdhY3RpdmUnOiAnQWN0aXZlJyxcclxuICAnaW5hY3RpdmUnOiAnSW5hY3RpdmUnLFxyXG4gICdzYXZlX2NoYW5nZXMnOiAnU2F2ZSBDaGFuZ2VzJyxcclxuICAnc2VhcmNoX3dvcmtlcnNfcGxhY2Vob2xkZXInOiAnU2VhcmNoIHdvcmtlcnMuLi4nLFxyXG4gICd3b3JrZXJzX21hbmFnZW1lbnQnOiAnV29ya2VycyBNYW5hZ2VtZW50JyxcclxuICAndmlld19tYW5hZ2VfdGVhbSc6ICdWaWV3IGFuZCBtYW5hZ2UgdGhlIHRhaWxvcmluZyB3b3Jrc2hvcCB0ZWFtJyxcclxuICAnYWRkX25ld193b3JrZXInOiAnQWRkIE5ldyBXb3JrZXInLFxyXG4gICdhZGRfbmV3X3dvcmtlcl9mb3JtJzogJ0FkZCBOZXcgV29ya2VyJyxcclxuICAnZnVsbF9uYW1lX3JlcXVpcmVkJzogJ0Z1bGwgTmFtZSAqJyxcclxuICAnZW1haWxfcmVxdWlyZWQnOiAnRW1haWwgKicsXHJcbiAgJ3Bhc3N3b3JkX3JlcXVpcmVkJzogJ1Bhc3N3b3JkIConLFxyXG4gICdwaG9uZV9yZXF1aXJlZF93b3JrZXInOiAnUGhvbmUgTnVtYmVyIConLFxyXG4gICdlbnRlcl9mdWxsX25hbWUnOiAnRW50ZXIgZnVsbCBuYW1lJyxcclxuICAnZW50ZXJfZW1haWwnOiAnRW50ZXIgZW1haWwnLFxyXG4gICdlbnRlcl9wYXNzd29yZCc6ICdFbnRlciBwYXNzd29yZCcsXHJcbiAgJ2VudGVyX3Bob25lJzogJ0VudGVyIHBob25lIG51bWJlcicsXHJcbiAgJ3NwZWNpYWx0eV9yZXF1aXJlZCc6ICdTcGVjaWFsdHkgKicsXHJcbiAgJ3NwZWNpYWx0eV9leGFtcGxlJzogJ0V4YW1wbGU6IEV2ZW5pbmcgZHJlc3MgdGFpbG9yaW5nJyxcclxuICAnZWRpdF93b3JrZXInOiAnRWRpdCBXb3JrZXInLFxyXG4gICduZXdfcGFzc3dvcmQnOiAnTmV3IFBhc3N3b3JkJyxcclxuICAnbGVhdmVfZW1wdHlfbm9fY2hhbmdlJzogJ0xlYXZlIGVtcHR5IGlmIHlvdSBkb25cXCd0IHdhbnQgdG8gY2hhbmdlIGl0JyxcclxuICAnbm9fd29ya2Vycyc6ICdObyB3b3JrZXJzJyxcclxuICAnbm9fd29ya2Vyc19mb3VuZCc6ICdObyB3b3JrZXJzIGZvdW5kIG1hdGNoaW5nIHRoZSBzZWFyY2ggY3JpdGVyaWEnLFxyXG4gICdqb2luZWRfb24nOiAnSm9pbmVkIG9uJyxcclxuICAndG90YWxfd29ya2Vycyc6ICdUb3RhbCBXb3JrZXJzJyxcclxuICAnYWN0aXZlX3dvcmtlcnMnOiAnQWN0aXZlIFdvcmtlcnMnLFxyXG4gICd0b3RhbF9jb21wbGV0ZWRfb3JkZXJzJzogJ1RvdGFsIENvbXBsZXRlZCBPcmRlcnMnLFxyXG4gICdjb25maXJtX2RlbGV0ZV93b3JrZXInOiAnQXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGRlbGV0ZSB0aGlzIHdvcmtlcj8nLFxyXG5cclxuICAvLyDZhdmB2KfYqtmK2K0g2LXZgdit2Kkg2KfZhNiq2YLYp9ix2YrYsVxyXG4gICdlbmdhZ2VtZW50X2RyZXNzJzogJ0VuZ2FnZW1lbnQgRHJlc3MnLFxyXG4gICdjYXN1YWxfZHJlc3MnOiAnQ2FzdWFsIERyZXNzJyxcclxuICAnb3RoZXInOiAnT3RoZXInLFxyXG4gICd0aGlzX3dlZWsnOiAnVGhpcyBXZWVrJyxcclxuICAndGhpc19tb250aCc6ICdUaGlzIE1vbnRoJyxcclxuICAndGhpc19xdWFydGVyJzogJ1RoaXMgUXVhcnRlcicsXHJcbiAgJ3RoaXNfeWVhcic6ICdUaGlzIFllYXInLFxyXG4gICdyZXBvcnRzX2FuYWx5dGljcyc6ICdSZXBvcnRzICYgQW5hbHl0aWNzJyxcclxuICAnbW9udGhseV90cmVuZCc6ICdNb250aGx5IFRyZW5kJyxcclxuICAncmV2ZW51ZV9sYWJlbCc6ICdSZXZlbnVlJyxcclxuXHJcbiAgLy8g2YXZgdin2KrZititINi12YHYrdipINin2YTZhdmI2KfYudmK2K9cclxuICAnY29uZmlybWVkJzogJ0NvbmZpcm1lZCcsXHJcbiAgJ3BtJzogJ1BNJyxcclxuICAnYW0nOiAnQU0nLFxyXG4gICdhbGxfc3RhdHVzZXMnOiAnQWxsIFN0YXR1c2VzJyxcclxuICAnYWxsX2RhdGVzJzogJ0FsbCBEYXRlcycsXHJcbiAgJ3RvZGF5JzogJ1RvZGF5JyxcclxuICAndG9tb3Jyb3cnOiAnVG9tb3Jyb3cnLFxyXG4gICdhcHBvaW50bWVudHNfbWFuYWdlbWVudCc6ICdBcHBvaW50bWVudHMgTWFuYWdlbWVudCcsXHJcbiAgJ3ZpZXdfbWFuYWdlX2FwcG9pbnRtZW50cyc6ICdWaWV3IGFuZCBtYW5hZ2UgYWxsIHRhaWxvcmluZyBhcHBvaW50bWVudHMnLFxyXG4gICdib29rX25ld19hcHBvaW50bWVudCc6ICdCb29rIE5ldyBBcHBvaW50bWVudCcsXHJcbiAgJ3NlYXJjaF9hcHBvaW50bWVudHNfcGxhY2Vob2xkZXInOiAnU2VhcmNoIGFwcG9pbnRtZW50cy4uLicsXHJcbiAgJ25vX2FwcG9pbnRtZW50cyc6ICdObyBhcHBvaW50bWVudHMnLFxyXG4gICdub19hcHBvaW50bWVudHNfZm91bmQnOiAnTm8gYXBwb2ludG1lbnRzIGZvdW5kIG1hdGNoaW5nIHRoZSBzZWFyY2ggY3JpdGVyaWEnLFxyXG4gICdjcmVhdGVkX29uJzogJ0NyZWF0ZWQgb24nLFxyXG4gICdjb25maXJtX2FwcG9pbnRtZW50JzogJ0NvbmZpcm0gQXBwb2ludG1lbnQnLFxyXG4gICdjYW5jZWxfYXBwb2ludG1lbnQnOiAnQ2FuY2VsIEFwcG9pbnRtZW50JyxcclxuXHJcbiAgLy8g2YXZgdin2KrZititINmF2YPZiNmG2KfYqiDYpdi22KfZgdmK2KlcclxuICAnb2YnOiAnb2YnLFxyXG4gICdpbWFnZXNfdGV4dCc6ICdpbWFnZXMnLFxyXG5cclxuICAvLyDZhdmB2KfYqtmK2K0g2YXZg9mI2YbYp9iqINin2YTYtdmI2LEg2YjYp9mE2KrYrdmF2YrZhFxyXG4gICdtYXhfaW1hZ2VzX3JlYWNoZWQnOiAnTWF4aW11bSBpbWFnZXMgcmVhY2hlZCcsXHJcbiAgJ2Ryb3BfaW1hZ2VzX2hlcmUnOiAnRHJvcCBpbWFnZXMgaGVyZScsXHJcbiAgJ2NsaWNrX29yX2RyYWdfaW1hZ2VzJzogJ0NsaWNrIG9yIGRyYWcgaW1hZ2VzIGhlcmUnLFxyXG4gICdpbWFnZV91cGxvYWRfZm9ybWF0JzogJ1BORywgSlBHLCBKUEVHIHVwIHRvIDVNQicsXHJcbiAgJ21heF9pbWFnZXNfdGV4dCc6ICdNYXhpbXVtJyxcclxuICAnb3JkZXJfbGFiZWwnOiAnT3JkZXInLFxyXG4gICdmb3JfY2xpZW50JzogJ0ZvciBjbGllbnQnLFxyXG5cclxuICAvLyDZhdmB2KfYqtmK2K0g2YXZg9mI2YYg2KrYudiv2YrZhCDYp9mE2LfZhNioXHJcbiAgJ29yZGVyX3VwZGF0ZV9lcnJvcic6ICdFcnJvciB1cGRhdGluZyBvcmRlcicsXHJcbiAgJ3ByaWNlX3Nhcl9yZXF1aXJlZCc6ICdQcmljZSAoU0FSKSAqJyxcclxuICAnc3RhdHVzX3BlbmRpbmcnOiAnUGVuZGluZycsXHJcbiAgJ3N0YXR1c19pbl9wcm9ncmVzcyc6ICdJbiBQcm9ncmVzcycsXHJcbiAgJ3N0YXR1c19jb21wbGV0ZWQnOiAnQ29tcGxldGVkJyxcclxuICAnc3RhdHVzX2RlbGl2ZXJlZCc6ICdEZWxpdmVyZWQnLFxyXG4gICdzdGF0dXNfY2FuY2VsbGVkJzogJ0NhbmNlbGxlZCcsXHJcblxyXG4gIC8vINmG2LXZiNi1INin2YTZgdmI2KrYsVxyXG4gICdob21lJzogJ0hvbWUnLFxyXG4gICd0cmFja19vcmRlcic6ICdUcmFjayBPcmRlcicsXHJcbiAgJ2ZhYnJpY3MnOiAnRmFicmljcycsXHJcbiAgJ2NvbnRhY3RfdXMnOiAnQ29udGFjdCBVcycsXHJcbiAgJ3lhc21pbl9hbHNoYW0nOiAnWWFzbWluIEFsc2hhbScsXHJcbiAgJ2N1c3RvbV9kcmVzc190YWlsb3JpbmcnOiAnQ3VzdG9tIERyZXNzIFRhaWxvcmluZydcclxufVxyXG5cclxuLy8gSG9vayDZhNmE2KrYsdis2YXYqVxyXG5leHBvcnQgZnVuY3Rpb24gdXNlVHJhbnNsYXRpb24oKSB7XHJcbiAgY29uc3QgW2xhbmd1YWdlLCBzZXRMYW5ndWFnZV0gPSB1c2VTdGF0ZTwnYXInIHwgJ2VuJz4oJ2FyJylcclxuXHJcbiAgLy8g2KrYrdmF2YrZhCDYp9mE2YTYutipINin2YTZhdit2YHZiNi42Kkg2LnZhtivINio2K/YoSDYp9mE2KrYt9io2YrZglxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBzYXZlZExhbmd1YWdlID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2Rhc2hib2FyZC1sYW5ndWFnZScpIGFzICdhcicgfCAnZW4nXHJcbiAgICBpZiAoc2F2ZWRMYW5ndWFnZSkge1xyXG4gICAgICBzZXRMYW5ndWFnZShzYXZlZExhbmd1YWdlKVxyXG4gICAgfVxyXG4gIH0sIFtdKVxyXG5cclxuICAvLyDYrdmB2Lgg2KfZhNmE2LrYqSDYudmG2K8g2KrYutmK2YrYsdmH2KdcclxuICBjb25zdCBjaGFuZ2VMYW5ndWFnZSA9IChuZXdMYW5ndWFnZTogJ2FyJyB8ICdlbicpID0+IHtcclxuICAgIHNldExhbmd1YWdlKG5ld0xhbmd1YWdlKVxyXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2Rhc2hib2FyZC1sYW5ndWFnZScsIG5ld0xhbmd1YWdlKVxyXG4gIH1cclxuXHJcbiAgLy8g2K/Yp9mE2Kkg2KfZhNiq2LHYrNmF2KlcclxuICBjb25zdCB0ID0gKGtleTogVHJhbnNsYXRpb25LZXkpOiBzdHJpbmcgPT4ge1xyXG4gICAgY29uc3QgdHJhbnNsYXRpb25zID0gbGFuZ3VhZ2UgPT09ICdhcicgPyBhclRyYW5zbGF0aW9ucyA6IGVuVHJhbnNsYXRpb25zXHJcbiAgICBjb25zdCB0cmFuc2xhdGlvbiA9IHRyYW5zbGF0aW9uc1trZXldXHJcbiAgICBcclxuICAgIGlmICh0eXBlb2YgdHJhbnNsYXRpb24gPT09ICdzdHJpbmcnKSB7XHJcbiAgICAgIHJldHVybiB0cmFuc2xhdGlvblxyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyDYpdiw2Kcg2YTZhSDYqtmI2KzYryDYp9mE2KrYsdis2YXYqdiMINij2LHYrNi5INin2YTZhdmB2KrYp9itINmG2YHYs9mHXHJcbiAgICByZXR1cm4ga2V5XHJcbiAgfVxyXG5cclxuICAvLyDYp9mE2KrYrdmC2YIg2YXZhiDYp9mE2YTYutipINin2YTYrdin2YTZitipXHJcbiAgY29uc3QgaXNBcmFiaWMgPSBsYW5ndWFnZSA9PT0gJ2FyJ1xyXG4gIGNvbnN0IGlzRW5nbGlzaCA9IGxhbmd1YWdlID09PSAnZW4nXHJcblxyXG4gIHJldHVybiB7XHJcbiAgICBsYW5ndWFnZSxcclxuICAgIGNoYW5nZUxhbmd1YWdlLFxyXG4gICAgdCxcclxuICAgIGlzQXJhYmljLFxyXG4gICAgaXNFbmdsaXNoXHJcbiAgfVxyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsImFyVHJhbnNsYXRpb25zIiwiZW5UcmFuc2xhdGlvbnMiLCJ1c2VUcmFuc2xhdGlvbiIsImxhbmd1YWdlIiwic2V0TGFuZ3VhZ2UiLCJzYXZlZExhbmd1YWdlIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImNoYW5nZUxhbmd1YWdlIiwibmV3TGFuZ3VhZ2UiLCJzZXRJdGVtIiwidCIsImtleSIsInRyYW5zbGF0aW9ucyIsInRyYW5zbGF0aW9uIiwiaXNBcmFiaWMiLCJpc0VuZ2xpc2giXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useTranslation.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/authStore.ts":
/*!********************************!*\
  !*** ./src/store/authStore.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// بيانات المستخدمين الافتراضية (سيتم استبدالها بنظام إدارة العمال)\nconst getStoredUsers = ()=>{\n    if (true) return [];\n    const stored = localStorage.getItem('yasmin-users');\n    if (stored) {\n        return JSON.parse(stored);\n    }\n    // المستخدمين الافتراضيين\n    const defaultUsers = [\n        {\n            id: '1',\n            email: '<EMAIL>',\n            password: 'admin123',\n            full_name: 'مدير النظام',\n            role: 'admin',\n            is_active: true\n        }\n    ];\n    localStorage.setItem('yasmin-users', JSON.stringify(defaultUsers));\n    return defaultUsers;\n};\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        user: null,\n        isLoading: false,\n        error: null,\n        signIn: async (email, password)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                console.log('🔐 بدء عملية تسجيل الدخول...', {\n                    email\n                });\n                // محاكاة تأخير الشبكة\n                await new Promise((resolve)=>setTimeout(resolve, 1500));\n                // البحث عن المستخدم في البيانات المحفوظة\n                const users = getStoredUsers();\n                const foundUser = users.find((user)=>user.email.toLowerCase() === email.toLowerCase() && user.password === password);\n                if (foundUser) {\n                    console.log('✅ تم العثور على المستخدم:', foundUser.full_name);\n                    const user = {\n                        id: foundUser.id,\n                        email: foundUser.email,\n                        full_name: foundUser.full_name,\n                        role: foundUser.role,\n                        is_active: foundUser.is_active,\n                        created_at: new Date().toISOString(),\n                        updated_at: new Date().toISOString(),\n                        token: `demo-token-${foundUser.id}-${Date.now()}`\n                    };\n                    // حفظ في localStorage أولاً\n                    if (false) {}\n                    // تحديث حالة المتجر\n                    set({\n                        user,\n                        isLoading: false,\n                        error: null\n                    });\n                    console.log('🎉 تم تسجيل الدخول بنجاح!');\n                    return true;\n                } else {\n                    console.log('❌ بيانات تسجيل الدخول غير صحيحة');\n                    set({\n                        error: 'بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور.',\n                        isLoading: false\n                    });\n                    return false;\n                }\n            } catch (error) {\n                console.error('💥 خطأ في تسجيل الدخول:', error);\n                set({\n                    error: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',\n                    isLoading: false\n                });\n                return false;\n            }\n        },\n        signOut: async ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                // محاكاة تأخير تسجيل الخروج\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                // مسح البيانات من localStorage\n                if (false) {}\n                set({\n                    user: null,\n                    isLoading: false,\n                    error: null\n                });\n            } catch (error) {\n                console.error('خطأ في تسجيل الخروج:', error);\n                set({\n                    isLoading: false,\n                    error: 'خطأ في تسجيل الخروج'\n                });\n            }\n        },\n        setUser: (user)=>{\n            set({\n                user\n            });\n            // تحديث localStorage\n            if (false) {}\n        },\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        checkAuth: async ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                // التحقق من وجود مستخدم محفوظ في localStorage\n                if (false) {}\n                set({\n                    user: null,\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error('خطأ في التحقق من المصادقة:', error);\n                set({\n                    user: null,\n                    isLoading: false\n                });\n            }\n        },\n        isAuthenticated: ()=>{\n            const state = get();\n            return state.user !== null && state.user.is_active;\n        }\n    }), {\n    name: 'yasmin-auth-storage',\n    partialize: (state)=>({\n            user: state.user\n        })\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/authStore.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/dataStore.ts":
/*!********************************!*\
  !*** ./src/store/dataStore.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDataStore: () => (/* binding */ useDataStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// توليد ID فريد\nconst generateId = ()=>{\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n};\nconst useDataStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // البيانات الأولية\n        appointments: [],\n        orders: [],\n        workers: [],\n        isLoading: false,\n        error: null,\n        // إدارة المواعيد\n        addAppointment: (appointmentData)=>{\n            const appointment = {\n                ...appointmentData,\n                id: generateId(),\n                status: 'pending',\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            set((state)=>({\n                    appointments: [\n                        ...state.appointments,\n                        appointment\n                    ],\n                    error: null\n                }));\n            console.log('✅ تم إضافة موعد جديد:', appointment);\n        },\n        updateAppointment: (id, updates)=>{\n            set((state)=>({\n                    appointments: state.appointments.map((appointment)=>appointment.id === id ? {\n                            ...appointment,\n                            ...updates,\n                            updatedAt: new Date().toISOString()\n                        } : appointment),\n                    error: null\n                }));\n            console.log('✅ تم تحديث الموعد:', id);\n        },\n        deleteAppointment: (id)=>{\n            set((state)=>({\n                    appointments: state.appointments.filter((appointment)=>appointment.id !== id),\n                    error: null\n                }));\n            console.log('✅ تم حذف الموعد:', id);\n        },\n        getAppointment: (id)=>{\n            const state = get();\n            return state.appointments.find((appointment)=>appointment.id === id);\n        },\n        // إدارة الطلبات\n        addOrder: (orderData)=>{\n            const order = {\n                ...orderData,\n                id: generateId(),\n                status: 'pending',\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            set((state)=>({\n                    orders: [\n                        ...state.orders,\n                        order\n                    ],\n                    error: null\n                }));\n            console.log('✅ تم إضافة طلب جديد:', order);\n        },\n        updateOrder: (id, updates)=>{\n            set((state)=>({\n                    orders: state.orders.map((order)=>order.id === id ? {\n                            ...order,\n                            ...updates,\n                            updatedAt: new Date().toISOString()\n                        } : order),\n                    error: null\n                }));\n            console.log('✅ تم تحديث الطلب:', id);\n        },\n        deleteOrder: (id)=>{\n            set((state)=>({\n                    orders: state.orders.filter((order)=>order.id !== id),\n                    error: null\n                }));\n            console.log('✅ تم حذف الطلب:', id);\n        },\n        getOrder: (id)=>{\n            const state = get();\n            return state.orders.find((order)=>order.id === id);\n        },\n        // إدارة العمال\n        addWorker: (workerData)=>{\n            const worker = {\n                ...workerData,\n                id: generateId(),\n                role: 'worker',\n                is_active: true,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            set((state)=>({\n                    workers: [\n                        ...state.workers,\n                        worker\n                    ],\n                    error: null\n                }));\n            // إضافة العامل إلى نظام المصادقة\n            if (false) {}\n            console.log('✅ تم إضافة عامل جديد:', worker);\n        },\n        updateWorker: (id, updates)=>{\n            set((state)=>({\n                    workers: state.workers.map((worker)=>worker.id === id ? {\n                            ...worker,\n                            ...updates,\n                            updatedAt: new Date().toISOString()\n                        } : worker),\n                    error: null\n                }));\n            // تحديث بيانات المصادقة إذا تم تغيير البريد أو كلمة المرور\n            if (updates.email || updates.password || updates.full_name) {\n                if (false) {}\n            }\n            console.log('✅ تم تحديث العامل:', id);\n        },\n        deleteWorker: (id)=>{\n            set((state)=>({\n                    workers: state.workers.filter((worker)=>worker.id !== id),\n                    error: null\n                }));\n            // حذف من نظام المصادقة\n            if (false) {}\n            console.log('✅ تم حذف العامل:', id);\n        },\n        getWorker: (id)=>{\n            const state = get();\n            return state.workers.find((worker)=>worker.id === id);\n        },\n        // دوال خاصة للعمال\n        startOrderWork: (orderId, workerId)=>{\n            set((state)=>({\n                    orders: state.orders.map((order)=>order.id === orderId && order.assignedWorker === workerId ? {\n                            ...order,\n                            status: 'in_progress',\n                            updatedAt: new Date().toISOString()\n                        } : order),\n                    error: null\n                }));\n            console.log('✅ تم بدء العمل في الطلب:', orderId);\n        },\n        completeOrder: (orderId, workerId, completedImages = [])=>{\n            set((state)=>({\n                    orders: state.orders.map((order)=>order.id === orderId && order.assignedWorker === workerId ? {\n                            ...order,\n                            status: 'completed',\n                            completedImages: completedImages.length > 0 ? completedImages : undefined,\n                            updatedAt: new Date().toISOString()\n                        } : order),\n                    error: null\n                }));\n            console.log('✅ تم إنهاء الطلب:', orderId);\n        },\n        // وظائف مساعدة\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        loadData: ()=>{\n            set({\n                isLoading: true\n            });\n            // البيانات محفوظة تلقائياً بواسطة persist middleware\n            set({\n                isLoading: false\n            });\n        },\n        // إحصائيات\n        getStats: ()=>{\n            const state = get();\n            return {\n                totalAppointments: state.appointments.length,\n                totalOrders: state.orders.length,\n                totalWorkers: state.workers.length,\n                pendingAppointments: state.appointments.filter((a)=>a.status === 'pending').length,\n                activeOrders: state.orders.filter((o)=>[\n                        'pending',\n                        'in_progress'\n                    ].includes(o.status)).length,\n                completedOrders: state.orders.filter((o)=>o.status === 'completed').length,\n                totalRevenue: state.orders.filter((o)=>o.status === 'completed').reduce((sum, order)=>sum + order.price, 0)\n            };\n        }\n    }), {\n    name: 'yasmin-data-storage',\n    partialize: (state)=>({\n            appointments: state.appointments,\n            orders: state.orders,\n            workers: state.workers\n        })\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/dataStore.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/zustand","vendor-chunks/motion-utils","vendor-chunks/styled-jsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();