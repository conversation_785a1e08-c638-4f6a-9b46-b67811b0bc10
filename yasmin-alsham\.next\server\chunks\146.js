exports.id=146,exports.ids=[146],exports.modules={2015:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return f},getRouteRegex:function(){return c},parseParameter:function(){return l}});let n=i(46143),r=i(71437),s=i(53293),o=i(72887),a=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(t){let e=t.match(a);return e?u(e[2]):u(t)}function u(t){let e=t.startsWith("[")&&t.endsWith("]");e&&(t=t.slice(1,-1));let i=t.startsWith("...");return i&&(t=t.slice(3)),{key:t,repeat:i,optional:e}}function h(t,e,i){let n={},l=1,h=[];for(let c of(0,o.removeTrailingSlash)(t).slice(1).split("/")){let t=r.INTERCEPTION_ROUTE_MARKERS.find(t=>c.startsWith(t)),o=c.match(a);if(t&&o&&o[2]){let{key:e,optional:i,repeat:r}=u(o[2]);n[e]={pos:l++,repeat:r,optional:i},h.push("/"+(0,s.escapeStringRegexp)(t)+"([^/]+?)")}else if(o&&o[2]){let{key:t,repeat:e,optional:r}=u(o[2]);n[t]={pos:l++,repeat:e,optional:r},i&&o[1]&&h.push("/"+(0,s.escapeStringRegexp)(o[1]));let a=e?r?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";i&&o[1]&&(a=a.substring(1)),h.push(a)}else h.push("/"+(0,s.escapeStringRegexp)(c));e&&o&&o[3]&&h.push((0,s.escapeStringRegexp)(o[3]))}return{parameterizedRoute:h.join(""),groups:n}}function c(t,e){let{includeSuffix:i=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:r=!1}=void 0===e?{}:e,{parameterizedRoute:s,groups:o}=h(t,i,n),a=s;return r||(a+="(?:/)?"),{re:RegExp("^"+a+"$"),groups:o}}function d(t){let e,{interceptionMarker:i,getSafeRouteKey:n,segment:r,routeKeys:o,keyPrefix:a,backreferenceDuplicateKeys:l}=t,{key:h,optional:c,repeat:d}=u(r),p=h.replace(/\W/g,"");a&&(p=""+a+p);let f=!1;(0===p.length||p.length>30)&&(f=!0),isNaN(parseInt(p.slice(0,1)))||(f=!0),f&&(p=n());let m=p in o;a?o[p]=""+a+h:o[p]=h;let g=i?(0,s.escapeStringRegexp)(i):"";return e=m&&l?"\\k<"+p+">":d?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",c?"(?:/"+g+e+")?":"/"+g+e}function p(t,e,i,l,u){let h,c=(h=0,()=>{let t="",e=++h;for(;e>0;)t+=String.fromCharCode(97+(e-1)%26),e=Math.floor((e-1)/26);return t}),p={},f=[];for(let h of(0,o.removeTrailingSlash)(t).slice(1).split("/")){let t=r.INTERCEPTION_ROUTE_MARKERS.some(t=>h.startsWith(t)),o=h.match(a);if(t&&o&&o[2])f.push(d({getSafeRouteKey:c,interceptionMarker:o[1],segment:o[2],routeKeys:p,keyPrefix:e?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(o&&o[2]){l&&o[1]&&f.push("/"+(0,s.escapeStringRegexp)(o[1]));let t=d({getSafeRouteKey:c,segment:o[2],routeKeys:p,keyPrefix:e?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&o[1]&&(t=t.substring(1)),f.push(t)}else f.push("/"+(0,s.escapeStringRegexp)(h));i&&o&&o[3]&&f.push((0,s.escapeStringRegexp)(o[3]))}return{namedParameterizedRoute:f.join(""),routeKeys:p}}function f(t,e){var i,n,r;let s=p(t,e.prefixRouteKeys,null!=(i=e.includeSuffix)&&i,null!=(n=e.includePrefix)&&n,null!=(r=e.backreferenceDuplicateKeys)&&r),o=s.namedParameterizedRoute;return e.excludeOptionalTrailingSlash||(o+="(?:/)?"),{...c(t,e),namedRegex:"^"+o+"$",routeKeys:s.routeKeys}}function m(t,e){let{parameterizedRoute:i}=h(t,!1,!1),{catchAll:n=!0}=e;if("/"===i)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:r}=p(t,!1,!1,!1,!1);return{namedRegex:"^"+r+(n?"(?:(/.*)?)":"")+"$"}}},6341:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{getPreviouslyRevalidatedTags:function(){return y},getUtils:function(){return g},interpolateDynamicPath:function(){return f},normalizeDynamicRouteParams:function(){return m},normalizeVercelUrl:function(){return p}});let n=i(79551),r=i(11959),s=i(12437),o=i(2015),a=i(78034),l=i(15526),u=i(72887),h=i(74722),c=i(46143),d=i(47912);function p(t,e,i){let r=(0,n.parse)(t.url,!0);for(let t of(delete r.search,Object.keys(r.query))){let n=t!==c.NEXT_QUERY_PARAM_PREFIX&&t.startsWith(c.NEXT_QUERY_PARAM_PREFIX),s=t!==c.NEXT_INTERCEPTION_MARKER_PREFIX&&t.startsWith(c.NEXT_INTERCEPTION_MARKER_PREFIX);(n||s||e.includes(t)||i&&Object.keys(i.groups).includes(t))&&delete r.query[t]}t.url=(0,n.format)(r)}function f(t,e,i){if(!i)return t;for(let n of Object.keys(i.groups)){let r,{optional:s,repeat:o}=i.groups[n],a=`[${o?"...":""}${n}]`;s&&(a=`[${a}]`);let l=e[n];r=Array.isArray(l)?l.map(t=>t&&encodeURIComponent(t)).join("/"):l?encodeURIComponent(l):"",t=t.replaceAll(a,r)}return t}function m(t,e,i,n){let r={};for(let s of Object.keys(e.groups)){let o=t[s];"string"==typeof o?o=(0,h.normalizeRscURL)(o):Array.isArray(o)&&(o=o.map(h.normalizeRscURL));let a=i[s],l=e.groups[s].optional;if((Array.isArray(a)?a.some(t=>Array.isArray(o)?o.some(e=>e.includes(t)):null==o?void 0:o.includes(t)):null==o?void 0:o.includes(a))||void 0===o&&!(l&&n))return{params:{},hasValidParams:!1};l&&(!o||Array.isArray(o)&&1===o.length&&("index"===o[0]||o[0]===`[[...${s}]]`))&&(o=void 0,delete t[s]),o&&"string"==typeof o&&e.groups[s].repeat&&(o=o.split("/")),o&&(r[s]=o)}return{params:r,hasValidParams:!0}}function g({page:t,i18n:e,basePath:i,rewrites:n,pageIsDynamic:h,trailingSlash:c,caseSensitive:g}){let y,v,x;return h&&(y=(0,o.getNamedRouteRegex)(t,{prefixRouteKeys:!1}),x=(v=(0,a.getRouteMatcher)(y))(t)),{handleRewrites:function(o,a){let d={},p=a.pathname,f=n=>{let u=(0,s.getPathMatch)(n.source+(c?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!a.pathname)return!1;let f=u(a.pathname);if((n.has||n.missing)&&f){let t=(0,l.matchHas)(o,a.query,n.has,n.missing);t?Object.assign(f,t):f=!1}if(f){let{parsedDestination:s,destQuery:o}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:f,query:a.query});if(s.protocol)return!0;if(Object.assign(d,o,f),Object.assign(a.query,s.query),delete s.query,Object.assign(a,s),!(p=a.pathname))return!1;if(i&&(p=p.replace(RegExp(`^${i}`),"")||"/"),e){let t=(0,r.normalizeLocalePath)(p,e.locales);p=t.pathname,a.query.nextInternalLocale=t.detectedLocale||f.nextInternalLocale}if(p===t)return!0;if(h&&v){let t=v(p);if(t)return a.query={...a.query,...t},!0}}return!1};for(let t of n.beforeFiles||[])f(t);if(p!==t){let e=!1;for(let t of n.afterFiles||[])if(e=f(t))break;if(!e&&!(()=>{let e=(0,u.removeTrailingSlash)(p||"");return e===(0,u.removeTrailingSlash)(t)||(null==v?void 0:v(e))})()){for(let t of n.fallback||[])if(e=f(t))break}}return d},defaultRouteRegex:y,dynamicRouteMatcher:v,defaultRouteMatches:x,getParamsFromRouteMatches:function(t){if(!y)return null;let{groups:e,routeKeys:i}=y,n=(0,a.getRouteMatcher)({re:{exec:t=>{let n=Object.fromEntries(new URLSearchParams(t));for(let[t,e]of Object.entries(n)){let i=(0,d.normalizeNextQueryParam)(t);i&&(n[i]=e,delete n[t])}let r={};for(let t of Object.keys(i)){let s=i[t];if(!s)continue;let o=e[s],a=n[t];if(!o.optional&&!a)return null;r[o.pos]=a}return r}},groups:e})(t);return n||null},normalizeDynamicRouteParams:(t,e)=>y&&x?m(t,y,x,e):{params:{},hasValidParams:!1},normalizeVercelUrl:(t,e)=>p(t,e,y),interpolateDynamicPath:(t,e)=>f(t,e,y)}}function y(t,e){return"string"==typeof t[c.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&t[c.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===e?t[c.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},7044:(t,e,i)=>{"use strict";i.d(e,{B:()=>n});let n=!1},8304:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return a},STATIC_METADATA_IMAGES:function(){return o},getExtensionRegexString:function(){return l},isMetadataPage:function(){return c},isMetadataRoute:function(){return d},isMetadataRouteFile:function(){return u},isStaticMetadataRoute:function(){return h}});let n=i(12958),r=i(74722),s=i(70554),o={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},a=["js","jsx","ts","tsx"],l=(t,e)=>e&&0!==e.length?`(?:\\.(${t.join("|")})|(\\.(${e.join("|")})))`:`(\\.(?:${t.join("|")}))`;function u(t,e,i){let r=(i?"":"?")+"$",s=`\\d?${i?"":"(-\\w{6})?"}`,a=[RegExp(`^[\\\\/]robots${l(e.concat("txt"),null)}${r}`),RegExp(`^[\\\\/]manifest${l(e.concat("webmanifest","json"),null)}${r}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],e)}${r}`),RegExp(`[\\\\/]${o.icon.filename}${s}${l(o.icon.extensions,e)}${r}`),RegExp(`[\\\\/]${o.apple.filename}${s}${l(o.apple.extensions,e)}${r}`),RegExp(`[\\\\/]${o.openGraph.filename}${s}${l(o.openGraph.extensions,e)}${r}`),RegExp(`[\\\\/]${o.twitter.filename}${s}${l(o.twitter.extensions,e)}${r}`)],u=(0,n.normalizePathSep)(t);return a.some(t=>t.test(u))}function h(t){let e=t.replace(/\/route$/,"");return(0,s.isAppRouteRoute)(t)&&u(e,[],!0)&&"/robots.txt"!==e&&"/manifest.webmanifest"!==e&&!e.endsWith("/sitemap.xml")}function c(t){return!(0,s.isAppRouteRoute)(t)&&u(t,[],!1)}function d(t){let e=(0,r.normalizeAppPath)(t).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==e[0]&&(e="/"+e),(0,s.isAppRouteRoute)(t)&&u(e,[],!1)}},12157:(t,e,i)=>{"use strict";i.d(e,{L:()=>n});let n=(0,i(43210).createContext)({})},12437:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getPathMatch",{enumerable:!0,get:function(){return r}});let n=i(35362);function r(t,e){let i=[],r=(0,n.pathToRegexp)(t,i,{delimiter:"/",sensitive:"boolean"==typeof(null==e?void 0:e.sensitive)&&e.sensitive,strict:null==e?void 0:e.strict}),s=(0,n.regexpToFunction)((null==e?void 0:e.regexModifier)?new RegExp(e.regexModifier(r.source),r.flags):r,i);return(t,n)=>{if("string"!=typeof t)return!1;let r=s(t);if(!r)return!1;if(null==e?void 0:e.removeUnnamedParams)for(let t of i)"number"==typeof t.name&&delete r.params[t.name];return{...n,...r.params}}}},12958:(t,e)=>{"use strict";function i(t){return t.replace(/\\/g,"/")}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"normalizePathSep",{enumerable:!0,get:function(){return i}})},15124:(t,e,i)=>{"use strict";i.d(e,{E:()=>r});var n=i(43210);let r=i(7044).B?n.useLayoutEffect:n.useEffect},15526:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{compileNonPath:function(){return h},matchHas:function(){return u},parseDestination:function(){return c},prepareDestination:function(){return d}});let n=i(35362),r=i(53293),s=i(76759),o=i(71437),a=i(88212);function l(t){return t.replace(/__ESC_COLON_/gi,":")}function u(t,e,i,n){void 0===i&&(i=[]),void 0===n&&(n=[]);let r={},s=i=>{let n,s=i.key;switch(i.type){case"header":s=s.toLowerCase(),n=t.headers[s];break;case"cookie":n="cookies"in t?t.cookies[i.key]:(0,a.getCookieParser)(t.headers)()[i.key];break;case"query":n=e[s];break;case"host":{let{host:e}=(null==t?void 0:t.headers)||{};n=null==e?void 0:e.split(":",1)[0].toLowerCase()}}if(!i.value&&n)return r[function(t){let e="";for(let i=0;i<t.length;i++){let n=t.charCodeAt(i);(n>64&&n<91||n>96&&n<123)&&(e+=t[i])}return e}(s)]=n,!0;if(n){let t=RegExp("^"+i.value+"$"),e=Array.isArray(n)?n.slice(-1)[0].match(t):n.match(t);if(e)return Array.isArray(e)&&(e.groups?Object.keys(e.groups).forEach(t=>{r[t]=e.groups[t]}):"host"===i.type&&e[0]&&(r.host=e[0])),!0}return!1};return!(!i.every(t=>s(t))||n.some(t=>s(t)))&&r}function h(t,e){if(!t.includes(":"))return t;for(let i of Object.keys(e))t.includes(":"+i)&&(t=t.replace(RegExp(":"+i+"\\*","g"),":"+i+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+i+"\\?","g"),":"+i+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+i+"\\+","g"),":"+i+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+i+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+i));return t=t.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+t,{validate:!1})(e).slice(1)}function c(t){let e=t.destination;for(let i of Object.keys({...t.params,...t.query}))i&&(e=e.replace(RegExp(":"+(0,r.escapeStringRegexp)(i),"g"),"__ESC_COLON_"+i));let i=(0,s.parseUrl)(e),n=i.pathname;n&&(n=l(n));let o=i.href;o&&(o=l(o));let a=i.hostname;a&&(a=l(a));let u=i.hash;return u&&(u=l(u)),{...i,pathname:n,hostname:a,href:o,hash:u}}function d(t){let e,i,r=Object.assign({},t.query),s=c(t),{hostname:a,query:u}=s,d=s.pathname;s.hash&&(d=""+d+s.hash);let p=[],f=[];for(let t of((0,n.pathToRegexp)(d,f),f))p.push(t.name);if(a){let t=[];for(let e of((0,n.pathToRegexp)(a,t),t))p.push(e.name)}let m=(0,n.compile)(d,{validate:!1});for(let[i,r]of(a&&(e=(0,n.compile)(a,{validate:!1})),Object.entries(u)))Array.isArray(r)?u[i]=r.map(e=>h(l(e),t.params)):"string"==typeof r&&(u[i]=h(l(r),t.params));let g=Object.keys(t.params).filter(t=>"nextInternalLocale"!==t);if(t.appendParamsToQuery&&!g.some(t=>p.includes(t)))for(let e of g)e in u||(u[e]=t.params[e]);if((0,o.isInterceptionRouteAppPath)(d))for(let e of d.split("/")){let i=o.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t));if(i){"(..)(..)"===i?(t.params["0"]="(..)",t.params["1"]="(..)"):t.params["0"]=i;break}}try{let[n,r]=(i=m(t.params)).split("#",2);e&&(s.hostname=e(t.params)),s.pathname=n,s.hash=(r?"#":"")+(r||""),delete s.search}catch(t){if(t.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw t}return s.query={...r,...s.query},{newUrl:i,destQuery:u,parsedDestination:s}}},18171:(t,e,i)=>{"use strict";i.d(e,{s:()=>r});var n=i(74479);function r(t){return(0,n.G)(t)&&"offsetHeight"in t}},21279:(t,e,i)=>{"use strict";i.d(e,{t:()=>n});let n=(0,i(43210).createContext)(null)},23736:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"parseRelativeUrl",{enumerable:!0,get:function(){return r}}),i(44827);let n=i(42785);function r(t,e,i){void 0===i&&(i=!0);let r=new URL("http://n"),s=e?new URL(e,r):t.startsWith(".")?new URL("http://n"):r,{pathname:o,searchParams:a,search:l,hash:u,href:h,origin:c}=new URL(t,s);if(c!==r.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+t),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:o,query:i?(0,n.searchParamsToUrlQuery)(a):void 0,search:l,hash:u,href:h.slice(c.length)}}},26001:(t,e,i)=>{"use strict";let n;function r(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function o(t,e,i,n){if("function"==typeof e){let[r,o]=s(n);e=e(void 0!==i?i:t.custom,r,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,o]=s(n);e=e(void 0!==i?i:t.custom,r,o)}return e}function a(t,e,i){let n=t.getProps();return o(n,e,void 0!==i?i:n.custom,t)}function l(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>sR});let u=t=>t,h={},c=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],d={value:null,addProjectionMetrics:null};function p(t,e){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=c.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,r=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,s=!1)=>{let a=s&&r?i:n;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{if(a=t,r){s=!0;return}r=!0,[i,n]=[n,i],i.forEach(u),e&&d.value&&d.value.frameloop[e].push(l),l=0,i.clear(),r=!1,s&&(s=!1,h.process(t))}};return h}(s,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:p,update:f,preRender:m,render:g,postRender:y}=o,v=()=>{let s=h.useManualTiming?r.timestamp:performance.now();i=!1,h.useManualTiming||(r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1)),r.timestamp=s,r.isProcessing=!0,a.process(r),l.process(r),u.process(r),p.process(r),f.process(r),m.process(r),g.process(r),y.process(r),r.isProcessing=!1,i&&e&&(n=!1,t(v))},x=()=>{i=!0,n=!0,r.isProcessing||t(v)};return{schedule:c.reduce((t,e)=>{let n=o[e];return t[e]=(t,e=!1,r=!1)=>(i||x(),n.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<c.length;e++)o[c[e]].cancel(t)},state:r,steps:o}}let{schedule:f,cancel:m,state:g,steps:y}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],x=new Set(v),b=new Set(["width","height","top","left","right","bottom",...v]);function P(t,e){-1===t.indexOf(e)&&t.push(e)}function T(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class E{constructor(){this.subscriptions=[]}add(t){return P(this.subscriptions,t),()=>T(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function w(){n=void 0}let A={now:()=>(void 0===n&&A.set(g.isProcessing||h.useManualTiming?g.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(w)}},S=t=>!isNaN(parseFloat(t)),R={current:void 0};class M{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=A.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=A.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=S(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new E);let i=this.events[t].add(e);return"change"===t?()=>{i(),f.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return R.current&&R.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=A.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function C(t,e){return new M(t,e)}let D=t=>Array.isArray(t),j=t=>!!(t&&t.getVelocity);function k(t,e){let i=t.getValue("willChange");if(j(i)&&i.add)return i.add(e);if(!i&&h.WillChange){let i=new h.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let V=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),O="data-"+V("framerAppearId"),_=(t,e)=>i=>e(t(i)),L=(...t)=>t.reduce(_),F=(t,e,i)=>i>e?e:i<t?t:i,I=t=>1e3*t,N=t=>t/1e3,$={layout:0,mainThread:0,waapi:0},U=()=>{},B=()=>{},W=t=>e=>"string"==typeof e&&e.startsWith(t),z=W("--"),X=W("var(--"),H=t=>!!X(t)&&q.test(t.split("/*")[0].trim()),q=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,K={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},Y={...K,transform:t=>F(0,1,t)},G={...K,default:1},Z=t=>Math.round(1e5*t)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>i=>!!("string"==typeof i&&J.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),te=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,s,o,a]=n.match(Q);return{[t]:parseFloat(r),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},ti=t=>F(0,255,t),tn={...K,transform:t=>Math.round(ti(t))},tr={test:tt("rgb","red"),parse:te("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+tn.transform(t)+", "+tn.transform(e)+", "+tn.transform(i)+", "+Z(Y.transform(n))+")"},ts={test:tt("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:tr.transform},to=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),ta=to("deg"),tl=to("%"),tu=to("px"),th=to("vh"),tc=to("vw"),td={...tl,parse:t=>tl.parse(t)/100,transform:t=>tl.transform(100*t)},tp={test:tt("hsl","hue"),parse:te("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+tl.transform(Z(e))+", "+tl.transform(Z(i))+", "+Z(Y.transform(n))+")"},tf={test:t=>tr.test(t)||ts.test(t)||tp.test(t),parse:t=>tr.test(t)?tr.parse(t):tp.test(t)?tp.parse(t):ts.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tr.transform(t):tp.transform(t),getAnimatableNone:t=>{let e=tf.parse(t);return e.alpha=0,tf.transform(e)}},tm=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tg="number",ty="color",tv=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tx(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,o=e.replace(tv,t=>(tf.test(t)?(n.color.push(s),r.push(ty),i.push(tf.parse(t))):t.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(t)):(n.number.push(s),r.push(tg),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:n,types:r}}function tb(t){return tx(t).values}function tP(t){let{split:e,types:i}=tx(t),n=e.length;return t=>{let r="";for(let s=0;s<n;s++)if(r+=e[s],void 0!==t[s]){let e=i[s];e===tg?r+=Z(t[s]):e===ty?r+=tf.transform(t[s]):r+=t[s]}return r}}let tT=t=>"number"==typeof t?0:tf.test(t)?tf.getAnimatableNone(t):t,tE={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Q)?.length||0)+(t.match(tm)?.length||0)>0},parse:tb,createTransformer:tP,getAnimatableNone:function(t){let e=tb(t);return tP(t)(e.map(tT))}};function tw(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tA(t,e){return i=>i>0?e:t}let tS=(t,e,i)=>t+(e-t)*i,tR=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},tM=[ts,tr,tp],tC=t=>tM.find(e=>e.test(t));function tD(t){let e=tC(t);if(U(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tp&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;r=tw(a,n,t+1/3),s=tw(a,n,t),o=tw(a,n,t-1/3)}else r=s=o=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(i)),i}let tj=(t,e)=>{let i=tD(t),n=tD(e);if(!i||!n)return tA(t,e);let r={...i};return t=>(r.red=tR(i.red,n.red,t),r.green=tR(i.green,n.green,t),r.blue=tR(i.blue,n.blue,t),r.alpha=tS(i.alpha,n.alpha,t),tr.transform(r))},tk=new Set(["none","hidden"]);function tV(t,e){return i=>tS(t,e,i)}function tO(t){return"number"==typeof t?tV:"string"==typeof t?H(t)?tA:tf.test(t)?tj:tF:Array.isArray(t)?t_:"object"==typeof t?tf.test(t)?tj:tL:tA}function t_(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>tO(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function tL(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=tO(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let tF=(t,e)=>{let i=tE.createTransformer(e),n=tx(t),r=tx(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?tk.has(t)&&!r.values.length||tk.has(e)&&!n.values.length?function(t,e){return tk.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):L(t_(function(t,e){let i=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let s=e.types[r],o=t.indexes[s][n[s]],a=t.values[o]??0;i[r]=a,n[s]++}return i}(n,r),r.values),i):(U(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tA(t,e))};function tI(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tS(t,e,i):tO(t)(t,e)}let tN=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>f.update(e,t),stop:()=>m(e),now:()=>g.isProcessing?g.timestamp:A.now()}},t$=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=Math.round(1e4*t(e/(r-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function tU(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tB(t,e,i){var n,r;let s=Math.max(e-5,0);return n=i-t(s),(r=e-s)?1e3/r*n:0}let tW={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tz(t,e){return t*Math.sqrt(1-e*e)}let tX=["duration","bounce"],tH=["stiffness","damping","mass"];function tq(t,e){return e.some(e=>void 0!==t[e])}function tK(t=tW.visualDuration,e=tW.bounce){let i,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:s}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:c,duration:d,velocity:p,isResolvedFromDuration:f}=function(t){let e={velocity:tW.velocity,stiffness:tW.stiffness,damping:tW.damping,mass:tW.mass,isResolvedFromDuration:!1,...t};if(!tq(t,tH)&&tq(t,tX))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*F(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:tW.mass,stiffness:n,damping:r}}else{let i=function({duration:t=tW.duration,bounce:e=tW.bounce,velocity:i=tW.velocity,mass:n=tW.mass}){let r,s;U(t<=I(tW.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=F(tW.minDamping,tW.maxDamping,o),t=F(tW.minDuration,tW.maxDuration,N(t)),o<1?(r=e=>{let n=e*o,r=n*t;return .001-(n-i)/tz(e,o)*Math.exp(-r)},s=e=>{let n=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=tz(Math.pow(e,2),o);return(n*i+i-s)*a*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,s,5/t);if(t=I(t),isNaN(a))return{stiffness:tW.stiffness,damping:tW.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:tW.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-N(n.velocity||0)}),m=p||0,g=h/(2*Math.sqrt(u*c)),y=a-o,v=N(Math.sqrt(u/c)),x=5>Math.abs(y);if(r||(r=x?tW.restSpeed.granular:tW.restSpeed.default),s||(s=x?tW.restDelta.granular:tW.restDelta.default),g<1){let t=tz(v,g);i=e=>a-Math.exp(-g*v*e)*((m+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-v*t)*(y+(m+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),n=Math.min(t*e,300);return a-i*((m+g*v*y)*Math.sinh(n)+t*y*Math.cosh(n))/t}}let b={calculatedDuration:f&&d||null,next:t=>{let e=i(t);if(f)l.done=t>=d;else{let n=0===t?m:0;g<1&&(n=0===t?I(m):tB(i,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(n)<=r&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(tU(b),2e4),e=t$(e=>b.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return b}function tY({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,d,p=t[0],f={done:!1,value:p},m=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,y=i*e,v=p+y,x=void 0===o?v:o(v);x!==v&&(y=x-p);let b=t=>-y*Math.exp(-t/n),P=t=>x+b(t),T=t=>{let e=b(t),i=P(t);f.done=Math.abs(e)<=u,f.value=f.done?x:i},E=t=>{m(f.value)&&(c=t,d=tK({keyframes:[f.value,g(f.value)],velocity:tB(P,t,f.value),damping:r,stiffness:s,restDelta:u,restSpeed:h}))};return E(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==c||(e=!0,T(t),E(t)),void 0!==c&&t>=c)?d.next(t-c):(e||T(t),f)}}}tK.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),r=Math.min(tU(n),2e4);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:N(r)}}(t,100,tK);return t.ease=e.ease,t.duration=I(e.duration),t.type="keyframes",t};let tG=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tZ(t,e,i,n){if(t===e&&i===n)return u;let r=e=>(function(t,e,i,n,r){let s,o,a=0;do(s=tG(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tG(r(t),e,n)}let tQ=tZ(.42,0,1,1),tJ=tZ(0,0,.58,1),t0=tZ(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t2=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t5=t=>e=>1-t(1-e),t3=tZ(.33,1.53,.69,.99),t4=t5(t3),t7=t2(t4),t9=t=>(t*=2)<1?.5*t4(t):.5*(2-Math.pow(2,-10*(t-1))),t6=t=>1-Math.sin(Math.acos(t)),t8=t5(t6),et=t2(t6),ee=t=>Array.isArray(t)&&"number"==typeof t[0],ei={linear:u,easeIn:tQ,easeInOut:t0,easeOut:tJ,circIn:t6,circInOut:et,circOut:t8,backIn:t4,backInOut:t7,backOut:t3,anticipate:t9},en=t=>"string"==typeof t,er=t=>{if(ee(t)){B(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,r]=t;return tZ(e,i,n,r)}return en(t)?(B(void 0!==ei[t],`Invalid easing type '${t}'`),ei[t]):t},es=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};function eo({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){var r;let s=t1(n)?n.map(er):er(n),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let s=t.length;if(B(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let n=[],r=i||h.mix||tI,s=t.length-1;for(let i=0;i<s;i++){let s=r(t[i],t[i+1]);e&&(s=L(Array.isArray(e)?e[i]||u:e,s)),n.push(s)}return n}(e,n,r),l=a.length,c=i=>{if(o&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=es(t[n],t[n+1],i);return a[n](r)};return i?e=>c(F(t[0],t[s-1],e)):c}((r=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=es(0,e,n);t.push(tS(i,1,r))}}(e,t.length-1),e}(e),r.map(e=>e*t)),e,{ease:Array.isArray(s)?s:e.map(()=>s||t0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let ea=t=>null!==t;function el(t,{repeat:e,repeatType:i="loop"},n,r=1){let s=t.filter(ea),o=r<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return o&&void 0!==n?n:s[o]}let eu={decay:tY,inertia:tY,tween:eo,keyframes:eo,spring:tK};function eh(t){"string"==typeof t.type&&(t.type=eu[t.type])}class ec{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ed=t=>t/100;class ep extends ec{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==A.now()&&this.tick(A.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},$.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;eh(t);let{type:e=eo,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:s=0}=t,{keyframes:o}=t,a=e||eo;a!==eo&&"number"!=typeof o[0]&&(this.mixKeyframes=L(ed,tI(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=tU(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:c,repeatDelay:d,type:p,onUpdate:f,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,x=i;if(h){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===c?(i=1-i,d&&(i-=d/o)):"mirror"===c&&(x=s)),v=F(0,1,i)*o}let b=y?{done:!1,value:u[0]}:x.next(v);r&&(b.value=r(b.value));let{done:P}=b;y||null===a||(P=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return T&&p!==tY&&(b.value=el(u,this.options,m,this.speed)),f&&f(b.value),T&&this.finish(),b}then(t,e){return this.finished.then(t,e)}get duration(){return N(this.calculatedDuration)}get time(){return N(this.currentTime)}set time(t){t=I(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(A.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=N(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tN,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(A.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,$.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let ef=t=>180*t/Math.PI,em=t=>ey(ef(Math.atan2(t[1],t[0]))),eg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:em,rotateZ:em,skewX:t=>ef(Math.atan(t[1])),skewY:t=>ef(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ey=t=>((t%=360)<0&&(t+=360),t),ev=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ex=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),eb={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ev,scaleY:ex,scale:t=>(ev(t)+ex(t))/2,rotateX:t=>ey(ef(Math.atan2(t[6],t[5]))),rotateY:t=>ey(ef(Math.atan2(-t[2],t[0]))),rotateZ:em,rotate:em,skewX:t=>ef(Math.atan(t[4])),skewY:t=>ef(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function eP(t){return+!!t.includes("scale")}function eT(t,e){let i,n;if(!t||"none"===t)return eP(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=eb,n=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=eg,n=e}if(!n)return eP(e);let s=i[e],o=n[1].split(",").map(ew);return"function"==typeof s?s(o):o[s]}let eE=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return eT(i,e)};function ew(t){return parseFloat(t.trim())}let eA=t=>t===K||t===tu,eS=new Set(["x","y","z"]),eR=v.filter(t=>!eS.has(t)),eM={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eT(e,"x"),y:(t,{transform:e})=>eT(e,"y")};eM.translateX=eM.x,eM.translateY=eM.y;let eC=new Set,eD=!1,ej=!1,ek=!1;function eV(){if(ej){let t=Array.from(eC).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eR.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}ej=!1,eD=!1,eC.forEach(t=>t.complete(ek)),eC.clear()}function eO(){eC.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(ej=!0)})}class e_{constructor(t,e,i,n,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(eC.add(this),eD||(eD=!0,f.read(eO),f.resolveKeyframes(eV))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let r=n?.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eC.delete(this)}cancel(){"scheduled"===this.state&&(eC.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eL=t=>t.startsWith("--");function eF(t){let e;return()=>(void 0===e&&(e=t()),e)}let eI=eF(()=>void 0!==window.ScrollTimeline),eN={},e$=function(t,e){let i=eF(t);return()=>eN[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eU=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,eB={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eU([0,.65,.55,1]),circOut:eU([.55,0,1,.45]),backIn:eU([.31,.01,.66,-.59]),backOut:eU([.33,1.53,.69,.99])};function eW(t){return"function"==typeof t&&"applyToOptions"in t}class ez extends ec{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=t,B("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return eW(t)&&e$()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let c=function t(e,i){if(e)return"function"==typeof e?e$()?t$(e,i):"ease-out":ee(e)?eU(e):Array.isArray(e)?e.map(e=>t(e,i)||eB.easeOut):eB[e]}(a,r);Array.isArray(c)&&(h.easing=c),d.value&&$.waapi++;let p={delay:n,duration:r,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(p.pseudoElement=u);let f=t.animate(h,p);return d.value&&f.finished.finally(()=>{$.waapi--}),f}(e,i,n,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=el(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eL(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return N(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return N(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=I(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eI())?(this.animation.timeline=t,u):e(this)}}let eX={anticipate:t9,backInOut:t7,circInOut:et};class eH extends ez{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eX&&(t.ease=eX[t.ease])}(t),eh(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:r,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new ep({...s,autoplay:!1}),a=I(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eq=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tE.test(t)||"0"===t)&&!t.startsWith("url("));var eK,eY,eG=i(18171);let eZ=new Set(["opacity","clipPath","filter","transform"]),eQ=eF(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eJ extends ec{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=A.now();let c={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,name:a,motionValue:l,element:u,...h},d=u?.KeyframeResolver||e_;this.keyframeResolver=new d(o,(t,e,i)=>this.onKeyframesResolved(t,e,c,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:r,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:c}=i;this.resolvedAt=A.now(),!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=eq(r,e),a=eq(s,e);return U(o===a,`You are trying to animate ${e} from "${r}" to "${s}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||eW(i))&&n)}(t,r,s,o)&&((h.instantAnimations||!a)&&c?.(el(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let d={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},p=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:o}=t;if(!(0,eG.s)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return eQ()&&i&&eZ.has(i)&&("transform"!==i||!l)&&!a&&!n&&"mirror"!==r&&0!==s&&"inertia"!==o}(d)?new eH({...d,element:d.motionValue.owner.current}):new ep(d);p.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),ek=!0,eO(),eV(),ek=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e0=t=>null!==t,e1={type:"spring",stiffness:500,damping:25,restSpeed:10},e2=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e5={type:"keyframes",duration:.8},e3={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e4=(t,{keyframes:e})=>e.length>2?e5:x.has(t)?t.startsWith("scale")?e2(e[1]):e1:e3,e7=(t,e,i,n={},r,s)=>o=>{let a=l(n,t)||{},u=a.delay||n.delay||0,{elapsed:c=0}=n;c-=I(u);let d={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&Object.assign(d,e4(t,d)),d.duration&&(d.duration=I(d.duration)),d.repeatDelay&&(d.repeatDelay=I(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let p=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0===d.delay&&(p=!0)),(h.instantAnimations||h.skipAnimations)&&(p=!0,d.duration=0,d.delay=0),d.allowFlatten=!a.type&&!a.ease,p&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let r=t.filter(e0),s=e&&"loop"!==i&&e%2==1?0:r.length-1;return r[s]}(d.keyframes,a);if(void 0!==t)return void f.update(()=>{d.onUpdate(t),d.onComplete()})}return a.isSync?new ep(d):new eJ(d)};function e9(t,e,{delay:i=0,transitionOverride:n,type:r}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...u}=e;n&&(s=n);let h=[],c=r&&t.animationState&&t.animationState.getState()[r];for(let e in u){let n=t.getValue(e,t.latestValues[e]??null),r=u[e];if(void 0===r||c&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(c,e))continue;let o={delay:i,...l(s||{},e)},a=n.get();if(void 0!==a&&!n.isAnimating&&!Array.isArray(r)&&r===a&&!o.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let i=t.props[O];if(i){let t=window.MotionHandoffAnimation(i,e,f);null!==t&&(o.startTime=t,d=!0)}}k(t,e),n.start(e7(e,n,r,t.shouldReduceMotion&&b.has(e)?{type:!1}:o,t,d));let p=n.animation;p&&h.push(p)}return o&&Promise.all(h).then(()=>{f.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=a(t,e)||{};for(let e in r={...r,...i}){var s;let i=D(s=r[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,C(i))}}(t,o)})}),h}function e6(t,e,i={}){let n=a(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(e9(t,n,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,n=0,r=0,s=1,o){let a=[],l=t.variantChildren.size,u=(l-1)*r,h="function"==typeof n,c=h?t=>n(t,l):1===s?(t=0)=>t*r:(t=0)=>u-t*r;return Array.from(t.variantChildren).sort(e8).forEach((t,r)=>{t.notify("AnimationStart",e),a.push(e6(t,e,{...o,delay:i+(h?0:n)+c(r)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,n,s,o,a,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([s(),o(i.delay)]);{let[t,e]="beforeChildren"===l?[s,o]:[o,s];return t().then(()=>e())}}function e8(t,e){return t.sortNodePosition(e)}function it(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function ie(t){return"string"==typeof t||Array.isArray(t)}let ii=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ir=["initial",...ii],is=ir.length,io=[...ii].reverse(),ia=ii.length;function il(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iu(){return{animate:il(!0),whileInView:il(),whileHover:il(),whileTap:il(),whileDrag:il(),whileFocus:il(),exit:il()}}class ih{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ic extends ih{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>e6(t,e,i)));else if("string"==typeof e)n=e6(t,e,i);else{let r="function"==typeof e?a(t,e,i.custom):e;n=Promise.all(e9(t,r,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iu(),n=!0,s=e=>(i,n)=>{let r=a(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...n}=r;i={...i,...n,...e}}return i};function o(o){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<is;t++){let n=ir[t],r=e.props[n];(ie(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},h=[],c=new Set,d={},p=1/0;for(let e=0;e<ia;e++){var f,m;let a=io[e],g=i[a],y=void 0!==l[a]?l[a]:u[a],v=ie(y),x=a===o?g.isActive:null;!1===x&&(p=e);let b=y===u[a]&&y!==l[a]&&v;if(b&&n&&t.manuallyAnimateOnMount&&(b=!1),g.protectedKeys={...d},!g.isActive&&null===x||!y&&!g.prevProp||r(y)||"boolean"==typeof y)continue;let P=(f=g.prevProp,"string"==typeof(m=y)?m!==f:!!Array.isArray(m)&&!it(m,f)),T=P||a===o&&g.isActive&&!b&&v||e>p&&v,E=!1,w=Array.isArray(y)?y:[y],A=w.reduce(s(a),{});!1===x&&(A={});let{prevResolvedValues:S={}}=g,R={...S,...A},M=e=>{T=!0,c.has(e)&&(E=!0,c.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in R){let e=A[t],i=S[t];if(d.hasOwnProperty(t))continue;let n=!1;(D(e)&&D(i)?it(e,i):e===i)?void 0!==e&&c.has(t)?M(t):g.protectedKeys[t]=!0:null!=e?M(t):c.add(t)}g.prevProp=y,g.prevResolvedValues=A,g.isActive&&(d={...d,...A}),n&&t.blockInitialAnimation&&(T=!1);let C=!(b&&P)||E;T&&C&&h.push(...w.map(t=>({animation:t,options:{type:a}})))}if(c.size){let e={};if("boolean"!=typeof l.initial){let i=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}c.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=n??null}),h.push({animation:e})}let g=!!h.length;return n&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(g=!1),n=!1,g?e(h):Promise.resolve()}return{animateChanges:o,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let r=o(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iu(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();r(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let id=0;class ip extends ih{constructor(){super(...arguments),this.id=id++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let im={x:!1,y:!1};function ig(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let iy=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iv(t){return{point:{x:t.pageX,y:t.pageY}}}let ix=t=>e=>iy(e)&&t(e,iv(e));function ib(t,e,i,n){return ig(t,e,ix(i),n)}function iP({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function iT(t){return t.max-t.min}function iE(t,e,i,n=.5){t.origin=n,t.originPoint=tS(e.min,e.max,t.origin),t.scale=iT(i)/iT(e),t.translate=tS(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iw(t,e,i,n){iE(t.x,e.x,i.x,n?n.originX:void 0),iE(t.y,e.y,i.y,n?n.originY:void 0)}function iA(t,e,i){t.min=i.min+e.min,t.max=t.min+iT(e)}function iS(t,e,i){t.min=e.min-i.min,t.max=t.min+iT(e)}function iR(t,e,i){iS(t.x,e.x,i.x),iS(t.y,e.y,i.y)}let iM=()=>({translate:0,scale:1,origin:0,originPoint:0}),iC=()=>({x:iM(),y:iM()}),iD=()=>({min:0,max:0}),ij=()=>({x:iD(),y:iD()});function ik(t){return[t("x"),t("y")]}function iV(t){return void 0===t||1===t}function iO({scale:t,scaleX:e,scaleY:i}){return!iV(t)||!iV(e)||!iV(i)}function i_(t){return iO(t)||iL(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iL(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iF(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function iI(t,e=0,i=1,n,r){t.min=iF(t.min,e,i,n,r),t.max=iF(t.max,e,i,n,r)}function iN(t,{x:e,y:i}){iI(t.x,e.translate,e.scale,e.originPoint),iI(t.y,i.translate,i.scale,i.originPoint)}function i$(t,e){t.min=t.min+e,t.max=t.max+e}function iU(t,e,i,n,r=.5){let s=tS(t.min,t.max,r);iI(t,e,i,s,n)}function iB(t,e){iU(t.x,e.x,e.scaleX,e.scale,e.originX),iU(t.y,e.y,e.scaleY,e.scale,e.originY)}function iW(t,e){return iP(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let iz=({current:t})=>t?t.ownerDocument.defaultView:null;function iX(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iH=(t,e)=>Math.abs(t-e);class iq{constructor(t,e,{transformPagePoint:i,contextWindow:n=window,dragSnapToOrigin:r=!1,distanceThreshold:s=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iG(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iH(t.x,e.x)**2+iH(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:n}=t,{timestamp:r}=g;this.history.push({...n,timestamp:r});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iK(e,this.transformPagePoint),f.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iG("pointercancel"===t.type?this.lastMoveEventInfo:iK(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!iy(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=s,this.contextWindow=n||window;let o=iK(iv(t),this.transformPagePoint),{point:a}=o,{timestamp:l}=g;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=e;u&&u(t,iG(o,this.history)),this.removeListeners=L(ib(this.contextWindow,"pointermove",this.handlePointerMove),ib(this.contextWindow,"pointerup",this.handlePointerUp),ib(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}}function iK(t,e){return e?{point:e(t.point)}:t}function iY(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iG({point:t},e){return{point:t,delta:iY(t,iZ(e)),offset:iY(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=iZ(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>I(.1)));)i--;if(!n)return{x:0,y:0};let s=N(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iZ(t){return t[t.length-1]}function iQ(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iJ(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function i0(t,e,i){return{min:i1(t,e),max:i1(t,i)}}function i1(t,e){return"number"==typeof t?t:t[e]||0}let i2=new WeakMap;class i5{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ij(),this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:i}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new iq(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iv(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(im[t])return null;else return im[t]=!0,()=>{im[t]=!1};return im.x||im.y?null:(im.x=im.y=!0,()=>{im.x=im.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ik(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tl.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=iT(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&f.postRender(()=>r(t,e)),k(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>ik(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,distanceThreshold:i,contextWindow:iz(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:r}=this.getProps();r&&f.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!i3(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?tS(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?tS(i,t,n.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&iX(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:r}){return{x:iQ(t.x,i,r),y:iQ(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i0(t,"left","right"),y:i0(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&ik(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iX(e))return!1;let n=e.current;B(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=iW(t,i),{scroll:r}=e;return r&&(i$(n.x,r.offset.x),i$(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o=(t=r.layout.layoutBox,{x:iJ(t.x,s.x),y:iJ(t.y,s.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iP(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(ik(o=>{if(!i3(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return k(this.visualElement,t),i.start(e7(t,i,0,e,this.visualElement,!1))}stopAnimation(){ik(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){ik(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){ik(e=>{let{drag:i}=this.getProps();if(!i3(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-tS(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iX(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};ik(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=iT(t),r=iT(e);return r>n?i=es(e.min,e.max-n,t.min):n>r&&(i=es(t.min,t.max-r,e.min)),F(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),ik(e=>{if(!i3(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set(tS(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;i2.set(this.visualElement,this);let t=ib(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iX(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),f.read(e);let r=ig(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(ik(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}}function i3(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i4 extends ih{constructor(t){super(t),this.removeGroupControls=u,this.removeListeners=u,this.controls=new i5(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let i7=t=>(e,i)=>{t&&f.postRender(()=>t(e,i))};class i9 extends ih{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(t){this.session=new iq(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iz(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:i7(t),onStart:i7(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&f.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=ib(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i6=i(60687);let{schedule:i8}=p(queueMicrotask,!1);var nt=i(43210),ne=i(86044),ni=i(12157);let nn=(0,nt.createContext)({}),nr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ns(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let no={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tu.test(t))return t;else t=parseFloat(t);let i=ns(t,e.target.x),n=ns(t,e.target.y);return`${i}% ${n}%`}},na={},nl=!1;class nu extends nt.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;for(let t in nc)na[t]=nc[t],z(t)&&(na[t].isCSSVariable=!0);r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),nl&&r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),nr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,{projection:s}=i;return s&&(s.isPresent=r,nl=!0,n||t.layoutDependency!==e||void 0===e||t.isPresent!==r?s.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?s.promote():s.relegate()||f.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),i8.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nh(t){let[e,i]=(0,ne.xQ)(),n=(0,nt.useContext)(ni.L);return(0,i6.jsx)(nu,{...t,layoutGroup:n,switchLayoutGroup:(0,nt.useContext)(nn),isPresent:e,safeToRemove:i})}let nc={borderRadius:{...no,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:no,borderTopRightRadius:no,borderBottomLeftRadius:no,borderBottomRightRadius:no,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tE.parse(t);if(n.length>5)return t;let r=tE.createTransformer(t),s=+("number"!=typeof n[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;n[0+s]/=o,n[1+s]/=a;let l=tS(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}};var nd=i(74479);function np(t){return(0,nd.G)(t)&&"ownerSVGElement"in t}let nf=(t,e)=>t.depth-e.depth;class nm{constructor(){this.children=[],this.isDirty=!1}add(t){P(this.children,t),this.isDirty=!0}remove(t){T(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nf),this.isDirty=!1,this.children.forEach(t)}}function ng(t){return j(t)?t.get():t}let ny=["TopLeft","TopRight","BottomLeft","BottomRight"],nv=ny.length,nx=t=>"string"==typeof t?parseFloat(t):t,nb=t=>"number"==typeof t||tu.test(t);function nP(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nT=nw(0,.5,t8),nE=nw(.5,.95,u);function nw(t,e,i){return n=>n<t?0:n>e?1:i(es(t,e,n))}function nA(t,e){t.min=e.min,t.max=e.max}function nS(t,e){nA(t.x,e.x),nA(t.y,e.y)}function nR(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nM(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function nC(t,e,[i,n,r],s,o){!function(t,e=0,i=1,n=.5,r,s=t,o=t){if(tl.test(e)&&(e=parseFloat(e),e=tS(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tS(s.min,s.max,n);t===s&&(a-=e),t.min=nM(t.min,e,i,a,r),t.max=nM(t.max,e,i,a,r)}(t,e[i],e[n],e[r],e.scale,s,o)}let nD=["x","scaleX","originX"],nj=["y","scaleY","originY"];function nk(t,e,i,n){nC(t.x,e,nD,i?i.x:void 0,n?n.x:void 0),nC(t.y,e,nj,i?i.y:void 0,n?n.y:void 0)}function nV(t){return 0===t.translate&&1===t.scale}function nO(t){return nV(t.x)&&nV(t.y)}function n_(t,e){return t.min===e.min&&t.max===e.max}function nL(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nF(t,e){return nL(t.x,e.x)&&nL(t.y,e.y)}function nI(t){return iT(t.x)/iT(t.y)}function nN(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class n${constructor(){this.members=[]}add(t){P(this.members,t),t.scheduleRender()}remove(t){if(T(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nU={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nB=["","X","Y","Z"],nW=0;function nz(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function nX({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=nW++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,d.value&&(nU.nodes=nU.calculatedTargetDeltas=nU.calculatedProjections=0),this.nodes.forEach(nK),this.nodes.forEach(n1),this.nodes.forEach(n2),this.nodes.forEach(nY),d.addProjectionMetrics&&d.addProjectionMetrics(nU)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new nm)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new E),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=np(e)&&!(np(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i,n=0,r=()=>this.root.updateBlockedByResize=!1;f.read(()=>{n=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==n&&(n=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=A.now(),n=({timestamp:r})=>{let s=r-i;s>=250&&(m(n),t(s-e))};return f.setup(n,!0),()=>m(n)}(r,250),nr.hasAnimatedSinceResize&&(nr.hasAnimatedSinceResize=!1,this.nodes.forEach(n0)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||n6,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),u=!this.targetLayout||!nF(this.targetLayout,n),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...l(s,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||n0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n5),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[O];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",f,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nZ);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(nQ);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(nJ),this.nodes.forEach(nH),this.nodes.forEach(nq)):this.nodes.forEach(nQ),this.clearAllSnapshots();let t=A.now();g.delta=F(0,1e3/60,t-g.timestamp),g.timestamp=t,g.isProcessing=!0,y.update.process(g),y.preRender.process(g),y.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,i8.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nG),this.sharedNodes.forEach(n3)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,f.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){f.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iT(this.snapshot.measuredBox.x)||iT(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ij(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nO(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||i_(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),re((e=n).x),re(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return ij();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rn))){let{scroll:t}=this.root;t&&(i$(e.x,t.offset.x),i$(e.y,t.offset.y))}return e}removeElementScroll(t){let e=ij();if(nS(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&nS(e,t),i$(e.x,r.offset.x),i$(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=ij();nS(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iB(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),i_(n.latestValues)&&iB(i,n.latestValues)}return i_(this.latestValues)&&iB(i,this.latestValues),i}removeTransform(t){let e=ij();nS(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!i_(i.latestValues))continue;iO(i.latestValues)&&i.updateSnapshot();let n=ij();nS(n,i.measurePageBox()),nk(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return i_(this.latestValues)&&nk(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ij(),this.relativeTargetOrigin=ij(),iR(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nS(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=ij(),this.targetWithTransforms=ij()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,iA(s.x,o.x,a.x),iA(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nS(this.target,this.layout.layoutBox),iN(this.target,this.targetDelta)):nS(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ij(),this.relativeTargetOrigin=ij(),iR(this.relativeTargetOrigin,this.target,t.target),nS(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}d.value&&nU.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iO(this.parent.latestValues)||iL(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===g.timestamp&&(i=!1),i)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;nS(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,n=!1){let r,s,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(r=i[a]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iB(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,iN(t,s)),n&&i_(r.latestValues)&&iB(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=ij());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nR(this.prevProjectionDelta.x,this.projectionDelta.x),nR(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iw(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&nN(this.projectionDelta.x,this.prevProjectionDelta.x)&&nN(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),d.value&&nU.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iC(),this.projectionDelta=iC(),this.projectionDeltaWithTransform=iC()}setAnimationOrigin(t,e=!1){let i,n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=iC();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=ij(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(n9));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(n4(o.x,t.x,n),n4(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,f,m,g;iR(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,f=this.relativeTargetOrigin,m=a,g=n,n7(p.x,f.x,m.x,g),n7(p.y,f.y,m.y,g),i&&(u=this.relativeTarget,d=i,n_(u.x,d.x)&&n_(u.y,d.y))&&(this.isProjectionDirty=!1),i||(i=ij()),nS(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=tS(0,i.opacity??1,nT(n)),t.opacityExit=tS(e.opacity??1,0,nE(n))):s&&(t.opacity=tS(e.opacity??1,i.opacity??1,n));for(let r=0;r<nv;r++){let s=`border${ny[r]}Radius`,o=nP(e,s),a=nP(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nb(o)===nb(a)?(t[s]=Math.max(tS(nx(o),nx(a),n),0),(tl.test(a)||tl.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=tS(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=f.update(()=>{nr.hasAnimatedSinceResize=!0,$.layout++,this.motionValue||(this.motionValue=C(0)),this.currentAnimation=function(t,e,i){let n=j(t)?t:C(t);return n.start(e7("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{$.layout--},onComplete:()=>{$.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&ri(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||ij();let e=iT(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=iT(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}nS(e,i),iB(e,r),iw(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new n$),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&nz("z",t,n,this.animationValues);for(let e=0;e<nB.length;e++)nz(`rotate${nB[e]}`,t,n,this.animationValues),nz(`skew${nB[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=ng(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=ng(e?.pointerEvents)||""),this.hasProjected&&!i_(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let r=n.animationValues||n.latestValues;this.applyTransformsToTarget();let s=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((r||s||o)&&(n=`translate3d(${r}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:o,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r);i&&(s=i(r,s)),t.transform=s;let{x:o,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*a.origin}% 0`,n.animationValues?t.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:t.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,na){if(void 0===r[e])continue;let{correct:i,applyTo:o,isCSSVariable:a}=na[e],l="none"===s?r[e]:i(r[e],n);if(o){let e=o.length;for(let i=0;i<e;i++)t[o[i]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=n===this?ng(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(nZ),this.root.sharedNodes.clear()}}}function nH(t){t.updateLayout()}function nq(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:r}=t.options,s=e.source!==t.layout.source;"size"===r?ik(t=>{let n=s?e.measuredBox[t]:e.layoutBox[t],r=iT(n);n.min=i[t].min,n.max=n.min+r}):ri(r,e.layoutBox,i)&&ik(n=>{let r=s?e.measuredBox[n]:e.layoutBox[n],o=iT(i[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=iC();iw(o,i,e.layoutBox);let a=iC();s?iw(a,t.applyTransform(n,!0),e.measuredBox):iw(a,i,e.layoutBox);let l=!nO(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=ij();iR(o,e.layoutBox,r.layoutBox);let a=ij();iR(a,i,s.layoutBox),nF(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nK(t){d.value&&nU.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nY(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function nG(t){t.clearSnapshot()}function nZ(t){t.clearMeasurements()}function nQ(t){t.isLayoutDirty=!1}function nJ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function n0(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function n1(t){t.resolveTargetDelta()}function n2(t){t.calcProjection()}function n5(t){t.resetSkewAndRotation()}function n3(t){t.removeLeadSnapshot()}function n4(t,e,i){t.translate=tS(e.translate,0,i),t.scale=tS(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function n7(t,e,i,n){t.min=tS(e.min,i.min,n),t.max=tS(e.max,i.max,n)}function n9(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let n6={duration:.45,ease:[.4,0,.1,1]},n8=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),rt=n8("applewebkit/")&&!n8("chrome/")?Math.round:u;function re(t){t.min=rt(t.min),t.max=rt(t.max)}function ri(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nI(e)-nI(i)))}function rn(t){return t!==t.root&&t.scroll?.wasRoot}let rr=nX({attachResizeListener:(t,e)=>ig(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rs={current:void 0},ro=nX({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rs.current){let t=new rr({});t.mount(window),t.setOptions({layoutScroll:!0}),rs.current=t}return rs.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function ra(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function rl(t){return!("touch"===t.pointerType||im.x||im.y)}function ru(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&f.postRender(()=>r(e,iv(e)))}class rh extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=ra(t,i),o=t=>{if(!rl(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let s=t=>{rl(t)&&(n(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(t=>{t.addEventListener("pointerenter",o,r)}),s}(t,(t,e)=>(ru(this.node,e,"Start"),t=>ru(this.node,t,"End"))))}unmount(){}}class rc extends ih{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=L(ig(this.node.current,"focus",()=>this.onFocus()),ig(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rd=(t,e)=>!!e&&(t===e||rd(t,e.parentElement)),rp=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rf=new WeakSet;function rm(t){return e=>{"Enter"===e.key&&t(e)}}function rg(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let ry=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=rm(()=>{if(rf.has(i))return;rg(i,"down");let t=rm(()=>{rg(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>rg(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function rv(t){return iy(t)&&!(im.x||im.y)}function rx(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&f.postRender(()=>r(e,iv(e)))}class rb extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=ra(t,i),o=t=>{let n=t.currentTarget;if(!rv(t))return;rf.add(n);let s=e(n,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),rf.has(n)&&rf.delete(n),rv(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,n===window||n===document||i.useGlobalTarget||rd(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),(0,eG.s)(t))&&(t.addEventListener("focus",t=>ry(t,r)),rp.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(rx(this.node,e,"Start"),(t,{success:e})=>rx(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rP=new WeakMap,rT=new WeakMap,rE=t=>{let e=rP.get(t.target);e&&e(t)},rw=t=>{t.forEach(rE)},rA={some:0,all:1};class rS extends ih{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:rA[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;rT.has(i)||rT.set(i,{});let n=rT.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(rw,{root:t,...e})),n[r]}(e);return rP.set(t,i),n.observe(t),()=>{rP.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rR=(0,nt.createContext)({strict:!1});var rM=i(32582);let rC=(0,nt.createContext)({});function rD(t){return r(t.animate)||ir.some(e=>ie(t[e]))}function rj(t){return!!(rD(t)||t.variants)}function rk(t){return Array.isArray(t)?t.join(" "):t}var rV=i(7044);let rO={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},r_={};for(let t in rO)r_[t]={isEnabled:e=>rO[t].some(t=>!!e[t])};let rL=Symbol.for("motionComponentSymbol");var rF=i(21279),rI=i(15124);function rN(t,{layout:e,layoutId:i}){return x.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!na[t]||"opacity"===t)}let r$=(t,e)=>e&&"number"==typeof t?e.transform(t):t,rU={...K,transform:Math.round},rB={borderWidth:tu,borderTopWidth:tu,borderRightWidth:tu,borderBottomWidth:tu,borderLeftWidth:tu,borderRadius:tu,radius:tu,borderTopLeftRadius:tu,borderTopRightRadius:tu,borderBottomRightRadius:tu,borderBottomLeftRadius:tu,width:tu,maxWidth:tu,height:tu,maxHeight:tu,top:tu,right:tu,bottom:tu,left:tu,padding:tu,paddingTop:tu,paddingRight:tu,paddingBottom:tu,paddingLeft:tu,margin:tu,marginTop:tu,marginRight:tu,marginBottom:tu,marginLeft:tu,backgroundPositionX:tu,backgroundPositionY:tu,rotate:ta,rotateX:ta,rotateY:ta,rotateZ:ta,scale:G,scaleX:G,scaleY:G,scaleZ:G,skew:ta,skewX:ta,skewY:ta,distance:tu,translateX:tu,translateY:tu,translateZ:tu,x:tu,y:tu,z:tu,perspective:tu,transformPerspective:tu,opacity:Y,originX:td,originY:td,originZ:tu,zIndex:rU,fillOpacity:Y,strokeOpacity:Y,numOctaves:rU},rW={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rz=v.length;function rX(t,e,i){let{style:n,vars:r,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(x.has(t)){o=!0;continue}if(z(t)){r[t]=i;continue}{let e=r$(i,rB[t]);t.startsWith("origin")?(a=!0,s[t]=e):n[t]=e}}if(!e.transform&&(o||i?n.transform=function(t,e,i){let n="",r=!0;for(let s=0;s<rz;s++){let o=v[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=r$(a,rB[o]);if(!l){r=!1;let e=rW[o]||o;n+=`${e}(${t}) `}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,r?"":n):r&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;n.transformOrigin=`${t} ${e} ${i}`}}let rH=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rq(t,e,i){for(let n in e)j(e[n])||rN(n,i)||(t[n]=e[n])}let rK={offset:"stroke-dashoffset",array:"stroke-dasharray"},rY={offset:"strokeDashoffset",array:"strokeDasharray"};function rG(t,{attrX:e,attrY:i,attrScale:n,pathLength:r,pathSpacing:s=1,pathOffset:o=0,...a},l,u,h){if(rX(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:d}=t;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=h?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==n&&(c.scale=n),void 0!==r&&function(t,e,i=1,n=0,r=!0){t.pathLength=1;let s=r?rK:rY;t[s.offset]=tu.transform(-n);let o=tu.transform(e),a=tu.transform(i);t[s.array]=`${o} ${a}`}(c,r,s,o,!1)}let rZ=()=>({...rH(),attrs:{}}),rQ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),rJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r0(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||rJ.has(t)}let r1=t=>!r0(t);try{!function(t){"function"==typeof t&&(r1=e=>e.startsWith("on")?!r0(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let r2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r5(t){if("string"!=typeof t||t.includes("-"));else if(r2.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var r3=i(72789);let r4=t=>(e,i)=>{let n=(0,nt.useContext)(rC),s=(0,nt.useContext)(rF.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,s){return{latestValues:function(t,e,i,n){let s={},a=n(t,{});for(let t in a)s[t]=ng(a[t]);let{initial:l,animate:u}=t,h=rD(t),c=rj(t);e&&c&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let d=!!i&&!1===i.initial,p=(d=d||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!r(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let n=o(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=d?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(i,n,s,t),renderState:e()}})(t,e,n,s);return i?a():(0,r3.M)(a)};function r7(t,e,i){let{style:n}=t,r={};for(let s in n)(j(n[s])||e.style&&j(e.style[s])||rN(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(r[s]=n[s]);return r}let r9={useVisualState:r4({scrapeMotionValuesFromProps:r7,createRenderState:rH})};function r6(t,e,i){let n=r7(t,e,i);for(let i in t)(j(t[i])||j(e[i]))&&(n[-1!==v.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let r8={useVisualState:r4({scrapeMotionValuesFromProps:r6,createRenderState:rZ})},st=t=>e=>e.test(t),se=[K,tu,tl,ta,tc,th,{test:t=>"auto"===t,parse:t=>t}],si=t=>se.find(st(t)),sn=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),sr=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ss=t=>/^0[^.\s]+$/u.test(t),so=new Set(["brightness","contrast","saturate","opacity"]);function sa(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(Q)||[];if(!n)return t;let r=i.replace(n,""),s=+!!so.has(e);return n!==i&&(s*=100),e+"("+s+r+")"}let sl=/\b([a-z-]*)\(.*?\)/gu,su={...tE,getAnimatableNone:t=>{let e=t.match(sl);return e?e.map(sa).join(" "):t}},sh={...rB,color:tf,backgroundColor:tf,outlineColor:tf,fill:tf,stroke:tf,borderColor:tf,borderTopColor:tf,borderRightColor:tf,borderBottomColor:tf,borderLeftColor:tf,filter:su,WebkitFilter:su},sc=t=>sh[t];function sd(t,e){let i=sc(t);return i!==su&&(i=tE),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let sp=new Set(["auto","none","0"]);class sf extends e_{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&H(n=n.trim())){let r=function t(e,i,n=1){B(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,s]=function(t){let e=sr.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${i??n}`,r]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return sn(t)?parseFloat(t):t}return H(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!b.has(i)||2!==t.length)return;let[n,r]=t,s=si(n),o=si(r);if(s!==o)if(eA(s)&&eA(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eM[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||ss(n)))&&i.push(e)}i.length&&function(t,e,i){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!sp.has(e)&&tx(e).values.length&&(n=t[r]),r++}if(n&&i)for(let r of e)t[r]=sd(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eM[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=eM[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let sm=[...se,tf,tE],sg=t=>sm.find(st(t)),sy={current:null},sv={current:!1},sx=new WeakMap,sb=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sP{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=e_,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=A.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,f.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=rD(e),this.isVariantNode=rj(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&j(e)&&e.set(a[t],!1)}}mount(t){this.current=t,sx.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sv.current||function(){if(sv.current=!0,rV.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sy.current=t.matches;t.addEventListener("change",e),e()}else sy.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sy.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=x.has(t);n&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&f.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in r_){let e=r_[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ij()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sb.length;e++){let i=sb[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if(j(r))t.addValue(n,r);else if(j(s))t.addValue(n,C(r,{owner:t}));else if(s!==r)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,C(void 0!==e?e:r,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=C(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(sn(i)||ss(i))?i=parseFloat(i):!sg(i)&&tE.test(e)&&(i=sd(t,e)),this.setBaseTarget(t,j(i)?i.get():i)),j(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=o(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||j(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new E),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sT extends sP{constructor(){super(...arguments),this.KeyframeResolver=sf}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;j(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function sE(t,{style:e,vars:i},n,r){let s,o=t.style;for(s in e)o[s]=e[s];for(s in r?.applyProjectionStyles(o,n),i)o.setProperty(s,i[s])}class sw extends sT{constructor(){super(...arguments),this.type="html",this.renderInstance=sE}readValueFromInstance(t,e){if(x.has(e))return this.projection?.isProjecting?eP(e):eE(t,e);{let i=window.getComputedStyle(t),n=(z(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return iW(t,e)}build(t,e,i){rX(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return r7(t,e,i)}}let sA=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sS extends sT{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ij}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(x.has(e)){let t=sc(e);return t&&t.default||0}return e=sA.has(e)?e:V(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return r6(t,e,i)}build(t,e,i){rG(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in sE(t,e,void 0,n),e.attrs)t.setAttribute(sA.has(i)?i:V(i),e.attrs[i])}mount(t){this.isSVGTag=rQ(t.tagName),super.mount(t)}}let sR=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((eK={animation:{Feature:ic},exit:{Feature:ip},inView:{Feature:rS},tap:{Feature:rb},focus:{Feature:rc},hover:{Feature:rh},pan:{Feature:i9},drag:{Feature:i4,ProjectionNode:ro,MeasureLayout:nh},layout:{ProjectionNode:ro,MeasureLayout:nh}},eY=(t,e)=>r5(t)?new sS(e):new sw(e,{allowProjection:t!==nt.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:n,Component:r}){function s(t,s){var o,a,l;let u,h={...(0,nt.useContext)(rM.Q),...t,layoutId:function({layoutId:t}){let e=(0,nt.useContext)(ni.L).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:c}=h,d=function(t){let{initial:e,animate:i}=function(t,e){if(rD(t)){let{initial:e,animate:i}=t;return{initial:!1===e||ie(e)?e:void 0,animate:ie(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,nt.useContext)(rC));return(0,nt.useMemo)(()=>({initial:e,animate:i}),[rk(e),rk(i)])}(t),p=n(t,c);if(!c&&rV.B){a=0,l=0,(0,nt.useContext)(rR).strict;let t=function(t){let{drag:e,layout:i}=r_;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(h);u=t.MeasureLayout,d.visualElement=function(t,e,i,n,r){let{visualElement:s}=(0,nt.useContext)(rC),o=(0,nt.useContext)(rR),a=(0,nt.useContext)(rF.t),l=(0,nt.useContext)(rM.Q).reducedMotion,u=(0,nt.useRef)(null);n=n||o.renderer,!u.current&&n&&(u.current=n(t,{visualState:e,parent:s,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let h=u.current,c=(0,nt.useContext)(nn);h&&!h.projection&&r&&("html"===h.type||"svg"===h.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!o||a&&iX(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,r,c);let d=(0,nt.useRef)(!1);(0,nt.useInsertionEffect)(()=>{h&&d.current&&h.update(i,a)});let p=i[O],f=(0,nt.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,rI.E)(()=>{h&&(d.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),i8.render(h.render),f.current&&h.animationState&&h.animationState.animateChanges())}),h}(r,p,h,e,t.ProjectionNode)}return(0,i6.jsxs)(rC.Provider,{value:d,children:[u&&d.visualElement?(0,i6.jsx)(u,{visualElement:d.visualElement,...h}):null,i(r,t,(o=d.visualElement,(0,nt.useCallback)(t=>{t&&p.onMount&&p.onMount(t),o&&(t?o.mount(t):o.unmount()),s&&("function"==typeof s?s(t):iX(s)&&(s.current=t))},[o])),p,c,d.visualElement)]})}t&&function(t){for(let e in t)r_[e]={...r_[e],...t[e]}}(t),s.displayName=`motion.${"string"==typeof r?r:`create(${r.displayName??r.name??""})`}`;let o=(0,nt.forwardRef)(s);return o[rL]=r,o}({...r5(t)?r8:r9,preloadedFeatures:eK,useRender:function(t=!1){return(e,i,n,{latestValues:r},s)=>{let o=(r5(e)?function(t,e,i,n){let r=(0,nt.useMemo)(()=>{let i=rZ();return rG(i,e,rQ(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};rq(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return rq(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,nt.useMemo)(()=>{let i=rH();return rX(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,r,s,e),a=function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(r1(r)||!0===i&&r0(r)||!e&&!r0(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(i,"string"==typeof e,t),l=e!==nt.Fragment?{...a,...o,ref:n}:{},{children:u}=i,h=(0,nt.useMemo)(()=>j(u)?u.get():u,[u]);return(0,nt.createElement)(e,{...l,children:h})}}(e),createVisualElement:eY,Component:t})}))},26415:t=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var e={};(()=>{e.parse=function(e,i){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var r={},s=e.split(n),o=(i||{}).decode||t,a=0;a<s.length;a++){var l=s[a],u=l.indexOf("=");if(!(u<0)){var h=l.substr(0,u).trim(),c=l.substr(++u,l.length).trim();'"'==c[0]&&(c=c.slice(1,-1)),void 0==r[h]&&(r[h]=function(t,e){try{return e(t)}catch(e){return t}}(c,o))}}return r},e.serialize=function(t,e,n){var s=n||{},o=s.encode||i;if("function"!=typeof o)throw TypeError("option encode is invalid");if(!r.test(t))throw TypeError("argument name is invalid");var a=o(e);if(a&&!r.test(a))throw TypeError("argument val is invalid");var l=t+"="+a;if(null!=s.maxAge){var u=s.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(s.domain){if(!r.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!r.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var t=decodeURIComponent,i=encodeURIComponent,n=/; */,r=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),t.exports=e})()},30660:(t,e)=>{"use strict";function i(t){let e=5381;for(let i=0;i<t.length;i++)e=(e<<5)+e+t.charCodeAt(i)|0;return e>>>0}function n(t){return i(t).toString(36).slice(0,5)}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{djb2Hash:function(){return i},hexHash:function(){return n}})},31658:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{fillMetadataSegment:function(){return d},normalizeMetadataPageToRoute:function(){return f},normalizeMetadataRoute:function(){return p}});let n=i(8304),r=function(t){return t&&t.__esModule?t:{default:t}}(i(78671)),s=i(6341),o=i(2015),a=i(30660),l=i(74722),u=i(12958),h=i(35499);function c(t){let e=r.default.dirname(t);if(t.endsWith("/sitemap"))return"";let i="";return e.split("/").some(t=>(0,h.isGroupSegment)(t)||(0,h.isParallelRouteSegment)(t))&&(i=(0,a.djb2Hash)(e).toString(36).slice(0,6)),i}function d(t,e,i){let n=(0,l.normalizeAppPath)(t),a=(0,o.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),h=(0,s.interpolateDynamicPath)(n,e,a),{name:d,ext:p}=r.default.parse(i),f=c(r.default.posix.join(t,d)),m=f?`-${f}`:"";return(0,u.normalizePathSep)(r.default.join(h,`${d}${m}${p}`))}function p(t){if(!(0,n.isMetadataPage)(t))return t;let e=t,i="";if("/robots"===t?e+=".txt":"/manifest"===t?e+=".webmanifest":i=c(t),!e.endsWith("/route")){let{dir:t,name:n,ext:s}=r.default.parse(e);e=r.default.posix.join(t,`${n}${i?`-${i}`:""}${s}`,"route")}return e}function f(t,e){let i=t.endsWith("/route"),n=i?t.slice(0,-6):t,r=n.endsWith("/sitemap")?".xml":"";return(e?`${n}/[__metadata_id__]`:`${n}${r}`)+(i?"/route":"")}},32582:(t,e,i)=>{"use strict";i.d(e,{Q:()=>n});let n=(0,i(43210).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},35362:t=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var e={};(()=>{function t(t,e){void 0===e&&(e={});for(var i=function(t){for(var e=[],i=0;i<t.length;){var n=t[i];if("*"===n||"+"===n||"?"===n){e.push({type:"MODIFIER",index:i,value:t[i++]});continue}if("\\"===n){e.push({type:"ESCAPED_CHAR",index:i++,value:t[i++]});continue}if("{"===n){e.push({type:"OPEN",index:i,value:t[i++]});continue}if("}"===n){e.push({type:"CLOSE",index:i,value:t[i++]});continue}if(":"===n){for(var r="",s=i+1;s<t.length;){var o=t.charCodeAt(s);if(o>=48&&o<=57||o>=65&&o<=90||o>=97&&o<=122||95===o){r+=t[s++];continue}break}if(!r)throw TypeError("Missing parameter name at "+i);e.push({type:"NAME",index:i,value:r}),i=s;continue}if("("===n){var a=1,l="",s=i+1;if("?"===t[s])throw TypeError('Pattern cannot start with "?" at '+s);for(;s<t.length;){if("\\"===t[s]){l+=t[s++]+t[s++];continue}if(")"===t[s]){if(0==--a){s++;break}}else if("("===t[s]&&(a++,"?"!==t[s+1]))throw TypeError("Capturing groups are not allowed at "+s);l+=t[s++]}if(a)throw TypeError("Unbalanced pattern at "+i);if(!l)throw TypeError("Missing pattern at "+i);e.push({type:"PATTERN",index:i,value:l}),i=s;continue}e.push({type:"CHAR",index:i,value:t[i++]})}return e.push({type:"END",index:i,value:""}),e}(t),n=e.prefixes,s=void 0===n?"./":n,o="[^"+r(e.delimiter||"/#?")+"]+?",a=[],l=0,u=0,h="",c=function(t){if(u<i.length&&i[u].type===t)return i[u++].value},d=function(t){var e=c(t);if(void 0!==e)return e;var n=i[u];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+t)},p=function(){for(var t,e="";t=c("CHAR")||c("ESCAPED_CHAR");)e+=t;return e};u<i.length;){var f=c("CHAR"),m=c("NAME"),g=c("PATTERN");if(m||g){var y=f||"";-1===s.indexOf(y)&&(h+=y,y=""),h&&(a.push(h),h=""),a.push({name:m||l++,prefix:y,suffix:"",pattern:g||o,modifier:c("MODIFIER")||""});continue}var v=f||c("ESCAPED_CHAR");if(v){h+=v;continue}if(h&&(a.push(h),h=""),c("OPEN")){var y=p(),x=c("NAME")||"",b=c("PATTERN")||"",P=p();d("CLOSE"),a.push({name:x||(b?l++:""),pattern:x&&!b?o:b,prefix:y,suffix:P,modifier:c("MODIFIER")||""});continue}d("END")}return a}function i(t,e){void 0===e&&(e={});var i=s(e),n=e.encode,r=void 0===n?function(t){return t}:n,o=e.validate,a=void 0===o||o,l=t.map(function(t){if("object"==typeof t)return RegExp("^(?:"+t.pattern+")$",i)});return function(e){for(var i="",n=0;n<t.length;n++){var s=t[n];if("string"==typeof s){i+=s;continue}var o=e?e[s.name]:void 0,u="?"===s.modifier||"*"===s.modifier,h="*"===s.modifier||"+"===s.modifier;if(Array.isArray(o)){if(!h)throw TypeError('Expected "'+s.name+'" to not repeat, but got an array');if(0===o.length){if(u)continue;throw TypeError('Expected "'+s.name+'" to not be empty')}for(var c=0;c<o.length;c++){var d=r(o[c],s);if(a&&!l[n].test(d))throw TypeError('Expected all "'+s.name+'" to match "'+s.pattern+'", but got "'+d+'"');i+=s.prefix+d+s.suffix}continue}if("string"==typeof o||"number"==typeof o){var d=r(String(o),s);if(a&&!l[n].test(d))throw TypeError('Expected "'+s.name+'" to match "'+s.pattern+'", but got "'+d+'"');i+=s.prefix+d+s.suffix;continue}if(!u){var p=h?"an array":"a string";throw TypeError('Expected "'+s.name+'" to be '+p)}}return i}}function n(t,e,i){void 0===i&&(i={});var n=i.decode,r=void 0===n?function(t){return t}:n;return function(i){var n=t.exec(i);if(!n)return!1;for(var s=n[0],o=n.index,a=Object.create(null),l=1;l<n.length;l++)!function(t){if(void 0!==n[t]){var i=e[t-1];"*"===i.modifier||"+"===i.modifier?a[i.name]=n[t].split(i.prefix+i.suffix).map(function(t){return r(t,i)}):a[i.name]=r(n[t],i)}}(l);return{path:s,index:o,params:a}}}function r(t){return t.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function s(t){return t&&t.sensitive?"":"i"}function o(t,e,i){void 0===i&&(i={});for(var n=i.strict,o=void 0!==n&&n,a=i.start,l=i.end,u=i.encode,h=void 0===u?function(t){return t}:u,c="["+r(i.endsWith||"")+"]|$",d="["+r(i.delimiter||"/#?")+"]",p=void 0===a||a?"^":"",f=0;f<t.length;f++){var m=t[f];if("string"==typeof m)p+=r(h(m));else{var g=r(h(m.prefix)),y=r(h(m.suffix));if(m.pattern)if(e&&e.push(m),g||y)if("+"===m.modifier||"*"===m.modifier){var v="*"===m.modifier?"?":"";p+="(?:"+g+"((?:"+m.pattern+")(?:"+y+g+"(?:"+m.pattern+"))*)"+y+")"+v}else p+="(?:"+g+"("+m.pattern+")"+y+")"+m.modifier;else p+="("+m.pattern+")"+m.modifier;else p+="(?:"+g+y+")"+m.modifier}}if(void 0===l||l)o||(p+=d+"?"),p+=i.endsWith?"(?="+c+")":"$";else{var x=t[t.length-1],b="string"==typeof x?d.indexOf(x[x.length-1])>-1:void 0===x;o||(p+="(?:"+d+"(?="+c+"))?"),b||(p+="(?="+d+"|"+c+")")}return new RegExp(p,s(i))}function a(e,i,n){if(e instanceof RegExp){if(!i)return e;var r=e.source.match(/\((?!\?)/g);if(r)for(var l=0;l<r.length;l++)i.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return e}return Array.isArray(e)?RegExp("(?:"+e.map(function(t){return a(t,i,n).source}).join("|")+")",s(n)):o(t(e,n),i,n)}Object.defineProperty(e,"__esModule",{value:!0}),e.parse=t,e.compile=function(e,n){return i(t(e,n),n)},e.tokensToFunction=i,e.match=function(t,e){var i=[];return n(a(t,i,e),i,e)},e.regexpToFunction=n,e.tokensToRegexp=o,e.pathToRegexp=a})(),t.exports=e})()},42785:(t,e)=>{"use strict";function i(t){let e={};for(let[i,n]of t.entries()){let t=e[i];void 0===t?e[i]=n:Array.isArray(t)?t.push(n):e[i]=[t,n]}return e}function n(t){return"string"==typeof t?t:("number"!=typeof t||isNaN(t))&&"boolean"!=typeof t?"":String(t)}function r(t){let e=new URLSearchParams;for(let[i,r]of Object.entries(t))if(Array.isArray(r))for(let t of r)e.append(i,n(t));else e.set(i,n(r));return e}function s(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];for(let e of i){for(let i of e.keys())t.delete(i);for(let[i,n]of e.entries())t.append(i,n)}return t}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{assign:function(){return s},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return r}})},44827:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{DecodeError:function(){return f},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return i},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return a},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return c},normalizeRepeatedSlashes:function(){return h},stringifyError:function(){return x}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function n(t){let e,i=!1;return function(){for(var n=arguments.length,r=Array(n),s=0;s<n;s++)r[s]=arguments[s];return i||(i=!0,e=t(...r)),e}}let r=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=t=>r.test(t);function o(){let{protocol:t,hostname:e,port:i}=window.location;return t+"//"+e+(i?":"+i:"")}function a(){let{href:t}=window.location,e=o();return t.substring(e.length)}function l(t){return"string"==typeof t?t:t.displayName||t.name||"Unknown"}function u(t){return t.finished||t.headersSent}function h(t){let e=t.split("?");return e[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(e[1]?"?"+e.slice(1).join("?"):"")}async function c(t,e){let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await c(e.Component,e.ctx)}:{};let n=await t.getInitialProps(e);if(i&&u(i))return n;if(!n)throw Object.defineProperty(Error('"'+l(t)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(t=>"function"==typeof performance[t]);class f extends Error{}class m extends Error{}class g extends Error{constructor(t){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+t}}class y extends Error{constructor(t,e){super(),this.message="Failed to load static file for page: "+t+" "+e}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(t){return JSON.stringify({message:t.message,stack:t.stack})}},53293:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"escapeStringRegexp",{enumerable:!0,get:function(){return r}});let i=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function r(t){return i.test(t)?t.replace(n,"\\$&"):t}},62688:(t,e,i)=>{"use strict";i.d(e,{A:()=>c});var n=i(43210);let r=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,i)=>i?i.toUpperCase():e.toLowerCase()),o=t=>{let e=s(t);return e.charAt(0).toUpperCase()+e.slice(1)},a=(...t)=>t.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim(),l=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,n.forwardRef)(({color:t="currentColor",size:e=24,strokeWidth:i=2,absoluteStrokeWidth:r,className:s="",children:o,iconNode:h,...c},d)=>(0,n.createElement)("svg",{ref:d,...u,width:e,height:e,stroke:t,strokeWidth:r?24*Number(i)/Number(e):i,className:a("lucide",s),...!o&&!l(c)&&{"aria-hidden":"true"},...c},[...h.map(([t,e])=>(0,n.createElement)(t,e)),...Array.isArray(o)?o:[o]])),c=(t,e)=>{let i=(0,n.forwardRef)(({className:i,...s},l)=>(0,n.createElement)(h,{ref:l,iconNode:e,className:a(`lucide-${r(o(t))}`,`lucide-${t}`,i),...s}));return i.displayName=o(t),i}},70554:(t,e)=>{"use strict";function i(t){return t.endsWith("/route")}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"isAppRouteRoute",{enumerable:!0,get:function(){return i}})},71437:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{INTERCEPTION_ROUTE_MARKERS:function(){return r},extractInterceptionRouteInformation:function(){return o},isInterceptionRouteAppPath:function(){return s}});let n=i(74722),r=["(..)(..)","(.)","(..)","(...)"];function s(t){return void 0!==t.split("/").find(t=>r.find(e=>t.startsWith(e)))}function o(t){let e,i,s;for(let n of t.split("/"))if(i=r.find(t=>n.startsWith(t))){[e,s]=t.split(i,2);break}if(!e||!i||!s)throw Object.defineProperty(Error("Invalid interception route: "+t+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(e=(0,n.normalizeAppPath)(e),i){case"(.)":s="/"===e?"/"+s:e+"/"+s;break;case"(..)":if("/"===e)throw Object.defineProperty(Error("Invalid interception route: "+t+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});s=e.split("/").slice(0,-1).concat(s).join("/");break;case"(...)":s="/"+s;break;case"(..)(..)":let o=e.split("/");if(o.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+t+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});s=o.slice(0,-2).concat(s).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:e,interceptedRoute:s}}},72789:(t,e,i)=>{"use strict";i.d(e,{M:()=>r});var n=i(43210);function r(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},74479:(t,e,i)=>{"use strict";function n(t){return"object"==typeof t&&null!==t}i.d(e,{G:()=>n})},74722:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{normalizeAppPath:function(){return s},normalizeRscURL:function(){return o}});let n=i(85531),r=i(35499);function s(t){return(0,n.ensureLeadingSlash)(t.split("/").reduce((t,e,i,n)=>!e||(0,r.isGroupSegment)(e)||"@"===e[0]||("page"===e||"route"===e)&&i===n.length-1?t:t+"/"+e,""))}function o(t){return t.replace(/\.rsc($|\?)/,"$1")}},76759:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"parseUrl",{enumerable:!0,get:function(){return s}});let n=i(42785),r=i(23736);function s(t){if(t.startsWith("/"))return(0,r.parseRelativeUrl)(t);let e=new URL(t);return{hash:e.hash,hostname:e.hostname,href:e.href,pathname:e.pathname,port:e.port,protocol:e.protocol,query:(0,n.searchParamsToUrlQuery)(e.searchParams),search:e.search}}},78034:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getRouteMatcher",{enumerable:!0,get:function(){return r}});let n=i(44827);function r(t){let{re:e,groups:i}=t;return t=>{let r=e.exec(t);if(!r)return!1;let s=t=>{try{return decodeURIComponent(t)}catch(t){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},o={};for(let[t,e]of Object.entries(i)){let i=r[e.pos];void 0!==i&&(e.repeat?o[t]=i.split("/").map(t=>s(t)):o[t]=s(i))}return o}}},85531:(t,e)=>{"use strict";function i(t){return t.startsWith("/")?t:"/"+t}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"ensureLeadingSlash",{enumerable:!0,get:function(){return i}})},86044:(t,e,i)=>{"use strict";i.d(e,{xQ:()=>s});var n=i(43210),r=i(21279);function s(t=!0){let e=(0,n.useContext)(r.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,n.useId)(),u=(0,n.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},88212:(t,e,i)=>{"use strict";function n(t){return function(){let{cookie:e}=t;if(!e)return{};let{parse:n}=i(26415);return n(Array.isArray(e)?e.join("; "):e)}}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getCookieParser",{enumerable:!0,get:function(){return n}})}};