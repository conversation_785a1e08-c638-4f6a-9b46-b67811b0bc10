import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // Disable problematic rules for Arabic RTL project
      "@next/next/no-img-element": "off", // Allow img tags for Arabic content
      "react/no-unescaped-entities": "off", // Allow Arabic characters and quotes
      "@typescript-eslint/no-unused-vars": "warn", // Warn instead of error
      "@typescript-eslint/no-explicit-any": "warn", // Warn for any types
      "react-hooks/exhaustive-deps": "warn", // Warn for missing dependencies
      "prefer-const": "warn", // Warn instead of error
      "no-unused-vars": "off", // Disable base rule in favor of TS rule

      // Arabic-specific allowances
      "jsx-a11y/alt-text": "warn", // Warn for missing alt text
      "jsx-a11y/anchor-is-valid": "warn", // Warn for invalid anchors

      // Production-ready settings
      "no-console": "warn", // Warn for console statements
      "no-debugger": "error", // Error for debugger statements
    },
    ignores: [
      "node_modules/**",
      ".next/**",
      "out/**",
      "build/**",
      "dist/**",
      "*.config.js",
      "*.config.mjs",
      "*.config.ts",
    ],
  },
];

export default eslintConfig;
