# 🚀 Ya<PERSON><PERSON> - Server Startup Guide

## موقع ياسمين الشام لتفصيل الفساتين - دليل تشغيل الخادم

### طرق تشغيل الخادم المتاحة:

## 1. ✅ الطريقة الموصى بها - PowerShell Script

```powershell
# افتح PowerShell في مجلد المشروع وشغل:
powershell -ExecutionPolicy Bypass -File start-server.ps1
```

**أو**

```powershell
# إذا كنت داخل PowerShell بالفعل:
Set-ExecutionPolicy Bypass -Scope Process
.\start-server.ps1
```

### المميزات:
- ✅ فحص تلقائي لـ Node.js و npm
- ✅ تثبيت التبعيات تلقائياً إذا لزم الأمر
- ✅ تنظيف الذاكرة المؤقتة للاستقرار
- ✅ اكتشاف تلقائي للمنفذ المتاح (3000 أو 3001)
- ✅ معالجة الأخطاء مع حلول بديلة
- ✅ استخدام التكوين المستقر (بدون Turbopack)

---

## 2. 🔧 الطريقة المباشرة - npm Commands

```bash
# الطريقة المستقرة (موصى بها)
npm run dev:safe

# أو الطريقة العادية
npm run dev
```

---

## 3. 📝 Batch File (للنقر المزدوج)

```cmd
# انقر نقراً مزدوجاً على:
start-server.bat
```

---

## 🔍 استكشاف الأخطاء وإصلاحها

### إذا واجهت مشاكل:

#### 1. مشكلة سياسة التنفيذ في PowerShell:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### 2. مشكلة المنفذ مستخدم:
- الخادم سيتبديل تلقائياً من 3000 إلى 3001
- أو أوقف العمليات التي تستخدم المنفذ:
```bash
netstat -ano | findstr :3000
taskkill /PID [رقم_العملية] /F
```

#### 3. مشكلة التبعيات:
```bash
# احذف node_modules وأعد التثبيت
rm -rf node_modules package-lock.json
npm install
```

#### 4. مشكلة الذاكرة المؤقتة:
```bash
# احذف مجلد .next
rm -rf .next
npm run dev:safe
```

---

## 🌐 الوصول للموقع

بعد تشغيل الخادم بنجاح، ستجد الموقع على:

- **المنفذ المفضل**: http://localhost:3000
- **المنفذ البديل**: http://localhost:3001 (إذا كان 3000 مستخدماً)

---

## 📋 متطلبات النظام

- **Node.js**: الإصدار 18 أو أحدث
- **npm**: الإصدار 8 أو أحدث
- **نظام التشغيل**: Windows 10/11
- **PowerShell**: الإصدار 5.1 أو أحدث

---

## 🎯 نصائح للاستخدام الأمثل

1. **استخدم `start-server.ps1`** للحصول على أفضل تجربة
2. **تأكد من إغلاق الخادم بـ Ctrl+C** قبل إعادة التشغيل
3. **نظف الذاكرة المؤقتة** إذا واجهت مشاكل في التحديث
4. **تحقق من إصدار Node.js** إذا واجهت أخطاء غريبة

---

## 📞 الدعم

إذا واجهت أي مشاكل، تأكد من:
- تشغيل الأوامر من مجلد المشروع الصحيح
- وجود ملف `package.json` في نفس المجلد
- عدم وجود عمليات أخرى تستخدم نفس المنفذ

---

**🎉 مبروك! موقع ياسمين الشام جاهز للاستخدام!**
