"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useTranslation.ts":
/*!*************************************!*\
  !*** ./src/hooks/useTranslation.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useTranslation auto */ \n// الترجمات العربية\nconst arTranslations = {\n    // التنقل والعناوين الرئيسية\n    'dashboard': 'لوحة التحكم',\n    'orders': 'الطلبات',\n    'appointments': 'المواعيد',\n    'settings': 'الإعدادات',\n    'workers': 'العمال',\n    'reports': 'التقارير',\n    'logout': 'تسجيل الخروج',\n    'welcome': 'مرحباً',\n    'welcome_back': 'مرحباً بعودتك',\n    // الأزرار والإجراءات\n    'add_new_order': 'إضافة طلب جديد',\n    'book_appointment': 'حجز موعد',\n    'view_details': 'عرض التفاصيل',\n    'edit': 'تعديل',\n    'delete': 'حذف',\n    'save': 'حفظ',\n    'cancel': 'إلغاء',\n    'submit': 'إرسال',\n    'search': 'بحث',\n    'filter': 'تصفية',\n    'export': 'تصدير',\n    'print': 'طباعة',\n    'back': 'رجوع',\n    'next': 'التالي',\n    'previous': 'السابق',\n    'close': 'إغلاق',\n    'confirm': 'تأكيد',\n    'loading': 'جاري التحميل...',\n    'saving': 'جاري الحفظ...',\n    // حالات الطلبات\n    'pending': 'في الانتظار',\n    'in_progress': 'قيد التنفيذ',\n    'completed': 'مكتمل',\n    'delivered': 'تم التسليم',\n    'cancelled': 'ملغي',\n    // نصوص عامة\n    'name': 'الاسم',\n    'email': 'البريد الإلكتروني',\n    'phone': 'رقم الهاتف',\n    'address': 'العنوان',\n    'date': 'التاريخ',\n    'time': 'الوقت',\n    'status': 'الحالة',\n    'price': 'السعر',\n    'total': 'المجموع',\n    'description': 'الوصف',\n    'notes': 'ملاحظات',\n    'client_name': 'اسم الزبونة',\n    'client_phone': 'رقم هاتف الزبونة',\n    // رسائل النجاح والخطأ\n    'success': 'نجح',\n    'error': 'خطأ',\n    'warning': 'تحذير',\n    'info': 'معلومات',\n    'order_added_success': 'تم إضافة الطلب بنجاح',\n    'order_updated_success': 'تم تحديث الطلب بنجاح',\n    'order_deleted_success': 'تم حذف الطلب بنجاح',\n    'fill_required_fields': 'يرجى ملء جميع الحقول المطلوبة',\n    // نصوص إضافية مطلوبة\n    'admin_dashboard': 'لوحة تحكم المدير',\n    'worker_dashboard': 'لوحة تحكم العامل',\n    'admin': 'مدير',\n    'worker': 'عامل',\n    'change_language': 'تغيير اللغة',\n    'my_active_orders': 'طلباتي النشطة',\n    'completed_orders': 'الطلبات المكتملة',\n    'total_orders': 'إجمالي الطلبات',\n    'total_revenue': 'إجمالي الإيرادات',\n    'recent_orders': 'الطلبات الحديثة',\n    'quick_actions': 'إجراءات سريعة',\n    'view_all_orders': 'عرض جميع الطلبات',\n    'add_order': 'إضافة طلب',\n    'manage_workers': 'إدارة العمال',\n    'view_reports': 'عرض التقارير',\n    'client_name_required': 'اسم الزبونة *',\n    'phone_required': 'رقم الهاتف *',\n    'order_description_required': 'وصف الطلب *',\n    'delivery_date_required': 'موعد التسليم *',\n    'price_sar': 'السعر (ريال سعودي)',\n    'measurements_cm': 'المقاسات (بالسنتيمتر)',\n    'additional_notes': 'ملاحظات إضافية',\n    'voice_notes_optional': 'ملاحظات صوتية (اختيارية)',\n    'design_images': 'صور التصميم',\n    'fabric_type': 'نوع القماش',\n    'responsible_worker': 'العامل المسؤول',\n    'choose_worker': 'اختر العامل المسؤول',\n    'order_status': 'حالة الطلب',\n    'back_to_dashboard': 'العودة إلى لوحة التحكم',\n    'overview_today': 'نظرة عامة على أنشطة اليوم',\n    'welcome_worker': 'مرحباً بك في مساحة العمل',\n    // مفاتيح مفقودة من لوحة التحكم\n    'homepage': 'الصفحة الرئيسية',\n    'my_completed_orders': 'طلباتي المكتملة',\n    'my_total_orders': 'إجمالي طلباتي',\n    'active_orders': 'الطلبات النشطة',\n    'today_appointments': 'مواعيد اليوم',\n    'statistics': 'الإحصائيات',\n    'no_orders_found': 'لا توجد طلبات',\n    'view_all': 'عرض الكل',\n    'worker_management': 'إدارة العمال',\n    'reminder': 'تذكير',\n    'you_have': 'لديك',\n    'today_appointments_reminder': 'موعد اليوم',\n    'and': 'و',\n    'orders_need_follow': 'طلبات تحتاج متابعة',\n    'detailed_reports': 'تقارير مفصلة',\n    'worker_description': 'يمكنك هنا متابعة طلباتك المخصصة لك وتحديث حالتها',\n    // مفاتيح صفحة إضافة الطلبات\n    'order_add_error': 'حدث خطأ أثناء إضافة الطلب',\n    'cm_placeholder': 'سم',\n    'shoulder': 'الكتف',\n    'shoulder_circumference': 'محيط الكتف',\n    'chest': 'الصدر',\n    'waist': 'الخصر',\n    'hips': 'الأرداف',\n    'dart_length': 'طول الخياطة',\n    'bodice_length': 'طول الجسم',\n    'neckline': 'خط الرقبة',\n    'armpit': 'الإبط',\n    'sleeve_length': 'طول الكم',\n    'forearm': 'الساعد',\n    'cuff': 'الكم',\n    'front_length': 'الطول الأمامي',\n    'back_length': 'الطول الخلفي',\n    // مفاتيح صفحة الطلبات\n    'enter_order_number': 'أدخل رقم الطلب',\n    'all_orders': 'جميع الطلبات',\n    'no_orders_assigned': 'لا توجد طلبات مخصصة لك',\n    'no_orders_assigned_desc': 'لم يتم تخصيص أي طلبات لك بعد',\n    'price_label': 'السعر',\n    'sar': 'ريال',\n    'view': 'عرض',\n    'completing': 'جاري الإنهاء...',\n    'start_work': 'بدء العمل',\n    'complete_order': 'إنهاء الطلب',\n    'complete_order_modal_title': 'إنهاء الطلب وتحميل صور العمل المكتمل',\n    'important_warning': 'تحذير مهم',\n    'complete_order_warning': 'بمجرد إنهاء الطلب، لن تتمكن من تعديل حالته مرة أخرى. تأكد من تحميل جميع صور العمل المكتمل قبل المتابعة.',\n    // مفاتيح صفحة العمال\n    'worker_added_success': 'تم إضافة العامل بنجاح',\n    'error_adding_worker': 'حدث خطأ أثناء إضافة العامل',\n    'worker_updated_success': 'تم تحديث العامل بنجاح',\n    'error_updating_worker': 'حدث خطأ أثناء تحديث العامل',\n    'worker_deleted_success': 'تم حذف العامل بنجاح',\n    'worker_deactivated': 'تم إلغاء تفعيل العامل',\n    'worker_activated': 'تم تفعيل العامل',\n    'adding': 'جاري الإضافة...',\n    'add_worker': 'إضافة عامل',\n    'active': 'نشط',\n    'inactive': 'غير نشط',\n    'save_changes': 'حفظ التغييرات',\n    'search_workers_placeholder': 'البحث عن العمال...',\n    // مفاتيح صفحة التقارير\n    'engagement_dress': 'فستان خطوبة',\n    'casual_dress': 'فستان يومي',\n    'other': 'أخرى',\n    'this_week': 'هذا الأسبوع',\n    'this_month': 'هذا الشهر',\n    'this_quarter': 'هذا الربع',\n    'this_year': 'هذا العام',\n    // مفاتيح صفحة المواعيد\n    'confirmed': 'مؤكد',\n    'pm': 'مساءً',\n    'am': 'صباحاً',\n    'all_statuses': 'جميع الحالات',\n    'all_dates': 'جميع التواريخ',\n    'today': 'اليوم',\n    'tomorrow': 'غداً',\n    // مفاتيح مكونات إضافية\n    'of': 'من',\n    'images_text': 'صور',\n    // مفاتيح مكون تعديل الطلب\n    'order_update_error': 'حدث خطأ أثناء تحديث الطلب',\n    'price_sar_required': 'السعر (ريال سعودي) *',\n    'status_pending': 'في الانتظار',\n    'status_in_progress': 'قيد التنفيذ',\n    'status_completed': 'مكتمل',\n    'status_delivered': 'تم التسليم',\n    'status_cancelled': 'ملغي',\n    // نصوص الفوتر\n    'home': 'الرئيسية',\n    'track_order': 'استعلام عن الطلب',\n    'fabrics': 'الأقمشة',\n    'contact_us': 'تواصلي معنا',\n    'yasmin_alsham': 'ياسمين الشام',\n    'custom_dress_tailoring': 'تفصيل فساتين حسب الطلب'\n};\n// الترجمات الإنجليزية\nconst enTranslations = {\n    // التنقل والعناوين الرئيسية\n    'dashboard': 'Dashboard',\n    'orders': 'Orders',\n    'appointments': 'Appointments',\n    'settings': 'Settings',\n    'workers': 'Workers',\n    'reports': 'Reports',\n    'logout': 'Logout',\n    'welcome': 'Welcome',\n    'welcome_back': 'Welcome Back',\n    // الأزرار والإجراءات\n    'add_new_order': 'Add New Order',\n    'book_appointment': 'Book Appointment',\n    'view_details': 'View Details',\n    'edit': 'Edit',\n    'delete': 'Delete',\n    'save': 'Save',\n    'cancel': 'Cancel',\n    'submit': 'Submit',\n    'search': 'Search',\n    'filter': 'Filter',\n    'export': 'Export',\n    'print': 'Print',\n    'back': 'Back',\n    'next': 'Next',\n    'previous': 'Previous',\n    'close': 'Close',\n    'confirm': 'Confirm',\n    'loading': 'Loading...',\n    'saving': 'Saving...',\n    // حالات الطلبات\n    'pending': 'Pending',\n    'in_progress': 'In Progress',\n    'completed': 'Completed',\n    'delivered': 'Delivered',\n    'cancelled': 'Cancelled',\n    // نصوص عامة\n    'name': 'Name',\n    'email': 'Email',\n    'phone': 'Phone',\n    'address': 'Address',\n    'date': 'Date',\n    'time': 'Time',\n    'status': 'Status',\n    'price': 'Price',\n    'total': 'Total',\n    'description': 'Description',\n    'notes': 'Notes',\n    'client_name': 'Client Name',\n    'client_phone': 'Client Phone',\n    // رسائل النجاح والخطأ\n    'success': 'Success',\n    'error': 'Error',\n    'warning': 'Warning',\n    'info': 'Info',\n    'order_added_success': 'Order added successfully',\n    'order_updated_success': 'Order updated successfully',\n    'order_deleted_success': 'Order deleted successfully',\n    'fill_required_fields': 'Please fill all required fields',\n    // نصوص إضافية مطلوبة\n    'admin_dashboard': 'Admin Dashboard',\n    'worker_dashboard': 'Worker Dashboard',\n    'admin': 'Admin',\n    'worker': 'Worker',\n    'change_language': 'Change Language',\n    'my_active_orders': 'My Active Orders',\n    'completed_orders': 'Completed Orders',\n    'total_orders': 'Total Orders',\n    'total_revenue': 'Total Revenue',\n    'recent_orders': 'Recent Orders',\n    'quick_actions': 'Quick Actions',\n    'view_all_orders': 'View All Orders',\n    'add_order': 'Add Order',\n    'manage_workers': 'Manage Workers',\n    'view_reports': 'View Reports',\n    'client_name_required': 'Client Name *',\n    'phone_required': 'Phone Number *',\n    'order_description_required': 'Order Description *',\n    'delivery_date_required': 'Delivery Date *',\n    'price_sar': 'Price (SAR)',\n    'measurements_cm': 'Measurements (cm)',\n    'additional_notes': 'Additional Notes',\n    'voice_notes_optional': 'Voice Notes (Optional)',\n    'design_images': 'Design Images',\n    'fabric_type': 'Fabric Type',\n    'responsible_worker': 'Responsible Worker',\n    'choose_worker': 'Choose Responsible Worker',\n    'order_status': 'Order Status',\n    'back_to_dashboard': 'Back to Dashboard',\n    'overview_today': 'Overview of today\\'s activities',\n    'welcome_worker': 'Welcome to your workspace',\n    // مفاتيح مفقودة من لوحة التحكم\n    'homepage': 'Homepage',\n    'my_completed_orders': 'My Completed Orders',\n    'my_total_orders': 'My Total Orders',\n    'active_orders': 'Active Orders',\n    'today_appointments': 'Today\\'s Appointments',\n    'statistics': 'Statistics',\n    'no_orders_found': 'No orders found',\n    'view_all': 'View All',\n    'worker_management': 'Worker Management',\n    'reminder': 'Reminder',\n    'you_have': 'You have',\n    'today_appointments_reminder': 'appointments today',\n    'and': 'and',\n    'orders_need_follow': 'orders that need follow-up',\n    'detailed_reports': 'Detailed Reports',\n    'worker_description': 'Here you can track your assigned orders and update their status',\n    // مفاتيح صفحة إضافة الطلبات\n    'order_add_error': 'An error occurred while adding the order',\n    'cm_placeholder': 'cm',\n    'shoulder': 'Shoulder',\n    'shoulder_circumference': 'Shoulder Circumference',\n    'chest': 'Chest',\n    'waist': 'Waist',\n    'hips': 'Hips',\n    'dart_length': 'Dart Length',\n    'bodice_length': 'Bodice Length',\n    'neckline': 'Neckline',\n    'armpit': 'Armpit',\n    'sleeve_length': 'Sleeve Length',\n    'forearm': 'Forearm',\n    'cuff': 'Cuff',\n    'front_length': 'Front Length',\n    'back_length': 'Back Length',\n    // مفاتيح صفحة الطلبات\n    'enter_order_number': 'Enter order number',\n    'all_orders': 'All Orders',\n    'no_orders_assigned': 'No orders assigned to you',\n    'no_orders_assigned_desc': 'No orders have been assigned to you yet',\n    'price_label': 'Price',\n    'sar': 'SAR',\n    'view': 'View',\n    'completing': 'Completing...',\n    // مفاتيح صفحة العمال\n    'worker_added_success': 'Worker added successfully',\n    'error_adding_worker': 'Error adding worker',\n    'worker_updated_success': 'Worker updated successfully',\n    'error_updating_worker': 'Error updating worker',\n    'worker_deleted_success': 'Worker deleted successfully',\n    'worker_deactivated': 'Worker deactivated',\n    'worker_activated': 'Worker activated',\n    'adding': 'Adding...',\n    'add_worker': 'Add Worker',\n    'active': 'Active',\n    'inactive': 'Inactive',\n    'save_changes': 'Save Changes',\n    'search_workers_placeholder': 'Search workers...',\n    // مفاتيح صفحة التقارير\n    'engagement_dress': 'Engagement Dress',\n    'casual_dress': 'Casual Dress',\n    'other': 'Other',\n    'this_week': 'This Week',\n    'this_month': 'This Month',\n    'this_quarter': 'This Quarter',\n    'this_year': 'This Year',\n    // مفاتيح صفحة المواعيد\n    'confirmed': 'Confirmed',\n    'pm': 'PM',\n    'am': 'AM',\n    'all_statuses': 'All Statuses',\n    'all_dates': 'All Dates',\n    'today': 'Today',\n    'tomorrow': 'Tomorrow',\n    // مفاتيح مكونات إضافية\n    'of': 'of',\n    'images_text': 'images',\n    // مفاتيح مكون تعديل الطلب\n    'order_update_error': 'Error updating order',\n    'price_sar_required': 'Price (SAR) *',\n    'status_pending': 'Pending',\n    'status_in_progress': 'In Progress',\n    'status_completed': 'Completed',\n    'status_delivered': 'Delivered',\n    'status_cancelled': 'Cancelled',\n    // نصوص الفوتر\n    'home': 'Home',\n    'track_order': 'Track Order',\n    'fabrics': 'Fabrics',\n    'contact_us': 'Contact Us',\n    'yasmin_alsham': 'Yasmin Alsham',\n    'custom_dress_tailoring': 'Custom Dress Tailoring'\n};\n// Hook للترجمة\nfunction useTranslation() {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('ar');\n    // تحميل اللغة المحفوظة عند بدء التطبيق\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTranslation.useEffect\": ()=>{\n            const savedLanguage = localStorage.getItem('dashboard-language');\n            if (savedLanguage) {\n                setLanguage(savedLanguage);\n            }\n        }\n    }[\"useTranslation.useEffect\"], []);\n    // حفظ اللغة عند تغييرها\n    const changeLanguage = (newLanguage)=>{\n        setLanguage(newLanguage);\n        localStorage.setItem('dashboard-language', newLanguage);\n    };\n    // دالة الترجمة\n    const t = (key)=>{\n        const translations = language === 'ar' ? arTranslations : enTranslations;\n        const translation = translations[key];\n        if (typeof translation === 'string') {\n            return translation;\n        }\n        // إذا لم توجد الترجمة، أرجع المفتاح نفسه\n        return key;\n    };\n    // التحقق من اللغة الحالية\n    const isArabic = language === 'ar';\n    const isEnglish = language === 'en';\n    return {\n        language,\n        changeLanguage,\n        t,\n        isArabic,\n        isEnglish\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useTranslation.ts\n"));

/***/ })

});