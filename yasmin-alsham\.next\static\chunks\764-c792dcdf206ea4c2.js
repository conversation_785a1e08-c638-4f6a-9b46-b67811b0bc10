"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[764],{1364:(e,r,t)=>{t.d(r,{D:()=>n});var o=t(5453),s=t(6786);let a=()=>Date.now().toString(36)+Math.random().toString(36).substr(2),n=(0,o.v)()((0,s.Zr)((e,r)=>({appointments:[],orders:[],workers:[],isLoading:!1,error:null,addAppointment:r=>{let t={...r,id:a(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({appointments:[...e.appointments,t],error:null})),console.log("✅ تم إضافة موعد جديد:",t)},updateAppointment:(r,t)=>{e(e=>({appointments:e.appointments.map(e=>e.id===r?{...e,...t,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الموعد:",r)},deleteAppointment:r=>{e(e=>({appointments:e.appointments.filter(e=>e.id!==r),error:null})),console.log("✅ تم حذف الموعد:",r)},getAppointment:e=>r().appointments.find(r=>r.id===e),addOrder:r=>{let t={...r,id:a(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({orders:[...e.orders,t],error:null})),console.log("✅ تم إضافة طلب جديد:",t)},updateOrder:(r,t)=>{e(e=>({orders:e.orders.map(e=>e.id===r?{...e,...t,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الطلب:",r)},deleteOrder:r=>{e(e=>({orders:e.orders.filter(e=>e.id!==r),error:null})),console.log("✅ تم حذف الطلب:",r)},getOrder:e=>r().orders.find(r=>r.id===e),addWorker:r=>{let t={...r,id:a(),role:"worker",is_active:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({workers:[...e.workers,t],error:null}));{let e=JSON.parse(localStorage.getItem("yasmin-users")||"[]");e.push({id:t.id,email:t.email,password:t.password,full_name:t.full_name,role:"worker",is_active:!0}),localStorage.setItem("yasmin-users",JSON.stringify(e))}console.log("✅ تم إضافة عامل جديد:",t)},updateWorker:(r,t)=>{if(e(e=>({workers:e.workers.map(e=>e.id===r?{...e,...t,updatedAt:new Date().toISOString()}:e),error:null})),t.email||t.password||t.full_name){let e=JSON.parse(localStorage.getItem("yasmin-users")||"[]"),o=e.findIndex(e=>e.id===r);-1!==o&&(t.email&&(e[o].email=t.email),t.password&&(e[o].password=t.password),t.full_name&&(e[o].full_name=t.full_name),localStorage.setItem("yasmin-users",JSON.stringify(e)))}console.log("✅ تم تحديث العامل:",r)},deleteWorker:r=>{e(e=>({workers:e.workers.filter(e=>e.id!==r),error:null}));{let e=JSON.parse(localStorage.getItem("yasmin-users")||"[]").filter(e=>e.id!==r);localStorage.setItem("yasmin-users",JSON.stringify(e))}console.log("✅ تم حذف العامل:",r)},getWorker:e=>r().workers.find(r=>r.id===e),startOrderWork:(r,t)=>{e(e=>({orders:e.orders.map(e=>e.id===r&&e.assignedWorker===t?{...e,status:"in_progress",updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم بدء العمل في الطلب:",r)},completeOrder:function(r,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];e(e=>({orders:e.orders.map(e=>e.id===r&&e.assignedWorker===t?{...e,status:"completed",completedImages:o.length>0?o:void 0,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم إنهاء الطلب:",r)},clearError:()=>{e({error:null})},loadData:()=>{e({isLoading:!0}),e({isLoading:!1})},getStats:()=>{let e=r();return{totalAppointments:e.appointments.length,totalOrders:e.orders.length,totalWorkers:e.workers.length,pendingAppointments:e.appointments.filter(e=>"pending"===e.status).length,activeOrders:e.orders.filter(e=>["pending","in_progress"].includes(e.status)).length,completedOrders:e.orders.filter(e=>"completed"===e.status).length,totalRevenue:e.orders.filter(e=>"completed"===e.status).reduce((e,r)=>e+r.price,0)}}}),{name:"yasmin-data-storage",partialize:e=>({appointments:e.appointments,orders:e.orders,workers:e.workers})}))},3294:(e,r,t)=>{t.d(r,{n:()=>n});var o=t(5453),s=t(6786);let a=()=>{let e=localStorage.getItem("yasmin-users");if(e)return JSON.parse(e);let r=[{id:"1",email:"<EMAIL>",password:"admin123",full_name:"مدير النظام",role:"admin",is_active:!0}];return localStorage.setItem("yasmin-users",JSON.stringify(r)),r},n=(0,o.v)()((0,s.Zr)((e,r)=>({user:null,isLoading:!1,error:null,signIn:async(r,t)=>{e({isLoading:!0,error:null});try{console.log("\uD83D\uDD10 بدء عملية تسجيل الدخول...",{email:r}),await new Promise(e=>setTimeout(e,1500));let o=a().find(e=>e.email.toLowerCase()===r.toLowerCase()&&e.password===t);if(!o)return console.log("❌ بيانات تسجيل الدخول غير صحيحة"),e({error:"بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور.",isLoading:!1}),!1;{console.log("✅ تم العثور على المستخدم:",o.full_name);let r={id:o.id,email:o.email,full_name:o.full_name,role:o.role,is_active:o.is_active,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),token:"demo-token-".concat(o.id,"-").concat(Date.now())};return localStorage.setItem("yasmin-auth-user",JSON.stringify(r)),console.log("\uD83D\uDCBE تم حفظ المستخدم في localStorage"),e({user:r,isLoading:!1,error:null}),console.log("\uD83C\uDF89 تم تسجيل الدخول بنجاح!"),!0}}catch(r){return console.error("\uD83D\uDCA5 خطأ في تسجيل الدخول:",r),e({error:"حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.",isLoading:!1}),!1}},signOut:async()=>{e({isLoading:!0});try{await new Promise(e=>setTimeout(e,500)),localStorage.removeItem("yasmin-auth-user"),e({user:null,isLoading:!1,error:null})}catch(r){console.error("خطأ في تسجيل الخروج:",r),e({isLoading:!1,error:"خطأ في تسجيل الخروج"})}},setUser:r=>{e({user:r}),r?localStorage.setItem("yasmin-auth-user",JSON.stringify(r)):localStorage.removeItem("yasmin-auth-user")},clearError:()=>{e({error:null})},checkAuth:async()=>{e({isLoading:!0});try{{let r=localStorage.getItem("yasmin-auth-user");if(r){let t=JSON.parse(r);e({user:t,isLoading:!1});return}}e({user:null,isLoading:!1})}catch(r){console.error("خطأ في التحقق من المصادقة:",r),e({user:null,isLoading:!1})}},isAuthenticated:()=>{let e=r();return null!==e.user&&e.user.is_active}}),{name:"yasmin-auth-storage",partialize:e=>({user:e.user})}))},9137:(e,r,t)=>{t.d(r,{B:()=>n});var o=t(2115);let s={dashboard:"لوحة التحكم",orders:"الطلبات",appointments:"المواعيد",settings:"الإعدادات",workers:"العمال",reports:"التقارير",logout:"تسجيل الخروج",welcome:"مرحباً",welcome_back:"مرحباً بعودتك",add_new_order:"إضافة طلب جديد",book_appointment:"حجز موعد",view_details:"عرض التفاصيل",edit:"تعديل",delete:"حذف",save:"حفظ",cancel:"إلغاء",submit:"إرسال",search:"بحث",filter:"تصفية",export:"تصدير",print:"طباعة",back:"رجوع",next:"التالي",previous:"السابق",close:"إغلاق",confirm:"تأكيد",loading:"جاري التحميل...",saving:"جاري الحفظ...",pending:"في الانتظار",in_progress:"قيد التنفيذ",completed:"مكتمل",delivered:"تم التسليم",cancelled:"ملغي",name:"الاسم",email:"البريد الإلكتروني",phone:"رقم الهاتف",address:"العنوان",date:"التاريخ",time:"الوقت",status:"الحالة",price:"السعر",total:"المجموع",description:"الوصف",notes:"ملاحظات",client_name:"اسم الزبونة",client_phone:"رقم هاتف الزبونة",success:"نجح",error:"خطأ",warning:"تحذير",info:"معلومات",order_added_success:"تم إضافة الطلب بنجاح",order_updated_success:"تم تحديث الطلب بنجاح",order_deleted_success:"تم حذف الطلب بنجاح",fill_required_fields:"يرجى ملء جميع الحقول المطلوبة",admin_dashboard:"لوحة تحكم المدير",worker_dashboard:"لوحة تحكم العامل",admin:"مدير",worker:"عامل",change_language:"تغيير اللغة",my_active_orders:"طلباتي النشطة",completed_orders:"الطلبات المكتملة",total_orders:"إجمالي الطلبات",total_revenue:"إجمالي الإيرادات",recent_orders:"الطلبات الحديثة",quick_actions:"إجراءات سريعة",view_all_orders:"عرض جميع الطلبات",add_order:"إضافة طلب",manage_workers:"إدارة العمال",view_reports:"عرض التقارير",client_name_required:"اسم الزبونة *",phone_required:"رقم الهاتف *",order_description_required:"وصف الطلب *",delivery_date_required:"موعد التسليم *",price_sar:"السعر (ريال سعودي)",measurements_cm:"المقاسات (بالسنتيمتر)",additional_notes:"ملاحظات إضافية",voice_notes_optional:"ملاحظات صوتية (اختيارية)",design_images:"صور التصميم",fabric_type:"نوع القماش",responsible_worker:"العامل المسؤول",choose_worker:"اختر العامل المسؤول",order_status:"حالة الطلب",back_to_dashboard:"العودة إلى لوحة التحكم",home:"الرئيسية",track_order:"استعلام عن الطلب",fabrics:"الأقمشة",contact_us:"تواصلي معنا",yasmin_alsham:"ياسمين الشام",custom_dress_tailoring:"تفصيل فساتين حسب الطلب"},a={dashboard:"Dashboard",orders:"Orders",appointments:"Appointments",settings:"Settings",workers:"Workers",reports:"Reports",logout:"Logout",welcome:"Welcome",welcome_back:"Welcome Back",add_new_order:"Add New Order",book_appointment:"Book Appointment",view_details:"View Details",edit:"Edit",delete:"Delete",save:"Save",cancel:"Cancel",submit:"Submit",search:"Search",filter:"Filter",export:"Export",print:"Print",back:"Back",next:"Next",previous:"Previous",close:"Close",confirm:"Confirm",loading:"Loading...",saving:"Saving...",pending:"Pending",in_progress:"In Progress",completed:"Completed",delivered:"Delivered",cancelled:"Cancelled",name:"Name",email:"Email",phone:"Phone",address:"Address",date:"Date",time:"Time",status:"Status",price:"Price",total:"Total",description:"Description",notes:"Notes",client_name:"Client Name",client_phone:"Client Phone",success:"Success",error:"Error",warning:"Warning",info:"Info",order_added_success:"Order added successfully",order_updated_success:"Order updated successfully",order_deleted_success:"Order deleted successfully",fill_required_fields:"Please fill all required fields",admin_dashboard:"Admin Dashboard",worker_dashboard:"Worker Dashboard",admin:"Admin",worker:"Worker",change_language:"Change Language",my_active_orders:"My Active Orders",completed_orders:"Completed Orders",total_orders:"Total Orders",total_revenue:"Total Revenue",recent_orders:"Recent Orders",quick_actions:"Quick Actions",view_all_orders:"View All Orders",add_order:"Add Order",manage_workers:"Manage Workers",view_reports:"View Reports",client_name_required:"Client Name *",phone_required:"Phone Number *",order_description_required:"Order Description *",delivery_date_required:"Delivery Date *",price_sar:"Price (SAR)",measurements_cm:"Measurements (cm)",additional_notes:"Additional Notes",voice_notes_optional:"Voice Notes (Optional)",design_images:"Design Images",fabric_type:"Fabric Type",responsible_worker:"Responsible Worker",choose_worker:"Choose Responsible Worker",order_status:"Order Status",back_to_dashboard:"Back to Dashboard",home:"Home",track_order:"Track Order",fabrics:"Fabrics",contact_us:"Contact Us",yasmin_alsham:"Yasmin Alsham",custom_dress_tailoring:"Custom Dress Tailoring"};function n(){let[e,r]=(0,o.useState)("ar");return(0,o.useEffect)(()=>{let e=localStorage.getItem("dashboard-language");e&&r(e)},[]),{language:e,changeLanguage:e=>{r(e),localStorage.setItem("dashboard-language",e)},t:r=>{let t=("ar"===e?s:a)[r];return"string"==typeof t?t:r},isArabic:"ar"===e,isEnglish:"en"===e}}}}]);