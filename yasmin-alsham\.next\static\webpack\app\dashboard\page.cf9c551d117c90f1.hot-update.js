"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useTranslation.ts":
/*!*************************************!*\
  !*** ./src/hooks/useTranslation.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useTranslation auto */ \n// الترجمات العربية\nconst arTranslations = {\n    // التنقل والعناوين الرئيسية\n    'dashboard': 'لوحة التحكم',\n    'orders': 'الطلبات',\n    'appointments': 'المواعيد',\n    'settings': 'الإعدادات',\n    'workers': 'العمال',\n    'reports': 'التقارير',\n    'logout': 'تسجيل الخروج',\n    'welcome': 'مرحباً',\n    'welcome_back': 'مرحباً بعودتك',\n    // الأزرار والإجراءات\n    'add_new_order': 'إضافة طلب جديد',\n    'book_appointment': 'حجز موعد',\n    'view_details': 'عرض التفاصيل',\n    'edit': 'تعديل',\n    'delete': 'حذف',\n    'save': 'حفظ',\n    'cancel': 'إلغاء',\n    'submit': 'إرسال',\n    'search': 'بحث',\n    'filter': 'تصفية',\n    'export': 'تصدير',\n    'print': 'طباعة',\n    'back': 'رجوع',\n    'next': 'التالي',\n    'previous': 'السابق',\n    'close': 'إغلاق',\n    'confirm': 'تأكيد',\n    'loading': 'جاري التحميل...',\n    'saving': 'جاري الحفظ...',\n    // حالات الطلبات\n    'pending': 'في الانتظار',\n    'in_progress': 'قيد التنفيذ',\n    'completed': 'مكتمل',\n    'delivered': 'تم التسليم',\n    'cancelled': 'ملغي',\n    // نصوص عامة\n    'name': 'الاسم',\n    'email': 'البريد الإلكتروني',\n    'phone': 'رقم الهاتف',\n    'address': 'العنوان',\n    'date': 'التاريخ',\n    'time': 'الوقت',\n    'status': 'الحالة',\n    'price': 'السعر',\n    'total': 'المجموع',\n    'description': 'الوصف',\n    'notes': 'ملاحظات',\n    'client_name': 'اسم الزبونة',\n    'client_phone': 'رقم هاتف الزبونة',\n    // رسائل النجاح والخطأ\n    'success': 'نجح',\n    'error': 'خطأ',\n    'warning': 'تحذير',\n    'info': 'معلومات',\n    'order_added_success': 'تم إضافة الطلب بنجاح',\n    'order_updated_success': 'تم تحديث الطلب بنجاح',\n    'order_deleted_success': 'تم حذف الطلب بنجاح',\n    'fill_required_fields': 'يرجى ملء جميع الحقول المطلوبة',\n    // نصوص إضافية مطلوبة\n    'admin_dashboard': 'لوحة تحكم المدير',\n    'worker_dashboard': 'لوحة تحكم العامل',\n    'admin': 'مدير',\n    'worker': 'عامل',\n    'change_language': 'تغيير اللغة',\n    'my_active_orders': 'طلباتي النشطة',\n    'completed_orders': 'الطلبات المكتملة',\n    'total_orders': 'إجمالي الطلبات',\n    'total_revenue': 'إجمالي الإيرادات',\n    'recent_orders': 'الطلبات الحديثة',\n    'quick_actions': 'إجراءات سريعة',\n    'view_all_orders': 'عرض جميع الطلبات',\n    'add_order': 'إضافة طلب',\n    'manage_workers': 'إدارة العمال',\n    'view_reports': 'عرض التقارير',\n    'client_name_required': 'اسم الزبونة *',\n    'phone_required': 'رقم الهاتف *',\n    'order_description_required': 'وصف الطلب *',\n    'delivery_date_required': 'موعد التسليم *',\n    'price_sar': 'السعر (ريال سعودي)',\n    'measurements_cm': 'المقاسات (بالسنتيمتر)',\n    'additional_notes': 'ملاحظات إضافية',\n    'voice_notes_optional': 'ملاحظات صوتية (اختيارية)',\n    'design_images': 'صور التصميم',\n    'fabric_type': 'نوع القماش',\n    'responsible_worker': 'العامل المسؤول',\n    'choose_worker': 'اختر العامل المسؤول',\n    'order_status': 'حالة الطلب',\n    'back_to_dashboard': 'العودة إلى لوحة التحكم',\n    'overview_today': 'نظرة عامة على أنشطة اليوم',\n    'welcome_worker': 'مرحباً بك في مساحة العمل',\n    // مفاتيح مفقودة من لوحة التحكم\n    'homepage': 'الصفحة الرئيسية',\n    'my_completed_orders': 'طلباتي المكتملة',\n    'my_total_orders': 'إجمالي طلباتي',\n    'active_orders': 'الطلبات النشطة',\n    'today_appointments': 'مواعيد اليوم',\n    'statistics': 'الإحصائيات',\n    'no_orders_found': 'لا توجد طلبات',\n    'view_all': 'عرض الكل',\n    'worker_management': 'إدارة العمال',\n    'reminder': 'تذكير',\n    'you_have': 'لديك',\n    'today_appointments_reminder': 'موعد اليوم',\n    'and': 'و',\n    'orders_need_follow': 'طلبات تحتاج متابعة',\n    'detailed_reports': 'تقارير مفصلة',\n    'worker_description': 'يمكنك هنا متابعة طلباتك المخصصة لك وتحديث حالتها',\n    // مفاتيح صفحة إضافة الطلبات\n    'order_add_error': 'حدث خطأ أثناء إضافة الطلب',\n    'cm_placeholder': 'سم',\n    'shoulder': 'الكتف',\n    'shoulder_circumference': 'محيط الكتف',\n    'chest': 'الصدر',\n    'waist': 'الخصر',\n    'hips': 'الأرداف',\n    'dart_length': 'طول الخياطة',\n    'bodice_length': 'طول الجسم',\n    'neckline': 'خط الرقبة',\n    'armpit': 'الإبط',\n    'sleeve_length': 'طول الكم',\n    'forearm': 'الساعد',\n    'cuff': 'الكم',\n    'front_length': 'الطول الأمامي',\n    'back_length': 'الطول الخلفي',\n    // مفاتيح صفحة الطلبات\n    'enter_order_number': 'أدخل رقم الطلب',\n    'all_orders': 'جميع الطلبات',\n    'no_orders_assigned': 'لا توجد طلبات مخصصة لك',\n    'no_orders_assigned_desc': 'لم يتم تخصيص أي طلبات لك بعد',\n    'price_label': 'السعر',\n    'sar': 'ريال',\n    'view': 'عرض',\n    'completing': 'جاري الإنهاء...',\n    // مفاتيح صفحة العمال\n    'worker_added_success': 'تم إضافة العامل بنجاح',\n    'error_adding_worker': 'حدث خطأ أثناء إضافة العامل',\n    'worker_updated_success': 'تم تحديث العامل بنجاح',\n    'error_updating_worker': 'حدث خطأ أثناء تحديث العامل',\n    'worker_deleted_success': 'تم حذف العامل بنجاح',\n    'worker_deactivated': 'تم إلغاء تفعيل العامل',\n    'worker_activated': 'تم تفعيل العامل',\n    'adding': 'جاري الإضافة...',\n    'add_worker': 'إضافة عامل',\n    'active': 'نشط',\n    'inactive': 'غير نشط',\n    'save_changes': 'حفظ التغييرات',\n    // مفاتيح صفحة التقارير\n    'engagement_dress': 'فستان خطوبة',\n    'casual_dress': 'فستان يومي',\n    'other': 'أخرى',\n    'this_week': 'هذا الأسبوع',\n    'this_month': 'هذا الشهر',\n    'this_quarter': 'هذا الربع',\n    'this_year': 'هذا العام',\n    // مفاتيح صفحة المواعيد\n    'confirmed': 'مؤكد',\n    'pm': 'مساءً',\n    'am': 'صباحاً',\n    'all_statuses': 'جميع الحالات',\n    'all_dates': 'جميع التواريخ',\n    'today': 'اليوم',\n    'tomorrow': 'غداً',\n    // مفاتيح مكونات إضافية\n    'of': 'من',\n    'images_text': 'صور',\n    // مفاتيح مكون تعديل الطلب\n    'order_update_error': 'حدث خطأ أثناء تحديث الطلب',\n    'price_sar_required': 'السعر (ريال سعودي) *',\n    'status_pending': 'في الانتظار',\n    'status_in_progress': 'قيد التنفيذ',\n    'status_completed': 'مكتمل',\n    'status_delivered': 'تم التسليم',\n    'status_cancelled': 'ملغي',\n    // نصوص الفوتر\n    'home': 'الرئيسية',\n    'track_order': 'استعلام عن الطلب',\n    'fabrics': 'الأقمشة',\n    'contact_us': 'تواصلي معنا',\n    'yasmin_alsham': 'ياسمين الشام',\n    'custom_dress_tailoring': 'تفصيل فساتين حسب الطلب'\n};\n// الترجمات الإنجليزية\nconst enTranslations = {\n    // التنقل والعناوين الرئيسية\n    'dashboard': 'Dashboard',\n    'orders': 'Orders',\n    'appointments': 'Appointments',\n    'settings': 'Settings',\n    'workers': 'Workers',\n    'reports': 'Reports',\n    'logout': 'Logout',\n    'welcome': 'Welcome',\n    'welcome_back': 'Welcome Back',\n    // الأزرار والإجراءات\n    'add_new_order': 'Add New Order',\n    'book_appointment': 'Book Appointment',\n    'view_details': 'View Details',\n    'edit': 'Edit',\n    'delete': 'Delete',\n    'save': 'Save',\n    'cancel': 'Cancel',\n    'submit': 'Submit',\n    'search': 'Search',\n    'filter': 'Filter',\n    'export': 'Export',\n    'print': 'Print',\n    'back': 'Back',\n    'next': 'Next',\n    'previous': 'Previous',\n    'close': 'Close',\n    'confirm': 'Confirm',\n    'loading': 'Loading...',\n    'saving': 'Saving...',\n    // حالات الطلبات\n    'pending': 'Pending',\n    'in_progress': 'In Progress',\n    'completed': 'Completed',\n    'delivered': 'Delivered',\n    'cancelled': 'Cancelled',\n    // نصوص عامة\n    'name': 'Name',\n    'email': 'Email',\n    'phone': 'Phone',\n    'address': 'Address',\n    'date': 'Date',\n    'time': 'Time',\n    'status': 'Status',\n    'price': 'Price',\n    'total': 'Total',\n    'description': 'Description',\n    'notes': 'Notes',\n    'client_name': 'Client Name',\n    'client_phone': 'Client Phone',\n    // رسائل النجاح والخطأ\n    'success': 'Success',\n    'error': 'Error',\n    'warning': 'Warning',\n    'info': 'Info',\n    'order_added_success': 'Order added successfully',\n    'order_updated_success': 'Order updated successfully',\n    'order_deleted_success': 'Order deleted successfully',\n    'fill_required_fields': 'Please fill all required fields',\n    // نصوص إضافية مطلوبة\n    'admin_dashboard': 'Admin Dashboard',\n    'worker_dashboard': 'Worker Dashboard',\n    'admin': 'Admin',\n    'worker': 'Worker',\n    'change_language': 'Change Language',\n    'my_active_orders': 'My Active Orders',\n    'completed_orders': 'Completed Orders',\n    'total_orders': 'Total Orders',\n    'total_revenue': 'Total Revenue',\n    'recent_orders': 'Recent Orders',\n    'quick_actions': 'Quick Actions',\n    'view_all_orders': 'View All Orders',\n    'add_order': 'Add Order',\n    'manage_workers': 'Manage Workers',\n    'view_reports': 'View Reports',\n    'client_name_required': 'Client Name *',\n    'phone_required': 'Phone Number *',\n    'order_description_required': 'Order Description *',\n    'delivery_date_required': 'Delivery Date *',\n    'price_sar': 'Price (SAR)',\n    'measurements_cm': 'Measurements (cm)',\n    'additional_notes': 'Additional Notes',\n    'voice_notes_optional': 'Voice Notes (Optional)',\n    'design_images': 'Design Images',\n    'fabric_type': 'Fabric Type',\n    'responsible_worker': 'Responsible Worker',\n    'choose_worker': 'Choose Responsible Worker',\n    'order_status': 'Order Status',\n    'back_to_dashboard': 'Back to Dashboard',\n    'overview_today': 'Overview of today\\'s activities',\n    'welcome_worker': 'Welcome to your workspace',\n    // مفاتيح مفقودة من لوحة التحكم\n    'homepage': 'Homepage',\n    'my_completed_orders': 'My Completed Orders',\n    'my_total_orders': 'My Total Orders',\n    'active_orders': 'Active Orders',\n    'today_appointments': 'Today\\'s Appointments',\n    'statistics': 'Statistics',\n    'no_orders_found': 'No orders found',\n    'view_all': 'View All',\n    'worker_management': 'Worker Management',\n    'reminder': 'Reminder',\n    'you_have': 'You have',\n    'today_appointments_reminder': 'appointments today',\n    'and': 'and',\n    'orders_need_follow': 'orders that need follow-up',\n    'detailed_reports': 'Detailed Reports',\n    'worker_description': 'Here you can track your assigned orders and update their status',\n    // مفاتيح صفحة إضافة الطلبات\n    'order_add_error': 'An error occurred while adding the order',\n    'cm_placeholder': 'cm',\n    'shoulder': 'Shoulder',\n    'shoulder_circumference': 'Shoulder Circumference',\n    'chest': 'Chest',\n    'waist': 'Waist',\n    'hips': 'Hips',\n    'dart_length': 'Dart Length',\n    'bodice_length': 'Bodice Length',\n    'neckline': 'Neckline',\n    'armpit': 'Armpit',\n    'sleeve_length': 'Sleeve Length',\n    'forearm': 'Forearm',\n    'cuff': 'Cuff',\n    'front_length': 'Front Length',\n    'back_length': 'Back Length',\n    // مفاتيح صفحة الطلبات\n    'enter_order_number': 'Enter order number',\n    'all_orders': 'All Orders',\n    'no_orders_assigned': 'No orders assigned to you',\n    'no_orders_assigned_desc': 'No orders have been assigned to you yet',\n    'price_label': 'Price',\n    'sar': 'SAR',\n    'view': 'View',\n    'completing': 'Completing...',\n    // مفاتيح صفحة العمال\n    'worker_added_success': 'Worker added successfully',\n    'error_adding_worker': 'Error adding worker',\n    'worker_updated_success': 'Worker updated successfully',\n    'error_updating_worker': 'Error updating worker',\n    'worker_deleted_success': 'Worker deleted successfully',\n    'worker_deactivated': 'Worker deactivated',\n    'worker_activated': 'Worker activated',\n    'adding': 'Adding...',\n    'add_worker': 'Add Worker',\n    'active': 'Active',\n    'inactive': 'Inactive',\n    'save_changes': 'Save Changes',\n    // مفاتيح صفحة التقارير\n    'engagement_dress': 'Engagement Dress',\n    'casual_dress': 'Casual Dress',\n    'other': 'Other',\n    'this_week': 'This Week',\n    'this_month': 'This Month',\n    'this_quarter': 'This Quarter',\n    'this_year': 'This Year',\n    // مفاتيح صفحة المواعيد\n    'confirmed': 'Confirmed',\n    'pm': 'PM',\n    'am': 'AM',\n    'all_statuses': 'All Statuses',\n    'all_dates': 'All Dates',\n    'today': 'Today',\n    'tomorrow': 'Tomorrow',\n    // مفاتيح مكونات إضافية\n    'of': 'of',\n    'images_text': 'images',\n    // نصوص الفوتر\n    'home': 'Home',\n    'track_order': 'Track Order',\n    'fabrics': 'Fabrics',\n    'contact_us': 'Contact Us',\n    'yasmin_alsham': 'Yasmin Alsham',\n    'custom_dress_tailoring': 'Custom Dress Tailoring'\n};\n// Hook للترجمة\nfunction useTranslation() {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('ar');\n    // تحميل اللغة المحفوظة عند بدء التطبيق\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTranslation.useEffect\": ()=>{\n            const savedLanguage = localStorage.getItem('dashboard-language');\n            if (savedLanguage) {\n                setLanguage(savedLanguage);\n            }\n        }\n    }[\"useTranslation.useEffect\"], []);\n    // حفظ اللغة عند تغييرها\n    const changeLanguage = (newLanguage)=>{\n        setLanguage(newLanguage);\n        localStorage.setItem('dashboard-language', newLanguage);\n    };\n    // دالة الترجمة\n    const t = (key)=>{\n        const translations = language === 'ar' ? arTranslations : enTranslations;\n        const translation = translations[key];\n        if (typeof translation === 'string') {\n            return translation;\n        }\n        // إذا لم توجد الترجمة، أرجع المفتاح نفسه\n        return key;\n    };\n    // التحقق من اللغة الحالية\n    const isArabic = language === 'ar';\n    const isEnglish = language === 'en';\n    return {\n        language,\n        changeLanguage,\n        t,\n        isArabic,\n        isEnglish\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useTranslation.ts\n"));

/***/ })

});