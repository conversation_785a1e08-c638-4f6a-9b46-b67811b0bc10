{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/store/dataStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\n\n// تعريف أنواع البيانات\nexport interface Appointment {\n  id: string\n  clientName: string\n  clientPhone: string\n  appointmentDate: string\n  appointmentTime: string\n  notes?: string\n  status: 'pending' | 'confirmed' | 'completed' | 'cancelled'\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Order {\n  id: string\n  clientName: string\n  clientPhone: string\n  description: string\n  fabric: string\n  measurements: {\n    // المقاسات الأساسية\n    shoulder?: number // الكتف\n    shoulderCircumference?: number // دوران الكتف\n    chest?: number // الصدر\n    waist?: number // الخصر\n    hips?: number // الأرداف\n\n    // مقاسات التفصيل المتقدمة\n    dartLength?: number // طول البنس\n    bodiceLength?: number // طول الصدرية\n    neckline?: number // فتحة الصدر\n    armpit?: number // الإبط\n\n    // مقاسات الأكمام\n    sleeveLength?: number // طول الكم\n    forearm?: number // الزند\n    cuff?: number // الأسوارة\n\n    // مقاسات الطول\n    frontLength?: number // طول الأمام\n    backLength?: number // طول الخلف\n\n    // للتوافق مع النظام القديم (سيتم إزالتها لاحقاً)\n    length?: number // طول الفستان (قديم)\n    shoulders?: number // عرض الكتف (قديم)\n    sleeves?: number // طول الأكمام (قديم)\n  }\n  price: number\n  status: 'pending' | 'in_progress' | 'completed' | 'delivered' | 'cancelled'\n  assignedWorker?: string\n  dueDate: string\n  notes?: string\n  voiceNotes?: Array<{\n    id: string\n    data: string\n    timestamp: number\n    duration?: number\n  }> // ملاحظات صوتية متعددة\n  images?: string[] // مصفوفة من base64 strings للصور\n  completedImages?: string[] // صور العمل المكتمل (للعمال فقط)\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Worker {\n  id: string\n  email: string\n  password: string\n  full_name: string\n  phone: string\n  specialty: string\n  role: 'worker'\n  is_active: boolean\n  createdAt: string\n  updatedAt: string\n}\n\ninterface DataState {\n  // البيانات\n  appointments: Appointment[]\n  orders: Order[]\n  workers: Worker[]\n  \n  // حالة التحميل\n  isLoading: boolean\n  error: string | null\n\n  // إدارة المواعيد\n  addAppointment: (appointment: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>) => void\n  updateAppointment: (id: string, updates: Partial<Appointment>) => void\n  deleteAppointment: (id: string) => void\n  getAppointment: (id: string) => Appointment | undefined\n\n  // إدارة الطلبات\n  addOrder: (order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) => void\n  updateOrder: (id: string, updates: Partial<Order>) => void\n  deleteOrder: (id: string) => void\n  getOrder: (id: string) => Order | undefined\n\n  // دوال خاصة للعمال\n  startOrderWork: (orderId: string, workerId: string) => void\n  completeOrder: (orderId: string, workerId: string, completedImages?: string[]) => void\n\n  // إدارة العمال\n  addWorker: (worker: Omit<Worker, 'id' | 'createdAt' | 'updatedAt' | 'role'>) => void\n  updateWorker: (id: string, updates: Partial<Worker>) => void\n  deleteWorker: (id: string) => void\n  getWorker: (id: string) => Worker | undefined\n\n  // وظائف مساعدة\n  clearError: () => void\n  loadData: () => void\n  \n  // إحصائيات\n  getStats: () => {\n    totalAppointments: number\n    totalOrders: number\n    totalWorkers: number\n    pendingAppointments: number\n    activeOrders: number\n    completedOrders: number\n    totalRevenue: number\n  }\n}\n\n// توليد ID فريد\nconst generateId = () => {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2)\n}\n\nexport const useDataStore = create<DataState>()(\n  persist(\n    (set, get) => ({\n      // البيانات الأولية\n      appointments: [],\n      orders: [],\n      workers: [],\n      isLoading: false,\n      error: null,\n\n      // إدارة المواعيد\n      addAppointment: (appointmentData) => {\n        const appointment: Appointment = {\n          ...appointmentData,\n          id: generateId(),\n          status: 'pending',\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        }\n\n        set((state) => ({\n          appointments: [...state.appointments, appointment],\n          error: null\n        }))\n\n        console.log('✅ تم إضافة موعد جديد:', appointment)\n      },\n\n      updateAppointment: (id, updates) => {\n        set((state) => ({\n          appointments: state.appointments.map(appointment =>\n            appointment.id === id\n              ? { ...appointment, ...updates, updatedAt: new Date().toISOString() }\n              : appointment\n          ),\n          error: null\n        }))\n\n        console.log('✅ تم تحديث الموعد:', id)\n      },\n\n      deleteAppointment: (id) => {\n        set((state) => ({\n          appointments: state.appointments.filter(appointment => appointment.id !== id),\n          error: null\n        }))\n\n        console.log('✅ تم حذف الموعد:', id)\n      },\n\n      getAppointment: (id) => {\n        const state = get()\n        return state.appointments.find(appointment => appointment.id === id)\n      },\n\n      // إدارة الطلبات\n      addOrder: (orderData) => {\n        const order: Order = {\n          ...orderData,\n          id: generateId(),\n          status: 'pending',\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        }\n\n        set((state) => ({\n          orders: [...state.orders, order],\n          error: null\n        }))\n\n        console.log('✅ تم إضافة طلب جديد:', order)\n      },\n\n      updateOrder: (id, updates) => {\n        set((state) => ({\n          orders: state.orders.map(order =>\n            order.id === id\n              ? { ...order, ...updates, updatedAt: new Date().toISOString() }\n              : order\n          ),\n          error: null\n        }))\n\n        console.log('✅ تم تحديث الطلب:', id)\n      },\n\n      deleteOrder: (id) => {\n        set((state) => ({\n          orders: state.orders.filter(order => order.id !== id),\n          error: null\n        }))\n\n        console.log('✅ تم حذف الطلب:', id)\n      },\n\n      getOrder: (id) => {\n        const state = get()\n        return state.orders.find(order => order.id === id)\n      },\n\n      // إدارة العمال\n      addWorker: (workerData) => {\n        const worker: Worker = {\n          ...workerData,\n          id: generateId(),\n          role: 'worker',\n          is_active: true,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        }\n\n        set((state) => ({\n          workers: [...state.workers, worker],\n          error: null\n        }))\n\n        // إضافة العامل إلى نظام المصادقة\n        if (typeof window !== 'undefined') {\n          const users = JSON.parse(localStorage.getItem('yasmin-users') || '[]')\n          users.push({\n            id: worker.id,\n            email: worker.email,\n            password: worker.password,\n            full_name: worker.full_name,\n            role: 'worker',\n            is_active: true\n          })\n          localStorage.setItem('yasmin-users', JSON.stringify(users))\n        }\n\n        console.log('✅ تم إضافة عامل جديد:', worker)\n      },\n\n      updateWorker: (id, updates) => {\n        set((state) => ({\n          workers: state.workers.map(worker =>\n            worker.id === id\n              ? { ...worker, ...updates, updatedAt: new Date().toISOString() }\n              : worker\n          ),\n          error: null\n        }))\n\n        // تحديث بيانات المصادقة إذا تم تغيير البريد أو كلمة المرور\n        if (updates.email || updates.password || updates.full_name) {\n          if (typeof window !== 'undefined') {\n            const users = JSON.parse(localStorage.getItem('yasmin-users') || '[]')\n            const userIndex = users.findIndex((user: any) => user.id === id)\n            if (userIndex !== -1) {\n              if (updates.email) users[userIndex].email = updates.email\n              if (updates.password) users[userIndex].password = updates.password\n              if (updates.full_name) users[userIndex].full_name = updates.full_name\n              localStorage.setItem('yasmin-users', JSON.stringify(users))\n            }\n          }\n        }\n\n        console.log('✅ تم تحديث العامل:', id)\n      },\n\n      deleteWorker: (id) => {\n        set((state) => ({\n          workers: state.workers.filter(worker => worker.id !== id),\n          error: null\n        }))\n\n        // حذف من نظام المصادقة\n        if (typeof window !== 'undefined') {\n          const users = JSON.parse(localStorage.getItem('yasmin-users') || '[]')\n          const filteredUsers = users.filter((user: any) => user.id !== id)\n          localStorage.setItem('yasmin-users', JSON.stringify(filteredUsers))\n        }\n\n        console.log('✅ تم حذف العامل:', id)\n      },\n\n      getWorker: (id) => {\n        const state = get()\n        return state.workers.find(worker => worker.id === id)\n      },\n\n      // دوال خاصة للعمال\n      startOrderWork: (orderId, workerId) => {\n        set((state) => ({\n          orders: state.orders.map(order =>\n            order.id === orderId && order.assignedWorker === workerId\n              ? { ...order, status: 'in_progress', updatedAt: new Date().toISOString() }\n              : order\n          ),\n          error: null\n        }))\n\n        console.log('✅ تم بدء العمل في الطلب:', orderId)\n      },\n\n      completeOrder: (orderId, workerId, completedImages = []) => {\n        set((state) => ({\n          orders: state.orders.map(order =>\n            order.id === orderId && order.assignedWorker === workerId\n              ? {\n                  ...order,\n                  status: 'completed',\n                  completedImages: completedImages.length > 0 ? completedImages : undefined,\n                  updatedAt: new Date().toISOString()\n                }\n              : order\n          ),\n          error: null\n        }))\n\n        console.log('✅ تم إنهاء الطلب:', orderId)\n      },\n\n      // وظائف مساعدة\n      clearError: () => {\n        set({ error: null })\n      },\n\n      loadData: () => {\n        set({ isLoading: true })\n        // البيانات محفوظة تلقائياً بواسطة persist middleware\n        set({ isLoading: false })\n      },\n\n      // إحصائيات\n      getStats: () => {\n        const state = get()\n        return {\n          totalAppointments: state.appointments.length,\n          totalOrders: state.orders.length,\n          totalWorkers: state.workers.length,\n          pendingAppointments: state.appointments.filter(a => a.status === 'pending').length,\n          activeOrders: state.orders.filter(o => ['pending', 'in_progress'].includes(o.status)).length,\n          completedOrders: state.orders.filter(o => o.status === 'completed').length,\n          totalRevenue: state.orders\n            .filter(o => o.status === 'completed')\n            .reduce((sum, order) => sum + order.price, 0)\n        }\n      }\n    }),\n    {\n      name: 'yasmin-data-storage',\n      partialize: (state) => ({\n        appointments: state.appointments,\n        orders: state.orders,\n        workers: state.workers\n      })\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA+HA,gBAAgB;AAChB,MAAM,aAAa;IACjB,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;AACrE;AAEO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,mBAAmB;QACnB,cAAc,EAAE;QAChB,QAAQ,EAAE;QACV,SAAS,EAAE;QACX,WAAW;QACX,OAAO;QAEP,iBAAiB;QACjB,gBAAgB,CAAC;YACf,MAAM,cAA2B;gBAC/B,GAAG,eAAe;gBAClB,IAAI;gBACJ,QAAQ;gBACR,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,cAAc;2BAAI,MAAM,YAAY;wBAAE;qBAAY;oBAClD,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,yBAAyB;QACvC;QAEA,mBAAmB,CAAC,IAAI;YACtB,IAAI,CAAC,QAAU,CAAC;oBACd,cAAc,MAAM,YAAY,CAAC,GAAG,CAAC,CAAA,cACnC,YAAY,EAAE,KAAK,KACf;4BAAE,GAAG,WAAW;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI,OAAO,WAAW;wBAAG,IAClE;oBAEN,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,sBAAsB;QACpC;QAEA,mBAAmB,CAAC;YAClB,IAAI,CAAC,QAAU,CAAC;oBACd,cAAc,MAAM,YAAY,CAAC,MAAM,CAAC,CAAA,cAAe,YAAY,EAAE,KAAK;oBAC1E,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,oBAAoB;QAClC;QAEA,gBAAgB,CAAC;YACf,MAAM,QAAQ;YACd,OAAO,MAAM,YAAY,CAAC,IAAI,CAAC,CAAA,cAAe,YAAY,EAAE,KAAK;QACnE;QAEA,gBAAgB;QAChB,UAAU,CAAC;YACT,MAAM,QAAe;gBACnB,GAAG,SAAS;gBACZ,IAAI;gBACJ,QAAQ;gBACR,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ;2BAAI,MAAM,MAAM;wBAAE;qBAAM;oBAChC,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,wBAAwB;QACtC;QAEA,aAAa,CAAC,IAAI;YAChB,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,QACvB,MAAM,EAAE,KAAK,KACT;4BAAE,GAAG,KAAK;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI,OAAO,WAAW;wBAAG,IAC5D;oBAEN,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,qBAAqB;QACnC;QAEA,aAAa,CAAC;YACZ,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;oBAClD,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,mBAAmB;QACjC;QAEA,UAAU,CAAC;YACT,MAAM,QAAQ;YACd,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACjD;QAEA,eAAe;QACf,WAAW,CAAC;YACV,MAAM,SAAiB;gBACrB,GAAG,UAAU;gBACb,IAAI;gBACJ,MAAM;gBACN,WAAW;gBACX,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,SAAS;2BAAI,MAAM,OAAO;wBAAE;qBAAO;oBACnC,OAAO;gBACT,CAAC;YAED,iCAAiC;YACjC,uCAAmC;;YAWnC;YAEA,QAAQ,GAAG,CAAC,yBAAyB;QACvC;QAEA,cAAc,CAAC,IAAI;YACjB,IAAI,CAAC,QAAU,CAAC;oBACd,SAAS,MAAM,OAAO,CAAC,GAAG,CAAC,CAAA,SACzB,OAAO,EAAE,KAAK,KACV;4BAAE,GAAG,MAAM;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI,OAAO,WAAW;wBAAG,IAC7D;oBAEN,OAAO;gBACT,CAAC;YAED,2DAA2D;YAC3D,IAAI,QAAQ,KAAK,IAAI,QAAQ,QAAQ,IAAI,QAAQ,SAAS,EAAE;gBAC1D,uCAAmC;;gBASnC;YACF;YAEA,QAAQ,GAAG,CAAC,sBAAsB;QACpC;QAEA,cAAc,CAAC;YACb,IAAI,CAAC,QAAU,CAAC;oBACd,SAAS,MAAM,OAAO,CAAC,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;oBACtD,OAAO;gBACT,CAAC;YAED,uBAAuB;YACvB,uCAAmC;;YAInC;YAEA,QAAQ,GAAG,CAAC,oBAAoB;QAClC;QAEA,WAAW,CAAC;YACV,MAAM,QAAQ;YACd,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;QACpD;QAEA,mBAAmB;QACnB,gBAAgB,CAAC,SAAS;YACxB,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,QACvB,MAAM,EAAE,KAAK,WAAW,MAAM,cAAc,KAAK,WAC7C;4BAAE,GAAG,KAAK;4BAAE,QAAQ;4BAAe,WAAW,IAAI,OAAO,WAAW;wBAAG,IACvE;oBAEN,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,4BAA4B;QAC1C;QAEA,eAAe,CAAC,SAAS,UAAU,kBAAkB,EAAE;YACrD,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,QACvB,MAAM,EAAE,KAAK,WAAW,MAAM,cAAc,KAAK,WAC7C;4BACE,GAAG,KAAK;4BACR,QAAQ;4BACR,iBAAiB,gBAAgB,MAAM,GAAG,IAAI,kBAAkB;4BAChE,WAAW,IAAI,OAAO,WAAW;wBACnC,IACA;oBAEN,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,qBAAqB;QACnC;QAEA,eAAe;QACf,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,UAAU;YACR,IAAI;gBAAE,WAAW;YAAK;YACtB,qDAAqD;YACrD,IAAI;gBAAE,WAAW;YAAM;QACzB;QAEA,WAAW;QACX,UAAU;YACR,MAAM,QAAQ;YACd,OAAO;gBACL,mBAAmB,MAAM,YAAY,CAAC,MAAM;gBAC5C,aAAa,MAAM,MAAM,CAAC,MAAM;gBAChC,cAAc,MAAM,OAAO,CAAC,MAAM;gBAClC,qBAAqB,MAAM,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;gBAClF,cAAc,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK;wBAAC;wBAAW;qBAAc,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM;gBAC5F,iBAAiB,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;gBAC1E,cAAc,MAAM,MAAM,CACvB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aACzB,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,KAAK,EAAE;YAC/C;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,cAAc,MAAM,YAAY;YAChC,QAAQ,MAAM,MAAM;YACpB,SAAS,MAAM,OAAO;QACxB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/utils/inputValidation.ts"], "sourcesContent": ["/**\n * دوال التحقق من صحة الإدخال للحقول الرقمية\n */\n\n// التحقق من أن النص يحتوي على أرقام فقط\nexport const isNumericOnly = (value: string): boolean => {\n  return /^\\d*\\.?\\d*$/.test(value)\n}\n\n// التحقق من أن النص يحتوي على أرقام صحيحة فقط (بدون فواصل عشرية)\nexport const isIntegerOnly = (value: string): boolean => {\n  return /^\\d*$/.test(value)\n}\n\n// التحقق من صحة رقم الهاتف (أرقام فقط مع إمكانية وجود + في البداية)\nexport const isValidPhoneNumber = (value: string): boolean => {\n  return /^(\\+)?\\d*$/.test(value)\n}\n\n// تنظيف الإدخال من الأحرف غير الرقمية\nexport const cleanNumericInput = (value: string): string => {\n  return value.replace(/[^\\d.]/g, '')\n}\n\n// تنظيف الإدخال من الأحرف غير الرقمية (أرقام صحيحة فقط)\nexport const cleanIntegerInput = (value: string): string => {\n  return value.replace(/[^\\d]/g, '')\n}\n\n// تنظيف رقم الهاتف\nexport const cleanPhoneInput = (value: string): string => {\n  // السماح بـ + في البداية فقط\n  if (value.startsWith('+')) {\n    return '+' + value.slice(1).replace(/[^\\d]/g, '')\n  }\n  return value.replace(/[^\\d]/g, '')\n}\n\n// التحقق من صحة المقاس (رقم موجب)\nexport const isValidMeasurement = (value: string): boolean => {\n  const num = parseFloat(value)\n  return !isNaN(num) && num > 0\n}\n\n// التحقق من صحة السعر (رقم موجب)\nexport const isValidPrice = (value: string): boolean => {\n  const num = parseFloat(value)\n  return !isNaN(num) && num > 0\n}\n\n// رسائل الخطأ\nexport const getValidationErrorMessage = (fieldType: string, language: 'ar' | 'en' = 'ar'): string => {\n  const messages = {\n    ar: {\n      measurement: 'يرجى إدخال رقم صحيح للمقاس',\n      price: 'يرجى إدخال سعر صحيح',\n      phone: 'يرجى إدخال رقم هاتف صحيح',\n      orderNumber: 'يرجى إدخال رقم طلب صحيح',\n      numeric: 'يرجى إدخال أرقام فقط',\n      positive: 'يرجى إدخال رقم أكبر من الصفر'\n    },\n    en: {\n      measurement: 'Please enter a valid measurement',\n      price: 'Please enter a valid price',\n      phone: 'Please enter a valid phone number',\n      orderNumber: 'Please enter a valid order number',\n      numeric: 'Please enter numbers only',\n      positive: 'Please enter a number greater than zero'\n    }\n  }\n  \n  return messages[language][fieldType] || messages[language].numeric\n}\n\n// دالة للتعامل مع تغيير الإدخال في الحقول الرقمية\nexport const handleNumericInputChange = (\n  value: string,\n  fieldType: 'measurement' | 'price' | 'phone' | 'orderNumber' | 'integer' | 'decimal',\n  onChange: (value: string) => void,\n  onError?: (error: string | null) => void\n) => {\n  let cleanedValue = value\n  let isValid = true\n  let errorMessage: string | null = null\n\n  switch (fieldType) {\n    case 'measurement':\n      cleanedValue = cleanNumericInput(value)\n      isValid = isValidMeasurement(cleanedValue) || cleanedValue === ''\n      if (!isValid && cleanedValue !== '') {\n        errorMessage = getValidationErrorMessage('measurement')\n      }\n      break\n\n    case 'price':\n      cleanedValue = cleanNumericInput(value)\n      isValid = isValidPrice(cleanedValue) || cleanedValue === ''\n      if (!isValid && cleanedValue !== '') {\n        errorMessage = getValidationErrorMessage('price')\n      }\n      break\n\n    case 'phone':\n      cleanedValue = cleanPhoneInput(value)\n      isValid = isValidPhoneNumber(cleanedValue)\n      if (!isValid) {\n        errorMessage = getValidationErrorMessage('phone')\n      }\n      break\n\n    case 'orderNumber':\n      cleanedValue = cleanIntegerInput(value)\n      isValid = isIntegerOnly(cleanedValue)\n      if (!isValid) {\n        errorMessage = getValidationErrorMessage('orderNumber')\n      }\n      break\n\n    case 'integer':\n      cleanedValue = cleanIntegerInput(value)\n      isValid = isIntegerOnly(cleanedValue)\n      if (!isValid) {\n        errorMessage = getValidationErrorMessage('numeric')\n      }\n      break\n\n    case 'decimal':\n      cleanedValue = cleanNumericInput(value)\n      isValid = isNumericOnly(cleanedValue)\n      if (!isValid) {\n        errorMessage = getValidationErrorMessage('numeric')\n      }\n      break\n  }\n\n  onChange(cleanedValue)\n  if (onError) {\n    onError(errorMessage)\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,wCAAwC;;;;;;;;;;;;;AACjC,MAAM,gBAAgB,CAAC;IAC5B,OAAO,cAAc,IAAI,CAAC;AAC5B;AAGO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,QAAQ,IAAI,CAAC;AACtB;AAGO,MAAM,qBAAqB,CAAC;IACjC,OAAO,aAAa,IAAI,CAAC;AAC3B;AAGO,MAAM,oBAAoB,CAAC;IAChC,OAAO,MAAM,OAAO,CAAC,WAAW;AAClC;AAGO,MAAM,oBAAoB,CAAC;IAChC,OAAO,MAAM,OAAO,CAAC,UAAU;AACjC;AAGO,MAAM,kBAAkB,CAAC;IAC9B,6BAA6B;IAC7B,IAAI,MAAM,UAAU,CAAC,MAAM;QACzB,OAAO,MAAM,MAAM,KAAK,CAAC,GAAG,OAAO,CAAC,UAAU;IAChD;IACA,OAAO,MAAM,OAAO,CAAC,UAAU;AACjC;AAGO,MAAM,qBAAqB,CAAC;IACjC,MAAM,MAAM,WAAW;IACvB,OAAO,CAAC,MAAM,QAAQ,MAAM;AAC9B;AAGO,MAAM,eAAe,CAAC;IAC3B,MAAM,MAAM,WAAW;IACvB,OAAO,CAAC,MAAM,QAAQ,MAAM;AAC9B;AAGO,MAAM,4BAA4B,CAAC,WAAmB,WAAwB,IAAI;IACvF,MAAM,WAAW;QACf,IAAI;YACF,aAAa;YACb,OAAO;YACP,OAAO;YACP,aAAa;YACb,SAAS;YACT,UAAU;QACZ;QACA,IAAI;YACF,aAAa;YACb,OAAO;YACP,OAAO;YACP,aAAa;YACb,SAAS;YACT,UAAU;QACZ;IACF;IAEA,OAAO,QAAQ,CAAC,SAAS,CAAC,UAAU,IAAI,QAAQ,CAAC,SAAS,CAAC,OAAO;AACpE;AAGO,MAAM,2BAA2B,CACtC,OACA,WACA,UACA;IAEA,IAAI,eAAe;IACnB,IAAI,UAAU;IACd,IAAI,eAA8B;IAElC,OAAQ;QACN,KAAK;YACH,eAAe,kBAAkB;YACjC,UAAU,mBAAmB,iBAAiB,iBAAiB;YAC/D,IAAI,CAAC,WAAW,iBAAiB,IAAI;gBACnC,eAAe,0BAA0B;YAC3C;YACA;QAEF,KAAK;YACH,eAAe,kBAAkB;YACjC,UAAU,aAAa,iBAAiB,iBAAiB;YACzD,IAAI,CAAC,WAAW,iBAAiB,IAAI;gBACnC,eAAe,0BAA0B;YAC3C;YACA;QAEF,KAAK;YACH,eAAe,gBAAgB;YAC/B,UAAU,mBAAmB;YAC7B,IAAI,CAAC,SAAS;gBACZ,eAAe,0BAA0B;YAC3C;YACA;QAEF,KAAK;YACH,eAAe,kBAAkB;YACjC,UAAU,cAAc;YACxB,IAAI,CAAC,SAAS;gBACZ,eAAe,0BAA0B;YAC3C;YACA;QAEF,KAAK;YACH,eAAe,kBAAkB;YACjC,UAAU,cAAc;YACxB,IAAI,CAAC,SAAS;gBACZ,eAAe,0BAA0B;YAC3C;YACA;QAEF,KAAK;YACH,eAAe,kBAAkB;YACjC,UAAU,cAAc;YACxB,IAAI,CAAC,SAAS;gBACZ,eAAe,0BAA0B;YAC3C;YACA;IACJ;IAEA,SAAS;IACT,IAAI,SAAS;QACX,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/components/NumericInput.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { AlertCircle } from 'lucide-react'\nimport { handleNumericInputChange } from '@/utils/inputValidation'\n\ninterface NumericInputProps {\n  value: string\n  onChange: (value: string) => void\n  type: 'measurement' | 'price' | 'phone' | 'orderNumber' | 'integer' | 'decimal'\n  placeholder?: string\n  className?: string\n  disabled?: boolean\n  required?: boolean\n  label?: string\n  id?: string\n}\n\nexport default function NumericInput({\n  value,\n  onChange,\n  type,\n  placeholder,\n  className = '',\n  disabled = false,\n  required = false,\n  label,\n  id\n}: NumericInputProps) {\n  const [error, setError] = useState<string | null>(null)\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const inputValue = e.target.value\n    \n    handleNumericInputChange(\n      inputValue,\n      type,\n      onChange,\n      setError\n    )\n  }\n\n  const baseClassName = `w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors duration-200 ${\n    error ? 'border-red-500 bg-red-50' : 'border-gray-300'\n  } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''} ${className}`\n\n  return (\n    <div className=\"space-y-2\">\n      {label && (\n        <label htmlFor={id} className=\"block text-sm font-medium text-gray-700\">\n          {label}\n          {required && <span className=\"text-red-500 mr-1\">*</span>}\n        </label>\n      )}\n      \n      <div className=\"relative\">\n        <input\n          id={id}\n          type=\"text\"\n          value={value}\n          onChange={handleChange}\n          placeholder={placeholder}\n          className={baseClassName}\n          disabled={disabled}\n          required={required}\n          inputMode={type === 'phone' ? 'tel' : 'numeric'}\n          autoComplete={type === 'phone' ? 'tel' : 'off'}\n        />\n        \n        {error && (\n          <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2\">\n            <AlertCircle className=\"w-5 h-5 text-red-500\" />\n          </div>\n        )}\n      </div>\n      \n      {error && (\n        <p className=\"text-sm text-red-600 flex items-center space-x-1 space-x-reverse\">\n          <AlertCircle className=\"w-4 h-4\" />\n          <span>{error}</span>\n        </p>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAkBe,SAAS,aAAa,EACnC,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,WAAW,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,WAAW,KAAK,EAChB,KAAK,EACL,EAAE,EACgB;IAClB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa,EAAE,MAAM,CAAC,KAAK;QAEjC,CAAA,GAAA,+HAAA,CAAA,2BAAwB,AAAD,EACrB,YACA,MACA,UACA;IAEJ;IAEA,MAAM,gBAAgB,CAAC,4HAA4H,EACjJ,QAAQ,6BAA6B,kBACtC,CAAC,EAAE,WAAW,mCAAmC,GAAG,CAAC,EAAE,WAAW;IAEnE,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,SAAS;gBAAI,WAAU;;oBAC3B;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,IAAI;wBACJ,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,aAAa;wBACb,WAAW;wBACX,UAAU;wBACV,UAAU;wBACV,WAAW,SAAS,UAAU,QAAQ;wBACtC,cAAc,SAAS,UAAU,QAAQ;;;;;;oBAG1C,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAK5B,uBACC,8OAAC;gBAAE,WAAU;;kCACX,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;kCAAM;;;;;;;;;;;;;;;;;;AAKjB", "debugId": null}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-al<PERSON>/src/app/book-appointment/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion } from 'framer-motion'\nimport { Calendar, Clock, MessageSquare, CheckCircle, AlertCircle, ArrowRight } from 'lucide-react'\nimport Link from 'next/link'\nimport { useDataStore } from '@/store/dataStore'\nimport NumericInput from '@/components/NumericInput'\n\nexport default function BookAppointmentPage() {\n  const [selectedDate, setSelectedDate] = useState<string>('')\n  const [selectedTime, setSelectedTime] = useState<string>('')\n  const [clientName, setClientName] = useState('')\n  const [clientPhone, setClientPhone] = useState('')\n  const [notes, setNotes] = useState('')\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)\n\n  // Hydration-safe state for date formatting\n  const [isMounted, setIsMounted] = useState(false)\n  const [formattedDates, setFormattedDates] = useState<{[key: string]: string}>({})\n\n  const { addAppointment, appointments } = useDataStore()\n\n  // Client-side date formatting to avoid hydration mismatch\n  useEffect(() => {\n    setIsMounted(true)\n\n    // Format dates client-side only\n    const dates = getAvailableDates()\n    const formatted: {[key: string]: string} = {}\n\n    dates.forEach(dateString => {\n      const date = new Date(dateString)\n\n      // التاريخ الميلادي فقط\n      const gregorianOptions: Intl.DateTimeFormatOptions = {\n        weekday: 'long',\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      }\n      const gregorianDate = date.toLocaleDateString('ar-US', gregorianOptions)\n\n      formatted[dateString] = gregorianDate\n    })\n\n    setFormattedDates(formatted)\n  }, [])\n\n  // توليد التواريخ المتاحة (اليوم الحالي + 30 يوم قادم، عدا الجمعة)\n  const getAvailableDates = () => {\n    const dates = []\n    const today = new Date()\n\n    for (let i = 0; i <= 30; i++) {\n      const date = new Date(today)\n      date.setDate(today.getDate() + i)\n\n      // تجاهل يوم الجمعة (5)\n      if (date.getDay() === 5) continue\n\n      dates.push(date.toISOString().split('T')[0])\n    }\n\n    return dates\n  }\n\n  // توليد جميع الأوقات مع حالة الحجز\n  const getAllTimesForDate = (date: string) => {\n    const allTimes = [\n      { time: '16:00', display: '4:00' },\n      { time: '16:45', display: '4:45' },\n      { time: '17:30', display: '5:30' },\n      { time: '18:15', display: '6:15' },\n      { time: '19:00', display: '7:00' },\n      { time: '20:00', display: '8:00' },\n      { time: '21:00', display: '9:00' }\n    ]\n\n    // التحقق من كون التاريخ هو اليوم الحالي\n    const today = new Date().toISOString().split('T')[0]\n    const isToday = date === today\n\n    // إذا كان اليوم الحالي، فلتر الأوقات المتبقية فقط\n    let availableTimes = allTimes\n    if (isToday) {\n      const now = new Date()\n      const currentHour = now.getHours()\n      const currentMinute = now.getMinutes()\n      const currentTimeInMinutes = currentHour * 60 + currentMinute\n\n      availableTimes = allTimes.filter(timeSlot => {\n        const [hours, minutes] = timeSlot.time.split(':').map(Number)\n        const slotTimeInMinutes = hours * 60 + minutes\n        // إضافة 30 دقيقة كحد أدنى للحجز المسبق\n        return slotTimeInMinutes > currentTimeInMinutes + 30\n      })\n    }\n\n    // الحصول على الأوقات المحجوزة\n    const bookedTimes = appointments\n      .filter(appointment =>\n        appointment.appointmentDate === date &&\n        appointment.status !== 'cancelled'\n      )\n      .map(appointment => appointment.appointmentTime)\n\n    return availableTimes.map(timeSlot => ({\n      ...timeSlot,\n      isBooked: bookedTimes.includes(timeSlot.time)\n    }))\n  }\n\n  // Hydration-safe date display function\n  const getDateDisplayText = (dateString: string) => {\n    if (!isMounted) {\n      return 'جاري تحميل التاريخ...'\n    }\n    return formattedDates[dateString] || 'تاريخ غير متاح'\n  }\n\n  // تنسيق الوقت للعرض\n  const formatTimeForDisplay = (timeString: string) => {\n    const [hours, minutes] = timeString.split(':')\n    const hour = parseInt(hours)\n    const ampm = hour >= 12 ? 'م' : 'ص'\n    const displayHour = hour > 12 ? hour - 12 : hour === 0 ? 12 : hour\n    return `${displayHour}:${minutes} ${ampm}`\n  }\n\n  // إرسال طلب الحجز\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    if (!selectedDate || !selectedTime || !clientName || !clientPhone) {\n      setMessage({ type: 'error', text: 'يرجى ملء جميع الحقول المطلوبة' })\n      return\n    }\n\n    setIsSubmitting(true)\n    setMessage(null)\n\n    try {\n      // محاكاة تأخير الشبكة\n      await new Promise(resolve => setTimeout(resolve, 1500))\n\n      // إضافة الموعد إلى المتجر\n      addAppointment({\n        clientName,\n        clientPhone,\n        appointmentDate: selectedDate,\n        appointmentTime: selectedTime,\n        notes: notes || undefined,\n        status: 'pending'\n      })\n\n      setMessage({\n        type: 'success',\n        text: 'تم حجز موعدك بنجاح! سنرسل لك تذكيراً قبل الموعد بساعتين.'\n      })\n\n      // إعادة تعيين النموذج\n      setSelectedDate('')\n      setSelectedTime('')\n      setClientName('')\n      setClientPhone('')\n      setNotes('')\n\n    } catch (error) {\n      console.error('خطأ في حجز الموعد:', error)\n      setMessage({ type: 'error', text: 'حدث خطأ أثناء حجز الموعد. يرجى المحاولة مرة أخرى.' })\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* زر العودة للصفحة الرئيسية */}\n        <div className=\"mb-8\">\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300 group\"\n          >\n            <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform duration-300\" />\n            <span className=\"font-medium\">العودة للصفحة الرئيسية</span>\n          </Link>\n        </div>\n\n        {/* العنوان */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-12\"\n        >\n          <h1 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\">\n              حجز موعد\n            </span>\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            احجزي موعدك بسهولة عبر نظامنا الذكي. سنقوم بتوزيع المواعيد تلقائياً على مدار أيام العمل\n          </p>\n        </motion.div>\n\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"grid lg:grid-cols-2 gap-12\">\n            {/* معلومات المواعيد */}\n            <motion.div\n              initial={{ opacity: 0, x: -50 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n              className=\"space-y-8\"\n            >\n              <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100\">\n                <h3 className=\"text-2xl font-bold text-gray-800 mb-6 flex items-center space-x-3 space-x-reverse\">\n                  <Calendar className=\"w-6 h-6 text-pink-600\" />\n                  <span>معلومات المواعيد</span>\n                </h3>\n                \n                <div className=\"space-y-6\">\n                  <div className=\"flex items-start space-x-4 space-x-reverse\">\n                    <div className=\"w-12 h-12 bg-gradient-to-br from-pink-400 to-rose-400 rounded-xl flex items-center justify-center flex-shrink-0\">\n                      <Clock className=\"w-6 h-6 text-white\" />\n                    </div>\n                    <div>\n                      <h4 className=\"font-bold text-gray-800 mb-2\">أوقات العمل</h4>\n                      <p className=\"text-gray-600 text-sm leading-relaxed\">\n                        نعمل 6 أيام في الأسبوع (عدا الجمعة)\n                      </p>\n                    </div>\n                  </div>\n                  \n\n                  \n                  <div className=\"flex items-start space-x-4 space-x-reverse\">\n                    <div className=\"w-12 h-12 bg-gradient-to-br from-rose-400 to-purple-400 rounded-xl flex items-center justify-center flex-shrink-0\">\n                      <MessageSquare className=\"w-6 h-6 text-white\" />\n                    </div>\n                    <div>\n                      <h4 className=\"font-bold text-gray-800 mb-2\">التذكيرات</h4>\n                      <p className=\"text-gray-600 text-sm leading-relaxed\">\n                        سنرسل لك تذكيراً تلقائياً<br />\n                        قبل موعدك بساعتين عبر الرسائل النصية\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* معلومات زمن التفصيل */}\n              <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100\">\n                <h3 className=\"text-2xl font-bold text-gray-800 mb-6 flex items-center space-x-3 space-x-reverse\">\n                  <Clock className=\"w-6 h-6 text-pink-600\" />\n                  <span>معلومات زمن التفصيل</span>\n                </h3>\n\n                <div className=\"space-y-6\">\n                  <div className=\"flex items-start space-x-4 space-x-reverse\">\n                    <div className=\"w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-400 rounded-xl flex items-center justify-center flex-shrink-0\">\n                      <Calendar className=\"w-6 h-6 text-white\" />\n                    </div>\n                    <div>\n                      <h4 className=\"font-bold text-gray-800 mb-2\">مدة التفصيل</h4>\n                      <p className=\"text-gray-600 text-sm leading-relaxed\">\n                        يستغرق تفصيل الفستان من <span className=\"font-semibold text-pink-600\">7 إلى 14 يوم عمل</span>\n                      </p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-start space-x-4 space-x-reverse\">\n                    <div className=\"w-12 h-12 bg-gradient-to-br from-orange-400 to-red-400 rounded-xl flex items-center justify-center flex-shrink-0\">\n                      <AlertCircle className=\"w-6 h-6 text-white\" />\n                    </div>\n                    <div>\n                      <h4 className=\"font-bold text-gray-800 mb-2\">ملاحظة مهمة</h4>\n                      <p className=\"text-gray-600 text-sm leading-relaxed\">\n                        قد تختلف مدة التفصيل في المواسم بسبب الضغط، يرجى التواصل عبر الواتساب لمزيد من المعلومات\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n\n            {/* نموذج الحجز */}\n            <motion.div\n              initial={{ opacity: 0, x: 50 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8, delay: 0.4 }}\n            >\n              <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100\">\n                <h3 className=\"text-2xl font-bold text-gray-800 mb-6\">احجزي موعدك الآن</h3>\n                \n                {message && (\n                  <motion.div\n                    initial={{ opacity: 0, y: -10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className={`mb-6 p-4 rounded-lg flex items-center space-x-3 space-x-reverse ${\n                      message.type === 'success' \n                        ? 'bg-green-50 text-green-800 border border-green-200' \n                        : 'bg-red-50 text-red-800 border border-red-200'\n                    }`}\n                  >\n                    {message.type === 'success' ? (\n                      <CheckCircle className=\"w-5 h-5 text-green-600\" />\n                    ) : (\n                      <AlertCircle className=\"w-5 h-5 text-red-600\" />\n                    )}\n                    <span>{message.text}</span>\n                  </motion.div>\n                )}\n\n                <form onSubmit={handleSubmit} className=\"space-y-6\">\n                  {/* اختيار التاريخ */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      اختاري التاريخ *\n                    </label>\n                    <select\n                      value={selectedDate}\n                      onChange={(e) => {\n                        setSelectedDate(e.target.value)\n                        setSelectedTime('') // إعادة تعيين الوقت عند تغيير التاريخ\n                      }}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300\"\n                      required\n                      disabled={!isMounted}\n                    >\n                      <option value=\"\">\n                        {isMounted ? 'اختاري التاريخ' : 'جاري تحميل التواريخ...'}\n                      </option>\n                      {getAvailableDates().map(date => (\n                        <option key={date} value={date}>\n                          {getDateDisplayText(date)}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n\n                  {/* اختيار الوقت */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      اختاري الوقت *\n                    </label>\n                    {selectedDate ? (\n                      <div className=\"grid grid-cols-2 gap-3\">\n                        {getAllTimesForDate(selectedDate).map(timeSlot => (\n                          <button\n                            key={timeSlot.time}\n                            type=\"button\"\n                            onClick={() => !timeSlot.isBooked && setSelectedTime(timeSlot.time)}\n                            disabled={timeSlot.isBooked}\n                            className={`p-3 rounded-lg border-2 transition-all duration-300 text-sm font-medium ${\n                              selectedTime === timeSlot.time\n                                ? 'border-pink-500 bg-pink-50 text-pink-700'\n                                : timeSlot.isBooked\n                                ? 'border-red-300 bg-red-100 text-red-600 cursor-not-allowed'\n                                : 'border-gray-300 bg-white text-gray-700 hover:border-pink-300 hover:bg-pink-50'\n                            }`}\n                          >\n                            <div className=\"text-center\">\n                              <div className=\"font-bold\">{timeSlot.display}</div>\n                              {timeSlot.isBooked && (\n                                <div className=\"text-xs mt-1\">محجوز</div>\n                              )}\n                            </div>\n                          </button>\n                        ))}\n                      </div>\n                    ) : (\n                      <div className=\"p-4 border-2 border-dashed border-gray-300 rounded-lg text-center text-gray-500\">\n                        يرجى اختيار التاريخ أولاً\n                      </div>\n                    )}\n                  </div>\n\n                  {/* الاسم */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      الاسم الكامل *\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={clientName}\n                      onChange={(e) => setClientName(e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300\"\n                      placeholder=\"أدخلي اسمك الكامل\"\n                      required\n                    />\n                  </div>\n\n                  {/* رقم الهاتف */}\n                  <div>\n                    <NumericInput\n                      value={clientPhone}\n                      onChange={setClientPhone}\n                      type=\"phone\"\n                      label=\"رقم الهاتف *\"\n                      placeholder=\"أدخلي رقم هاتفك\"\n                      required\n                      disabled={isSubmitting}\n                    />\n                  </div>\n\n                  {/* ملاحظات */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      ملاحظات إضافية (اختياري)\n                    </label>\n                    <textarea\n                      value={notes}\n                      onChange={(e) => setNotes(e.target.value)}\n                      rows={4}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300\"\n                      placeholder=\"أي ملاحظات أو طلبات خاصة...\"\n                    />\n                  </div>\n\n                  {/* زر الإرسال */}\n                  <button\n                    type=\"submit\"\n                    disabled={isSubmitting}\n                    className=\"w-full btn-primary py-4 text-lg disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    {isSubmitting ? (\n                      <div className=\"flex items-center justify-center space-x-2 space-x-reverse\">\n                        <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                        <span>جاري الحجز...</span>\n                      </div>\n                    ) : (\n                      <div className=\"flex items-center justify-center space-x-2 space-x-reverse\">\n                        <Calendar className=\"w-5 h-5\" />\n                        <span>احجزي الموعد</span>\n                      </div>\n                    )}\n                  </button>\n                </form>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsD;IAE3F,2CAA2C;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAE/E,MAAM,EAAE,cAAc,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAEpD,0DAA0D;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;QAEb,gCAAgC;QAChC,MAAM,QAAQ;QACd,MAAM,YAAqC,CAAC;QAE5C,MAAM,OAAO,CAAC,CAAA;YACZ,MAAM,OAAO,IAAI,KAAK;YAEtB,uBAAuB;YACvB,MAAM,mBAA+C;gBACnD,SAAS;gBACT,MAAM;gBACN,OAAO;gBACP,KAAK;YACP;YACA,MAAM,gBAAgB,KAAK,kBAAkB,CAAC,SAAS;YAEvD,SAAS,CAAC,WAAW,GAAG;QAC1B;QAEA,kBAAkB;IACpB,GAAG,EAAE;IAEL,kEAAkE;IAClE,MAAM,oBAAoB;QACxB,MAAM,QAAQ,EAAE;QAChB,MAAM,QAAQ,IAAI;QAElB,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAK;YAC5B,MAAM,OAAO,IAAI,KAAK;YACtB,KAAK,OAAO,CAAC,MAAM,OAAO,KAAK;YAE/B,uBAAuB;YACvB,IAAI,KAAK,MAAM,OAAO,GAAG;YAEzB,MAAM,IAAI,CAAC,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC7C;QAEA,OAAO;IACT;IAEA,mCAAmC;IACnC,MAAM,qBAAqB,CAAC;QAC1B,MAAM,WAAW;YACf;gBAAE,MAAM;gBAAS,SAAS;YAAO;YACjC;gBAAE,MAAM;gBAAS,SAAS;YAAO;YACjC;gBAAE,MAAM;gBAAS,SAAS;YAAO;YACjC;gBAAE,MAAM;gBAAS,SAAS;YAAO;YACjC;gBAAE,MAAM;gBAAS,SAAS;YAAO;YACjC;gBAAE,MAAM;gBAAS,SAAS;YAAO;YACjC;gBAAE,MAAM;gBAAS,SAAS;YAAO;SAClC;QAED,wCAAwC;QACxC,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACpD,MAAM,UAAU,SAAS;QAEzB,kDAAkD;QAClD,IAAI,iBAAiB;QACrB,IAAI,SAAS;YACX,MAAM,MAAM,IAAI;YAChB,MAAM,cAAc,IAAI,QAAQ;YAChC,MAAM,gBAAgB,IAAI,UAAU;YACpC,MAAM,uBAAuB,cAAc,KAAK;YAEhD,iBAAiB,SAAS,MAAM,CAAC,CAAA;gBAC/B,MAAM,CAAC,OAAO,QAAQ,GAAG,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;gBACtD,MAAM,oBAAoB,QAAQ,KAAK;gBACvC,uCAAuC;gBACvC,OAAO,oBAAoB,uBAAuB;YACpD;QACF;QAEA,8BAA8B;QAC9B,MAAM,cAAc,aACjB,MAAM,CAAC,CAAA,cACN,YAAY,eAAe,KAAK,QAChC,YAAY,MAAM,KAAK,aAExB,GAAG,CAAC,CAAA,cAAe,YAAY,eAAe;QAEjD,OAAO,eAAe,GAAG,CAAC,CAAA,WAAY,CAAC;gBACrC,GAAG,QAAQ;gBACX,UAAU,YAAY,QAAQ,CAAC,SAAS,IAAI;YAC9C,CAAC;IACH;IAEA,uCAAuC;IACvC,MAAM,qBAAqB,CAAC;QAC1B,IAAI,CAAC,WAAW;YACd,OAAO;QACT;QACA,OAAO,cAAc,CAAC,WAAW,IAAI;IACvC;IAEA,oBAAoB;IACpB,MAAM,uBAAuB,CAAC;QAC5B,MAAM,CAAC,OAAO,QAAQ,GAAG,WAAW,KAAK,CAAC;QAC1C,MAAM,OAAO,SAAS;QACtB,MAAM,OAAO,QAAQ,KAAK,MAAM;QAChC,MAAM,cAAc,OAAO,KAAK,OAAO,KAAK,SAAS,IAAI,KAAK;QAC9D,OAAO,GAAG,YAAY,CAAC,EAAE,QAAQ,CAAC,EAAE,MAAM;IAC5C;IAEA,kBAAkB;IAClB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,cAAc,CAAC,aAAa;YACjE,WAAW;gBAAE,MAAM;gBAAS,MAAM;YAAgC;YAClE;QACF;QAEA,gBAAgB;QAChB,WAAW;QAEX,IAAI;YACF,sBAAsB;YACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,0BAA0B;YAC1B,eAAe;gBACb;gBACA;gBACA,iBAAiB;gBACjB,iBAAiB;gBACjB,OAAO,SAAS;gBAChB,QAAQ;YACV;YAEA,WAAW;gBACT,MAAM;gBACN,MAAM;YACR;YAEA,sBAAsB;YACtB,gBAAgB;YAChB,gBAAgB;YAChB,cAAc;YACd,eAAe;YACf,SAAS;QAEX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,WAAW;gBAAE,MAAM;gBAAS,MAAM;YAAoD;QACxF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;gCAAK,WAAU;0CAAc;;;;;;;;;;;;;;;;;8BAKlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAA6E;;;;;;;;;;;sCAI/F,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAKzE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;kEAAK;;;;;;;;;;;;0DAGR,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;0EAEnB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA+B;;;;;;kFAC7C,8OAAC;wEAAE,WAAU;kFAAwC;;;;;;;;;;;;;;;;;;kEAQzD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;;;;;;0EAE3B,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA+B;;;;;;kFAC7C,8OAAC;wEAAE,WAAU;;4EAAwC;0FAC1B,8OAAC;;;;;4EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDASzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;kEAAK;;;;;;;;;;;;0DAGR,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAEtB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA+B;;;;;;kFAC7C,8OAAC;wEAAE,WAAU;;4EAAwC;0FAC3B,8OAAC;gFAAK,WAAU;0FAA8B;;;;;;;;;;;;;;;;;;;;;;;;kEAK5E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;0EAEzB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA+B;;;;;;kFAC7C,8OAAC;wEAAE,WAAU;kFAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAU/D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CAExC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;wCAErD,yBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,WAAW,CAAC,gEAAgE,EAC1E,QAAQ,IAAI,KAAK,YACb,uDACA,gDACJ;;gDAED,QAAQ,IAAI,KAAK,0BAChB,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;yEAEvB,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DAEzB,8OAAC;8DAAM,QAAQ,IAAI;;;;;;;;;;;;sDAIvB,8OAAC;4CAAK,UAAU;4CAAc,WAAU;;8DAEtC,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,OAAO;4DACP,UAAU,CAAC;gEACT,gBAAgB,EAAE,MAAM,CAAC,KAAK;gEAC9B,gBAAgB,IAAI,sCAAsC;;4DAC5D;4DACA,WAAU;4DACV,QAAQ;4DACR,UAAU,CAAC;;8EAEX,8OAAC;oEAAO,OAAM;8EACX,YAAY,mBAAmB;;;;;;gEAEjC,oBAAoB,GAAG,CAAC,CAAA,qBACvB,8OAAC;wEAAkB,OAAO;kFACvB,mBAAmB;uEADT;;;;;;;;;;;;;;;;;8DAQnB,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;wDAG/D,6BACC,8OAAC;4DAAI,WAAU;sEACZ,mBAAmB,cAAc,GAAG,CAAC,CAAA,yBACpC,8OAAC;oEAEC,MAAK;oEACL,SAAS,IAAM,CAAC,SAAS,QAAQ,IAAI,gBAAgB,SAAS,IAAI;oEAClE,UAAU,SAAS,QAAQ;oEAC3B,WAAW,CAAC,wEAAwE,EAClF,iBAAiB,SAAS,IAAI,GAC1B,6CACA,SAAS,QAAQ,GACjB,8DACA,iFACJ;8EAEF,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FAAa,SAAS,OAAO;;;;;;4EAC3C,SAAS,QAAQ,kBAChB,8OAAC;gFAAI,WAAU;0FAAe;;;;;;;;;;;;mEAf7B,SAAS,IAAI;;;;;;;;;iFAsBxB,8OAAC;4DAAI,WAAU;sEAAkF;;;;;;;;;;;;8DAOrG,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAKZ,8OAAC;8DACC,cAAA,8OAAC,kIAAA,CAAA,UAAY;wDACX,OAAO;wDACP,UAAU;wDACV,MAAK;wDACL,OAAM;wDACN,aAAY;wDACZ,QAAQ;wDACR,UAAU;;;;;;;;;;;8DAKd,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4DACxC,MAAM;4DACN,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAKhB,8OAAC;oDACC,MAAK;oDACL,UAAU;oDACV,WAAU;8DAET,6BACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;6EAGR,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY9B", "debugId": null}}]}