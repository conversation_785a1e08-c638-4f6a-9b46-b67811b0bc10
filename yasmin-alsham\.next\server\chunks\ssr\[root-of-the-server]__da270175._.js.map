{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/store/authStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\n\n// تعريف نوع المستخدم\nexport interface AuthUser {\n  id: string\n  email: string\n  full_name: string\n  role: 'admin' | 'worker'\n  is_active: boolean\n  created_at: string\n  updated_at: string\n  token?: string\n}\n\ninterface AuthState {\n  user: AuthUser | null\n  isLoading: boolean\n  error: string | null\n\n  // Actions\n  signIn: (email: string, password: string) => Promise<boolean>\n  signOut: () => Promise<void>\n  setUser: (user: AuthUser | null) => void\n  clearError: () => void\n  checkAuth: () => Promise<void>\n  isAuthenticated: () => boolean\n}\n\n// بيانات المستخدمين الافتراضية (سيتم استبدالها بنظام إدارة العمال)\nconst getStoredUsers = () => {\n  if (typeof window === 'undefined') return []\n\n  const stored = localStorage.getItem('yasmin-users')\n  if (stored) {\n    return JSON.parse(stored)\n  }\n\n  // المستخدمين الافتراضيين\n  const defaultUsers = [\n    {\n      id: '1',\n      email: '<EMAIL>',\n      password: 'admin123',\n      full_name: 'مدير النظام',\n      role: 'admin' as const,\n      is_active: true\n    }\n  ]\n\n  localStorage.setItem('yasmin-users', JSON.stringify(defaultUsers))\n  return defaultUsers\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      isLoading: false,\n      error: null,\n\n      signIn: async (email: string, password: string) => {\n        set({ isLoading: true, error: null })\n\n        try {\n          console.log('🔐 بدء عملية تسجيل الدخول...', { email })\n\n          // محاكاة تأخير الشبكة\n          await new Promise(resolve => setTimeout(resolve, 1500))\n\n          // البحث عن المستخدم في البيانات المحفوظة\n          const users = getStoredUsers()\n          const foundUser = users.find(\n            user => user.email.toLowerCase() === email.toLowerCase() && user.password === password\n          )\n\n          if (foundUser) {\n            console.log('✅ تم العثور على المستخدم:', foundUser.full_name)\n\n            const user: AuthUser = {\n              id: foundUser.id,\n              email: foundUser.email,\n              full_name: foundUser.full_name,\n              role: foundUser.role,\n              is_active: foundUser.is_active,\n              created_at: new Date().toISOString(),\n              updated_at: new Date().toISOString(),\n              token: `demo-token-${foundUser.id}-${Date.now()}`\n            }\n\n            // حفظ في localStorage أولاً\n            if (typeof window !== 'undefined') {\n              localStorage.setItem('yasmin-auth-user', JSON.stringify(user))\n              console.log('💾 تم حفظ المستخدم في localStorage')\n            }\n\n            // تحديث حالة المتجر\n            set({ user, isLoading: false, error: null })\n            console.log('🎉 تم تسجيل الدخول بنجاح!')\n\n            return true\n          } else {\n            console.log('❌ بيانات تسجيل الدخول غير صحيحة')\n            set({\n              error: 'بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور.',\n              isLoading: false\n            })\n            return false\n          }\n        } catch (error) {\n          console.error('💥 خطأ في تسجيل الدخول:', error)\n          set({ error: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.', isLoading: false })\n          return false\n        }\n      },\n\n      signOut: async () => {\n        set({ isLoading: true })\n\n        try {\n          // محاكاة تأخير تسجيل الخروج\n          await new Promise(resolve => setTimeout(resolve, 500))\n\n          // مسح البيانات من localStorage\n          if (typeof window !== 'undefined') {\n            localStorage.removeItem('yasmin-auth-user')\n          }\n\n          set({ user: null, isLoading: false, error: null })\n        } catch (error) {\n          console.error('خطأ في تسجيل الخروج:', error)\n          set({ isLoading: false, error: 'خطأ في تسجيل الخروج' })\n        }\n      },\n\n      setUser: (user: AuthUser | null) => {\n        set({ user })\n\n        // تحديث localStorage\n        if (typeof window !== 'undefined') {\n          if (user) {\n            localStorage.setItem('yasmin-auth-user', JSON.stringify(user))\n          } else {\n            localStorage.removeItem('yasmin-auth-user')\n          }\n        }\n      },\n\n      clearError: () => {\n        set({ error: null })\n      },\n\n      checkAuth: async () => {\n        set({ isLoading: true })\n\n        try {\n          // التحقق من وجود مستخدم محفوظ في localStorage\n          if (typeof window !== 'undefined') {\n            const savedUser = localStorage.getItem('yasmin-auth-user')\n            if (savedUser) {\n              const user = JSON.parse(savedUser) as AuthUser\n              set({ user, isLoading: false })\n              return\n            }\n          }\n\n          set({ user: null, isLoading: false })\n        } catch (error) {\n          console.error('خطأ في التحقق من المصادقة:', error)\n          set({ user: null, isLoading: false })\n        }\n      },\n\n      isAuthenticated: () => {\n        const state = get()\n        return state.user !== null && state.user.is_active\n      }\n    }),\n    {\n      name: 'yasmin-auth-storage',\n      partialize: (state) => ({ user: state.user })\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA4BA,mEAAmE;AACnE,MAAM,iBAAiB;IACrB,wCAAmC,OAAO,EAAE;;IAE5C,MAAM;IAKN,yBAAyB;IACzB,MAAM;AAaR;AAEO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,WAAW;QACX,OAAO;QAEP,QAAQ,OAAO,OAAe;YAC5B,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,QAAQ,GAAG,CAAC,gCAAgC;oBAAE;gBAAM;gBAEpD,sBAAsB;gBACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,yCAAyC;gBACzC,MAAM,QAAQ;gBACd,MAAM,YAAY,MAAM,IAAI,CAC1B,CAAA,OAAQ,KAAK,KAAK,CAAC,WAAW,OAAO,MAAM,WAAW,MAAM,KAAK,QAAQ,KAAK;gBAGhF,IAAI,WAAW;oBACb,QAAQ,GAAG,CAAC,6BAA6B,UAAU,SAAS;oBAE5D,MAAM,OAAiB;wBACrB,IAAI,UAAU,EAAE;wBAChB,OAAO,UAAU,KAAK;wBACtB,WAAW,UAAU,SAAS;wBAC9B,MAAM,UAAU,IAAI;wBACpB,WAAW,UAAU,SAAS;wBAC9B,YAAY,IAAI,OAAO,WAAW;wBAClC,YAAY,IAAI,OAAO,WAAW;wBAClC,OAAO,CAAC,WAAW,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;oBACnD;oBAEA,4BAA4B;oBAC5B,uCAAmC;;oBAGnC;oBAEA,oBAAoB;oBACpB,IAAI;wBAAE;wBAAM,WAAW;wBAAO,OAAO;oBAAK;oBAC1C,QAAQ,GAAG,CAAC;oBAEZ,OAAO;gBACT,OAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ,IAAI;wBACF,OAAO;wBACP,WAAW;oBACb;oBACA,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,IAAI;oBAAE,OAAO;oBAA8C,WAAW;gBAAM;gBAC5E,OAAO;YACT;QACF;QAEA,SAAS;YACP,IAAI;gBAAE,WAAW;YAAK;YAEtB,IAAI;gBACF,4BAA4B;gBAC5B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,+BAA+B;gBAC/B,uCAAmC;;gBAEnC;gBAEA,IAAI;oBAAE,MAAM;oBAAM,WAAW;oBAAO,OAAO;gBAAK;YAClD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,IAAI;oBAAE,WAAW;oBAAO,OAAO;gBAAsB;YACvD;QACF;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;YAAK;YAEX,qBAAqB;YACrB,uCAAmC;;YAMnC;QACF;QAEA,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,WAAW;YACT,IAAI;gBAAE,WAAW;YAAK;YAEtB,IAAI;gBACF,8CAA8C;gBAC9C,uCAAmC;;gBAOnC;gBAEA,IAAI;oBAAE,MAAM;oBAAM,WAAW;gBAAM;YACrC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,IAAI;oBAAE,MAAM;oBAAM,WAAW;gBAAM;YACrC;QACF;QAEA,iBAAiB;YACf,MAAM,QAAQ;YACd,OAAO,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,CAAC,SAAS;QACpD;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YAAE,MAAM,MAAM,IAAI;QAAC,CAAC;AAC9C", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/store/dataStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\n\n// تعريف أنواع البيانات\nexport interface Appointment {\n  id: string\n  clientName: string\n  clientPhone: string\n  appointmentDate: string\n  appointmentTime: string\n  notes?: string\n  status: 'pending' | 'confirmed' | 'completed' | 'cancelled'\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Order {\n  id: string\n  clientName: string\n  clientPhone: string\n  description: string\n  fabric: string\n  measurements: {\n    // المقاسات الأساسية\n    shoulder?: number // الكتف\n    shoulderCircumference?: number // دوران الكتف\n    chest?: number // الصدر\n    waist?: number // الخصر\n    hips?: number // الأرداف\n\n    // مقاسات التفصيل المتقدمة\n    dartLength?: number // طول البنس\n    bodiceLength?: number // طول الصدرية\n    neckline?: number // فتحة الصدر\n    armpit?: number // الإبط\n\n    // مقاسات الأكمام\n    sleeveLength?: number // طول الكم\n    forearm?: number // الزند\n    cuff?: number // الأسوارة\n\n    // مقاسات الطول\n    frontLength?: number // طول الأمام\n    backLength?: number // طول الخلف\n\n    // للتوافق مع النظام القديم (سيتم إزالتها لاحقاً)\n    length?: number // طول الفستان (قديم)\n    shoulders?: number // عرض الكتف (قديم)\n    sleeves?: number // طول الأكمام (قديم)\n  }\n  price: number\n  status: 'pending' | 'in_progress' | 'completed' | 'delivered' | 'cancelled'\n  assignedWorker?: string\n  dueDate: string\n  notes?: string\n  voiceNotes?: Array<{\n    id: string\n    data: string\n    timestamp: number\n    duration?: number\n  }> // ملاحظات صوتية متعددة\n  images?: string[] // مصفوفة من base64 strings للصور\n  completedImages?: string[] // صور العمل المكتمل (للعمال فقط)\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Worker {\n  id: string\n  email: string\n  password: string\n  full_name: string\n  phone: string\n  specialty: string\n  role: 'worker'\n  is_active: boolean\n  createdAt: string\n  updatedAt: string\n}\n\ninterface DataState {\n  // البيانات\n  appointments: Appointment[]\n  orders: Order[]\n  workers: Worker[]\n  \n  // حالة التحميل\n  isLoading: boolean\n  error: string | null\n\n  // إدارة المواعيد\n  addAppointment: (appointment: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>) => void\n  updateAppointment: (id: string, updates: Partial<Appointment>) => void\n  deleteAppointment: (id: string) => void\n  getAppointment: (id: string) => Appointment | undefined\n\n  // إدارة الطلبات\n  addOrder: (order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) => void\n  updateOrder: (id: string, updates: Partial<Order>) => void\n  deleteOrder: (id: string) => void\n  getOrder: (id: string) => Order | undefined\n\n  // دوال خاصة للعمال\n  startOrderWork: (orderId: string, workerId: string) => void\n  completeOrder: (orderId: string, workerId: string, completedImages?: string[]) => void\n\n  // إدارة العمال\n  addWorker: (worker: Omit<Worker, 'id' | 'createdAt' | 'updatedAt' | 'role'>) => void\n  updateWorker: (id: string, updates: Partial<Worker>) => void\n  deleteWorker: (id: string) => void\n  getWorker: (id: string) => Worker | undefined\n\n  // وظائف مساعدة\n  clearError: () => void\n  loadData: () => void\n  \n  // إحصائيات\n  getStats: () => {\n    totalAppointments: number\n    totalOrders: number\n    totalWorkers: number\n    pendingAppointments: number\n    activeOrders: number\n    completedOrders: number\n    totalRevenue: number\n  }\n}\n\n// توليد ID فريد\nconst generateId = () => {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2)\n}\n\nexport const useDataStore = create<DataState>()(\n  persist(\n    (set, get) => ({\n      // البيانات الأولية\n      appointments: [],\n      orders: [],\n      workers: [],\n      isLoading: false,\n      error: null,\n\n      // إدارة المواعيد\n      addAppointment: (appointmentData) => {\n        const appointment: Appointment = {\n          ...appointmentData,\n          id: generateId(),\n          status: 'pending',\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        }\n\n        set((state) => ({\n          appointments: [...state.appointments, appointment],\n          error: null\n        }))\n\n        console.log('✅ تم إضافة موعد جديد:', appointment)\n      },\n\n      updateAppointment: (id, updates) => {\n        set((state) => ({\n          appointments: state.appointments.map(appointment =>\n            appointment.id === id\n              ? { ...appointment, ...updates, updatedAt: new Date().toISOString() }\n              : appointment\n          ),\n          error: null\n        }))\n\n        console.log('✅ تم تحديث الموعد:', id)\n      },\n\n      deleteAppointment: (id) => {\n        set((state) => ({\n          appointments: state.appointments.filter(appointment => appointment.id !== id),\n          error: null\n        }))\n\n        console.log('✅ تم حذف الموعد:', id)\n      },\n\n      getAppointment: (id) => {\n        const state = get()\n        return state.appointments.find(appointment => appointment.id === id)\n      },\n\n      // إدارة الطلبات\n      addOrder: (orderData) => {\n        const order: Order = {\n          ...orderData,\n          id: generateId(),\n          status: 'pending',\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        }\n\n        set((state) => ({\n          orders: [...state.orders, order],\n          error: null\n        }))\n\n        console.log('✅ تم إضافة طلب جديد:', order)\n      },\n\n      updateOrder: (id, updates) => {\n        set((state) => ({\n          orders: state.orders.map(order =>\n            order.id === id\n              ? { ...order, ...updates, updatedAt: new Date().toISOString() }\n              : order\n          ),\n          error: null\n        }))\n\n        console.log('✅ تم تحديث الطلب:', id)\n      },\n\n      deleteOrder: (id) => {\n        set((state) => ({\n          orders: state.orders.filter(order => order.id !== id),\n          error: null\n        }))\n\n        console.log('✅ تم حذف الطلب:', id)\n      },\n\n      getOrder: (id) => {\n        const state = get()\n        return state.orders.find(order => order.id === id)\n      },\n\n      // إدارة العمال\n      addWorker: (workerData) => {\n        const worker: Worker = {\n          ...workerData,\n          id: generateId(),\n          role: 'worker',\n          is_active: true,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        }\n\n        set((state) => ({\n          workers: [...state.workers, worker],\n          error: null\n        }))\n\n        // إضافة العامل إلى نظام المصادقة\n        if (typeof window !== 'undefined') {\n          const users = JSON.parse(localStorage.getItem('yasmin-users') || '[]')\n          users.push({\n            id: worker.id,\n            email: worker.email,\n            password: worker.password,\n            full_name: worker.full_name,\n            role: 'worker',\n            is_active: true\n          })\n          localStorage.setItem('yasmin-users', JSON.stringify(users))\n        }\n\n        console.log('✅ تم إضافة عامل جديد:', worker)\n      },\n\n      updateWorker: (id, updates) => {\n        set((state) => ({\n          workers: state.workers.map(worker =>\n            worker.id === id\n              ? { ...worker, ...updates, updatedAt: new Date().toISOString() }\n              : worker\n          ),\n          error: null\n        }))\n\n        // تحديث بيانات المصادقة إذا تم تغيير البريد أو كلمة المرور\n        if (updates.email || updates.password || updates.full_name) {\n          if (typeof window !== 'undefined') {\n            const users = JSON.parse(localStorage.getItem('yasmin-users') || '[]')\n            const userIndex = users.findIndex((user: any) => user.id === id)\n            if (userIndex !== -1) {\n              if (updates.email) users[userIndex].email = updates.email\n              if (updates.password) users[userIndex].password = updates.password\n              if (updates.full_name) users[userIndex].full_name = updates.full_name\n              localStorage.setItem('yasmin-users', JSON.stringify(users))\n            }\n          }\n        }\n\n        console.log('✅ تم تحديث العامل:', id)\n      },\n\n      deleteWorker: (id) => {\n        set((state) => ({\n          workers: state.workers.filter(worker => worker.id !== id),\n          error: null\n        }))\n\n        // حذف من نظام المصادقة\n        if (typeof window !== 'undefined') {\n          const users = JSON.parse(localStorage.getItem('yasmin-users') || '[]')\n          const filteredUsers = users.filter((user: any) => user.id !== id)\n          localStorage.setItem('yasmin-users', JSON.stringify(filteredUsers))\n        }\n\n        console.log('✅ تم حذف العامل:', id)\n      },\n\n      getWorker: (id) => {\n        const state = get()\n        return state.workers.find(worker => worker.id === id)\n      },\n\n      // دوال خاصة للعمال\n      startOrderWork: (orderId, workerId) => {\n        set((state) => ({\n          orders: state.orders.map(order =>\n            order.id === orderId && order.assignedWorker === workerId\n              ? { ...order, status: 'in_progress', updatedAt: new Date().toISOString() }\n              : order\n          ),\n          error: null\n        }))\n\n        console.log('✅ تم بدء العمل في الطلب:', orderId)\n      },\n\n      completeOrder: (orderId, workerId, completedImages = []) => {\n        set((state) => ({\n          orders: state.orders.map(order =>\n            order.id === orderId && order.assignedWorker === workerId\n              ? {\n                  ...order,\n                  status: 'completed',\n                  completedImages: completedImages.length > 0 ? completedImages : undefined,\n                  updatedAt: new Date().toISOString()\n                }\n              : order\n          ),\n          error: null\n        }))\n\n        console.log('✅ تم إنهاء الطلب:', orderId)\n      },\n\n      // وظائف مساعدة\n      clearError: () => {\n        set({ error: null })\n      },\n\n      loadData: () => {\n        set({ isLoading: true })\n        // البيانات محفوظة تلقائياً بواسطة persist middleware\n        set({ isLoading: false })\n      },\n\n      // إحصائيات\n      getStats: () => {\n        const state = get()\n        return {\n          totalAppointments: state.appointments.length,\n          totalOrders: state.orders.length,\n          totalWorkers: state.workers.length,\n          pendingAppointments: state.appointments.filter(a => a.status === 'pending').length,\n          activeOrders: state.orders.filter(o => ['pending', 'in_progress'].includes(o.status)).length,\n          completedOrders: state.orders.filter(o => o.status === 'completed').length,\n          totalRevenue: state.orders\n            .filter(o => o.status === 'completed')\n            .reduce((sum, order) => sum + order.price, 0)\n        }\n      }\n    }),\n    {\n      name: 'yasmin-data-storage',\n      partialize: (state) => ({\n        appointments: state.appointments,\n        orders: state.orders,\n        workers: state.workers\n      })\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA+HA,gBAAgB;AAChB,MAAM,aAAa;IACjB,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;AACrE;AAEO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,mBAAmB;QACnB,cAAc,EAAE;QAChB,QAAQ,EAAE;QACV,SAAS,EAAE;QACX,WAAW;QACX,OAAO;QAEP,iBAAiB;QACjB,gBAAgB,CAAC;YACf,MAAM,cAA2B;gBAC/B,GAAG,eAAe;gBAClB,IAAI;gBACJ,QAAQ;gBACR,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,cAAc;2BAAI,MAAM,YAAY;wBAAE;qBAAY;oBAClD,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,yBAAyB;QACvC;QAEA,mBAAmB,CAAC,IAAI;YACtB,IAAI,CAAC,QAAU,CAAC;oBACd,cAAc,MAAM,YAAY,CAAC,GAAG,CAAC,CAAA,cACnC,YAAY,EAAE,KAAK,KACf;4BAAE,GAAG,WAAW;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI,OAAO,WAAW;wBAAG,IAClE;oBAEN,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,sBAAsB;QACpC;QAEA,mBAAmB,CAAC;YAClB,IAAI,CAAC,QAAU,CAAC;oBACd,cAAc,MAAM,YAAY,CAAC,MAAM,CAAC,CAAA,cAAe,YAAY,EAAE,KAAK;oBAC1E,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,oBAAoB;QAClC;QAEA,gBAAgB,CAAC;YACf,MAAM,QAAQ;YACd,OAAO,MAAM,YAAY,CAAC,IAAI,CAAC,CAAA,cAAe,YAAY,EAAE,KAAK;QACnE;QAEA,gBAAgB;QAChB,UAAU,CAAC;YACT,MAAM,QAAe;gBACnB,GAAG,SAAS;gBACZ,IAAI;gBACJ,QAAQ;gBACR,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ;2BAAI,MAAM,MAAM;wBAAE;qBAAM;oBAChC,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,wBAAwB;QACtC;QAEA,aAAa,CAAC,IAAI;YAChB,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,QACvB,MAAM,EAAE,KAAK,KACT;4BAAE,GAAG,KAAK;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI,OAAO,WAAW;wBAAG,IAC5D;oBAEN,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,qBAAqB;QACnC;QAEA,aAAa,CAAC;YACZ,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;oBAClD,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,mBAAmB;QACjC;QAEA,UAAU,CAAC;YACT,MAAM,QAAQ;YACd,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACjD;QAEA,eAAe;QACf,WAAW,CAAC;YACV,MAAM,SAAiB;gBACrB,GAAG,UAAU;gBACb,IAAI;gBACJ,MAAM;gBACN,WAAW;gBACX,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,SAAS;2BAAI,MAAM,OAAO;wBAAE;qBAAO;oBACnC,OAAO;gBACT,CAAC;YAED,iCAAiC;YACjC,uCAAmC;;YAWnC;YAEA,QAAQ,GAAG,CAAC,yBAAyB;QACvC;QAEA,cAAc,CAAC,IAAI;YACjB,IAAI,CAAC,QAAU,CAAC;oBACd,SAAS,MAAM,OAAO,CAAC,GAAG,CAAC,CAAA,SACzB,OAAO,EAAE,KAAK,KACV;4BAAE,GAAG,MAAM;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI,OAAO,WAAW;wBAAG,IAC7D;oBAEN,OAAO;gBACT,CAAC;YAED,2DAA2D;YAC3D,IAAI,QAAQ,KAAK,IAAI,QAAQ,QAAQ,IAAI,QAAQ,SAAS,EAAE;gBAC1D,uCAAmC;;gBASnC;YACF;YAEA,QAAQ,GAAG,CAAC,sBAAsB;QACpC;QAEA,cAAc,CAAC;YACb,IAAI,CAAC,QAAU,CAAC;oBACd,SAAS,MAAM,OAAO,CAAC,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;oBACtD,OAAO;gBACT,CAAC;YAED,uBAAuB;YACvB,uCAAmC;;YAInC;YAEA,QAAQ,GAAG,CAAC,oBAAoB;QAClC;QAEA,WAAW,CAAC;YACV,MAAM,QAAQ;YACd,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;QACpD;QAEA,mBAAmB;QACnB,gBAAgB,CAAC,SAAS;YACxB,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,QACvB,MAAM,EAAE,KAAK,WAAW,MAAM,cAAc,KAAK,WAC7C;4BAAE,GAAG,KAAK;4BAAE,QAAQ;4BAAe,WAAW,IAAI,OAAO,WAAW;wBAAG,IACvE;oBAEN,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,4BAA4B;QAC1C;QAEA,eAAe,CAAC,SAAS,UAAU,kBAAkB,EAAE;YACrD,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,QACvB,MAAM,EAAE,KAAK,WAAW,MAAM,cAAc,KAAK,WAC7C;4BACE,GAAG,KAAK;4BACR,QAAQ;4BACR,iBAAiB,gBAAgB,MAAM,GAAG,IAAI,kBAAkB;4BAChE,WAAW,IAAI,OAAO,WAAW;wBACnC,IACA;oBAEN,OAAO;gBACT,CAAC;YAED,QAAQ,GAAG,CAAC,qBAAqB;QACnC;QAEA,eAAe;QACf,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,UAAU;YACR,IAAI;gBAAE,WAAW;YAAK;YACtB,qDAAqD;YACrD,IAAI;gBAAE,WAAW;YAAM;QACzB;QAEA,WAAW;QACX,UAAU;YACR,MAAM,QAAQ;YACd,OAAO;gBACL,mBAAmB,MAAM,YAAY,CAAC,MAAM;gBAC5C,aAAa,MAAM,MAAM,CAAC,MAAM;gBAChC,cAAc,MAAM,OAAO,CAAC,MAAM;gBAClC,qBAAqB,MAAM,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;gBAClF,cAAc,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK;wBAAC;wBAAW;qBAAc,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM;gBAC5F,iBAAiB,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;gBAC1E,cAAc,MAAM,MAAM,CACvB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aACzB,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,KAAK,EAAE;YAC/C;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,cAAc,MAAM,YAAY;YAChC,QAAQ,MAAM,MAAM;YACpB,SAAS,MAAM,OAAO;QACxB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/hooks/useTranslation.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\n// نوع البيانات للترجمات\ntype TranslationKey = string\ntype TranslationValue = string | { [key: string]: string }\ntype Translations = { [key: string]: TranslationValue }\n\n// الترجمات العربية\nconst arTranslations: Translations = {\n  // التنقل والعناوين الرئيسية\n  'dashboard': 'لوحة التحكم',\n  'orders': 'الطلبات',\n  'appointments': 'المواعيد',\n  'settings': 'الإعدادات',\n  'logout': 'تسجيل الخروج',\n  'profile': 'الملف الشخصي',\n  'notifications': 'الإشعارات',\n  \n  // الأزرار والإجراءات\n  'add_new_order': 'إضافة طلب جديد',\n  'book_appointment': 'حجز موعد',\n  'view_details': 'عرض التفاصيل',\n  'edit': 'تعديل',\n  'delete': 'حذف',\n  'save': 'حفظ',\n  'cancel': 'إلغاء',\n  'submit': 'إرسال',\n  'search': 'بحث',\n  'filter': 'تصفية',\n  'export': 'تصدير',\n  'print': 'طباعة',\n  \n  // الحالات\n  'pending': 'في الانتظار',\n  'in_progress': 'قيد التنفيذ',\n  'completed': 'مكتمل',\n  'delivered': 'تم التسليم',\n  'cancelled': 'ملغي',\n  \n  // الإحصائيات\n  'active_orders': 'الطلبات النشطة',\n  'completed_orders': 'الطلبات المكتملة',\n  'total_orders': 'إجمالي الطلبات',\n  'my_active_orders': 'طلباتي النشطة',\n  'my_completed_orders': 'طلباتي المكتملة',\n  'my_total_orders': 'إجمالي طلباتي',\n  'today_appointments': 'مواعيد اليوم',\n  \n  // الأدوار\n  'admin': 'مدير',\n  'worker': 'عامل',\n  \n  // الرسائل\n  'welcome_back': 'مرحباً بعودتك',\n  'overview_today': 'إليك نظرة عامة على أنشطة اليوم',\n  'no_orders_found': 'لا توجد طلبات',\n  'no_appointments_found': 'لا توجد مواعيد',\n  'loading': 'جاري التحميل...',\n  'error': 'خطأ',\n  'success': 'نجح',\n  \n  // أقسام لوحة التحكم\n  'recent_orders': 'الطلبات الحديثة',\n  'quick_actions': 'الإجراءات السريعة',\n  'statistics': 'الإحصائيات',\n  'recent_activity': 'النشاط الحديث',\n  \n  // تفاصيل الطلب\n  'order_details': 'تفاصيل الطلب',\n  'customer_info': 'معلومات الزبون',\n  'order_info': 'معلومات الطلب',\n  'measurements': 'المقاسات',\n  'images': 'الصور',\n  'notes': 'الملاحظات',\n  'voice_notes': 'ملاحظات صوتية',\n  'voice_notes_optional': 'ملاحظات صوتية (اختياري)',\n  \n  // التواريخ والأوقات\n  'order_date': 'تاريخ الطلب',\n  'delivery_date': 'موعد التسليم',\n  'created_at': 'تاريخ الإنشاء',\n  'updated_at': 'تاريخ التحديث',\n  \n  // أخرى\n  'language': 'اللغة',\n  'arabic': 'العربية',\n  'english': 'English',\n  'change_language': 'تغيير اللغة',\n  'next_language': 'اللغة التالية',\n\n  // نصوص رفع الصور\n  'click_or_drag_images': 'انقر أو اسحب الصور هنا',\n  'drop_images_here': 'اتركها هنا...',\n  'image_upload_format': 'PNG, JPG, GIF حتى 10MB لكل صورة',\n  'max_images_text': 'حد أقصى',\n  'images_text': 'صور',\n  'of': 'من',\n  'add_image': 'إضافة صورة',\n  'max_images_reached': 'تم الوصول للحد الأقصى من الصور',\n\n  // نصوص الملاحظات الصوتية\n  'start_recording': 'بدء التسجيل',\n  'stop_recording': 'إيقاف التسجيل',\n  'click_to_record_voice_note': 'اضغطي لتسجيل ملاحظة صوتية',\n  'voice_note': 'ملاحظة صوتية',\n  'microphone_access_error': 'لا يمكن الوصول إلى الميكروفون. يرجى التأكد من الأذونات.',\n\n  // Delete Order Modal\n  'confirm_delete_order': 'تأكيد حذف الطلب',\n  'warning_delete_order': 'تحذير: حذف الطلب',\n  'delete_order_warning_message': 'هذا الإجراء لا يمكن التراجع عنه. سيتم حذف الطلب وجميع البيانات المرتبطة به نهائياً.',\n  'order_details': 'تفاصيل الطلب',\n  'admin_email': 'البريد الإلكتروني للمدير',\n  'admin_password': 'كلمة مرور المدير',\n  'enter_admin_email': 'أدخل البريد الإلكتروني للمدير',\n  'enter_admin_password': 'أدخل كلمة مرور المدير',\n  'please_fill_all_fields': 'يرجى ملء جميع الحقول',\n  'email_does_not_match': 'البريد الإلكتروني لا يطابق بريد المدير المسجل',\n  'incorrect_password': 'كلمة المرور غير صحيحة',\n  'confirm_delete': 'تأكيد الحذف',\n  'delete_order': 'حذف الطلب',\n  'order_deleted_successfully': 'تم حذف الطلب بنجاح',\n\n  // نصوص إضافية مفقودة\n  'workers_management': 'إدارة العمال',\n  'reports': 'التقارير',\n  'reminder': 'تذكير',\n  'view_all': 'عرض الكل',\n  'my_tools': 'أدواتي',\n  'client': 'العميل',\n  'type': 'النوع',\n  'status': 'الحالة',\n  'due_date': 'موعد التسليم',\n  'assigned_worker': 'العامل المكلف',\n  'priority': 'الأولوية',\n  'high': 'عالية',\n  'medium': 'متوسطة',\n  'low': 'منخفضة',\n  'urgent': 'عاجل',\n  'normal': 'عادي',\n\n  // نصوص الصفحات\n  'search_placeholder': 'البحث بالاسم أو رقم الطلب أو وصف الطلب...',\n  'search_by_text': 'البحث بالنص',\n  'search_by_order_number': 'البحث برقم الطلب',\n  'enter_order_number': 'أدخل رقم الطلب...',\n  'filter_status': 'فلتر الحالة',\n  'all_orders': 'جميع الطلبات',\n  'order_date_label': 'تاريخ الطلب',\n  'delivery_date_label': 'موعد التسليم',\n  'worker_label': 'العامل',\n  'price_label': 'السعر',\n  'status_label': 'الحالة',\n  'actions': 'الإجراءات',\n  'start_work': 'بدء العمل',\n  'complete_work': 'إنهاء العمل',\n  'view_order': 'عرض الطلب',\n  'edit_order': 'تعديل الطلب',\n  'sar': 'ر.س',\n  'no_worker_assigned': 'لم يتم تعيين عامل',\n  'assigned_to_me': 'مُكلف لي',\n  'not_assigned_to_me': 'غير مُكلف لي',\n\n  'order_details': 'تفاصيل الطلب',\n  'customer_information': 'معلومات الزبون',\n  'name': 'الاسم:',\n  'description': 'الوصف:',\n  'fabric_type': 'نوع القماش:',\n  'status': 'الحالة',\n  'order_date': 'تاريخ الطلب',\n  'delivery_date': 'موعد التسليم',\n  'assigned_worker': 'العامل المسؤول',\n  'measurements_cm': 'المقاسات (سم)',\n  'basic_measurements': 'المقاسات الأساسية',\n  'shoulder': 'الكتف',\n  'shoulder_circumference': 'دوران الكتف',\n  'chest_bust': 'الصدر',\n  'waist': 'الخصر',\n  'hips': 'الأرداف',\n  'advanced_tailoring_measurements': 'مقاسات التفصيل المتقدمة',\n  'dart_length': 'طول البنس',\n  'bodice_length': 'طول الصدرية',\n  'neckline': 'فتحة الصدر',\n  'armpit': 'الإبط',\n  'sleeve_measurements': 'مقاسات الأكمام',\n  'sleeve_length': 'طول الكم',\n  'forearm': 'الزند',\n  'cuff': 'الأسوارة',\n  'length_measurements': 'مقاسات الطول',\n  'front_length': 'طول الأمام',\n  'back_length': 'طول الخلف',\n  'additional_measurements': 'مقاسات إضافية',\n  'dress_length': 'طول الفستان',\n  'shoulder_width': 'عرض الكتف',\n  'sleeve_length_old': 'طول الأكمام',\n  'design_images': 'صور التصميم',\n  'notes': 'الملاحظات',\n  'voice_notes': 'ملاحظات صوتية',\n\n  // نصوص لوحة التحكم\n  'homepage': 'الصفحة الرئيسية',\n  'home': 'الرئيسية',\n  'admin_dashboard': 'لوحة تحكم المدير',\n  'worker_dashboard': 'لوحة تحكم العامل',\n  'logout': 'تسجيل الخروج',\n  'welcome_worker': 'مرحباً بك في لوحة تحكم العامل',\n  'worker_description': 'يمكنك متابعة طلباتك المخصصة لك وتحديث حالتها من هنا',\n\n  'quick_actions': 'الإجراءات السريعة',\n  'worker_management': 'إدارة العمال',\n  'reports': 'التقارير',\n  'detailed_reports': 'عرض التقارير التفصيلية',\n  'reminder': 'تذكير',\n  'today_appointments_reminder': 'مواعيد اليوم',\n  'orders_need_follow': 'طلبات تحتاج متابعة',\n  'view_all': 'عرض جميع',\n  'and': 'و',\n  'you_have': 'لديك',\n\n  // نصوص EditOrderModal\n  'edit_order': 'تعديل الطلب',\n  'fill_required_fields': 'يرجى ملء جميع الحقول المطلوبة',\n  'order_updated_success': 'تم تحديث الطلب بنجاح!',\n  'order_update_error': 'حدث خطأ أثناء تحديث الطلب',\n  'client_name_required': 'اسم الزبونة *',\n  'phone_required': 'رقم الهاتف *',\n  'order_description_required': 'وصف الطلب *',\n  'fabric_type_optional': 'نوع القماش',\n  'price_sar_required': 'السعر (ريال سعودي) *',\n  'delivery_date_required': 'موعد التسليم *',\n  'status_and_worker': 'الحالة والعامل',\n  'order_status': 'حالة الطلب',\n  'responsible_worker': 'العامل المسؤول',\n  'choose_worker': 'اختر العامل المسؤول',\n  'measurements_cm': 'المقاسات (بالسنتيمتر)',\n  'cm_placeholder': 'سم',\n  'additional_notes_placeholder': 'أي ملاحظات أو تفاصيل إضافية...',\n  'voice_notes_section': 'الملاحظات الصوتية',\n  'cancel': 'إلغاء',\n  'saving': 'جاري الحفظ...',\n  'save_changes': 'حفظ التغييرات',\n  'customer_information': 'معلومات الزبون',\n  'order_details': 'تفاصيل الطلب',\n  'design_images': 'صور التصميم',\n\n  // مقاسات التفصيل\n  'basic_measurements': 'المقاسات الأساسية',\n  'advanced_measurements': 'مقاسات التفصيل المتقدمة',\n  'sleeve_measurements': 'مقاسات الأكمام',\n  'length_measurements': 'مقاسات الطول',\n  'shoulder': 'الكتف',\n  'shoulder_circumference': 'دوران الكتف',\n  'chest': 'الصدر',\n  'waist': 'الخصر',\n  'hips': 'الأرداف',\n  'dart_length': 'طول البنس',\n  'bodice_length': 'طول الصدرية',\n  'neckline': 'فتحة الصدر',\n  'armpit': 'الإبط',\n  'sleeve_length': 'طول الكم',\n  'forearm': 'الزند',\n  'cuff': 'الأسوارة',\n  'front_length': 'طول الأمام',\n  'back_length': 'طول الخلف',\n  'notes_section': 'الملاحظات',\n\n  // حالات الطلب\n  'status_pending': 'في الانتظار',\n  'status_in_progress': 'قيد التنفيذ',\n  'status_completed': 'مكتمل',\n  'status_delivered': 'تم التسليم',\n  'status_cancelled': 'ملغي',\n\n  // نصوص OrderModal\n  'not_specified': 'غير محدد',\n  'close': 'إغلاق',\n  'design_image_alt': 'صورة التصميم',\n  'completed_work_images': 'صور العمل المكتمل',\n  'completed_work_description': 'تم رفع هذه الصور من قبل العامل عند إنهاء الطلب',\n  'completed_work_image_alt': 'صورة العمل المكتمل',\n\n  // نصوص صفحة الطلبات\n  'back_to_dashboard': 'العودة إلى',\n  'orders_management': 'إدارة الطلبات',\n  'view_manage_orders': 'عرض وإدارة جميع طلبات التفصيل',\n  'no_orders_assigned': 'لا توجد طلبات مخصصة لك',\n  'no_orders_assigned_desc': 'لم يتم تخصيص أي طلبات لك بعد',\n  'no_orders_found_desc': 'لم يتم العثور على طلبات تطابق معايير البحث',\n  'fabric_label': 'القماش:',\n  'notes_label': 'ملاحظات:',\n  'view': 'عرض',\n  'start_work': 'بدء التنفيذ',\n  'complete_order': 'إنهاء الطلب',\n  'quick_stats': 'إحصائيات سريعة',\n  'complete_order_modal_title': 'إنهاء الطلب',\n  'order_label': 'طلب:',\n  'for_client': 'للعميلة:',\n  'important_warning': 'تنبيه مهم:',\n  'complete_order_warning': 'بعد الضغط على \"إنهاء الطلب\" سيتم تغيير حالة الطلب إلى \"مكتمل\" ولن تتمكن من التراجع عن هذا الإجراء. تأكد من اكتمال العمل قبل المتابعة.',\n  'completing': 'جاري الإنهاء...',\n\n  // نصوص صفحة التقارير\n  'checking_permissions': 'جاري التحقق من الصلاحيات...',\n  'back_to_dashboard': 'العودة إلى لوحة التحكم',\n  'reports_analytics': 'التقارير والإحصائيات',\n  'comprehensive_analysis': 'تحليل شامل لأداء المحل والمبيعات',\n  'this_week': 'هذا الأسبوع',\n  'this_month': 'هذا الشهر',\n  'this_quarter': 'هذا الربع',\n  'this_year': 'هذا العام',\n  'export': 'تصدير',\n  'key_indicators': 'المؤشرات الرئيسية',\n  'total_revenue': 'إجمالي الإيرادات',\n  'total_orders': 'إجمالي الطلبات',\n  'completed_orders': 'طلبات مكتملة',\n  'average_order_value': 'متوسط قيمة الطلب',\n  'top_workers_month': 'أفضل العمال هذا الشهر',\n  'orders_by_type': 'الطلبات حسب النوع',\n  'monthly_trend': 'الاتجاه الشهري للإيرادات والطلبات',\n  'orders_count': 'طلب',\n  'revenue_label': 'الإيرادات',\n  'orders_label': 'الطلبات',\n  'wedding_dress': 'فستان زفاف',\n  'evening_dress': 'فستان سهرة',\n  'engagement_dress': 'فستان خطوبة',\n  'casual_dress': 'فستان يومي',\n  'other': 'أخرى',\n\n  // نصوص صفحة العمال\n  'fill_required_fields': 'يرجى ملء جميع الحقول المطلوبة',\n  'worker_added_success': 'تم إضافة العامل بنجاح!',\n  'error_adding_worker': 'حدث خطأ أثناء إضافة العامل',\n  'worker_updated_success': 'تم تحديث العامل بنجاح!',\n  'error_updating_worker': 'حدث خطأ أثناء تحديث العامل',\n  'confirm_delete_worker': 'هل أنت متأكد من حذف هذا العامل؟',\n  'worker_deleted_success': 'تم حذف العامل بنجاح!',\n  'worker_deactivated': 'تم إلغاء تفعيل العامل',\n  'worker_activated': 'تم تفعيل العامل',\n  'workers_management': 'إدارة العمال',\n  'view_manage_team': 'عرض وإدارة فريق العمل والخياطين',\n  'add_new_worker': 'إضافة عامل جديد',\n  'search_workers_placeholder': 'البحث بالاسم أو البريد الإلكتروني أو التخصص...',\n  'add_new_worker_form': 'إضافة عامل جديد',\n  'full_name_required': 'الاسم الكامل *',\n  'enter_full_name': 'أدخل الاسم الكامل',\n  'email_required': 'البريد الإلكتروني *',\n  'enter_email': 'أدخل البريد الإلكتروني',\n  'password_required': 'كلمة المرور *',\n  'enter_password': 'أدخل كلمة المرور',\n  'phone_required': 'رقم الهاتف *',\n  'enter_phone': 'أدخل رقم الهاتف',\n  'specialty_required': 'التخصص *',\n  'specialty_example': 'مثال: فساتين الزفاف، التطريز',\n  'adding': 'جاري الإضافة...',\n  'add_worker': 'إضافة العامل',\n  'edit_worker': 'تعديل العامل',\n  'new_password': 'كلمة المرور الجديدة',\n  'leave_empty_no_change': 'اتركها فارغة إذا لم تريد تغييرها',\n  'status': 'الحالة',\n  'active': 'نشط',\n  'inactive': 'غير نشط',\n  'saving': 'جاري الحفظ...',\n  'save_changes': 'حفظ التغييرات',\n  'no_workers': 'لا توجد عمال',\n  'no_workers_found': 'لم يتم العثور على عمال يطابقون معايير البحث',\n  'completed_orders': 'طلب مكتمل',\n  'joined_on': 'انضم في:',\n  'total_workers': 'إجمالي العمال',\n  'active_workers': 'عمال نشطين',\n  'total_completed_orders': 'إجمالي الطلبات المكتملة',\n\n  // نصوص صفحة المواعيد\n  'loading': 'جاري التحميل...',\n  'appointments_management': 'إدارة المواعيد',\n  'view_manage_appointments': 'عرض وإدارة جميع المواعيد المحجوزة',\n  'book_new_appointment': 'حجز موعد جديد',\n  'search_appointments_placeholder': 'البحث بالاسم أو الهاتف أو رقم الموعد...',\n  'all_statuses': 'جميع الحالات',\n  'pending': 'في الانتظار',\n  'confirmed': 'مؤكد',\n  'completed': 'مكتمل',\n  'cancelled': 'ملغي',\n  'all_dates': 'جميع التواريخ',\n  'today': 'اليوم',\n  'tomorrow': 'غداً',\n  'this_week': 'هذا الأسبوع',\n  'no_appointments': 'لا توجد مواعيد',\n  'no_appointments_found': 'لم يتم العثور على مواعيد تطابق معايير البحث',\n  'client_info': 'معلومات العميل',\n  'appointment_details': 'تفاصيل الموعد',\n  'date_time': 'التاريخ والوقت',\n  'created_on': 'تم الإنشاء:',\n  'actions': 'الإجراءات',\n  'confirm_appointment': 'تأكيد الموعد',\n  'mark_attended': 'تم الحضور',\n  'cancel_appointment': 'إلغاء الموعد',\n  'quick_stats': 'إحصائيات سريعة',\n  'am': 'ص',\n  'pm': 'م',\n\n  // نصوص صفحة إضافة الطلبات\n  'add_new_order': 'إضافة طلب جديد',\n  'add_new_order_description': 'أضف طلب تفصيل جديد مع جميع التفاصيل والمقاسات المطلوبة',\n  'basic_information': 'المعلومات الأساسية',\n  'client_name_required': 'اسم الزبونة *',\n  'enter_client_name': 'أدخل اسم الزبونة',\n  'phone_required': 'رقم الهاتف *',\n  'enter_phone': 'أدخل رقم الهاتف',\n  'order_description_required': 'وصف الطلب *',\n  'order_description_placeholder': 'مثال: فستان زفاف أبيض مطرز',\n  'fabric_type': 'نوع القماش',\n  'fabric_type_placeholder': 'مثال: ساتان، شيفون، دانتيل',\n  'price_sar': 'السعر (ريال سعودي) *',\n  'responsible_worker': 'العامل المسؤول',\n  'choose_worker': 'اختر العامل المسؤول',\n  'delivery_date_required': 'موعد التسليم *',\n  'design_images': 'صور التصميم',\n  'measurements_cm': 'المقاسات (بالسنتيمتر)',\n  'basic_measurements': 'المقاسات الأساسية',\n  'shoulder': 'الكتف',\n  'shoulder_circumference': 'دوران الكتف',\n  'chest': 'الصدر',\n  'waist': 'الخصر',\n  'hips': 'الأرداف',\n  'advanced_measurements': 'مقاسات التفصيل المتقدمة',\n  'dart_length': 'طول البنس',\n  'bodice_length': 'طول الصدرية',\n  'neckline': 'فتحة الصدر',\n  'armpit': 'الإبط',\n  'sleeve_measurements': 'مقاسات الأكمام',\n  'sleeve_length': 'طول الكم',\n  'forearm': 'الزند',\n  'cuff': 'الأسوارة',\n  'length_measurements': 'مقاسات الطول',\n  'front_length': 'طول الأمام',\n  'back_length': 'طول الخلف',\n  'additional_notes': 'ملاحظات إضافية',\n  'additional_notes_placeholder': 'أي ملاحظات أو تفاصيل إضافية حول التصميم أو التفصيل...',\n  'voice_notes': 'الملاحظات الصوتية',\n  'saving': 'جاري الحفظ...',\n  'save_order': 'حفظ الطلب',\n  'cancel': 'إلغاء',\n  'cm_placeholder': 'سم',\n  'fill_required_fields': 'يرجى ملء جميع الحقول المطلوبة',\n  'order_added_success': 'تم إضافة الطلب بنجاح! سيتم توجيهك إلى صفحة الطلبات...',\n  'order_add_error': 'حدث خطأ أثناء إضافة الطلب. يرجى المحاولة مرة أخرى.',\n  'back_to_dashboard': 'العودة إلى لوحة التحكم',\n\n  // نصوص الفوتر\n  'home': 'الرئيسية',\n  'book_appointment': 'حجز موعد',\n  'track_order': 'استعلام عن الطلب',\n  'fabrics': 'الأقمشة',\n  'saturday_thursday': 'السبت - الخميس',\n  'friday': 'الجمعة',\n  'closed': 'مغلق',\n  'facebook': 'فيسبوك',\n  'instagram': 'إنستغرام',\n  'whatsapp': 'واتساب',\n  'yasmin_alsham': 'ياسمين الشام',\n  'custom_dress_tailoring': 'تفصيل فساتين حسب الطلب',\n  'footer_description': 'نحن نجمع بين التراث الدمشقي العريق والتصاميم العصرية لنقدم لك فساتين تعكس شخصيتك وتبرز جمالك الطبيعي.',\n  'quick_links': 'روابط سريعة',\n  'contact_us': 'تواصلي معنا',\n  'address': 'العنوان',\n  'address_text': 'الخبر الشمالية، التقاطع السادس، شارع الأمير مشعل، الخبر، السعودية',\n  'phone_numbers': 'أرقام الهاتف',\n  'tailoring_department': 'قسم التفصيل - ياسمين الشام',\n  'ready_made_department': 'قسم الجاهز والمقاسات - ياسمين الشام 2',\n  'email': 'البريد الإلكتروني',\n  'working_hours': 'أوقات العمل',\n  'work_schedule': 'مواعيد العمل',\n  'work_with_love': 'نعمل بحب لإسعادك',\n  'all_rights_reserved': 'جميع الحقوق محفوظة.',\n  'privacy_policy': 'سياسة الخصوصية',\n  'terms_conditions': 'الشروط والأحكام',\n  'made_with': 'صُنع بـ',\n  'in_damascus': 'في دمشق',\n\n\n}\n\n// الترجمات الإنجليزية\nconst enTranslations: Translations = {\n  // التنقل والعناوين الرئيسية\n  'dashboard': 'Dashboard',\n  'orders': 'Orders',\n  'appointments': 'Appointments',\n  'settings': 'Settings',\n  'logout': 'Logout',\n  'profile': 'Profile',\n  'notifications': 'Notifications',\n  \n  // الأزرار والإجراءات\n  'add_new_order': 'Add New Order',\n  'book_appointment': 'Book Appointment',\n  'view_details': 'View Details',\n  'edit': 'Edit',\n  'delete': 'Delete',\n  'save': 'Save',\n  'cancel': 'Cancel',\n  'submit': 'Submit',\n  'search': 'Search',\n  'filter': 'Filter',\n  'export': 'Export',\n  'print': 'Print',\n  \n  // الحالات\n  'pending': 'Pending',\n  'in_progress': 'In Progress',\n  'completed': 'Completed',\n  'delivered': 'Delivered',\n  'cancelled': 'Cancelled',\n  \n  // الإحصائيات\n  'active_orders': 'Active Orders',\n  'completed_orders': 'Completed Orders',\n  'total_orders': 'Total Orders',\n  'my_active_orders': 'My Active Orders',\n  'my_completed_orders': 'My Completed Orders',\n  'my_total_orders': 'My Total Orders',\n  'today_appointments': 'Today\\'s Appointments',\n  \n  // الأدوار\n  'admin': 'Admin',\n  'worker': 'Worker',\n  \n  // الرسائل\n  'welcome_back': 'Welcome Back',\n  'overview_today': 'Here\\'s an overview of today\\'s activities',\n  'no_orders_found': 'No orders found',\n  'no_appointments_found': 'No appointments found',\n  'loading': 'Loading...',\n  'error': 'Error',\n  'success': 'Success',\n  \n  // أقسام لوحة التحكم\n  'recent_orders': 'Recent Orders',\n  'quick_actions': 'Quick Actions',\n  'statistics': 'Statistics',\n  'recent_activity': 'Recent Activity',\n  \n  // تفاصيل الطلب\n  'order_details': 'Order Details',\n  'customer_info': 'Customer Information',\n  'order_info': 'Order Information',\n  'measurements': 'Measurements',\n  'images': 'Images',\n  'notes': 'Notes',\n  'voice_notes': 'Voice Notes',\n  'voice_notes_optional': 'Voice Notes (Optional)',\n  \n  // التواريخ والأوقات\n  'order_date': 'Order Date',\n  'delivery_date': 'Delivery Date',\n  'created_at': 'Created At',\n  'updated_at': 'Updated At',\n  \n  // أخرى\n  'language': 'Language',\n  'arabic': 'العربية',\n  'english': 'English',\n  'change_language': 'Change Language',\n  'next_language': 'Next Language',\n\n  // نصوص رفع الصور\n  'click_or_drag_images': 'Click or drag images here',\n  'drop_images_here': 'Drop them here...',\n  'image_upload_format': 'PNG, JPG, GIF up to 10MB per image',\n  'max_images_text': 'maximum',\n  'images_text': 'images',\n  'of': 'of',\n  'add_image': 'Add image',\n  'max_images_reached': 'Maximum number of images reached',\n\n  // نصوص الملاحظات الصوتية\n  'start_recording': 'Start Recording',\n  'stop_recording': 'Stop Recording',\n  'click_to_record_voice_note': 'Click to record a voice note',\n  'voice_note': 'Voice Note',\n  'microphone_access_error': 'Cannot access microphone. Please check permissions.',\n\n  // Delete Order Modal\n  'confirm_delete_order': 'Confirm Delete Order',\n  'warning_delete_order': 'Warning: Delete Order',\n  'delete_order_warning_message': 'This action cannot be undone. The order and all associated data will be permanently deleted.',\n  'order_details': 'Order Details',\n  'admin_email': 'Admin Email',\n  'admin_password': 'Admin Password',\n  'enter_admin_email': 'Enter admin email',\n  'enter_admin_password': 'Enter admin password',\n  'please_fill_all_fields': 'Please fill all fields',\n  'email_does_not_match': 'Email does not match the registered admin email',\n  'incorrect_password': 'Incorrect password',\n  'confirm_delete': 'Confirm Delete',\n  'delete_order': 'Delete Order',\n  'order_deleted_successfully': 'Order deleted successfully',\n\n  // نصوص إضافية مفقودة\n  'workers_management': 'Workers Management',\n  'reports': 'Reports',\n  'reminder': 'Reminder',\n  'view_all': 'View All',\n  'my_tools': 'My Tools',\n  'client': 'Client',\n  'type': 'Type',\n  'status': 'Status',\n  'due_date': 'Due Date',\n  'assigned_worker': 'Assigned Worker',\n  'priority': 'Priority',\n  'high': 'High',\n  'medium': 'Medium',\n  'low': 'Low',\n  'urgent': 'Urgent',\n  'normal': 'Normal',\n\n  // نصوص الصفحات\n  'search_placeholder': 'Search by name, order number, or description...',\n  'search_by_text': 'Search by Text',\n  'search_by_order_number': 'Search by Order Number',\n  'enter_order_number': 'Enter order number...',\n  'filter_status': 'Filter Status',\n  'all_orders': 'All Orders',\n  'order_date_label': 'Order Date',\n  'delivery_date_label': 'Delivery Date',\n  'worker_label': 'Worker',\n  'price_label': 'Price',\n  'status_label': 'Status',\n  'actions': 'Actions',\n  'start_work': 'Start Work',\n  'complete_work': 'Complete Work',\n  'view_order': 'View Order',\n  'edit_order': 'Edit Order',\n  'sar': 'SAR',\n  'no_worker_assigned': 'No worker assigned',\n  'assigned_to_me': 'Assigned to me',\n  'not_assigned_to_me': 'Not assigned to me',\n\n  'order_details': 'Order Details',\n  'customer_information': 'Customer Information',\n  'name': 'Name:',\n  'description': 'Description:',\n  'fabric_type': 'Fabric Type:',\n  'status': 'Status',\n  'order_date': 'Order Date',\n  'delivery_date': 'Delivery Date',\n  'assigned_worker': 'Assigned Worker',\n  'measurements_cm': 'Measurements (cm)',\n  'basic_measurements': 'Basic Measurements',\n  'shoulder': 'Shoulder',\n  'shoulder_circumference': 'Shoulder Circumference',\n  'chest_bust': 'Chest/Bust',\n  'waist': 'Waist',\n  'hips': 'Hips',\n  'advanced_tailoring_measurements': 'Advanced Tailoring Measurements',\n  'dart_length': 'Dart Length',\n  'bodice_length': 'Bodice Length',\n  'neckline': 'Neckline',\n  'armpit': 'Armpit',\n  'sleeve_measurements': 'Sleeve Measurements',\n  'sleeve_length': 'Sleeve Length',\n  'forearm': 'Forearm',\n  'cuff': 'Cuff',\n  'length_measurements': 'Length Measurements',\n  'front_length': 'Front Length',\n  'back_length': 'Back Length',\n  'additional_measurements': 'Additional Measurements',\n  'dress_length': 'Dress Length',\n  'shoulder_width': 'Shoulder Width',\n  'sleeve_length_old': 'Sleeve Length',\n  'design_images': 'Design Images',\n  'notes': 'Notes',\n  'voice_notes': 'Voice Notes',\n\n  // نصوص لوحة التحكم\n  'homepage': 'Homepage',\n  'home': 'Home',\n  'admin_dashboard': 'Admin Dashboard',\n  'worker_dashboard': 'Worker Dashboard',\n  'logout': 'Logout',\n  'welcome_worker': 'Welcome to Worker Dashboard',\n  'worker_description': 'You can track your assigned orders and update their status here',\n\n  'quick_actions': 'Quick Actions',\n  'worker_management': 'Worker Management',\n  'reports': 'Reports',\n  'detailed_reports': 'View Detailed Reports',\n  'reminder': 'Reminder',\n  'today_appointments_reminder': 'Today\\'s Appointments',\n  'orders_need_follow': 'Orders Need Follow-up',\n  'view_all': 'View All',\n  'and': 'and',\n  'you_have': 'You have',\n\n  // نصوص EditOrderModal\n  'edit_order': 'Edit Order',\n  'fill_required_fields': 'Please fill all required fields',\n  'order_updated_success': 'Order updated successfully!',\n  'order_update_error': 'Error occurred while updating order',\n  'client_name_required': 'Client Name *',\n  'phone_required': 'Phone Number *',\n  'order_description_required': 'Order Description *',\n  'fabric_type_optional': 'Fabric Type',\n  'price_sar_required': 'Price (SAR) *',\n  'delivery_date_required': 'Delivery Date *',\n  'status_and_worker': 'Status and Worker',\n  'order_status': 'Order Status',\n  'responsible_worker': 'Responsible Worker',\n  'choose_worker': 'Choose Responsible Worker',\n  'measurements_cm': 'Measurements (cm)',\n  'cm_placeholder': 'cm',\n  'additional_notes_placeholder': 'Any additional notes or details...',\n  'voice_notes_section': 'Voice Notes',\n  'cancel': 'Cancel',\n  'saving': 'Saving...',\n  'save_changes': 'Save Changes',\n  'customer_information': 'Customer Information',\n  'order_details': 'Order Details',\n  'design_images': 'Design Images',\n\n  // مقاسات التفصيل\n  'basic_measurements': 'Basic Measurements',\n  'advanced_measurements': 'Advanced Tailoring Measurements',\n  'sleeve_measurements': 'Sleeve Measurements',\n  'length_measurements': 'Length Measurements',\n  'shoulder': 'Shoulder',\n  'shoulder_circumference': 'Shoulder Circumference',\n  'chest': 'Chest',\n  'waist': 'Waist',\n  'hips': 'Hips',\n  'dart_length': 'Dart Length',\n  'bodice_length': 'Bodice Length',\n  'neckline': 'Neckline',\n  'armpit': 'Armpit',\n  'sleeve_length': 'Sleeve Length',\n  'forearm': 'Forearm',\n  'cuff': 'Cuff',\n  'front_length': 'Front Length',\n  'back_length': 'Back Length',\n  'notes_section': 'Notes',\n\n  // حالات الطلب\n  'status_pending': 'Pending',\n  'status_in_progress': 'In Progress',\n  'status_completed': 'Completed',\n  'status_delivered': 'Delivered',\n  'status_cancelled': 'Cancelled',\n\n  // نصوص OrderModal\n  'not_specified': 'Not Specified',\n  'close': 'Close',\n  'design_image_alt': 'Design Image',\n  'completed_work_images': 'Completed Work Images',\n  'completed_work_description': 'These images were uploaded by the worker upon order completion',\n  'completed_work_image_alt': 'Completed Work Image',\n\n  // نصوص صفحة الطلبات\n  'back_to_dashboard': 'Back to',\n  'orders_management': 'Orders Management',\n  'view_manage_orders': 'View and manage all tailoring orders',\n  'no_orders_assigned': 'No orders assigned to you',\n  'no_orders_assigned_desc': 'No orders have been assigned to you yet',\n  'no_orders_found_desc': 'No orders found matching the search criteria',\n  'fabric_label': 'Fabric:',\n  'notes_label': 'Notes:',\n  'view': 'View',\n  'start_work': 'Start Work',\n  'complete_order': 'Complete Order',\n  'quick_stats': 'Quick Stats',\n  'complete_order_modal_title': 'Complete Order',\n  'order_label': 'Order:',\n  'for_client': 'For client:',\n  'important_warning': 'Important Warning:',\n  'complete_order_warning': 'After clicking \"Complete Order\", the order status will be changed to \"completed\" and you will not be able to undo this action. Make sure the work is complete before proceeding.',\n  'completing': 'Completing...',\n\n  // نصوص صفحة التقارير\n  'checking_permissions': 'Checking permissions...',\n  'back_to_dashboard': 'Back to Dashboard',\n  'reports_analytics': 'Reports & Analytics',\n  'comprehensive_analysis': 'Comprehensive analysis of store performance and sales',\n  'this_week': 'This Week',\n  'this_month': 'This Month',\n  'this_quarter': 'This Quarter',\n  'this_year': 'This Year',\n  'export': 'Export',\n  'key_indicators': 'Key Indicators',\n  'total_revenue': 'Total Revenue',\n  'total_orders': 'Total Orders',\n  'completed_orders': 'Completed Orders',\n  'average_order_value': 'Average Order Value',\n  'top_workers_month': 'Top Workers This Month',\n  'orders_by_type': 'Orders by Type',\n  'monthly_trend': 'Monthly Revenue & Orders Trend',\n  'orders_count': 'orders',\n  'revenue_label': 'Revenue',\n  'orders_label': 'Orders',\n  'wedding_dress': 'Wedding Dress',\n  'evening_dress': 'Evening Dress',\n  'engagement_dress': 'Engagement Dress',\n  'casual_dress': 'Casual Dress',\n  'other': 'Other',\n\n  // نصوص صفحة العمال\n  'fill_required_fields': 'Please fill all required fields',\n  'worker_added_success': 'Worker added successfully!',\n  'error_adding_worker': 'Error occurred while adding worker',\n  'worker_updated_success': 'Worker updated successfully!',\n  'error_updating_worker': 'Error occurred while updating worker',\n  'confirm_delete_worker': 'Are you sure you want to delete this worker?',\n  'worker_deleted_success': 'Worker deleted successfully!',\n  'worker_deactivated': 'Worker deactivated',\n  'worker_activated': 'Worker activated',\n  'workers_management': 'Workers Management',\n  'view_manage_team': 'View and manage work team and tailors',\n  'add_new_worker': 'Add New Worker',\n  'search_workers_placeholder': 'Search by name, email, or specialty...',\n  'add_new_worker_form': 'Add New Worker',\n  'full_name_required': 'Full Name *',\n  'enter_full_name': 'Enter full name',\n  'email_required': 'Email *',\n  'enter_email': 'Enter email',\n  'password_required': 'Password *',\n  'enter_password': 'Enter password',\n  'phone_required': 'Phone Number *',\n  'enter_phone': 'Enter phone number',\n  'specialty_required': 'Specialty *',\n  'specialty_example': 'Example: Wedding dresses, Embroidery',\n  'adding': 'Adding...',\n  'add_worker': 'Add Worker',\n  'edit_worker': 'Edit Worker',\n  'new_password': 'New Password',\n  'leave_empty_no_change': 'Leave empty if you don\\'t want to change it',\n  'status': 'Status',\n  'active': 'Active',\n  'inactive': 'Inactive',\n  'saving': 'Saving...',\n  'save_changes': 'Save Changes',\n  'no_workers': 'No Workers',\n  'no_workers_found': 'No workers found matching the search criteria',\n  'completed_orders': 'completed orders',\n  'joined_on': 'Joined on:',\n  'total_workers': 'Total Workers',\n  'active_workers': 'Active Workers',\n  'total_completed_orders': 'Total Completed Orders',\n\n  // نصوص صفحة المواعيد\n  'loading': 'Loading...',\n  'appointments_management': 'Appointments Management',\n  'view_manage_appointments': 'View and manage all booked appointments',\n  'book_new_appointment': 'Book New Appointment',\n  'search_appointments_placeholder': 'Search by name, phone, or appointment number...',\n  'all_statuses': 'All Statuses',\n  'pending': 'Pending',\n  'confirmed': 'Confirmed',\n  'completed': 'Completed',\n  'cancelled': 'Cancelled',\n  'all_dates': 'All Dates',\n  'today': 'Today',\n  'tomorrow': 'Tomorrow',\n  'this_week': 'This Week',\n  'no_appointments': 'No Appointments',\n  'no_appointments_found': 'No appointments found matching the search criteria',\n  'client_info': 'Client Information',\n  'appointment_details': 'Appointment Details',\n  'date_time': 'Date & Time',\n  'created_on': 'Created:',\n  'actions': 'Actions',\n  'confirm_appointment': 'Confirm Appointment',\n  'mark_attended': 'Mark Attended',\n  'cancel_appointment': 'Cancel Appointment',\n  'quick_stats': 'Quick Statistics',\n  'am': 'AM',\n  'pm': 'PM',\n\n  // نصوص صفحة إضافة الطلبات\n  'add_new_order': 'Add New Order',\n  'add_new_order_description': 'Add a new tailoring order with all required details and measurements',\n  'basic_information': 'Basic Information',\n  'client_name_required': 'Client Name *',\n  'enter_client_name': 'Enter client name',\n  'phone_required': 'Phone Number *',\n  'enter_phone': 'Enter phone number',\n  'order_description_required': 'Order Description *',\n  'order_description_placeholder': 'Example: White embroidered wedding dress',\n  'fabric_type': 'Fabric Type',\n  'fabric_type_placeholder': 'Example: Satin, Chiffon, Lace',\n  'price_sar': 'Price (Saudi Riyal) *',\n  'responsible_worker': 'Responsible Worker',\n  'choose_worker': 'Choose responsible worker',\n  'delivery_date_required': 'Delivery Date *',\n  'design_images': 'Design Images',\n  'measurements_cm': 'Measurements (in centimeters)',\n  'basic_measurements': 'Basic Measurements',\n  'shoulder': 'Shoulder',\n  'shoulder_circumference': 'Shoulder Circumference',\n  'chest': 'Chest',\n  'waist': 'Waist',\n  'hips': 'Hips',\n  'advanced_measurements': 'Advanced Tailoring Measurements',\n  'dart_length': 'Dart Length',\n  'bodice_length': 'Bodice Length',\n  'neckline': 'Neckline',\n  'armpit': 'Armpit',\n  'sleeve_measurements': 'Sleeve Measurements',\n  'sleeve_length': 'Sleeve Length',\n  'forearm': 'Forearm',\n  'cuff': 'Cuff',\n  'length_measurements': 'Length Measurements',\n  'front_length': 'Front Length',\n  'back_length': 'Back Length',\n  'additional_notes': 'Additional Notes',\n  'additional_notes_placeholder': 'Any additional notes or details about the design or tailoring...',\n  'voice_notes': 'Voice Notes',\n  'saving': 'Saving...',\n  'save_order': 'Save Order',\n  'cancel': 'Cancel',\n  'cm_placeholder': 'cm',\n  'fill_required_fields': 'Please fill all required fields',\n  'order_added_success': 'Order added successfully! You will be redirected to orders page...',\n  'order_add_error': 'An error occurred while adding the order. Please try again.',\n  'back_to_dashboard': 'Back to Dashboard',\n\n  // Footer texts\n  'home': 'Home',\n  'book_appointment': 'Book Appointment',\n  'track_order': 'Track Order',\n  'fabrics': 'Fabrics',\n  'saturday_thursday': 'Saturday - Thursday',\n  'friday': 'Friday',\n  'closed': 'Closed',\n  'facebook': 'Facebook',\n  'instagram': 'Instagram',\n  'whatsapp': 'WhatsApp',\n  'yasmin_alsham': 'Yasmin Alsham',\n  'custom_dress_tailoring': 'Custom Dress Tailoring',\n  'footer_description': 'We combine the ancient Damascene heritage with modern designs to offer you dresses that reflect your personality and highlight your natural beauty.',\n  'quick_links': 'Quick Links',\n  'contact_us': 'Contact Us',\n  'address': 'Address',\n  'address_text': 'North Khobar, 6th Intersection, Prince Mishaal Street, Khobar, Saudi Arabia',\n  'phone_numbers': 'Phone Numbers',\n  'tailoring_department': 'Tailoring Department - Yasmin Alsham',\n  'ready_made_department': 'Ready-made & Sizing Department - Yasmin Alsham 2',\n  'email': 'Email',\n  'working_hours': 'Working Hours',\n  'work_schedule': 'Work Schedule',\n  'work_with_love': 'We work with love to make you happy',\n  'all_rights_reserved': 'All rights reserved.',\n  'privacy_policy': 'Privacy Policy',\n  'terms_conditions': 'Terms & Conditions',\n  'made_with': 'Made with',\n  'in_damascus': 'in Damascus',\n\n  // Header texts\n  'ready_dresses': 'Ready Dresses',\n\n  // Hero texts\n  'hero_subtitle': 'Custom Dress Tailoring with Damascene Elegance',\n  'hero_subtitle_desktop': 'Custom Dress Tailoring',\n  'hero_subtitle_elegant': 'with Authentic Damascene Elegance',\n  'hero_description_mobile': 'We specialize in tailoring elegant dresses with an authentic Damascene touch. From luxurious wedding dresses to elegant evening gowns, we turn your dreams into reality with expert hands and innovative designs.',\n  'hero_description_desktop': 'We combine the ancient Damascene heritage with modern designs to offer you dresses that reflect your personality and highlight your natural beauty. Every dress is a story, and every story tells of elegance and beauty.',\n  'explore_ready_designs': 'Explore Our Ready Designs',\n  'yasmin_alsham_alt': 'Yasmin Alsham - Custom Dress Tailoring'\n}\n\n// Hook للترجمة\nexport function useTranslation() {\n  const [language, setLanguage] = useState<'ar' | 'en'>('ar')\n\n  // تحميل اللغة المحفوظة عند بدء التطبيق\n  useEffect(() => {\n    const savedLanguage = localStorage.getItem('dashboard-language') as 'ar' | 'en'\n    if (savedLanguage) {\n      setLanguage(savedLanguage)\n    }\n  }, [])\n\n  // حفظ اللغة عند تغييرها\n  const changeLanguage = (newLanguage: 'ar' | 'en') => {\n    setLanguage(newLanguage)\n    localStorage.setItem('dashboard-language', newLanguage)\n  }\n\n  // دالة الترجمة\n  const t = (key: TranslationKey): string => {\n    const translations = language === 'ar' ? arTranslations : enTranslations\n    const translation = translations[key]\n    \n    if (typeof translation === 'string') {\n      return translation\n    }\n    \n    // إذا لم توجد الترجمة، أرجع المفتاح نفسه\n    return key\n  }\n\n  // التحقق من اللغة الحالية\n  const isArabic = language === 'ar'\n  const isEnglish = language === 'en'\n\n  return {\n    language,\n    changeLanguage,\n    t,\n    isArabic,\n    isEnglish\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AASA,mBAAmB;AACnB,MAAM,iBAA+B;IACnC,4BAA4B;IAC5B,aAAa;IACb,UAAU;IACV,gBAAgB;IAChB,YAAY;IACZ,UAAU;IACV,WAAW;IACX,iBAAiB;IAEjB,qBAAqB;IACrB,iBAAiB;IACjB,oBAAoB;IACpB,gBAAgB;IAChB,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,SAAS;IAET,UAAU;IACV,WAAW;IACX,eAAe;IACf,aAAa;IACb,aAAa;IACb,aAAa;IAEb,aAAa;IACb,iBAAiB;IACjB,oBAAoB;IACpB,gBAAgB;IAChB,oBAAoB;IACpB,uBAAuB;IACvB,mBAAmB;IACnB,sBAAsB;IAEtB,UAAU;IACV,SAAS;IACT,UAAU;IAEV,UAAU;IACV,gBAAgB;IAChB,kBAAkB;IAClB,mBAAmB;IACnB,yBAAyB;IACzB,WAAW;IACX,SAAS;IACT,WAAW;IAEX,oBAAoB;IACpB,iBAAiB;IACjB,iBAAiB;IACjB,cAAc;IACd,mBAAmB;IAEnB,eAAe;IACf,iBAAiB;IACjB,iBAAiB;IACjB,cAAc;IACd,gBAAgB;IAChB,UAAU;IACV,SAAS;IACT,eAAe;IACf,wBAAwB;IAExB,oBAAoB;IACpB,cAAc;IACd,iBAAiB;IACjB,cAAc;IACd,cAAc;IAEd,OAAO;IACP,YAAY;IACZ,UAAU;IACV,WAAW;IACX,mBAAmB;IACnB,iBAAiB;IAEjB,iBAAiB;IACjB,wBAAwB;IACxB,oBAAoB;IACpB,uBAAuB;IACvB,mBAAmB;IACnB,eAAe;IACf,MAAM;IACN,aAAa;IACb,sBAAsB;IAEtB,yBAAyB;IACzB,mBAAmB;IACnB,kBAAkB;IAClB,8BAA8B;IAC9B,cAAc;IACd,2BAA2B;IAE3B,qBAAqB;IACrB,wBAAwB;IACxB,wBAAwB;IACxB,gCAAgC;IAChC,iBAAiB;IACjB,eAAe;IACf,kBAAkB;IAClB,qBAAqB;IACrB,wBAAwB;IACxB,0BAA0B;IAC1B,wBAAwB;IACxB,sBAAsB;IACtB,kBAAkB;IAClB,gBAAgB;IAChB,8BAA8B;IAE9B,qBAAqB;IACrB,sBAAsB;IACtB,WAAW;IACX,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,UAAU;IACV,QAAQ;IACR,UAAU;IACV,YAAY;IACZ,mBAAmB;IACnB,YAAY;IACZ,QAAQ;IACR,UAAU;IACV,OAAO;IACP,UAAU;IACV,UAAU;IAEV,eAAe;IACf,sBAAsB;IACtB,kBAAkB;IAClB,0BAA0B;IAC1B,sBAAsB;IACtB,iBAAiB;IACjB,cAAc;IACd,oBAAoB;IACpB,uBAAuB;IACvB,gBAAgB;IAChB,eAAe;IACf,gBAAgB;IAChB,WAAW;IACX,cAAc;IACd,iBAAiB;IACjB,cAAc;IACd,cAAc;IACd,OAAO;IACP,sBAAsB;IACtB,kBAAkB;IAClB,sBAAsB;IAEtB,iBAAiB;IACjB,wBAAwB;IACxB,QAAQ;IACR,eAAe;IACf,eAAe;IACf,UAAU;IACV,cAAc;IACd,iBAAiB;IACjB,mBAAmB;IACnB,mBAAmB;IACnB,sBAAsB;IACtB,YAAY;IACZ,0BAA0B;IAC1B,cAAc;IACd,SAAS;IACT,QAAQ;IACR,mCAAmC;IACnC,eAAe;IACf,iBAAiB;IACjB,YAAY;IACZ,UAAU;IACV,uBAAuB;IACvB,iBAAiB;IACjB,WAAW;IACX,QAAQ;IACR,uBAAuB;IACvB,gBAAgB;IAChB,eAAe;IACf,2BAA2B;IAC3B,gBAAgB;IAChB,kBAAkB;IAClB,qBAAqB;IACrB,iBAAiB;IACjB,SAAS;IACT,eAAe;IAEf,mBAAmB;IACnB,YAAY;IACZ,QAAQ;IACR,mBAAmB;IACnB,oBAAoB;IACpB,UAAU;IACV,kBAAkB;IAClB,sBAAsB;IAEtB,iBAAiB;IACjB,qBAAqB;IACrB,WAAW;IACX,oBAAoB;IACpB,YAAY;IACZ,+BAA+B;IAC/B,sBAAsB;IACtB,YAAY;IACZ,OAAO;IACP,YAAY;IAEZ,sBAAsB;IACtB,cAAc;IACd,wBAAwB;IACxB,yBAAyB;IACzB,sBAAsB;IACtB,wBAAwB;IACxB,kBAAkB;IAClB,8BAA8B;IAC9B,wBAAwB;IACxB,sBAAsB;IACtB,0BAA0B;IAC1B,qBAAqB;IACrB,gBAAgB;IAChB,sBAAsB;IACtB,iBAAiB;IACjB,mBAAmB;IACnB,kBAAkB;IAClB,gCAAgC;IAChC,uBAAuB;IACvB,UAAU;IACV,UAAU;IACV,gBAAgB;IAChB,wBAAwB;IACxB,iBAAiB;IACjB,iBAAiB;IAEjB,iBAAiB;IACjB,sBAAsB;IACtB,yBAAyB;IACzB,uBAAuB;IACvB,uBAAuB;IACvB,YAAY;IACZ,0BAA0B;IAC1B,SAAS;IACT,SAAS;IACT,QAAQ;IACR,eAAe;IACf,iBAAiB;IACjB,YAAY;IACZ,UAAU;IACV,iBAAiB;IACjB,WAAW;IACX,QAAQ;IACR,gBAAgB;IAChB,eAAe;IACf,iBAAiB;IAEjB,cAAc;IACd,kBAAkB;IAClB,sBAAsB;IACtB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IAEpB,kBAAkB;IAClB,iBAAiB;IACjB,SAAS;IACT,oBAAoB;IACpB,yBAAyB;IACzB,8BAA8B;IAC9B,4BAA4B;IAE5B,oBAAoB;IACpB,qBAAqB;IACrB,qBAAqB;IACrB,sBAAsB;IACtB,sBAAsB;IACtB,2BAA2B;IAC3B,wBAAwB;IACxB,gBAAgB;IAChB,eAAe;IACf,QAAQ;IACR,cAAc;IACd,kBAAkB;IAClB,eAAe;IACf,8BAA8B;IAC9B,eAAe;IACf,cAAc;IACd,qBAAqB;IACrB,0BAA0B;IAC1B,cAAc;IAEd,qBAAqB;IACrB,wBAAwB;IACxB,qBAAqB;IACrB,qBAAqB;IACrB,0BAA0B;IAC1B,aAAa;IACb,cAAc;IACd,gBAAgB;IAChB,aAAa;IACb,UAAU;IACV,kBAAkB;IAClB,iBAAiB;IACjB,gBAAgB;IAChB,oBAAoB;IACpB,uBAAuB;IACvB,qBAAqB;IACrB,kBAAkB;IAClB,iBAAiB;IACjB,gBAAgB;IAChB,iBAAiB;IACjB,gBAAgB;IAChB,iBAAiB;IACjB,iBAAiB;IACjB,oBAAoB;IACpB,gBAAgB;IAChB,SAAS;IAET,mBAAmB;IACnB,wBAAwB;IACxB,wBAAwB;IACxB,uBAAuB;IACvB,0BAA0B;IAC1B,yBAAyB;IACzB,yBAAyB;IACzB,0BAA0B;IAC1B,sBAAsB;IACtB,oBAAoB;IACpB,sBAAsB;IACtB,oBAAoB;IACpB,kBAAkB;IAClB,8BAA8B;IAC9B,uBAAuB;IACvB,sBAAsB;IACtB,mBAAmB;IACnB,kBAAkB;IAClB,eAAe;IACf,qBAAqB;IACrB,kBAAkB;IAClB,kBAAkB;IAClB,eAAe;IACf,sBAAsB;IACtB,qBAAqB;IACrB,UAAU;IACV,cAAc;IACd,eAAe;IACf,gBAAgB;IAChB,yBAAyB;IACzB,UAAU;IACV,UAAU;IACV,YAAY;IACZ,UAAU;IACV,gBAAgB;IAChB,cAAc;IACd,oBAAoB;IACpB,oBAAoB;IACpB,aAAa;IACb,iBAAiB;IACjB,kBAAkB;IAClB,0BAA0B;IAE1B,qBAAqB;IACrB,WAAW;IACX,2BAA2B;IAC3B,4BAA4B;IAC5B,wBAAwB;IACxB,mCAAmC;IACnC,gBAAgB;IAChB,WAAW;IACX,aAAa;IACb,aAAa;IACb,aAAa;IACb,aAAa;IACb,SAAS;IACT,YAAY;IACZ,aAAa;IACb,mBAAmB;IACnB,yBAAyB;IACzB,eAAe;IACf,uBAAuB;IACvB,aAAa;IACb,cAAc;IACd,WAAW;IACX,uBAAuB;IACvB,iBAAiB;IACjB,sBAAsB;IACtB,eAAe;IACf,MAAM;IACN,MAAM;IAEN,0BAA0B;IAC1B,iBAAiB;IACjB,6BAA6B;IAC7B,qBAAqB;IACrB,wBAAwB;IACxB,qBAAqB;IACrB,kBAAkB;IAClB,eAAe;IACf,8BAA8B;IAC9B,iCAAiC;IACjC,eAAe;IACf,2BAA2B;IAC3B,aAAa;IACb,sBAAsB;IACtB,iBAAiB;IACjB,0BAA0B;IAC1B,iBAAiB;IACjB,mBAAmB;IACnB,sBAAsB;IACtB,YAAY;IACZ,0BAA0B;IAC1B,SAAS;IACT,SAAS;IACT,QAAQ;IACR,yBAAyB;IACzB,eAAe;IACf,iBAAiB;IACjB,YAAY;IACZ,UAAU;IACV,uBAAuB;IACvB,iBAAiB;IACjB,WAAW;IACX,QAAQ;IACR,uBAAuB;IACvB,gBAAgB;IAChB,eAAe;IACf,oBAAoB;IACpB,gCAAgC;IAChC,eAAe;IACf,UAAU;IACV,cAAc;IACd,UAAU;IACV,kBAAkB;IAClB,wBAAwB;IACxB,uBAAuB;IACvB,mBAAmB;IACnB,qBAAqB;IAErB,cAAc;IACd,QAAQ;IACR,oBAAoB;IACpB,eAAe;IACf,WAAW;IACX,qBAAqB;IACrB,UAAU;IACV,UAAU;IACV,YAAY;IACZ,aAAa;IACb,YAAY;IACZ,iBAAiB;IACjB,0BAA0B;IAC1B,sBAAsB;IACtB,eAAe;IACf,cAAc;IACd,WAAW;IACX,gBAAgB;IAChB,iBAAiB;IACjB,wBAAwB;IACxB,yBAAyB;IACzB,SAAS;IACT,iBAAiB;IACjB,iBAAiB;IACjB,kBAAkB;IAClB,uBAAuB;IACvB,kBAAkB;IAClB,oBAAoB;IACpB,aAAa;IACb,eAAe;AAGjB;AAEA,sBAAsB;AACtB,MAAM,iBAA+B;IACnC,4BAA4B;IAC5B,aAAa;IACb,UAAU;IACV,gBAAgB;IAChB,YAAY;IACZ,UAAU;IACV,WAAW;IACX,iBAAiB;IAEjB,qBAAqB;IACrB,iBAAiB;IACjB,oBAAoB;IACpB,gBAAgB;IAChB,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,SAAS;IAET,UAAU;IACV,WAAW;IACX,eAAe;IACf,aAAa;IACb,aAAa;IACb,aAAa;IAEb,aAAa;IACb,iBAAiB;IACjB,oBAAoB;IACpB,gBAAgB;IAChB,oBAAoB;IACpB,uBAAuB;IACvB,mBAAmB;IACnB,sBAAsB;IAEtB,UAAU;IACV,SAAS;IACT,UAAU;IAEV,UAAU;IACV,gBAAgB;IAChB,kBAAkB;IAClB,mBAAmB;IACnB,yBAAyB;IACzB,WAAW;IACX,SAAS;IACT,WAAW;IAEX,oBAAoB;IACpB,iBAAiB;IACjB,iBAAiB;IACjB,cAAc;IACd,mBAAmB;IAEnB,eAAe;IACf,iBAAiB;IACjB,iBAAiB;IACjB,cAAc;IACd,gBAAgB;IAChB,UAAU;IACV,SAAS;IACT,eAAe;IACf,wBAAwB;IAExB,oBAAoB;IACpB,cAAc;IACd,iBAAiB;IACjB,cAAc;IACd,cAAc;IAEd,OAAO;IACP,YAAY;IACZ,UAAU;IACV,WAAW;IACX,mBAAmB;IACnB,iBAAiB;IAEjB,iBAAiB;IACjB,wBAAwB;IACxB,oBAAoB;IACpB,uBAAuB;IACvB,mBAAmB;IACnB,eAAe;IACf,MAAM;IACN,aAAa;IACb,sBAAsB;IAEtB,yBAAyB;IACzB,mBAAmB;IACnB,kBAAkB;IAClB,8BAA8B;IAC9B,cAAc;IACd,2BAA2B;IAE3B,qBAAqB;IACrB,wBAAwB;IACxB,wBAAwB;IACxB,gCAAgC;IAChC,iBAAiB;IACjB,eAAe;IACf,kBAAkB;IAClB,qBAAqB;IACrB,wBAAwB;IACxB,0BAA0B;IAC1B,wBAAwB;IACxB,sBAAsB;IACtB,kBAAkB;IAClB,gBAAgB;IAChB,8BAA8B;IAE9B,qBAAqB;IACrB,sBAAsB;IACtB,WAAW;IACX,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,UAAU;IACV,QAAQ;IACR,UAAU;IACV,YAAY;IACZ,mBAAmB;IACnB,YAAY;IACZ,QAAQ;IACR,UAAU;IACV,OAAO;IACP,UAAU;IACV,UAAU;IAEV,eAAe;IACf,sBAAsB;IACtB,kBAAkB;IAClB,0BAA0B;IAC1B,sBAAsB;IACtB,iBAAiB;IACjB,cAAc;IACd,oBAAoB;IACpB,uBAAuB;IACvB,gBAAgB;IAChB,eAAe;IACf,gBAAgB;IAChB,WAAW;IACX,cAAc;IACd,iBAAiB;IACjB,cAAc;IACd,cAAc;IACd,OAAO;IACP,sBAAsB;IACtB,kBAAkB;IAClB,sBAAsB;IAEtB,iBAAiB;IACjB,wBAAwB;IACxB,QAAQ;IACR,eAAe;IACf,eAAe;IACf,UAAU;IACV,cAAc;IACd,iBAAiB;IACjB,mBAAmB;IACnB,mBAAmB;IACnB,sBAAsB;IACtB,YAAY;IACZ,0BAA0B;IAC1B,cAAc;IACd,SAAS;IACT,QAAQ;IACR,mCAAmC;IACnC,eAAe;IACf,iBAAiB;IACjB,YAAY;IACZ,UAAU;IACV,uBAAuB;IACvB,iBAAiB;IACjB,WAAW;IACX,QAAQ;IACR,uBAAuB;IACvB,gBAAgB;IAChB,eAAe;IACf,2BAA2B;IAC3B,gBAAgB;IAChB,kBAAkB;IAClB,qBAAqB;IACrB,iBAAiB;IACjB,SAAS;IACT,eAAe;IAEf,mBAAmB;IACnB,YAAY;IACZ,QAAQ;IACR,mBAAmB;IACnB,oBAAoB;IACpB,UAAU;IACV,kBAAkB;IAClB,sBAAsB;IAEtB,iBAAiB;IACjB,qBAAqB;IACrB,WAAW;IACX,oBAAoB;IACpB,YAAY;IACZ,+BAA+B;IAC/B,sBAAsB;IACtB,YAAY;IACZ,OAAO;IACP,YAAY;IAEZ,sBAAsB;IACtB,cAAc;IACd,wBAAwB;IACxB,yBAAyB;IACzB,sBAAsB;IACtB,wBAAwB;IACxB,kBAAkB;IAClB,8BAA8B;IAC9B,wBAAwB;IACxB,sBAAsB;IACtB,0BAA0B;IAC1B,qBAAqB;IACrB,gBAAgB;IAChB,sBAAsB;IACtB,iBAAiB;IACjB,mBAAmB;IACnB,kBAAkB;IAClB,gCAAgC;IAChC,uBAAuB;IACvB,UAAU;IACV,UAAU;IACV,gBAAgB;IAChB,wBAAwB;IACxB,iBAAiB;IACjB,iBAAiB;IAEjB,iBAAiB;IACjB,sBAAsB;IACtB,yBAAyB;IACzB,uBAAuB;IACvB,uBAAuB;IACvB,YAAY;IACZ,0BAA0B;IAC1B,SAAS;IACT,SAAS;IACT,QAAQ;IACR,eAAe;IACf,iBAAiB;IACjB,YAAY;IACZ,UAAU;IACV,iBAAiB;IACjB,WAAW;IACX,QAAQ;IACR,gBAAgB;IAChB,eAAe;IACf,iBAAiB;IAEjB,cAAc;IACd,kBAAkB;IAClB,sBAAsB;IACtB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IAEpB,kBAAkB;IAClB,iBAAiB;IACjB,SAAS;IACT,oBAAoB;IACpB,yBAAyB;IACzB,8BAA8B;IAC9B,4BAA4B;IAE5B,oBAAoB;IACpB,qBAAqB;IACrB,qBAAqB;IACrB,sBAAsB;IACtB,sBAAsB;IACtB,2BAA2B;IAC3B,wBAAwB;IACxB,gBAAgB;IAChB,eAAe;IACf,QAAQ;IACR,cAAc;IACd,kBAAkB;IAClB,eAAe;IACf,8BAA8B;IAC9B,eAAe;IACf,cAAc;IACd,qBAAqB;IACrB,0BAA0B;IAC1B,cAAc;IAEd,qBAAqB;IACrB,wBAAwB;IACxB,qBAAqB;IACrB,qBAAqB;IACrB,0BAA0B;IAC1B,aAAa;IACb,cAAc;IACd,gBAAgB;IAChB,aAAa;IACb,UAAU;IACV,kBAAkB;IAClB,iBAAiB;IACjB,gBAAgB;IAChB,oBAAoB;IACpB,uBAAuB;IACvB,qBAAqB;IACrB,kBAAkB;IAClB,iBAAiB;IACjB,gBAAgB;IAChB,iBAAiB;IACjB,gBAAgB;IAChB,iBAAiB;IACjB,iBAAiB;IACjB,oBAAoB;IACpB,gBAAgB;IAChB,SAAS;IAET,mBAAmB;IACnB,wBAAwB;IACxB,wBAAwB;IACxB,uBAAuB;IACvB,0BAA0B;IAC1B,yBAAyB;IACzB,yBAAyB;IACzB,0BAA0B;IAC1B,sBAAsB;IACtB,oBAAoB;IACpB,sBAAsB;IACtB,oBAAoB;IACpB,kBAAkB;IAClB,8BAA8B;IAC9B,uBAAuB;IACvB,sBAAsB;IACtB,mBAAmB;IACnB,kBAAkB;IAClB,eAAe;IACf,qBAAqB;IACrB,kBAAkB;IAClB,kBAAkB;IAClB,eAAe;IACf,sBAAsB;IACtB,qBAAqB;IACrB,UAAU;IACV,cAAc;IACd,eAAe;IACf,gBAAgB;IAChB,yBAAyB;IACzB,UAAU;IACV,UAAU;IACV,YAAY;IACZ,UAAU;IACV,gBAAgB;IAChB,cAAc;IACd,oBAAoB;IACpB,oBAAoB;IACpB,aAAa;IACb,iBAAiB;IACjB,kBAAkB;IAClB,0BAA0B;IAE1B,qBAAqB;IACrB,WAAW;IACX,2BAA2B;IAC3B,4BAA4B;IAC5B,wBAAwB;IACxB,mCAAmC;IACnC,gBAAgB;IAChB,WAAW;IACX,aAAa;IACb,aAAa;IACb,aAAa;IACb,aAAa;IACb,SAAS;IACT,YAAY;IACZ,aAAa;IACb,mBAAmB;IACnB,yBAAyB;IACzB,eAAe;IACf,uBAAuB;IACvB,aAAa;IACb,cAAc;IACd,WAAW;IACX,uBAAuB;IACvB,iBAAiB;IACjB,sBAAsB;IACtB,eAAe;IACf,MAAM;IACN,MAAM;IAEN,0BAA0B;IAC1B,iBAAiB;IACjB,6BAA6B;IAC7B,qBAAqB;IACrB,wBAAwB;IACxB,qBAAqB;IACrB,kBAAkB;IAClB,eAAe;IACf,8BAA8B;IAC9B,iCAAiC;IACjC,eAAe;IACf,2BAA2B;IAC3B,aAAa;IACb,sBAAsB;IACtB,iBAAiB;IACjB,0BAA0B;IAC1B,iBAAiB;IACjB,mBAAmB;IACnB,sBAAsB;IACtB,YAAY;IACZ,0BAA0B;IAC1B,SAAS;IACT,SAAS;IACT,QAAQ;IACR,yBAAyB;IACzB,eAAe;IACf,iBAAiB;IACjB,YAAY;IACZ,UAAU;IACV,uBAAuB;IACvB,iBAAiB;IACjB,WAAW;IACX,QAAQ;IACR,uBAAuB;IACvB,gBAAgB;IAChB,eAAe;IACf,oBAAoB;IACpB,gCAAgC;IAChC,eAAe;IACf,UAAU;IACV,cAAc;IACd,UAAU;IACV,kBAAkB;IAClB,wBAAwB;IACxB,uBAAuB;IACvB,mBAAmB;IACnB,qBAAqB;IAErB,eAAe;IACf,QAAQ;IACR,oBAAoB;IACpB,eAAe;IACf,WAAW;IACX,qBAAqB;IACrB,UAAU;IACV,UAAU;IACV,YAAY;IACZ,aAAa;IACb,YAAY;IACZ,iBAAiB;IACjB,0BAA0B;IAC1B,sBAAsB;IACtB,eAAe;IACf,cAAc;IACd,WAAW;IACX,gBAAgB;IAChB,iBAAiB;IACjB,wBAAwB;IACxB,yBAAyB;IACzB,SAAS;IACT,iBAAiB;IACjB,iBAAiB;IACjB,kBAAkB;IAClB,uBAAuB;IACvB,kBAAkB;IAClB,oBAAoB;IACpB,aAAa;IACb,eAAe;IAEf,eAAe;IACf,iBAAiB;IAEjB,aAAa;IACb,iBAAiB;IACjB,yBAAyB;IACzB,yBAAyB;IACzB,2BAA2B;IAC3B,4BAA4B;IAC5B,yBAAyB;IACzB,qBAAqB;AACvB;AAGO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,IAAI,eAAe;YACjB,YAAY;QACd;IACF,GAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,iBAAiB,CAAC;QACtB,YAAY;QACZ,aAAa,OAAO,CAAC,sBAAsB;IAC7C;IAEA,eAAe;IACf,MAAM,IAAI,CAAC;QACT,MAAM,eAAe,aAAa,OAAO,iBAAiB;QAC1D,MAAM,cAAc,YAAY,CAAC,IAAI;QAErC,IAAI,OAAO,gBAAgB,UAAU;YACnC,OAAO;QACT;QAEA,yCAAyC;QACzC,OAAO;IACT;IAEA,0BAA0B;IAC1B,MAAM,WAAW,aAAa;IAC9B,MAAM,YAAY,aAAa;IAE/B,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1357, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/components/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuthStore } from '@/store/authStore'\nimport { motion } from 'framer-motion'\nimport { Shield, AlertCircle } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requiredRole?: 'admin' | 'worker'\n  redirectTo?: string\n}\n\nexport default function ProtectedRoute({ \n  children, \n  requiredRole, \n  redirectTo = '/login' \n}: ProtectedRouteProps) {\n  const { user, isLoading, checkAuth } = useAuthStore()\n  const router = useRouter()\n  const [isChecking, setIsChecking] = useState(true)\n\n  useEffect(() => {\n    const initAuth = async () => {\n      await checkAuth()\n      setIsChecking(false)\n    }\n    \n    initAuth()\n  }, [checkAuth])\n\n  useEffect(() => {\n    if (!isChecking && !isLoading) {\n      if (!user) {\n        router.push(redirectTo)\n        return\n      }\n\n      if (requiredRole && user.role !== requiredRole) {\n        router.push('/dashboard')\n        return\n      }\n\n      if (!user.is_active) {\n        router.push('/login')\n        return\n      }\n    }\n  }, [user, isChecking, isLoading, requiredRole, router, redirectTo])\n\n  // عرض شاشة التحميل أثناء التحقق من المصادقة\n  if (isChecking || isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center\">\n        <motion.div\n          initial={{ opacity: 0, scale: 0.9 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.5 }}\n          className=\"text-center\"\n        >\n          <div className=\"w-16 h-16 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <Shield className=\"w-8 h-8 text-white animate-pulse\" />\n          </div>\n          <div className=\"w-12 h-12 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 font-medium\">جاري التحقق من الصلاحيات...</p>\n        </motion.div>\n      </div>\n    )\n  }\n\n  // عرض رسالة خطأ إذا لم يكن المستخدم مخول\n  if (!user) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center max-w-md mx-auto px-4\"\n        >\n          <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <AlertCircle className=\"w-8 h-8 text-red-600\" />\n          </div>\n          <h2 className=\"text-2xl font-bold text-gray-800 mb-4\">غير مخول للوصول</h2>\n          <p className=\"text-gray-600 mb-6\">\n            يجب تسجيل الدخول للوصول إلى هذه الصفحة\n          </p>\n          <button\n            onClick={() => router.push('/login')}\n            className=\"btn-primary px-6 py-3\"\n          >\n            تسجيل الدخول\n          </button>\n        </motion.div>\n      </div>\n    )\n  }\n\n  if (requiredRole && user.role !== requiredRole) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center max-w-md mx-auto px-4\"\n        >\n          <div className=\"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <AlertCircle className=\"w-8 h-8 text-yellow-600\" />\n          </div>\n          <h2 className=\"text-2xl font-bold text-gray-800 mb-4\">صلاحيات غير كافية</h2>\n          <p className=\"text-gray-600 mb-6\">\n            ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة\n          </p>\n          <button\n            onClick={() => router.push('/dashboard')}\n            className=\"btn-primary px-6 py-3\"\n          >\n            العودة إلى لوحة التحكم\n          </button>\n        </motion.div>\n      </div>\n    )\n  }\n\n  if (!user.is_active) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center max-w-md mx-auto px-4\"\n        >\n          <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <AlertCircle className=\"w-8 h-8 text-gray-600\" />\n          </div>\n          <h2 className=\"text-2xl font-bold text-gray-800 mb-4\">حساب غير نشط</h2>\n          <p className=\"text-gray-600 mb-6\">\n            تم إلغاء تفعيل حسابك. يرجى التواصل مع المدير.\n          </p>\n          <button\n            onClick={() => router.push('/login')}\n            className=\"btn-primary px-6 py-3\"\n          >\n            تسجيل الدخول\n          </button>\n        </motion.div>\n      </div>\n    )\n  }\n\n  // عرض المحتوى إذا كان كل شيء صحيح\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AANA;;;;;;;AAce,SAAS,eAAe,EACrC,QAAQ,EACR,YAAY,EACZ,aAAa,QAAQ,EACD;IACpB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAClD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,MAAM;YACN,cAAc;QAChB;QAEA;IACF,GAAG;QAAC;KAAU;IAEd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,cAAc,CAAC,WAAW;YAC7B,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,gBAAgB,KAAK,IAAI,KAAK,cAAc;gBAC9C,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,CAAC,KAAK,SAAS,EAAE;gBACnB,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;IACF,GAAG;QAAC;QAAM;QAAY;QAAW;QAAc;QAAQ;KAAW;IAElE,4CAA4C;IAC5C,IAAI,cAAc,WAAW;QAC3B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAI;gBAClC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAA4B;;;;;;;;;;;;;;;;;IAIjD;IAEA,yCAAyC;IACzC,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAEzB,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,IAAI,gBAAgB,KAAK,IAAI,KAAK,cAAc;QAC9C,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAEzB,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,IAAI,CAAC,KAAK,SAAS,EAAE;QACnB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAEzB,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,kCAAkC;IAClC,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 1685, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-al<PERSON>/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { motion } from 'framer-motion'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { useAuthStore } from '@/store/authStore'\nimport { useDataStore } from '@/store/dataStore'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport ProtectedRoute from '@/components/ProtectedRoute'\nimport {\n  BarChart3,\n  Users,\n  Calendar,\n  Package,\n  Settings,\n  LogOut,\n  TrendingUp,\n  Clock,\n  CheckCircle,\n  Plus,\n  ArrowRight,\n  Languages\n} from 'lucide-react'\n\nfunction DashboardContent() {\n  const { user, signOut } = useAuthStore()\n  const { orders, appointments, getStats } = useDataStore()\n  const { t, language, changeLanguage, isArabic } = useTranslation()\n  const router = useRouter()\n\n\n\n  const handleSignOut = async () => {\n    await signOut()\n    router.push('/')\n  }\n\n\n\n  // حساب الإحصائيات الحقيقية\n  const realStats = getStats()\n\n  // حساب المواعيد اليوم\n  const todayAppointments = appointments.filter(appointment => {\n    const today = new Date().toISOString().split('T')[0]\n    return appointment.appointmentDate === today && appointment.status !== 'cancelled'\n  }).length\n\n  // الإحصائيات حسب الدور\n  const getStatsForRole = () => {\n    if (user.role === 'worker') {\n      // إحصائيات العامل - طلباته فقط\n      const workerOrders = orders.filter(order => order.assignedWorker === user.id)\n      const workerCompletedOrders = workerOrders.filter(order => order.status === 'completed')\n      const workerActiveOrders = workerOrders.filter(order => ['pending', 'in_progress'].includes(order.status))\n\n      return [\n        {\n          title: t('my_active_orders'),\n          value: workerActiveOrders.length.toString(),\n          change: '+0%',\n          icon: Package,\n          color: 'from-blue-400 to-blue-600'\n        },\n        {\n          title: t('my_completed_orders'),\n          value: workerCompletedOrders.length.toString(),\n          change: '+0%',\n          icon: CheckCircle,\n          color: 'from-green-400 to-green-600'\n        },\n        {\n          title: t('my_total_orders'),\n          value: workerOrders.length.toString(),\n          change: '+0%',\n          icon: Users,\n          color: 'from-purple-400 to-purple-600'\n        }\n      ]\n    } else {\n      // إحصائيات المدير - جميع البيانات\n      return [\n        {\n          title: t('active_orders'),\n          value: realStats.activeOrders.toString(),\n          change: '+0%',\n          icon: Package,\n          color: 'from-blue-400 to-blue-600'\n        },\n        {\n          title: t('today_appointments'),\n          value: todayAppointments.toString(),\n          change: '+0',\n          icon: Calendar,\n          color: 'from-green-400 to-green-600'\n        },\n        {\n          title: t('completed_orders'),\n          value: realStats.completedOrders.toString(),\n          change: '+0%',\n          icon: CheckCircle,\n          color: 'from-purple-400 to-purple-600'\n        },\n        {\n          title: t('total_orders'),\n          value: realStats.totalOrders.toString(),\n          change: '+0%',\n          icon: Users,\n          color: 'from-pink-400 to-pink-600'\n        }\n      ]\n    }\n  }\n\n  const stats = getStatsForRole()\n\n  // أحدث الطلبات (آخر 3 طلبات) - مفلترة حسب الدور\n  const recentOrders = orders\n    .filter(order => {\n      // إذا كان المستخدم عامل، اعرض طلباته فقط\n      if (user.role === 'worker') {\n        return order.assignedWorker === user.id\n      }\n      // إذا كان مدير، اعرض جميع الطلبات\n      return true\n    })\n    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())\n    .slice(0, 3)\n    .map(order => ({\n      id: order.id,\n      client: order.clientName,\n      type: order.description,\n      status: order.status,\n      dueDate: order.dueDate\n    }))\n\n  const getStatusColor = (status: string) => {\n    const colors = {\n      pending: 'bg-yellow-100 text-yellow-800',\n      in_progress: 'bg-blue-100 text-blue-800',\n      completed: 'bg-green-100 text-green-800',\n      delivered: 'bg-purple-100 text-purple-800'\n    }\n    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'\n  }\n\n  const getStatusLabel = (status: string) => {\n    const labels = {\n      pending: t('pending'),\n      in_progress: t('in_progress'),\n      completed: t('completed'),\n      delivered: t('delivered')\n    }\n    return labels[status as keyof typeof labels] || status\n  }\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString)\n    return date.toLocaleDateString('ar-SA', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    })\n  }\n\n  const getStatusInfo = (status: string) => {\n    const statusMap = {\n      pending: { label: t('pending'), color: 'text-yellow-600', bgColor: 'bg-yellow-100' },\n      in_progress: { label: t('in_progress'), color: 'text-blue-600', bgColor: 'bg-blue-100' },\n      completed: { label: t('completed'), color: 'text-green-600', bgColor: 'bg-green-100' },\n      delivered: { label: t('delivered'), color: 'text-purple-600', bgColor: 'bg-purple-100' },\n      cancelled: { label: t('cancelled'), color: 'text-red-600', bgColor: 'bg-red-100' }\n    }\n    return statusMap[status as keyof typeof statusMap] || statusMap.pending\n  }\n\n\n\n  return (\n    <>\n      <style jsx global>{`\n        /* ضمان ظهور زر تغيير اللغة في جميع الأوضاع */\n        button[title*=\"تغيير اللغة\"],\n        button[title*=\"Change Language\"] {\n          display: block !important;\n          visibility: visible !important;\n          opacity: 1 !important;\n          position: relative !important;\n          z-index: 99999 !important;\n          flex-shrink: 0 !important;\n        }\n\n        /* للشاشات الكبيرة جداً */\n        @media (min-width: 1920px) {\n          button[title*=\"تغيير اللغة\"],\n          button[title*=\"Change Language\"] {\n            display: block !important;\n            visibility: visible !important;\n            opacity: 1 !important;\n          }\n        }\n\n        /* للشاشات 4K وما فوق */\n        @media (min-width: 2560px) {\n          button[title*=\"تغيير اللغة\"],\n          button[title*=\"Change Language\"] {\n            display: block !important;\n            visibility: visible !important;\n            opacity: 1 !important;\n          }\n        }\n\n        /* وضع الشاشة الكاملة */\n        @media (display-mode: fullscreen) {\n          button[title*=\"تغيير اللغة\"],\n          button[title*=\"Change Language\"] {\n            display: block !important;\n            visibility: visible !important;\n            opacity: 1 !important;\n          }\n        }\n\n        /* للتأكد من عدم إخفاء الزر بواسطة overflow */\n        .language-btn-container {\n          overflow: visible !important;\n        }\n      `}</style>\n      <div className=\"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50\">\n        {/* الهيدر المحسن */}\n      <header className=\"bg-white/80 backdrop-blur-md border-b border-pink-100 shadow-sm\">\n        <div className=\"w-full max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8\">\n          {/* هيدر للشاشات الكبيرة */}\n          <div className=\"hidden lg:flex items-center justify-between h-16\">\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <Link\n                href=\"/\"\n                className=\"text-pink-600 hover:text-pink-700 transition-colors duration-300 group flex items-center space-x-2 space-x-reverse\"\n              >\n                <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform duration-300\" />\n                <span className=\"text-sm font-medium\">{t('homepage')}</span>\n              </Link>\n              <div className=\"w-px h-6 bg-gray-300\"></div>\n              <div className=\"max-w-md\">\n                <h1 className=\"text-xl xl:text-2xl font-bold text-gray-800 truncate\">\n                  {t('welcome_back')}، {user.full_name || user.email}\n                </h1>\n                <p className=\"text-gray-600 text-sm\">\n                  {user.role === 'admin' ? t('admin_dashboard') : t('worker_dashboard')}\n                </p>\n              </div>\n              <span className=\"px-3 py-1 bg-gradient-to-r from-pink-100 to-rose-100 text-pink-700 rounded-full text-sm font-medium whitespace-nowrap\">\n                {user.role === 'admin' ? t('admin') : t('worker')}\n              </span>\n            </div>\n\n            <div className=\"flex items-center space-x-4 space-x-reverse language-btn-container\">\n              <div className=\"flex items-center space-x-3 space-x-reverse language-btn-container\">\n                <div className=\"text-right max-w-xs\">\n                  <p className=\"font-medium text-gray-800 truncate\">{user.full_name}</p>\n                  <p className=\"text-sm text-gray-600 truncate\">{user.email}</p>\n                </div>\n\n                {/* زر تغيير اللغة للشاشات الكبيرة */}\n                <button\n                  onClick={() => changeLanguage(language === 'ar' ? 'en' : 'ar')}\n                  className=\"px-3 py-1.5 text-sm text-gray-600 hover:text-pink-600 bg-gray-100 hover:bg-pink-50 rounded-full transition-all duration-300 font-medium min-w-[70px] text-center\"\n                  title={t('change_language')}\n                  style={{\n                    display: 'block !important',\n                    visibility: 'visible !important',\n                    position: 'relative',\n                    zIndex: 99999,\n                    minWidth: '70px',\n                    flexShrink: 0,\n                    opacity: 1\n                  }}\n                >\n                  {language === 'ar' ? 'English' : 'عربي'}\n                </button>\n\n                <button\n                  onClick={handleSignOut}\n                  className=\"p-2 text-gray-600 hover:text-red-600 transition-colors duration-300\"\n                  title={t('logout')}\n                >\n                  <LogOut className=\"w-5 h-5\" />\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* هيدر للشاشات الصغيرة والمتوسطة */}\n          <div className=\"lg:hidden\">\n            {/* الصف الأول */}\n            <div className=\"flex items-center justify-between h-14 sm:h-16\">\n              <div className=\"flex items-center space-x-2 space-x-reverse min-w-0 flex-1 overflow-hidden\">\n                <Link\n                  href=\"/\"\n                  className=\"text-pink-600 hover:text-pink-700 transition-colors duration-300 group flex items-center space-x-1 space-x-reverse flex-shrink-0\"\n                >\n                  <ArrowRight className=\"w-4 h-4 sm:w-5 sm:h-5 group-hover:translate-x-1 transition-transform duration-300\" />\n                  <span className=\"text-xs sm:text-sm font-medium hidden sm:inline whitespace-nowrap\">{t('homepage')}</span>\n                  <span className=\"text-xs font-medium sm:hidden whitespace-nowrap\">{t('home')}</span>\n                </Link>\n                <div className=\"w-px h-4 sm:h-6 bg-gray-300 flex-shrink-0\"></div>\n                <div className=\"min-w-0 flex-1 overflow-hidden\">\n                  <h1 className=\"text-sm sm:text-lg md:text-xl font-bold text-gray-800 truncate\">\n                    {t('welcome_back')}، {user.full_name || user.email}\n                  </h1>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-1 sm:space-x-2 space-x-reverse flex-shrink-0 min-w-0 language-btn-container\">\n                <span className=\"px-2 sm:px-3 py-1 bg-gradient-to-r from-pink-100 to-rose-100 text-pink-700 rounded-full text-xs sm:text-sm font-medium whitespace-nowrap\">\n                  {t(user.role === 'admin' ? 'admin' : 'worker')}\n                </span>\n                <button\n                  onClick={() => changeLanguage(language === 'ar' ? 'en' : 'ar')}\n                  className=\"px-2 sm:px-3 py-1 text-xs sm:text-sm text-gray-600 hover:text-pink-600 bg-gray-100 hover:bg-pink-50 rounded-full transition-all duration-300 flex-shrink-0 font-medium min-w-[60px] sm:min-w-[70px] text-center\"\n                  title={t('change_language')}\n                  style={{\n                    display: 'block !important',\n                    visibility: 'visible !important',\n                    position: 'relative',\n                    zIndex: 99999,\n                    minWidth: '60px',\n                    flexShrink: 0,\n                    opacity: 1\n                  }}\n                >\n                  {language === 'ar' ? 'English' : 'عربي'}\n                </button>\n\n                <button\n                  onClick={handleSignOut}\n                  className=\"p-1.5 sm:p-2 text-gray-600 hover:text-red-600 transition-colors duration-300 flex-shrink-0\"\n                  title={t('logout')}\n                >\n                  <LogOut className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n                </button>\n              </div>\n            </div>\n\n            {/* الصف الثاني - معلومات المستخدم */}\n            <div className=\"pb-3 sm:pb-4 border-t border-gray-100\">\n              <div className=\"pt-2 sm:pt-3\">\n                <p className=\"text-xs sm:text-sm text-gray-600 mb-1\">\n                  {t('dashboard')} - {t(user.role === 'admin' ? 'admin' : 'worker')}\n                </p>\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"min-w-0 flex-1\">\n                    <p className=\"font-medium text-gray-800 text-sm sm:text-base truncate\">{user.full_name}</p>\n                    <p className=\"text-xs sm:text-sm text-gray-600 truncate\">{user.email}</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"w-full max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8\">\n        {/* الترحيب وأزرار العمل المحسن */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"mb-6 sm:mb-8\"\n        >\n          <div className=\"flex flex-col gap-4 sm:gap-6\">\n            {/* قسم الترحيب */}\n            <div className=\"text-center sm:text-right overflow-hidden\">\n              <h2 className=\"text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-gray-800 mb-2 sm:mb-3 break-words\">\n                <span className=\"block sm:hidden\">{t('welcome_back')}</span>\n                <span className=\"hidden sm:inline\">{t('welcome_back')}، </span>\n                <span className=\"text-pink-600 break-words\">{user.full_name}</span>\n              </h2>\n              <p className=\"text-sm sm:text-base md:text-lg text-gray-600 max-w-2xl mx-auto sm:mx-0 break-words\">\n                {t('overview_today')}\n              </p>\n            </div>\n\n            {/* أزرار العمل للمدير */}\n            {user.role === 'admin' && (\n              <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center sm:justify-start w-full\">\n                <Link\n                  href=\"/dashboard/add-order\"\n                  className=\"btn-primary inline-flex items-center justify-center space-x-2 space-x-reverse px-4 sm:px-6 py-3 sm:py-4 group text-sm sm:text-base w-full sm:w-auto min-w-0 flex-shrink-0\"\n                >\n                  <Plus className=\"w-4 h-4 sm:w-5 sm:h-5 group-hover:scale-110 transition-transform duration-300 flex-shrink-0\" />\n                  <span className=\"whitespace-nowrap\">{t('add_new_order')}</span>\n                </Link>\n\n                <Link\n                  href=\"/book-appointment\"\n                  className=\"btn-secondary inline-flex items-center justify-center space-x-2 space-x-reverse px-4 sm:px-6 py-3 sm:py-4 group text-sm sm:text-base w-full sm:w-auto min-w-0 flex-shrink-0\"\n                >\n                  <Calendar className=\"w-4 h-4 sm:w-5 sm:h-5 group-hover:scale-110 transition-transform duration-300 flex-shrink-0\" />\n                  <span className=\"whitespace-nowrap\">{t('book_appointment')}</span>\n                </Link>\n              </div>\n            )}\n\n            {/* رسالة ترحيب للعامل */}\n            {user.role === 'worker' && (\n              <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg sm:rounded-xl p-4 sm:p-6 text-center sm:text-right overflow-hidden\">\n                <h3 className=\"text-lg sm:text-xl font-semibold text-blue-800 mb-2 break-words\">\n                  {t('welcome_worker')}\n                </h3>\n                <p className=\"text-sm sm:text-base text-blue-600 break-words\">\n                  {t('worker_description')}\n                </p>\n              </div>\n            )}\n          </div>\n        </motion.div>\n\n        {/* الإحصائيات - تصميم مختلف للشاشات الصغيرة والكبيرة */}\n\n        {/* الإحصائيات للشاشات الكبيرة */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"hidden lg:grid lg:grid-cols-4 gap-6 mb-8\"\n        >\n          {stats.map((stat, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}\n              className=\"bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-100 hover:shadow-lg transition-all duration-300\"\n            >\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-lg flex items-center justify-center`}>\n                  <stat.icon className=\"w-6 h-6 text-white\" />\n                </div>\n                <span className=\"text-sm font-medium text-green-600 flex items-center space-x-1\">\n                  <TrendingUp className=\"w-4 h-4\" />\n                  <span>{stat.change}</span>\n                </span>\n              </div>\n\n              <h3 className=\"text-2xl font-bold text-gray-800 mb-1\">{stat.value}</h3>\n              <p className=\"text-gray-600 text-sm\">{stat.title}</p>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* الإحصائيات للشاشات الصغيرة - تصميم مضغوط */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"block lg:hidden mb-8\"\n        >\n          <div className=\"bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-pink-100\">\n            <h3 className=\"text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2 space-x-reverse\">\n              <BarChart3 className=\"w-5 h-5 text-pink-600\" />\n              <span>{t('statistics')}</span>\n            </h3>\n\n            <div className=\"grid grid-cols-3 gap-3\">\n              {stats.map((stat, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}\n                  className=\"text-center p-3 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border border-gray-200\"\n                >\n                  <div className={`w-8 h-8 bg-gradient-to-r ${stat.color} rounded-lg flex items-center justify-center mx-auto mb-2`}>\n                    <stat.icon className=\"w-4 h-4 text-white\" />\n                  </div>\n\n                  <h4 className=\"text-lg font-bold text-gray-800 mb-1\">{stat.value}</h4>\n                  <p className=\"text-xs text-gray-600 leading-tight\">{stat.title}</p>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n        </motion.div>\n\n        {/* ترتيب مختلف للشاشات الصغيرة والكبيرة */}\n        <div className=\"block lg:hidden space-y-8\">\n          {/* الطلبات الحديثة - للشاشات الصغيرة (في الأعلى) */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n            className=\"bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-100\"\n          >\n            <h3 className=\"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse\">\n              <Package className=\"w-5 h-5 text-pink-600\" />\n              <span>{t('recent_orders')}</span>\n            </h3>\n\n            <div className=\"space-y-4\">\n              {recentOrders.map((order, index) => (\n                <motion.div\n                  key={order.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.4, delay: 0.5 + index * 0.1 }}\n                  className=\"flex items-center justify-between p-4 bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg border border-pink-200\"\n                >\n                  <div>\n                    <h4 className=\"font-medium text-gray-800\">{order.client}</h4>\n                    <p className=\"text-sm text-gray-600\">{order.type}</p>\n                    <p className=\"text-xs text-gray-500\">#{order.id}</p>\n                  </div>\n\n                  <div className=\"text-right flex items-center space-x-2 space-x-reverse\">\n                    <div>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusInfo(order.status).bgColor} ${getStatusInfo(order.status).color}`}>\n                        {getStatusInfo(order.status).label}\n                      </span>\n                      <p className=\"text-xs text-gray-500 mt-1\">{formatDate(order.dueDate)}</p>\n                    </div>\n\n\n                  </div>\n                </motion.div>\n              ))}\n\n              {recentOrders.length === 0 && (\n                <div className=\"text-center py-8\">\n                  <Package className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n                  <p className=\"text-gray-500\">{t('no_orders_found')}</p>\n                </div>\n              )}\n            </div>\n\n            <Link\n              href=\"/dashboard/orders\"\n              className=\"w-full mt-4 btn-secondary py-2 text-sm inline-flex items-center justify-center\"\n            >\n              {t('view_all')} {t('orders')}\n            </Link>\n          </motion.div>\n\n\n\n          {/* الإجراءات السريعة - للمدير فقط في الشاشات الصغيرة */}\n          {user.role === 'admin' && (\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.6 }}\n              className=\"bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-100\"\n            >\n              <h3 className=\"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse\">\n                <Settings className=\"w-5 h-5 text-pink-600\" />\n                <span>{t('quick_actions')}</span>\n              </h3>\n\n              <div className=\"grid gap-4 grid-cols-2\">\n                <Link\n                  href=\"/dashboard/add-order\"\n                  className=\"p-4 bg-gradient-to-r from-pink-50 to-pink-100 rounded-lg border border-pink-200 hover:shadow-md transition-all duration-300 text-center block\"\n                >\n                  <Plus className=\"w-6 h-6 text-pink-600 mx-auto mb-2\" />\n                  <span className=\"text-sm font-medium text-pink-800\">{t('add_new_order')}</span>\n                </Link>\n\n                <Link\n                  href=\"/dashboard/workers\"\n                  className=\"p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200 hover:shadow-md transition-all duration-300 text-center block\"\n                >\n                  <Users className=\"w-6 h-6 text-blue-600 mx-auto mb-2\" />\n                  <span className=\"text-sm font-medium text-blue-800\">{t('worker_management')}</span>\n                </Link>\n\n                <Link\n                  href=\"/dashboard/appointments\"\n                  className=\"p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border border-purple-200 hover:shadow-md transition-all duration-300 text-center block col-span-2\"\n                >\n                  <Calendar className=\"w-6 h-6 text-purple-600 mx-auto mb-2\" />\n                  <span className=\"text-sm font-medium text-purple-800\">{t('appointments')}</span>\n                </Link>\n              </div>\n\n              <div className=\"mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200\">\n                <h4 className=\"font-medium text-gray-800 mb-2\">{t('reminder')}</h4>\n                <p className=\"text-sm text-gray-600\">\n                  {t('you_have')} {todayAppointments} {t('today_appointments_reminder')} {t('and')} {realStats.activeOrders} {t('orders_need_follow')}\n                </p>\n              </div>\n            </motion.div>\n          )}\n\n\n\n          {/* التقارير - للشاشات الصغيرة في الأسفل */}\n          {user.role === 'admin' && (\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.8 }}\n              className=\"bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-100\"\n            >\n              <h3 className=\"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse\">\n                <BarChart3 className=\"w-5 h-5 text-green-600\" />\n                <span>{t('reports')}</span>\n              </h3>\n\n              <Link\n                href=\"/dashboard/reports\"\n                className=\"w-full p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200 hover:shadow-md transition-all duration-300 text-center block\"\n              >\n                <BarChart3 className=\"w-8 h-8 text-green-600 mx-auto mb-2\" />\n                <span className=\"text-base font-medium text-green-800\">{t('detailed_reports')}</span>\n              </Link>\n            </motion.div>\n          )}\n        </div>\n\n        {/* التخطيط الأصلي للشاشات الكبيرة */}\n        <div className=\"hidden lg:grid lg:grid-cols-2 gap-8\">\n          {/* الطلبات الحديثة */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n            className=\"bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-100\"\n          >\n            <h3 className=\"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse\">\n              <Package className=\"w-5 h-5 text-pink-600\" />\n              <span>{t('recent_orders')}</span>\n            </h3>\n\n            <div className=\"space-y-4\">\n              {recentOrders.map((order, index) => (\n                <motion.div\n                  key={order.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.4, delay: 0.6 + index * 0.1 }}\n                  className=\"flex items-center justify-between p-4 bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg border border-pink-200\"\n                >\n                  <div>\n                    <h4 className=\"font-medium text-gray-800\">{order.client}</h4>\n                    <p className=\"text-sm text-gray-600\">{order.type}</p>\n                    <p className=\"text-xs text-gray-500\">#{order.id}</p>\n                  </div>\n\n                  <div className=\"text-right flex items-center space-x-2 space-x-reverse\">\n                    <div>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>\n                        {getStatusLabel(order.status)}\n                      </span>\n                      <p className=\"text-xs text-gray-500 mt-1 flex items-center space-x-1 space-x-reverse\">\n                        <Clock className=\"w-3 h-3\" />\n                        <span>{order.dueDate}</span>\n                      </p>\n                    </div>\n\n\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            <Link\n              href=\"/dashboard/orders\"\n              className=\"w-full mt-4 btn-secondary py-2 text-sm inline-flex items-center justify-center\"\n            >\n              {t('view_all')} {t('orders')}\n            </Link>\n          </motion.div>\n\n          {/* الإجراءات السريعة - للمدير فقط */}\n          {user.role === 'admin' && (\n            <motion.div\n              initial={{ opacity: 0, x: 30 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6, delay: 0.6 }}\n              className=\"bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-100\"\n            >\n              <h3 className=\"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse\">\n                <Settings className=\"w-5 h-5 text-pink-600\" />\n                <span>{t('quick_actions')}</span>\n              </h3>\n\n              <div className=\"grid gap-4 grid-cols-2\">\n                <Link\n                  href=\"/dashboard/add-order\"\n                  className=\"p-4 bg-gradient-to-r from-pink-50 to-pink-100 rounded-lg border border-pink-200 hover:shadow-md transition-all duration-300 text-center block\"\n                >\n                  <Plus className=\"w-6 h-6 text-pink-600 mx-auto mb-2\" />\n                  <span className=\"text-sm font-medium text-pink-800\">{t('add_new_order')}</span>\n                </Link>\n\n                <Link\n                  href=\"/dashboard/workers\"\n                  className=\"p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200 hover:shadow-md transition-all duration-300 text-center block\"\n                >\n                  <Users className=\"w-6 h-6 text-blue-600 mx-auto mb-2\" />\n                  <span className=\"text-sm font-medium text-blue-800\">{t('worker_management')}</span>\n                </Link>\n\n                <Link\n                  href=\"/dashboard/reports\"\n                  className=\"p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200 hover:shadow-md transition-all duration-300 text-center block\"\n                >\n                  <BarChart3 className=\"w-6 h-6 text-green-600 mx-auto mb-2\" />\n                  <span className=\"text-sm font-medium text-green-800\">{t('reports')}</span>\n                </Link>\n\n                <Link\n                  href=\"/dashboard/appointments\"\n                  className=\"p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border border-purple-200 hover:shadow-md transition-all duration-300 text-center block\"\n                >\n                  <Calendar className=\"w-6 h-6 text-purple-600 mx-auto mb-2\" />\n                  <span className=\"text-sm font-medium text-purple-800\">{t('appointments')}</span>\n                </Link>\n              </div>\n\n              <div className=\"mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200\">\n                <h4 className=\"font-medium text-gray-800 mb-2\">{t('reminder')}</h4>\n                <p className=\"text-sm text-gray-600\">\n                  {t('you_have')} {todayAppointments} {t('today_appointments_reminder')} {t('and')} {realStats.activeOrders} {t('orders_need_follow')}\n                </p>\n              </div>\n            </motion.div>\n          )}\n        </div>\n      </div>\n    </div>\n    </>\n  )\n}\n\nexport default function DashboardPage() {\n  return (\n    <ProtectedRoute>\n      <DashboardContent />\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;;;AAyBA,SAAS;IACP,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IACrC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IACtD,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAC/D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAIvB,MAAM,gBAAgB;QACpB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAIA,2BAA2B;IAC3B,MAAM,YAAY;IAElB,sBAAsB;IACtB,MAAM,oBAAoB,aAAa,MAAM,CAAC,CAAA;QAC5C,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACpD,OAAO,YAAY,eAAe,KAAK,SAAS,YAAY,MAAM,KAAK;IACzE,GAAG,MAAM;IAET,uBAAuB;IACvB,MAAM,kBAAkB;QACtB,IAAI,KAAK,IAAI,KAAK,UAAU;YAC1B,+BAA+B;YAC/B,MAAM,eAAe,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,cAAc,KAAK,KAAK,EAAE;YAC5E,MAAM,wBAAwB,aAAa,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;YAC5E,MAAM,qBAAqB,aAAa,MAAM,CAAC,CAAA,QAAS;oBAAC;oBAAW;iBAAc,CAAC,QAAQ,CAAC,MAAM,MAAM;YAExG,OAAO;gBACL;oBACE,OAAO,EAAE;oBACT,OAAO,mBAAmB,MAAM,CAAC,QAAQ;oBACzC,QAAQ;oBACR,MAAM,wMAAA,CAAA,UAAO;oBACb,OAAO;gBACT;gBACA;oBACE,OAAO,EAAE;oBACT,OAAO,sBAAsB,MAAM,CAAC,QAAQ;oBAC5C,QAAQ;oBACR,MAAM,2NAAA,CAAA,cAAW;oBACjB,OAAO;gBACT;gBACA;oBACE,OAAO,EAAE;oBACT,OAAO,aAAa,MAAM,CAAC,QAAQ;oBACnC,QAAQ;oBACR,MAAM,oMAAA,CAAA,QAAK;oBACX,OAAO;gBACT;aACD;QACH,OAAO;YACL,kCAAkC;YAClC,OAAO;gBACL;oBACE,OAAO,EAAE;oBACT,OAAO,UAAU,YAAY,CAAC,QAAQ;oBACtC,QAAQ;oBACR,MAAM,wMAAA,CAAA,UAAO;oBACb,OAAO;gBACT;gBACA;oBACE,OAAO,EAAE;oBACT,OAAO,kBAAkB,QAAQ;oBACjC,QAAQ;oBACR,MAAM,0MAAA,CAAA,WAAQ;oBACd,OAAO;gBACT;gBACA;oBACE,OAAO,EAAE;oBACT,OAAO,UAAU,eAAe,CAAC,QAAQ;oBACzC,QAAQ;oBACR,MAAM,2NAAA,CAAA,cAAW;oBACjB,OAAO;gBACT;gBACA;oBACE,OAAO,EAAE;oBACT,OAAO,UAAU,WAAW,CAAC,QAAQ;oBACrC,QAAQ;oBACR,MAAM,oMAAA,CAAA,QAAK;oBACX,OAAO;gBACT;aACD;QACH;IACF;IAEA,MAAM,QAAQ;IAEd,gDAAgD;IAChD,MAAM,eAAe,OAClB,MAAM,CAAC,CAAA;QACN,yCAAyC;QACzC,IAAI,KAAK,IAAI,KAAK,UAAU;YAC1B,OAAO,MAAM,cAAc,KAAK,KAAK,EAAE;QACzC;QACA,kCAAkC;QAClC,OAAO;IACT,GACC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,IAC9E,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAA,QAAS,CAAC;YACb,IAAI,MAAM,EAAE;YACZ,QAAQ,MAAM,UAAU;YACxB,MAAM,MAAM,WAAW;YACvB,QAAQ,MAAM,MAAM;YACpB,SAAS,MAAM,OAAO;QACxB,CAAC;IAEH,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,SAAS;YACT,aAAa;YACb,WAAW;YACX,WAAW;QACb;QACA,OAAO,MAAM,CAAC,OAA8B,IAAI;IAClD;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,SAAS,EAAE;YACX,aAAa,EAAE;YACf,WAAW,EAAE;YACb,WAAW,EAAE;QACf;QACA,OAAO,MAAM,CAAC,OAA8B,IAAI;IAClD;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,YAAY;YAChB,SAAS;gBAAE,OAAO,EAAE;gBAAY,OAAO;gBAAmB,SAAS;YAAgB;YACnF,aAAa;gBAAE,OAAO,EAAE;gBAAgB,OAAO;gBAAiB,SAAS;YAAc;YACvF,WAAW;gBAAE,OAAO,EAAE;gBAAc,OAAO;gBAAkB,SAAS;YAAe;YACrF,WAAW;gBAAE,OAAO,EAAE;gBAAc,OAAO;gBAAmB,SAAS;YAAgB;YACvF,WAAW;gBAAE,OAAO,EAAE;gBAAc,OAAO;gBAAgB,SAAS;YAAa;QACnF;QACA,OAAO,SAAS,CAAC,OAAiC,IAAI,UAAU,OAAO;IACzE;IAIA,qBACE;;;;;;0BAgDE,8OAAC;yDAAc;;kCAEf,8OAAC;iEAAiB;kCAChB,cAAA,8OAAC;qEAAc;;8CAEb,8OAAC;6EAAc;;sDACb,8OAAC;qFAAc;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,8OAAC;qGAAe;sEAAuB,EAAE;;;;;;;;;;;;8DAE3C,8OAAC;6FAAc;;;;;;8DACf,8OAAC;6FAAc;;sEACb,8OAAC;qGAAa;;gEACX,EAAE;gEAAgB;gEAAG,KAAK,SAAS,IAAI,KAAK,KAAK;;;;;;;sEAEpD,8OAAC;qGAAY;sEACV,KAAK,IAAI,KAAK,UAAU,EAAE,qBAAqB,EAAE;;;;;;;;;;;;8DAGtD,8OAAC;6FAAe;8DACb,KAAK,IAAI,KAAK,UAAU,EAAE,WAAW,EAAE;;;;;;;;;;;;sDAI5C,8OAAC;qFAAc;sDACb,cAAA,8OAAC;yFAAc;;kEACb,8OAAC;iGAAc;;0EACb,8OAAC;yGAAY;0EAAsC,KAAK,SAAS;;;;;;0EACjE,8OAAC;yGAAY;0EAAkC,KAAK,KAAK;;;;;;;;;;;;kEAI3D,8OAAC;wDACC,SAAS,IAAM,eAAe,aAAa,OAAO,OAAO;wDAEzD,OAAO,EAAE;wDACT,OAAO;4DACL,SAAS;4DACT,YAAY;4DACZ,UAAU;4DACV,QAAQ;4DACR,UAAU;4DACV,YAAY;4DACZ,SAAS;wDACX;iGAVU;kEAYT,aAAa,OAAO,YAAY;;;;;;kEAGnC,8OAAC;wDACC,SAAS;wDAET,OAAO,EAAE;iGADC;kEAGV,cAAA,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO1B,8OAAC;6EAAc;;sDAEb,8OAAC;qFAAc;;8DACb,8OAAC;6FAAc;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;;8EAEV,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,8OAAC;6GAAe;8EAAqE,EAAE;;;;;;8EACvF,8OAAC;6GAAe;8EAAmD,EAAE;;;;;;;;;;;;sEAEvE,8OAAC;qGAAc;;;;;;sEACf,8OAAC;qGAAc;sEACb,cAAA,8OAAC;yGAAa;;oEACX,EAAE;oEAAgB;oEAAG,KAAK,SAAS,IAAI,KAAK,KAAK;;;;;;;;;;;;;;;;;;8DAKxD,8OAAC;6FAAc;;sEACb,8OAAC;qGAAe;sEACb,EAAE,KAAK,IAAI,KAAK,UAAU,UAAU;;;;;;sEAEvC,8OAAC;4DACC,SAAS,IAAM,eAAe,aAAa,OAAO,OAAO;4DAEzD,OAAO,EAAE;4DACT,OAAO;gEACL,SAAS;gEACT,YAAY;gEACZ,UAAU;gEACV,QAAQ;gEACR,UAAU;gEACV,YAAY;gEACZ,SAAS;4DACX;qGAVU;sEAYT,aAAa,OAAO,YAAY;;;;;;sEAGnC,8OAAC;4DACC,SAAS;4DAET,OAAO,EAAE;qGADC;sEAGV,cAAA,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAMxB,8OAAC;qFAAc;sDACb,cAAA,8OAAC;yFAAc;;kEACb,8OAAC;iGAAY;;4DACV,EAAE;4DAAa;4DAAI,EAAE,KAAK,IAAI,KAAK,UAAU,UAAU;;;;;;;kEAE1D,8OAAC;iGAAc;kEACb,cAAA,8OAAC;qGAAc;;8EACb,8OAAC;6GAAY;8EAA2D,KAAK,SAAS;;;;;;8EACtF,8OAAC;6GAAY;8EAA6C,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASlF,8OAAC;iEAAc;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CAEV,cAAA,8OAAC;6EAAc;;sDAEb,8OAAC;qFAAc;;8DACb,8OAAC;6FAAa;;sEACZ,8OAAC;qGAAe;sEAAmB,EAAE;;;;;;sEACrC,8OAAC;qGAAe;;gEAAoB,EAAE;gEAAgB;;;;;;;sEACtD,8OAAC;qGAAe;sEAA6B,KAAK,SAAS;;;;;;;;;;;;8DAE7D,8OAAC;6FAAY;8DACV,EAAE;;;;;;;;;;;;wCAKN,KAAK,IAAI,KAAK,yBACb,8OAAC;qFAAc;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;qGAAe;sEAAqB,EAAE;;;;;;;;;;;;8DAGzC,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;qGAAe;sEAAqB,EAAE;;;;;;;;;;;;;;;;;;wCAM5C,KAAK,IAAI,KAAK,0BACb,8OAAC;qFAAc;;8DACb,8OAAC;6FAAa;8DACX,EAAE;;;;;;8DAEL,8OAAC;6FAAY;8DACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;0CAUb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM,QAAQ;wCAAI;wCACtD,WAAU;;0DAEV,8OAAC;yFAAc;;kEACb,8OAAC;iGAAe,CAAC,2BAA2B,EAAE,KAAK,KAAK,CAAC,4CAA4C,CAAC;kEACpG,cAAA,8OAAC,KAAK,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAEvB,8OAAC;iGAAe;;0EACd,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,8OAAC;;0EAAM,KAAK,MAAM;;;;;;;;;;;;;;;;;;0DAItB,8OAAC;yFAAa;0DAAyC,KAAK,KAAK;;;;;;0DACjE,8OAAC;yFAAY;0DAAyB,KAAK,KAAK;;;;;;;uCAjB3C;;;;;;;;;;0CAuBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CAEV,cAAA,8OAAC;6EAAc;;sDACb,8OAAC;qFAAa;;8DACZ,8OAAC,kNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;;8DAAM,EAAE;;;;;;;;;;;;sDAGX,8OAAC;qFAAc;sDACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAI;oDAClC,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,YAAY;wDAAE,UAAU;wDAAK,OAAO,MAAM,QAAQ;oDAAI;oDACtD,WAAU;;sEAEV,8OAAC;qGAAe,CAAC,yBAAyB,EAAE,KAAK,KAAK,CAAC,yDAAyD,CAAC;sEAC/G,cAAA,8OAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;;;;;;sEAGvB,8OAAC;qGAAa;sEAAwC,KAAK,KAAK;;;;;;sEAChE,8OAAC;qGAAY;sEAAuC,KAAK,KAAK;;;;;;;mDAXzD;;;;;;;;;;;;;;;;;;;;;0CAmBf,8OAAC;yEAAc;;kDAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAEV,8OAAC;yFAAa;;kEACZ,8OAAC,wMAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,8OAAC;;kEAAM,EAAE;;;;;;;;;;;;0DAGX,8OAAC;yFAAc;;oDACZ,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DAET,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAG;4DAC7B,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAE;4DAC5B,YAAY;gEAAE,UAAU;gEAAK,OAAO,MAAM,QAAQ;4DAAI;4DACtD,WAAU;;8EAEV,8OAAC;;;sFACC,8OAAC;qHAAa;sFAA6B,MAAM,MAAM;;;;;;sFACvD,8OAAC;qHAAY;sFAAyB,MAAM,IAAI;;;;;;sFAChD,8OAAC;qHAAY;;gFAAwB;gFAAE,MAAM,EAAE;;;;;;;;;;;;;8EAGjD,8OAAC;6GAAc;8EACb,cAAA,8OAAC;;;0FACC,8OAAC;yHAAgB,CAAC,2CAA2C,EAAE,cAAc,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,cAAc,MAAM,MAAM,EAAE,KAAK,EAAE;0FACtI,cAAc,MAAM,MAAM,EAAE,KAAK;;;;;;0FAEpC,8OAAC;yHAAY;0FAA8B,WAAW,MAAM,OAAO;;;;;;;;;;;;;;;;;;2DAjBlE,MAAM,EAAE;;;;;oDAyBhB,aAAa,MAAM,KAAK,mBACvB,8OAAC;iGAAc;;0EACb,8OAAC,wMAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,8OAAC;yGAAY;0EAAiB,EAAE;;;;;;;;;;;;;;;;;;0DAKtC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;oDAET,EAAE;oDAAY;oDAAE,EAAE;;;;;;;;;;;;;oCAOtB,KAAK,IAAI,KAAK,yBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAEV,8OAAC;yFAAa;;kEACZ,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;;kEAAM,EAAE;;;;;;;;;;;;0DAGX,8OAAC;yFAAc;;kEACb,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;;0EAEV,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;yGAAe;0EAAqC,EAAE;;;;;;;;;;;;kEAGzD,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;;0EAEV,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;yGAAe;0EAAqC,EAAE;;;;;;;;;;;;kEAGzD,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;;0EAEV,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;yGAAe;0EAAuC,EAAE;;;;;;;;;;;;;;;;;;0DAI7D,8OAAC;yFAAc;;kEACb,8OAAC;iGAAa;kEAAkC,EAAE;;;;;;kEAClD,8OAAC;iGAAY;;4DACV,EAAE;4DAAY;4DAAE;4DAAkB;4DAAE,EAAE;4DAA+B;4DAAE,EAAE;4DAAO;4DAAE,UAAU,YAAY;4DAAC;4DAAE,EAAE;;;;;;;;;;;;;;;;;;;oCASrH,KAAK,IAAI,KAAK,yBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAEV,8OAAC;yFAAa;;kEACZ,8OAAC,kNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,8OAAC;;kEAAM,EAAE;;;;;;;;;;;;0DAGX,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC,kNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,8OAAC;iGAAe;kEAAwC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;0CAOlE,8OAAC;yEAAc;;kDAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAEV,8OAAC;yFAAa;;kEACZ,8OAAC,wMAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,8OAAC;;kEAAM,EAAE;;;;;;;;;;;;0DAGX,8OAAC;yFAAc;0DACZ,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDAET,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAG;wDAC7B,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAE;wDAC5B,YAAY;4DAAE,UAAU;4DAAK,OAAO,MAAM,QAAQ;wDAAI;wDACtD,WAAU;;0EAEV,8OAAC;;;kFACC,8OAAC;iHAAa;kFAA6B,MAAM,MAAM;;;;;;kFACvD,8OAAC;iHAAY;kFAAyB,MAAM,IAAI;;;;;;kFAChD,8OAAC;iHAAY;;4EAAwB;4EAAE,MAAM,EAAE;;;;;;;;;;;;;0EAGjD,8OAAC;yGAAc;0EACb,cAAA,8OAAC;;;sFACC,8OAAC;qHAAgB,CAAC,2CAA2C,EAAE,eAAe,MAAM,MAAM,GAAG;sFAC1F,eAAe,MAAM,MAAM;;;;;;sFAE9B,8OAAC;qHAAY;;8FACX,8OAAC,oMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;8FACjB,8OAAC;;8FAAM,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;uDAnBrB,MAAM,EAAE;;;;;;;;;;0DA6BnB,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;oDAET,EAAE;oDAAY;oDAAE,EAAE;;;;;;;;;;;;;oCAKtB,KAAK,IAAI,KAAK,yBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAEV,8OAAC;yFAAa;;kEACZ,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;;kEAAM,EAAE;;;;;;;;;;;;0DAGX,8OAAC;yFAAc;;kEACb,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;;0EAEV,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;yGAAe;0EAAqC,EAAE;;;;;;;;;;;;kEAGzD,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;;0EAEV,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;yGAAe;0EAAqC,EAAE;;;;;;;;;;;;kEAGzD,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;;0EAEV,8OAAC,kNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,8OAAC;yGAAe;0EAAsC,EAAE;;;;;;;;;;;;kEAG1D,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;;0EAEV,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;yGAAe;0EAAuC,EAAE;;;;;;;;;;;;;;;;;;0DAI7D,8OAAC;yFAAc;;kEACb,8OAAC;iGAAa;kEAAkC,EAAE;;;;;;kEAClD,8OAAC;iGAAY;;4DACV,EAAE;4DAAY;4DAAE;4DAAkB;4DAAE,EAAE;4DAA+B;4DAAE,EAAE;4DAAO;4DAAE,UAAU,YAAY;4DAAC;4DAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhI;AAEe,SAAS;IACtB,qBACE,8OAAC,oIAAA,CAAA,UAAc;kBACb,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}