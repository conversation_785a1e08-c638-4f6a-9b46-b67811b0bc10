"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ReadyDesigns.tsx":
/*!*****************************************!*\
  !*** ./src/components/ReadyDesigns.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReadyDesigns)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Eye_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Eye,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Eye_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Eye,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Eye_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Eye,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Eye_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Eye,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Eye_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Eye,Heart,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ReadyDesigns() {\n    _s();\n    const [selectedImageIndex, setSelectedImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGalleryOpen, setIsGalleryOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentImageIndexes, setCurrentImageIndexes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        1: 0,\n        2: 0,\n        3: 0,\n        4: 0\n    });\n    const readyDesigns = [\n        {\n            id: 1,\n            title: 'فستان زفاف كلاسيكي',\n            description: 'فستان زفاف أنيق بتصميم كلاسيكي مع تطريز يدوي',\n            category: 'فساتين زفاف',\n            images: [\n                '/wedding-dress-1.jpg.jpg',\n                '/wedding-dress-1a.jpg.jpg',\n                '/wedding-dress-1b.jpg.jpg'\n            ]\n        },\n        {\n            id: 2,\n            title: 'فستان سهرة راقي',\n            description: 'فستان سهرة طويل بقصة عصرية ولمسات ذهبية',\n            category: 'فساتين سهرة',\n            images: [\n                '/wedding-dress-2.jpg.jpg',\n                '/wedding-dress-2a.jpg.jpg',\n                '/wedding-dress-2b.jpg.jpg'\n            ]\n        },\n        {\n            id: 3,\n            title: 'فستان كوكتيل أنيق',\n            description: 'فستان كوكتيل قصير بتصميم عصري ومميز',\n            category: 'فساتين كوكتيل',\n            images: [\n                '/wedding-dress-3.jpg.jpg',\n                '/wedding-dress-3a.jpg.jpg',\n                '/wedding-dress-3b.jpg.jpg'\n            ]\n        },\n        {\n            id: 4,\n            title: 'فستان خطوبة مميز',\n            description: 'فستان خطوبة بتصميم رومانسي وتفاصيل دقيقة',\n            category: 'فساتين خطوبة',\n            images: [\n                '/wedding-dress-4.jpg.jpg',\n                '/wedding-dress-4a.jpg.jpg',\n                '/wedding-dress-4b.jpg.jpg'\n            ]\n        }\n    ];\n    // دوال التنقل بين صور البطاقة\n    const nextCardImage = (designId, e)=>{\n        e.stopPropagation();\n        setCurrentImageIndexes((prev)=>({\n                ...prev,\n                [designId]: (prev[designId] + 1) % 3\n            }));\n    };\n    const prevCardImage = (designId, e)=>{\n        e.stopPropagation();\n        setCurrentImageIndexes((prev)=>({\n                ...prev,\n                [designId]: prev[designId] === 0 ? 2 : prev[designId] - 1\n            }));\n    };\n    const setCardImage = (designId, imageIndex, e)=>{\n        e.stopPropagation();\n        setCurrentImageIndexes((prev)=>({\n                ...prev,\n                [designId]: imageIndex\n            }));\n    };\n    // دوال إدارة المعرض\n    const openGallery = (index)=>{\n        setSelectedImageIndex(index);\n        setIsGalleryOpen(true);\n        document.body.style.overflow = 'hidden' // منع التمرير في الخلفية\n        ;\n    };\n    const closeGallery = ()=>{\n        setIsGalleryOpen(false);\n        setSelectedImageIndex(null);\n        document.body.style.overflow = 'unset' // إعادة التمرير\n        ;\n    };\n    const nextImage = ()=>{\n        if (selectedImageIndex !== null) {\n            const designId = readyDesigns[selectedImageIndex].id;\n            setCurrentImageIndexes((prev)=>({\n                    ...prev,\n                    [designId]: (prev[designId] + 1) % 3\n                }));\n        }\n    };\n    const prevImage = ()=>{\n        if (selectedImageIndex !== null) {\n            const designId = readyDesigns[selectedImageIndex].id;\n            setCurrentImageIndexes((prev)=>({\n                    ...prev,\n                    [designId]: prev[designId] === 0 ? 2 : prev[designId] - 1\n                }));\n        }\n    };\n    // إدارة مفتاح Escape\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReadyDesigns.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"ReadyDesigns.useEffect.handleKeyDown\": (event)=>{\n                    if (event.key === 'Escape' && isGalleryOpen) {\n                        closeGallery();\n                    }\n                    if (event.key === 'ArrowRight' && isGalleryOpen) {\n                        nextImage();\n                    }\n                    if (event.key === 'ArrowLeft' && isGalleryOpen) {\n                        prevImage();\n                    }\n                }\n            }[\"ReadyDesigns.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"ReadyDesigns.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleKeyDown);\n                    document.body.style.overflow = 'unset' // تنظيف عند إلغاء المكون\n                    ;\n                }\n            })[\"ReadyDesigns.useEffect\"];\n        }\n    }[\"ReadyDesigns.useEffect\"], [\n        isGalleryOpen,\n        selectedImageIndex\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                                children: \"تصاميمنا الجاهزة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed mb-6\",\n                            children: \"مجموعة مختارة من أجمل تصاميمنا الجاهزة التي يمكنك طلبها مباشرة أو تخصيصها حسب ذوقك\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 max-w-2xl mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-800 font-medium text-center\",\n                                children: \"✨ الفساتين الجاهزة متوفرة للشراء المباشر - لا يتطلب حجز موعد\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-8 mb-12\",\n                    children: readyDesigns.map((design, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-[4/5] bg-gradient-to-br from-pink-100 via-rose-100 to-purple-100 relative overflow-hidden cursor-pointer\",\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            openGallery(index);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: design.images[currentImageIndexes[design.id]],\n                                                alt: \"\".concat(design.title, \" - صورة \").concat(currentImageIndexes[design.id] + 1),\n                                                className: \"w-full h-full object-cover transition-opacity duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>prevCardImage(design.id, e),\n                                                className: \"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-all duration-300 z-10\",\n                                                \"aria-label\": \"الصورة السابقة\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Eye_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>nextCardImage(design.id, e),\n                                                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-all duration-300 z-10\",\n                                                \"aria-label\": \"الصورة التالية\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Eye_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1 space-x-reverse opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                children: design.images.map((_, imgIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>setCardImage(design.id, imgIndex, e),\n                                                        className: \"w-2 h-2 rounded-full transition-colors duration-300 \".concat(currentImageIndexes[design.id] === imgIndex ? 'bg-white' : 'bg-white/50'),\n                                                        \"aria-label\": \"عرض الصورة \".concat(imgIndex + 1)\n                                                    }, imgIndex, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white/90 rounded-full p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Eye_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-6 h-6 text-pink-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"absolute top-3 left-3 bg-white/80 hover:bg-white rounded-full p-2 transition-all duration-300 hover:scale-110 shadow-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Eye_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4 text-pink-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/designs/\".concat(design.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 cursor-pointer hover:bg-gray-50 transition-colors duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-gradient-to-r from-pink-100 to-rose-100 text-pink-700 px-2 py-1 rounded-full text-xs font-medium\",\n                                                        children: design.category\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-bold text-gray-800 mb-2 group-hover:text-pink-600 transition-colors duration-300\",\n                                                    children: design.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 leading-relaxed\",\n                                                    children: design.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, this)\n                        }, design.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/designs\",\n                        className: \"btn-primary inline-flex items-center space-x-3 space-x-reverse text-lg group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Eye_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-6 h-6 group-hover:scale-110 transition-transform duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"عرض جميع التصاميم\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, this),\n                isGalleryOpen && selectedImageIndex !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4\",\n                    onClick: closeGallery,\n                    role: \"dialog\",\n                    \"aria-modal\": \"true\",\n                    \"aria-labelledby\": \"gallery-title\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-4xl w-full\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: closeGallery,\n                                className: \"absolute top-4 right-4 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-2 transition-colors duration-300\",\n                                \"aria-label\": \"إغلاق المعرض\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Eye_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: prevImage,\n                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-colors duration-300\",\n                                \"aria-label\": \"الصورة السابقة\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Eye_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: nextImage,\n                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-colors duration-300\",\n                                \"aria-label\": \"الصورة التالية\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Eye_Heart_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                className: \"bg-white rounded-2xl overflow-hidden shadow-2xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-[4/5] bg-gradient-to-br from-pink-100 via-rose-100 to-purple-100 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: readyDesigns[selectedImageIndex].images[currentImageIndexes[readyDesigns[selectedImageIndex].id]],\n                                        alt: \"\".concat(readyDesigns[selectedImageIndex].title, \" - صورة \").concat(currentImageIndexes[readyDesigns[selectedImageIndex].id] + 1),\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 17\n                                }, this)\n                            }, selectedImageIndex, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ReadyDesigns.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n_s(ReadyDesigns, \"4B+jfAqiOjcPxXFLEkYcy0OW4UM=\");\n_c = ReadyDesigns;\nvar _c;\n$RefreshReg$(_c, \"ReadyDesigns\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ReadyDesigns.tsx\n"));

/***/ })

});