(()=>{var e={};e.id=580,e.ids=[580],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5316:(e,r,s)=>{Promise.resolve().then(s.bind(s,19956))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18541:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var a=s(65239),t=s(48088),n=s(88170),i=s.n(n),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(r,d);let o={children:["",{children:["dashboard",{children:["add-order",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,97902)),"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\add-order\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\add-order\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/dashboard/add-order/page",pathname:"/dashboard/add-order",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19956:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>w});var a=s(60687),t=s(43210),n=s(26001),i=s(16189),l=s(85814),d=s.n(l),o=s(99720),c=s(34270),m=s(99182),u=s(20769),p=s(65938),h=s(61465),x=s(1733),b=s(70334),g=s(5336),v=s(93613),f=s(58869),j=s(9005),y=s(33800),N=s(58887),_=s(8819);function k(){let{user:e}=(0,o.n)(),{addOrder:r,workers:s}=(0,c.D)(),{t:l,isArabic:u}=(0,m.B)(),k=(0,i.useRouter)(),[w,A]=(0,t.useState)({clientName:"",clientPhone:"",description:"",fabric:"",measurements:{shoulder:"",shoulderCircumference:"",chest:"",waist:"",hips:"",dartLength:"",bodiceLength:"",neckline:"",armpit:"",sleeveLength:"",forearm:"",cuff:"",frontLength:"",backLength:""},price:"",assignedWorker:"",dueDate:"",notes:"",voiceNotes:[],images:[]}),[C,L]=(0,t.useState)(!1),[P,q]=(0,t.useState)(null),S=(e,r)=>{A(s=>({...s,[e]:r}))},D=(e,r)=>{A(s=>({...s,measurements:{...s.measurements,[e]:r}}))},M=async e=>{if(e.preventDefault(),!w.clientName||!w.clientPhone||!w.description||!w.dueDate||!w.price)return void q({type:"error",text:l("fill_required_fields")});L(!0),q(null);try{await new Promise(e=>setTimeout(e,1500)),r({clientName:w.clientName,clientPhone:w.clientPhone,description:w.description,fabric:w.fabric,measurements:{shoulder:w.measurements.shoulder?Number(w.measurements.shoulder):void 0,shoulderCircumference:w.measurements.shoulderCircumference?Number(w.measurements.shoulderCircumference):void 0,chest:w.measurements.chest?Number(w.measurements.chest):void 0,waist:w.measurements.waist?Number(w.measurements.waist):void 0,hips:w.measurements.hips?Number(w.measurements.hips):void 0,dartLength:w.measurements.dartLength?Number(w.measurements.dartLength):void 0,bodiceLength:w.measurements.bodiceLength?Number(w.measurements.bodiceLength):void 0,neckline:w.measurements.neckline?Number(w.measurements.neckline):void 0,armpit:w.measurements.armpit?Number(w.measurements.armpit):void 0,sleeveLength:w.measurements.sleeveLength?Number(w.measurements.sleeveLength):void 0,forearm:w.measurements.forearm?Number(w.measurements.forearm):void 0,cuff:w.measurements.cuff?Number(w.measurements.cuff):void 0,frontLength:w.measurements.frontLength?Number(w.measurements.frontLength):void 0,backLength:w.measurements.backLength?Number(w.measurements.backLength):void 0},price:Number(w.price),assignedWorker:w.assignedWorker||void 0,dueDate:w.dueDate,notes:w.notes||void 0,voiceNotes:w.voiceNotes.length>0?w.voiceNotes:void 0,images:w.images.length>0?w.images:void 0,status:"pending"}),q({type:"success",text:l("order_added_success")}),setTimeout(()=>{k.push("/dashboard/orders")},2e3)}catch(e){console.error("Error adding order:",e),q({type:"error",text:l("order_add_error")})}finally{L(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-8",children:(0,a.jsxs)(d(),{href:"/dashboard",className:"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300",children:[(0,a.jsx)(b.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:l("back_to_dashboard")})]})}),(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold mb-4",children:(0,a.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent",children:l("add_new_order")})}),(0,a.jsx)("p",{className:"text-lg text-gray-600",children:l("add_new_order_description")})]}),P&&(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:`mb-8 p-4 rounded-lg flex items-center space-x-3 space-x-reverse max-w-4xl mx-auto ${"success"===P.type?"bg-green-50 text-green-800 border border-green-200":"bg-red-50 text-red-800 border border-red-200"}`,children:["success"===P.type?(0,a.jsx)(g.A,{className:"w-5 h-5 text-green-600"}):(0,a.jsx)(v.A,{className:"w-5 h-5 text-red-600"}),(0,a.jsx)("span",{children:P.text})]}),(0,a.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"max-w-4xl mx-auto",children:(0,a.jsxs)("form",{onSubmit:M,className:"space-y-8",children:[(0,a.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(f.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsx)("span",{children:l("basic_information")})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:l("client_name_required")}),(0,a.jsx)("input",{type:"text",value:w.clientName,onChange:e=>S("clientName",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:l("enter_client_name"),required:!0})]}),(0,a.jsx)("div",{children:(0,a.jsx)(x.A,{value:w.clientPhone,onChange:e=>S("clientPhone",e),type:"phone",label:l("phone_required"),placeholder:l("enter_phone"),required:!0,disabled:C})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:l("order_description_required")}),(0,a.jsx)("input",{type:"text",value:w.description,onChange:e=>S("description",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:l("order_description_placeholder"),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:l("fabric_type")}),(0,a.jsx)("input",{type:"text",value:w.fabric,onChange:e=>S("fabric",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:l("fabric_type_placeholder")})]}),(0,a.jsx)("div",{children:(0,a.jsx)(x.A,{value:w.price,onChange:e=>S("price",e),type:"price",label:l("price_sar"),placeholder:"0",required:!0,disabled:C})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:l("responsible_worker")}),(0,a.jsxs)("select",{value:w.assignedWorker,onChange:e=>S("assignedWorker",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",children:[(0,a.jsx)("option",{value:"",children:l("choose_worker")}),s.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.full_name," - ",e.specialty]},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:l("delivery_date_required")}),(0,a.jsx)("input",{type:"date",value:w.dueDate,onChange:e=>S("dueDate",e.target.value),min:new Date().toISOString().split("T")[0],className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",required:!0})]})]})]}),(0,a.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(j.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsx)("span",{children:l("design_images")})]}),(0,a.jsx)(p.A,{images:w.images,onImagesChange:e=>S("images",e),maxImages:10})]}),(0,a.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(y.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsx)("span",{children:l("measurements_cm")})]}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2",children:l("basic_measurements")}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{children:(0,a.jsx)(x.A,{value:w.measurements.shoulder,onChange:e=>D("shoulder",e),type:"measurement",label:l("shoulder"),placeholder:l("cm_placeholder"),disabled:C})}),(0,a.jsx)("div",{children:(0,a.jsx)(x.A,{value:w.measurements.shoulderCircumference,onChange:e=>D("shoulderCircumference",e),type:"measurement",label:l("shoulder_circumference"),placeholder:l("cm_placeholder"),disabled:C})}),(0,a.jsx)("div",{children:(0,a.jsx)(x.A,{value:w.measurements.chest,onChange:e=>D("chest",e),type:"measurement",label:l("chest"),placeholder:l("cm_placeholder"),disabled:C})}),(0,a.jsx)("div",{children:(0,a.jsx)(x.A,{value:w.measurements.waist,onChange:e=>D("waist",e),type:"measurement",label:l("waist"),placeholder:l("cm_placeholder"),disabled:C})}),(0,a.jsx)("div",{children:(0,a.jsx)(x.A,{value:w.measurements.hips,onChange:e=>D("hips",e),type:"measurement",label:l("hips"),placeholder:l("cm_placeholder"),disabled:C})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2",children:l("advanced_measurements")}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{children:(0,a.jsx)(x.A,{value:w.measurements.dartLength,onChange:e=>D("dartLength",e),type:"measurement",label:l("dart_length"),placeholder:l("cm_placeholder"),disabled:C})}),(0,a.jsx)("div",{children:(0,a.jsx)(x.A,{value:w.measurements.bodiceLength,onChange:e=>D("bodiceLength",e),type:"measurement",label:l("bodice_length"),placeholder:l("cm_placeholder"),disabled:C})}),(0,a.jsx)("div",{children:(0,a.jsx)(x.A,{value:w.measurements.neckline,onChange:e=>D("neckline",e),type:"measurement",label:l("neckline"),placeholder:l("cm_placeholder"),disabled:C})}),(0,a.jsx)("div",{children:(0,a.jsx)(x.A,{value:w.measurements.armpit,onChange:e=>D("armpit",e),type:"measurement",label:l("armpit"),placeholder:l("cm_placeholder"),disabled:C})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2",children:l("sleeve_measurements")}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{children:(0,a.jsx)(x.A,{value:w.measurements.sleeveLength,onChange:e=>D("sleeveLength",e),type:"measurement",label:l("sleeve_length"),placeholder:l("cm_placeholder"),disabled:C})}),(0,a.jsx)("div",{children:(0,a.jsx)(x.A,{value:w.measurements.forearm,onChange:e=>D("forearm",e),type:"measurement",label:l("forearm"),placeholder:l("cm_placeholder"),disabled:C})}),(0,a.jsx)("div",{children:(0,a.jsx)(x.A,{value:w.measurements.cuff,onChange:e=>D("cuff",e),type:"measurement",label:l("cuff"),placeholder:l("cm_placeholder"),disabled:C})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2",children:l("length_measurements")}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{children:(0,a.jsx)(x.A,{value:w.measurements.frontLength,onChange:e=>D("frontLength",e),type:"measurement",label:l("front_length"),placeholder:l("cm_placeholder"),disabled:C})}),(0,a.jsx)("div",{children:(0,a.jsx)(x.A,{value:w.measurements.backLength,onChange:e=>D("backLength",e),type:"measurement",label:l("back_length"),placeholder:l("cm_placeholder"),disabled:C})})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",children:[(0,a.jsx)(N.A,{className:"w-5 h-5 text-pink-600"}),(0,a.jsx)("span",{children:l("additional_notes")})]}),(0,a.jsx)("textarea",{value:w.notes,onChange:e=>S("notes",e.target.value),rows:4,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:l("additional_notes_placeholder")}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:l("voice_notes_optional")}),(0,a.jsx)(h.A,{voiceNotes:w.voiceNotes||[],onVoiceNotesChange:e=>S("voiceNotes",e),disabled:C})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)("button",{type:"submit",disabled:C,className:"btn-primary py-4 px-8 text-lg disabled:opacity-50 disabled:cursor-not-allowed",children:C?(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[(0,a.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,a.jsx)("span",{children:l("saving")})]}):(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[(0,a.jsx)(_.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:l("save_order")})]})}),(0,a.jsx)(d(),{href:"/dashboard",className:"btn-secondary py-4 px-8 text-lg inline-flex items-center justify-center",children:l("cancel")})]})]})})]})})}function w(){return(0,a.jsx)(u.A,{requiredRole:"admin",children:(0,a.jsx)(k,{})})}},20769:(e,r,s)=>{"use strict";s.d(r,{A:()=>c});var a=s(60687),t=s(43210),n=s(16189),i=s(99720),l=s(26001),d=s(99891),o=s(93613);function c({children:e,requiredRole:r,redirectTo:s="/login"}){let{user:c,isLoading:m,checkAuth:u}=(0,i.n)(),p=(0,n.useRouter)(),[h,x]=(0,t.useState)(!0);return h||m?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(d.A,{className:"w-8 h-8 text-white animate-pulse"})}),(0,a.jsx)("div",{className:"w-12 h-12 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 font-medium",children:"جاري التحقق من الصلاحيات..."})]})}):c?r&&c.role!==r?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center max-w-md mx-auto px-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(o.A,{className:"w-8 h-8 text-yellow-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"صلاحيات غير كافية"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة"}),(0,a.jsx)("button",{onClick:()=>p.push("/dashboard"),className:"btn-primary px-6 py-3",children:"العودة إلى لوحة التحكم"})]})}):c.is_active?(0,a.jsx)(a.Fragment,{children:e}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center max-w-md mx-auto px-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(o.A,{className:"w-8 h-8 text-gray-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"حساب غير نشط"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"تم إلغاء تفعيل حسابك. يرجى التواصل مع المدير."}),(0,a.jsx)("button",{onClick:()=>p.push("/login"),className:"btn-primary px-6 py-3",children:"تسجيل الدخول"})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center max-w-md mx-auto px-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(o.A,{className:"w-8 h-8 text-red-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"غير مخول للوصول"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"يجب تسجيل الدخول للوصول إلى هذه الصفحة"}),(0,a.jsx)("button",{onClick:()=>p.push("/login"),className:"btn-primary px-6 py-3",children:"تسجيل الدخول"})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},52164:(e,r,s)=>{Promise.resolve().then(s.bind(s,97902))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},97902:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\add-order\\page.tsx","default")},99891:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),a=r.X(0,[447,507,146,814,267,238,59,306],()=>s(18541));module.exports=a})();