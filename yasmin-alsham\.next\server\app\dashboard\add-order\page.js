/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/add-order/page";
exports.ids = ["app/dashboard/add-order/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fadd-order%2Fpage&page=%2Fdashboard%2Fadd-order%2Fpage&appPaths=%2Fdashboard%2Fadd-order%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fadd-order%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fadd-order%2Fpage&page=%2Fdashboard%2Fadd-order%2Fpage&appPaths=%2Fdashboard%2Fadd-order%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fadd-order%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/add-order/page.tsx */ \"(rsc)/./src/app/dashboard/add-order/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'add-order',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/add-order/page\",\n        pathname: \"/dashboard/add-order\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fadd-order%2Fpage&page=%2Fdashboard%2Fadd-order%2Fpage&appPaths=%2Fdashboard%2Fadd-order%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fadd-order%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Kufi_Arabic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-kufi%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoKufi%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Kufi_Arabic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-kufi%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoKufi%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cadd-order%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cadd-order%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/add-order/page.tsx */ \"(rsc)/./src/app/dashboard/add-order/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2toYWxlJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1lBU01JTiUyMEFMU0hBTSU1QyU1Q3lhc21pbi1hbHNoYW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNhZGQtb3JkZXIlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0xBQTBKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxraGFsZVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxZQVNNSU4gQUxTSEFNXFxcXHlhc21pbi1hbHNoYW1cXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcYWRkLW9yZGVyXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cadd-order%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca2hhbGVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcWUFTTUlOIEFMU0hBTVxceWFzbWluLWFsc2hhbVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/add-order/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/dashboard/add-order/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\add-order\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b8e683c6119e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGtoYWxlXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFlBU01JTiBBTFNIQU1cXHlhc21pbi1hbHNoYW1cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImI4ZTY4M2M2MTE5ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_variable_font_cairo_subsets_arabic_latin_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"variable\":\"--font-cairo\",\"subsets\":[\"arabic\",\"latin\"],\"display\":\"swap\"}],\"variableName\":\"cairo\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Cairo\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-cairo\\\",\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"cairo\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_variable_font_cairo_subsets_arabic_latin_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_variable_font_cairo_subsets_arabic_latin_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Kufi_Arabic_arguments_variable_font_noto_kufi_subsets_arabic_display_swap_variableName_notoKufi___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Noto_Kufi_Arabic\",\"arguments\":[{\"variable\":\"--font-noto-kufi\",\"subsets\":[\"arabic\"],\"display\":\"swap\"}],\"variableName\":\"notoKufi\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Noto_Kufi_Arabic\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-noto-kufi\\\",\\\"subsets\\\":[\\\"arabic\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoKufi\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Kufi_Arabic_arguments_variable_font_noto_kufi_subsets_arabic_display_swap_variableName_notoKufi___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Kufi_Arabic_arguments_variable_font_noto_kufi_subsets_arabic_display_swap_variableName_notoKufi___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"ياسمين الشام - تفصيل فساتين حسب الطلب\",\n    description: \"محل ياسمين الشام لتفصيل الفساتين النسائية بأناقة دمشقية. حجز مواعيد، استعلام عن الطلبات، وأفضل الأقمشة.\",\n    keywords: \"تفصيل فساتين، خياطة نسائية، ياسمين الشام، فساتين دمشقية، حجز موعد\",\n    authors: [\n        {\n            name: \"ياسمين الشام\"\n        }\n    ],\n    openGraph: {\n        title: \"ياسمين الشام - تفصيل فساتين حسب الطلب\",\n        description: \"محل ياسمين الشام لتفصيل الفساتين النسائية بأناقة دمشقية\",\n        type: \"website\",\n        locale: \"ar_SA\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_variable_font_cairo_subsets_arabic_latin_display_swap_variableName_cairo___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Kufi_Arabic_arguments_variable_font_noto_kufi_subsets_arabic_display_swap_variableName_notoKufi___WEBPACK_IMPORTED_MODULE_3___default().variable)} font-cairo antialiased bg-gradient-to-br from-rose-50 to-pink-50 min-h-screen`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Kufi_Arabic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-kufi%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoKufi%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Kufi_Arabic%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-noto-kufi%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoKufi%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cadd-order%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cadd-order%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/add-order/page.tsx */ \"(ssr)/./src/app/dashboard/add-order/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2toYWxlJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1lBU01JTiUyMEFMU0hBTSU1QyU1Q3lhc21pbi1hbHNoYW0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNhZGQtb3JkZXIlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0xBQTBKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxraGFsZVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxZQVNNSU4gQUxTSEFNXFxcXHlhc21pbi1hbHNoYW1cXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcYWRkLW9yZGVyXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Ckhale%5C%5CDocuments%5C%5Caugment-projects%5C%5CYASMIN%20ALSHAM%5C%5Cyasmin-alsham%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cadd-order%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/add-order/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/dashboard/add-order/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddOrderPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/authStore */ \"(ssr)/./src/store/authStore.ts\");\n/* harmony import */ var _store_dataStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/dataStore */ \"(ssr)/./src/store/dataStore.ts\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(ssr)/./src/hooks/useTranslation.ts\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(ssr)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _components_ImageUpload__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ImageUpload */ \"(ssr)/./src/components/ImageUpload.tsx\");\n/* harmony import */ var _components_VoiceNotes__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/VoiceNotes */ \"(ssr)/./src/components/VoiceNotes.tsx\");\n/* harmony import */ var _components_NumericInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/NumericInput */ \"(ssr)/./src/components/NumericInput.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Image_MessageSquare_Ruler_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Image,MessageSquare,Ruler,Save,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Image_MessageSquare_Ruler_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Image,MessageSquare,Ruler,Save,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Image_MessageSquare_Ruler_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Image,MessageSquare,Ruler,Save,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Image_MessageSquare_Ruler_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Image,MessageSquare,Ruler,Save,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Image_MessageSquare_Ruler_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Image,MessageSquare,Ruler,Save,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Image_MessageSquare_Ruler_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Image,MessageSquare,Ruler,Save,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/ruler.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Image_MessageSquare_Ruler_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Image,MessageSquare,Ruler,Save,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Image_MessageSquare_Ruler_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Image,MessageSquare,Ruler,Save,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\nfunction AddOrderContent() {\n    const { user } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_4__.useAuthStore)();\n    const { addOrder, workers } = (0,_store_dataStore__WEBPACK_IMPORTED_MODULE_5__.useDataStore)();\n    const { t, isArabic } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // حالة النموذج\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        clientName: '',\n        clientPhone: '',\n        description: '',\n        fabric: '',\n        measurements: {\n            // المقاسات الأساسية\n            shoulder: '',\n            shoulderCircumference: '',\n            chest: '',\n            waist: '',\n            hips: '',\n            // مقاسات التفصيل المتقدمة\n            dartLength: '',\n            bodiceLength: '',\n            neckline: '',\n            armpit: '',\n            // مقاسات الأكمام\n            sleeveLength: '',\n            forearm: '',\n            cuff: '',\n            // مقاسات الطول\n            frontLength: '',\n            backLength: ''\n        },\n        price: '',\n        assignedWorker: '',\n        dueDate: '',\n        notes: '',\n        voiceNotes: [],\n        images: []\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // معالجة تغيير الحقول\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    // معالجة تغيير الملاحظات الصوتية\n    const handleVoiceNotesChange = (voiceNotes)=>{\n        setFormData((prev)=>({\n                ...prev,\n                voiceNotes\n            }));\n    };\n    // معالجة تغيير المقاسات\n    const handleMeasurementChange = (measurement, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                measurements: {\n                    ...prev.measurements,\n                    [measurement]: value\n                }\n            }));\n    };\n    // إرسال النموذج\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // التحقق من الحقول المطلوبة\n        if (!formData.clientName || !formData.clientPhone || !formData.description || !formData.dueDate || !formData.price) {\n            setMessage({\n                type: 'error',\n                text: t('fill_required_fields')\n            });\n            return;\n        }\n        setIsSubmitting(true);\n        setMessage(null);\n        try {\n            // محاكاة تأخير الشبكة\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            // إضافة الطلب إلى المتجر\n            addOrder({\n                clientName: formData.clientName,\n                clientPhone: formData.clientPhone,\n                description: formData.description,\n                fabric: formData.fabric,\n                measurements: {\n                    // المقاسات الأساسية\n                    shoulder: formData.measurements.shoulder ? Number(formData.measurements.shoulder) : undefined,\n                    shoulderCircumference: formData.measurements.shoulderCircumference ? Number(formData.measurements.shoulderCircumference) : undefined,\n                    chest: formData.measurements.chest ? Number(formData.measurements.chest) : undefined,\n                    waist: formData.measurements.waist ? Number(formData.measurements.waist) : undefined,\n                    hips: formData.measurements.hips ? Number(formData.measurements.hips) : undefined,\n                    // مقاسات التفصيل المتقدمة\n                    dartLength: formData.measurements.dartLength ? Number(formData.measurements.dartLength) : undefined,\n                    bodiceLength: formData.measurements.bodiceLength ? Number(formData.measurements.bodiceLength) : undefined,\n                    neckline: formData.measurements.neckline ? Number(formData.measurements.neckline) : undefined,\n                    armpit: formData.measurements.armpit ? Number(formData.measurements.armpit) : undefined,\n                    // مقاسات الأكمام\n                    sleeveLength: formData.measurements.sleeveLength ? Number(formData.measurements.sleeveLength) : undefined,\n                    forearm: formData.measurements.forearm ? Number(formData.measurements.forearm) : undefined,\n                    cuff: formData.measurements.cuff ? Number(formData.measurements.cuff) : undefined,\n                    // مقاسات الطول\n                    frontLength: formData.measurements.frontLength ? Number(formData.measurements.frontLength) : undefined,\n                    backLength: formData.measurements.backLength ? Number(formData.measurements.backLength) : undefined\n                },\n                price: Number(formData.price),\n                assignedWorker: formData.assignedWorker || undefined,\n                dueDate: formData.dueDate,\n                notes: formData.notes || undefined,\n                voiceNotes: formData.voiceNotes.length > 0 ? formData.voiceNotes : undefined,\n                images: formData.images.length > 0 ? formData.images : undefined,\n                status: 'pending'\n            });\n            setMessage({\n                type: 'success',\n                text: t('order_added_success')\n            });\n            // إعادة تعيين النموذج والتوجيه بعد 2 ثانية\n            setTimeout(()=>{\n                router.push('/dashboard/orders');\n            }, 2000);\n        } catch (error) {\n            console.error('Error adding order:', error);\n            setMessage({\n                type: 'error',\n                text: t('order_add_error')\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/dashboard\",\n                        className: \"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Image_MessageSquare_Ruler_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: t('back_to_dashboard')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl sm:text-4xl font-bold mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                                children: t('add_new_order')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600\",\n                            children: t('add_new_order_description')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, this),\n                message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: `mb-8 p-4 rounded-lg flex items-center space-x-3 space-x-reverse max-w-4xl mx-auto ${message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200'}`,\n                    children: [\n                        message.type === 'success' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Image_MessageSquare_Ruler_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"w-5 h-5 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Image_MessageSquare_Ruler_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"w-5 h-5 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: message.text\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.2\n                    },\n                    className: \"max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Image_MessageSquare_Ruler_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-5 h-5 text-pink-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t('basic_information')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: t('client_name_required')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.clientName,\n                                                        onChange: (e)=>handleInputChange('clientName', e.target.value),\n                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300\",\n                                                        placeholder: t('enter_client_name'),\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NumericInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    value: formData.clientPhone,\n                                                    onChange: (value)=>handleInputChange('clientPhone', value),\n                                                    type: \"phone\",\n                                                    label: t('phone_required'),\n                                                    placeholder: t('enter_phone'),\n                                                    required: true,\n                                                    disabled: isSubmitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: t('order_description_required')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.description,\n                                                        onChange: (e)=>handleInputChange('description', e.target.value),\n                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300\",\n                                                        placeholder: t('order_description_placeholder'),\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: t('fabric_type')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.fabric,\n                                                        onChange: (e)=>handleInputChange('fabric', e.target.value),\n                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300\",\n                                                        placeholder: t('fabric_type_placeholder')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NumericInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    value: formData.price,\n                                                    onChange: (value)=>handleInputChange('price', value),\n                                                    type: \"price\",\n                                                    label: t('price_sar'),\n                                                    placeholder: \"0\",\n                                                    required: true,\n                                                    disabled: isSubmitting\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: t('responsible_worker')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.assignedWorker,\n                                                        onChange: (e)=>handleInputChange('assignedWorker', e.target.value),\n                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: t('choose_worker')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            workers.map((worker)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: worker.id,\n                                                                    children: [\n                                                                        worker.full_name,\n                                                                        \" - \",\n                                                                        worker.specialty\n                                                                    ]\n                                                                }, worker.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: t('delivery_date_required')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: formData.dueDate,\n                                                        onChange: (e)=>handleInputChange('dueDate', e.target.value),\n                                                        min: new Date().toISOString().split('T')[0],\n                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Image_MessageSquare_Ruler_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-5 h-5 text-pink-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t('design_images')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ImageUpload__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        images: formData.images,\n                                        onImagesChange: (images)=>handleInputChange('images', images),\n                                        maxImages: 10\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Image_MessageSquare_Ruler_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-5 h-5 text-pink-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t('measurements_cm')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2\",\n                                                        children: t('basic_measurements')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NumericInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    value: formData.measurements.shoulder,\n                                                                    onChange: (value)=>handleMeasurementChange('shoulder', value),\n                                                                    type: \"measurement\",\n                                                                    label: t('shoulder'),\n                                                                    placeholder: t('cm_placeholder'),\n                                                                    disabled: isSubmitting\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NumericInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    value: formData.measurements.shoulderCircumference,\n                                                                    onChange: (value)=>handleMeasurementChange('shoulderCircumference', value),\n                                                                    type: \"measurement\",\n                                                                    label: t('shoulder_circumference'),\n                                                                    placeholder: t('cm_placeholder'),\n                                                                    disabled: isSubmitting\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NumericInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    value: formData.measurements.chest,\n                                                                    onChange: (value)=>handleMeasurementChange('chest', value),\n                                                                    type: \"measurement\",\n                                                                    label: t('chest'),\n                                                                    placeholder: t('cm_placeholder'),\n                                                                    disabled: isSubmitting\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NumericInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    value: formData.measurements.waist,\n                                                                    onChange: (value)=>handleMeasurementChange('waist', value),\n                                                                    type: \"measurement\",\n                                                                    label: t('waist'),\n                                                                    placeholder: t('cm_placeholder'),\n                                                                    disabled: isSubmitting\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                    lineNumber: 428,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NumericInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    value: formData.measurements.hips,\n                                                                    onChange: (value)=>handleMeasurementChange('hips', value),\n                                                                    type: \"measurement\",\n                                                                    label: t('hips'),\n                                                                    placeholder: t('cm_placeholder'),\n                                                                    disabled: isSubmitting\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2\",\n                                                        children: t('advanced_measurements')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NumericInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    value: formData.measurements.dartLength,\n                                                                    onChange: (value)=>handleMeasurementChange('dartLength', value),\n                                                                    type: \"measurement\",\n                                                                    label: t('dart_length'),\n                                                                    placeholder: t('cm_placeholder'),\n                                                                    disabled: isSubmitting\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NumericInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    value: formData.measurements.bodiceLength,\n                                                                    onChange: (value)=>handleMeasurementChange('bodiceLength', value),\n                                                                    type: \"measurement\",\n                                                                    label: t('bodice_length'),\n                                                                    placeholder: t('cm_placeholder'),\n                                                                    disabled: isSubmitting\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NumericInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    value: formData.measurements.neckline,\n                                                                    onChange: (value)=>handleMeasurementChange('neckline', value),\n                                                                    type: \"measurement\",\n                                                                    label: t('neckline'),\n                                                                    placeholder: t('cm_placeholder'),\n                                                                    disabled: isSubmitting\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NumericInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    value: formData.measurements.armpit,\n                                                                    onChange: (value)=>handleMeasurementChange('armpit', value),\n                                                                    type: \"measurement\",\n                                                                    label: t('armpit'),\n                                                                    placeholder: t('cm_placeholder'),\n                                                                    disabled: isSubmitting\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2\",\n                                                        children: t('sleeve_measurements')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NumericInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    value: formData.measurements.sleeveLength,\n                                                                    onChange: (value)=>handleMeasurementChange('sleeveLength', value),\n                                                                    type: \"measurement\",\n                                                                    label: t('sleeve_length'),\n                                                                    placeholder: t('cm_placeholder'),\n                                                                    disabled: isSubmitting\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NumericInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    value: formData.measurements.forearm,\n                                                                    onChange: (value)=>handleMeasurementChange('forearm', value),\n                                                                    type: \"measurement\",\n                                                                    label: t('forearm'),\n                                                                    placeholder: t('cm_placeholder'),\n                                                                    disabled: isSubmitting\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NumericInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    value: formData.measurements.cuff,\n                                                                    onChange: (value)=>handleMeasurementChange('cuff', value),\n                                                                    type: \"measurement\",\n                                                                    label: t('cuff'),\n                                                                    placeholder: t('cm_placeholder'),\n                                                                    disabled: isSubmitting\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                lineNumber: 531,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-md font-semibold text-gray-800 mb-4 border-b border-pink-200 pb-2\",\n                                                        children: t('length_measurements')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NumericInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    value: formData.measurements.frontLength,\n                                                                    onChange: (value)=>handleMeasurementChange('frontLength', value),\n                                                                    type: \"measurement\",\n                                                                    label: t('front_length'),\n                                                                    placeholder: t('cm_placeholder'),\n                                                                    disabled: isSubmitting\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                    lineNumber: 551,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NumericInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    value: formData.measurements.backLength,\n                                                                    onChange: (value)=>handleMeasurementChange('backLength', value),\n                                                                    type: \"measurement\",\n                                                                    label: t('back_length'),\n                                                                    placeholder: t('cm_placeholder'),\n                                                                    disabled: isSubmitting\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Image_MessageSquare_Ruler_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-5 h-5 text-pink-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 579,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: t('additional_notes')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: formData.notes,\n                                        onChange: (e)=>handleInputChange('notes', e.target.value),\n                                        rows: 4,\n                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300\",\n                                        placeholder: t('additional_notes_placeholder')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                                children: t('voice_notes_optional')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VoiceNotes__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                voiceNotes: formData.voiceNotes || [],\n                                                onVoiceNotesChange: handleVoiceNotesChange,\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: \"btn-primary py-4 px-8 text-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2 space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: t('saving')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                    lineNumber: 614,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2 space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Image_MessageSquare_Ruler_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                    lineNumber: 618,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: t('save_order')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                            lineNumber: 617,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"btn-secondary py-4 px-8 text-lg inline-flex items-center justify-center\",\n                                        children: t('cancel')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n            lineNumber: 190,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n        lineNumber: 189,\n        columnNumber: 5\n    }, this);\n}\nfunction AddOrderPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddOrderContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n            lineNumber: 641,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\add-order\\\\page.tsx\",\n        lineNumber: 640,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/add-order/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ImageUpload.tsx":
/*!****************************************!*\
  !*** ./src/components/ImageUpload.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Image_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Plus,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Plus,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Plus,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Plus,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(ssr)/./src/hooks/useTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ImageUpload({ images, onImagesChange, maxImages = 10 }) {\n    const [dragOver, setDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const handleFileSelect = (files)=>{\n        if (!files) return;\n        const newImages = [];\n        const remainingSlots = maxImages - images.length;\n        Array.from(files).slice(0, remainingSlots).forEach((file)=>{\n            if (file.type.startsWith('image/')) {\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    const result = e.target?.result;\n                    if (result) {\n                        newImages.push(result);\n                        if (newImages.length === Math.min(files.length, remainingSlots)) {\n                            onImagesChange([\n                                ...images,\n                                ...newImages\n                            ]);\n                        }\n                    }\n                };\n                reader.readAsDataURL(file);\n            }\n        });\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setDragOver(false);\n        handleFileSelect(e.dataTransfer.files);\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        setDragOver(true);\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setDragOver(false);\n    };\n    const removeImage = (index)=>{\n        const newImages = images.filter((_, i)=>i !== index);\n        onImagesChange(newImages);\n    };\n    const openFileDialog = ()=>{\n        fileInputRef.current?.click();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `border-2 border-dashed rounded-lg p-6 text-center transition-all duration-300 cursor-pointer ${dragOver ? 'border-pink-400 bg-pink-50' : images.length >= maxImages ? 'border-gray-200 bg-gray-50 cursor-not-allowed' : 'border-gray-300 hover:border-pink-400 hover:bg-pink-50'}`,\n                onDrop: handleDrop,\n                onDragOver: handleDragOver,\n                onDragLeave: handleDragLeave,\n                onClick: images.length < maxImages ? openFileDialog : undefined,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        accept: \"image/*\",\n                        multiple: true,\n                        onChange: (e)=>handleFileSelect(e.target.files),\n                        className: \"hidden\",\n                        disabled: images.length >= maxImages\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    images.length >= maxImages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-400 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: [\n                                    t('max_images_reached'),\n                                    \" (\",\n                                    maxImages,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-400 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-gray-700\",\n                                        children: dragOver ? t('drop_images_here') : t('click_or_drag_images')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: [\n                                            t('image_upload_format'),\n                                            \" (\",\n                                            t('max_images_text'),\n                                            \" \",\n                                            maxImages,\n                                            \" \",\n                                            t('images_text'),\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: [\n                                            images.length,\n                                            \" \",\n                                            t('of'),\n                                            \" \",\n                                            maxImages,\n                                            \" \",\n                                            t('images_text')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-gray-700\",\n                        children: \"الصور المرفوعة:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                            children: [\n                                images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        className: \"relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square rounded-lg overflow-hidden border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: image,\n                                                    alt: `صورة ${index + 1}`,\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>removeImage(index),\n                                                className: \"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-red-600\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded\",\n                                                children: index + 1\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, this)),\n                                images.length < maxImages && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    className: \"aspect-square border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center cursor-pointer hover:border-pink-400 hover:bg-pink-50 transition-all duration-300\",\n                                    onClick: openFileDialog,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-8 h-8 text-gray-400 mx-auto mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: t('add_image')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ImageUpload.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ImageUpload.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/NumericInput.tsx":
/*!*****************************************!*\
  !*** ./src/components/NumericInput.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NumericInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _utils_inputValidation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/inputValidation */ \"(ssr)/./src/utils/inputValidation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction NumericInput({ value, onChange, type, placeholder, className = '', disabled = false, required = false, label, id }) {\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleChange = (e)=>{\n        const inputValue = e.target.value;\n        (0,_utils_inputValidation__WEBPACK_IMPORTED_MODULE_2__.handleNumericInputChange)(inputValue, type, onChange, setError);\n    };\n    const baseClassName = `w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors duration-200 ${error ? 'border-red-500 bg-red-50' : 'border-gray-300'} ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''} ${className}`;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                htmlFor: id,\n                className: \"block text-sm font-medium text-gray-700\",\n                children: [\n                    label,\n                    required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-red-500 mr-1\",\n                        children: \"*\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\NumericInput.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 24\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\NumericInput.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        id: id,\n                        type: \"text\",\n                        value: value,\n                        onChange: handleChange,\n                        placeholder: placeholder,\n                        className: baseClassName,\n                        disabled: disabled,\n                        required: required,\n                        inputMode: type === 'phone' ? 'tel' : 'numeric',\n                        autoComplete: type === 'phone' ? 'tel' : 'off'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\NumericInput.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-5 h-5 text-red-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\NumericInput.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\NumericInput.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\NumericInput.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-red-600 flex items-center space-x-1 space-x-reverse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\NumericInput.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\NumericInput.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\NumericInput.tsx\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\NumericInput.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9OdW1lcmljSW5wdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRWdDO0FBQ1U7QUFDd0I7QUFjbkQsU0FBU0csYUFBYSxFQUNuQ0MsS0FBSyxFQUNMQyxRQUFRLEVBQ1JDLElBQUksRUFDSkMsV0FBVyxFQUNYQyxZQUFZLEVBQUUsRUFDZEMsV0FBVyxLQUFLLEVBQ2hCQyxXQUFXLEtBQUssRUFDaEJDLEtBQUssRUFDTEMsRUFBRSxFQUNnQjtJQUNsQixNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBR2QsK0NBQVFBLENBQWdCO0lBRWxELE1BQU1lLGVBQWUsQ0FBQ0M7UUFDcEIsTUFBTUMsYUFBYUQsRUFBRUUsTUFBTSxDQUFDZCxLQUFLO1FBRWpDRixnRkFBd0JBLENBQ3RCZSxZQUNBWCxNQUNBRCxVQUNBUztJQUVKO0lBRUEsTUFBTUssZ0JBQWdCLENBQUMsNEhBQTRILEVBQ2pKTixRQUFRLDZCQUE2QixrQkFDdEMsQ0FBQyxFQUFFSixXQUFXLG1DQUFtQyxHQUFHLENBQUMsRUFBRUQsV0FBVztJQUVuRSxxQkFDRSw4REFBQ1k7UUFBSVosV0FBVTs7WUFDWkcsdUJBQ0MsOERBQUNBO2dCQUFNVSxTQUFTVDtnQkFBSUosV0FBVTs7b0JBQzNCRztvQkFDQUQsMEJBQVksOERBQUNZO3dCQUFLZCxXQUFVO2tDQUFvQjs7Ozs7Ozs7Ozs7OzBCQUlyRCw4REFBQ1k7Z0JBQUlaLFdBQVU7O2tDQUNiLDhEQUFDZTt3QkFDQ1gsSUFBSUE7d0JBQ0pOLE1BQUs7d0JBQ0xGLE9BQU9BO3dCQUNQQyxVQUFVVTt3QkFDVlIsYUFBYUE7d0JBQ2JDLFdBQVdXO3dCQUNYVixVQUFVQTt3QkFDVkMsVUFBVUE7d0JBQ1ZjLFdBQVdsQixTQUFTLFVBQVUsUUFBUTt3QkFDdENtQixjQUFjbkIsU0FBUyxVQUFVLFFBQVE7Ozs7OztvQkFHMUNPLHVCQUNDLDhEQUFDTzt3QkFBSVosV0FBVTtrQ0FDYiw0RUFBQ1AsdUZBQVdBOzRCQUFDTyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztZQUs1QkssdUJBQ0MsOERBQUNhO2dCQUFFbEIsV0FBVTs7a0NBQ1gsOERBQUNQLHVGQUFXQTt3QkFBQ08sV0FBVTs7Ozs7O2tDQUN2Qiw4REFBQ2M7a0NBQU1UOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLakIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca2hhbGVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcWUFTTUlOIEFMU0hBTVxceWFzbWluLWFsc2hhbVxcc3JjXFxjb21wb25lbnRzXFxOdW1lcmljSW5wdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgQWxlcnRDaXJjbGUgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyBoYW5kbGVOdW1lcmljSW5wdXRDaGFuZ2UgfSBmcm9tICdAL3V0aWxzL2lucHV0VmFsaWRhdGlvbidcblxuaW50ZXJmYWNlIE51bWVyaWNJbnB1dFByb3BzIHtcbiAgdmFsdWU6IHN0cmluZ1xuICBvbkNoYW5nZTogKHZhbHVlOiBzdHJpbmcpID0+IHZvaWRcbiAgdHlwZTogJ21lYXN1cmVtZW50JyB8ICdwcmljZScgfCAncGhvbmUnIHwgJ29yZGVyTnVtYmVyJyB8ICdpbnRlZ2VyJyB8ICdkZWNpbWFsJ1xuICBwbGFjZWhvbGRlcj86IHN0cmluZ1xuICBjbGFzc05hbWU/OiBzdHJpbmdcbiAgZGlzYWJsZWQ/OiBib29sZWFuXG4gIHJlcXVpcmVkPzogYm9vbGVhblxuICBsYWJlbD86IHN0cmluZ1xuICBpZD86IHN0cmluZ1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOdW1lcmljSW5wdXQoe1xuICB2YWx1ZSxcbiAgb25DaGFuZ2UsXG4gIHR5cGUsXG4gIHBsYWNlaG9sZGVyLFxuICBjbGFzc05hbWUgPSAnJyxcbiAgZGlzYWJsZWQgPSBmYWxzZSxcbiAgcmVxdWlyZWQgPSBmYWxzZSxcbiAgbGFiZWwsXG4gIGlkXG59OiBOdW1lcmljSW5wdXRQcm9wcykge1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpXG5cbiAgY29uc3QgaGFuZGxlQ2hhbmdlID0gKGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgaW5wdXRWYWx1ZSA9IGUudGFyZ2V0LnZhbHVlXG4gICAgXG4gICAgaGFuZGxlTnVtZXJpY0lucHV0Q2hhbmdlKFxuICAgICAgaW5wdXRWYWx1ZSxcbiAgICAgIHR5cGUsXG4gICAgICBvbkNoYW5nZSxcbiAgICAgIHNldEVycm9yXG4gICAgKVxuICB9XG5cbiAgY29uc3QgYmFzZUNsYXNzTmFtZSA9IGB3LWZ1bGwgcHgtNCBweS0zIGJvcmRlciByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXBpbmstNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDAgJHtcbiAgICBlcnJvciA/ICdib3JkZXItcmVkLTUwMCBiZy1yZWQtNTAnIDogJ2JvcmRlci1ncmF5LTMwMCdcbiAgfSAke2Rpc2FibGVkID8gJ2JnLWdyYXktMTAwIGN1cnNvci1ub3QtYWxsb3dlZCcgOiAnJ30gJHtjbGFzc05hbWV9YFxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgIHtsYWJlbCAmJiAoXG4gICAgICAgIDxsYWJlbCBodG1sRm9yPXtpZH0gY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAge2xhYmVsfVxuICAgICAgICAgIHtyZXF1aXJlZCAmJiA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDAgbXItMVwiPio8L3NwYW4+fVxuICAgICAgICA8L2xhYmVsPlxuICAgICAgKX1cbiAgICAgIFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICA8aW5wdXRcbiAgICAgICAgICBpZD17aWR9XG4gICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgIHZhbHVlPXt2YWx1ZX1cbiAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ2hhbmdlfVxuICAgICAgICAgIHBsYWNlaG9sZGVyPXtwbGFjZWhvbGRlcn1cbiAgICAgICAgICBjbGFzc05hbWU9e2Jhc2VDbGFzc05hbWV9XG4gICAgICAgICAgZGlzYWJsZWQ9e2Rpc2FibGVkfVxuICAgICAgICAgIHJlcXVpcmVkPXtyZXF1aXJlZH1cbiAgICAgICAgICBpbnB1dE1vZGU9e3R5cGUgPT09ICdwaG9uZScgPyAndGVsJyA6ICdudW1lcmljJ31cbiAgICAgICAgICBhdXRvQ29tcGxldGU9e3R5cGUgPT09ICdwaG9uZScgPyAndGVsJyA6ICdvZmYnfVxuICAgICAgICAvPlxuICAgICAgICBcbiAgICAgICAge2Vycm9yICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yXCI+XG4gICAgICAgICAgICA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXJlZC01MDBcIiAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgICBcbiAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1yZWQtNjAwIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSBzcGFjZS14LXJldmVyc2VcIj5cbiAgICAgICAgICA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgPHNwYW4+e2Vycm9yfTwvc3Bhbj5cbiAgICAgICAgPC9wPlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiQWxlcnRDaXJjbGUiLCJoYW5kbGVOdW1lcmljSW5wdXRDaGFuZ2UiLCJOdW1lcmljSW5wdXQiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwidHlwZSIsInBsYWNlaG9sZGVyIiwiY2xhc3NOYW1lIiwiZGlzYWJsZWQiLCJyZXF1aXJlZCIsImxhYmVsIiwiaWQiLCJlcnJvciIsInNldEVycm9yIiwiaGFuZGxlQ2hhbmdlIiwiZSIsImlucHV0VmFsdWUiLCJ0YXJnZXQiLCJiYXNlQ2xhc3NOYW1lIiwiZGl2IiwiaHRtbEZvciIsInNwYW4iLCJpbnB1dCIsImlucHV0TW9kZSIsImF1dG9Db21wbGV0ZSIsInAiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/NumericInput.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProtectedRoute.tsx":
/*!*******************************************!*\
  !*** ./src/components/ProtectedRoute.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/authStore */ \"(ssr)/./src/store/authStore.ts\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction ProtectedRoute({ children, requiredRole, redirectTo = '/login' }) {\n    const { user, isLoading, checkAuth } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            const initAuth = {\n                \"ProtectedRoute.useEffect.initAuth\": async ()=>{\n                    await checkAuth();\n                    setIsChecking(false);\n                }\n            }[\"ProtectedRoute.useEffect.initAuth\"];\n            initAuth();\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        checkAuth\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            if (!isChecking && !isLoading) {\n                if (!user) {\n                    router.push(redirectTo);\n                    return;\n                }\n                if (requiredRole && user.role !== requiredRole) {\n                    router.push('/dashboard');\n                    return;\n                }\n                if (!user.is_active) {\n                    router.push('/login');\n                    return;\n                }\n            }\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        user,\n        isChecking,\n        isLoading,\n        requiredRole,\n        router,\n        redirectTo\n    ]);\n    // عرض شاشة التحميل أثناء التحقق من المصادقة\n    if (isChecking || isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    scale: 0.9\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                transition: {\n                    duration: 0.5\n                },\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-8 h-8 text-white animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 font-medium\",\n                        children: \"جاري التحقق من الصلاحيات...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this);\n    }\n    // عرض رسالة خطأ إذا لم يكن المستخدم مخول\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6\n                },\n                className: \"text-center max-w-md mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-8 h-8 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-800 mb-4\",\n                        children: \"غير مخول للوصول\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"يجب تسجيل الدخول للوصول إلى هذه الصفحة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/login'),\n                        className: \"btn-primary px-6 py-3\",\n                        children: \"تسجيل الدخول\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this);\n    }\n    if (requiredRole && user.role !== requiredRole) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6\n                },\n                className: \"text-center max-w-md mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-8 h-8 text-yellow-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-800 mb-4\",\n                        children: \"صلاحيات غير كافية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/dashboard'),\n                        className: \"btn-primary px-6 py-3\",\n                        children: \"العودة إلى لوحة التحكم\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user.is_active) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6\n                },\n                className: \"text-center max-w-md mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-8 h-8 text-gray-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-800 mb-4\",\n                        children: \"حساب غير نشط\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"تم إلغاء تفعيل حسابك. يرجى التواصل مع المدير.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/login'),\n                        className: \"btn-primary px-6 py-3\",\n                        children: \"تسجيل الدخول\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n            lineNumber: 129,\n            columnNumber: 7\n        }, this);\n    }\n    // عرض المحتوى إذا كان كل شيء صحيح\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/VoiceNotes.tsx":
/*!***************************************!*\
  !*** ./src/components/VoiceNotes.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VoiceNotes)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Pause,Play,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Pause,Play,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Pause,Play,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Pause,Play,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Pause,Play,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _hooks_useTranslation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useTranslation */ \"(ssr)/./src/hooks/useTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction VoiceNotes({ voiceNotes = [], onVoiceNotesChange, disabled = false }) {\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [playingId, setPlayingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { t } = (0,_hooks_useTranslation__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [recordingTime, setRecordingTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentAudioBlob, setCurrentAudioBlob] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioRefsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    const timerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chunksRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    // تحويل base64 إلى Blob للتشغيل\n    const base64ToBlob = (base64)=>{\n        const byteCharacters = atob(base64.split(',')[1]);\n        const byteNumbers = new Array(byteCharacters.length);\n        for(let i = 0; i < byteCharacters.length; i++){\n            byteNumbers[i] = byteCharacters.charCodeAt(i);\n        }\n        const byteArray = new Uint8Array(byteNumbers);\n        return new Blob([\n            byteArray\n        ], {\n            type: 'audio/webm'\n        });\n    };\n    // بدء التسجيل\n    const startRecording = async ()=>{\n        try {\n            setError(null);\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            const mediaRecorder = new MediaRecorder(stream);\n            mediaRecorderRef.current = mediaRecorder;\n            chunksRef.current = [];\n            mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0) {\n                    chunksRef.current.push(event.data);\n                }\n            };\n            mediaRecorder.onstop = ()=>{\n                const blob = new Blob(chunksRef.current, {\n                    type: 'audio/webm'\n                });\n                setCurrentAudioBlob(blob);\n                // تحويل إلى base64 وإضافة إلى القائمة\n                const reader = new FileReader();\n                reader.onloadend = ()=>{\n                    const base64 = reader.result;\n                    const newVoiceNote = {\n                        id: Date.now().toString(),\n                        data: base64,\n                        timestamp: Date.now(),\n                        duration: recordingTime\n                    };\n                    const updatedNotes = [\n                        ...voiceNotes,\n                        newVoiceNote\n                    ];\n                    onVoiceNotesChange(updatedNotes);\n                };\n                reader.readAsDataURL(blob);\n                // إيقاف جميع المسارات\n                stream.getTracks().forEach((track)=>track.stop());\n            };\n            mediaRecorder.start();\n            setIsRecording(true);\n            setRecordingTime(0);\n            // بدء العداد\n            timerRef.current = setInterval(()=>{\n                setRecordingTime((prev)=>prev + 1);\n            }, 1000);\n        } catch (error) {\n            console.error('خطأ في بدء التسجيل:', error);\n            setError(t('microphone_access_error'));\n        }\n    };\n    // إيقاف التسجيل\n    const stopRecording = ()=>{\n        if (mediaRecorderRef.current && isRecording) {\n            mediaRecorderRef.current.stop();\n            setIsRecording(false);\n            if (timerRef.current) {\n                clearInterval(timerRef.current);\n                timerRef.current = null;\n            }\n        }\n    };\n    // تشغيل/إيقاف الصوت\n    const togglePlayback = (voiceNote)=>{\n        const audioRefs = audioRefsRef.current;\n        // إيقاف أي تشغيل حالي\n        if (playingId && playingId !== voiceNote.id) {\n            const currentAudio = audioRefs.get(playingId);\n            if (currentAudio) {\n                currentAudio.pause();\n            }\n        }\n        let audio = audioRefs.get(voiceNote.id);\n        if (!audio) {\n            const blob = base64ToBlob(voiceNote.data);\n            audio = new Audio(URL.createObjectURL(blob));\n            audio.onended = ()=>setPlayingId(null);\n            audioRefs.set(voiceNote.id, audio);\n        }\n        if (playingId === voiceNote.id) {\n            audio.pause();\n            setPlayingId(null);\n        } else {\n            audio.play();\n            setPlayingId(voiceNote.id);\n        }\n    };\n    // حذف ملاحظة صوتية محددة\n    const deleteVoiceNote = (noteId)=>{\n        const audioRefs = audioRefsRef.current;\n        const audio = audioRefs.get(noteId);\n        if (audio) {\n            audio.pause();\n            audioRefs.delete(noteId);\n        }\n        if (playingId === noteId) {\n            setPlayingId(null);\n        }\n        const updatedNotes = voiceNotes.filter((note)=>note.id !== noteId);\n        onVoiceNotesChange(updatedNotes);\n    };\n    // تنسيق الوقت\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n    // تنسيق التاريخ\n    const formatDate = (timestamp)=>{\n        const date = new Date(timestamp);\n        return date.toLocaleString('ar-SA', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    // تنظيف الموارد عند إلغاء التحميل\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VoiceNotes.useEffect\": ()=>{\n            return ({\n                \"VoiceNotes.useEffect\": ()=>{\n                    if (timerRef.current) {\n                        clearInterval(timerRef.current);\n                    }\n                    const audioRefs = audioRefsRef.current;\n                    audioRefs.forEach({\n                        \"VoiceNotes.useEffect\": (audio)=>audio.pause()\n                    }[\"VoiceNotes.useEffect\"]);\n                    audioRefs.clear();\n                }\n            })[\"VoiceNotes.useEffect\"];\n        }\n    }[\"VoiceNotes.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 bg-red-50 text-red-800 border border-red-200 rounded-lg text-sm\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                lineNumber: 194,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4 border border-gray-200\",\n                children: !isRecording ? // زر بدء التسجيل\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: startRecording,\n                            disabled: disabled,\n                            className: \"inline-flex items-center space-x-2 space-x-reverse px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t('start_recording')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 mt-2\",\n                            children: t('click_to_record_voice_note')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 11\n                }, this) : // واجهة التسجيل\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            animate: {\n                                scale: [\n                                    1,\n                                    1.1,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 1,\n                                repeat: Infinity\n                            },\n                            className: \"inline-flex items-center space-x-2 space-x-reverse mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 bg-red-500 rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-600 font-medium\",\n                                    children: \"جاري التسجيل...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-2xl font-bold text-gray-800 mb-4\",\n                            children: formatTime(recordingTime)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: stopRecording,\n                            className: \"inline-flex items-center space-x-2 space-x-reverse px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t('stop_recording')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            voiceNotes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-medium text-gray-700\",\n                            children: [\n                                t('voice_notes'),\n                                \" (\",\n                                voiceNotes.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 11\n                    }, this),\n                    voiceNotes.map((note, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.3,\n                                delay: index * 0.1\n                            },\n                            className: \"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>togglePlayback(note),\n                                                className: \"p-2 bg-pink-600 text-white rounded-full hover:bg-pink-700 transition-colors duration-300\",\n                                                children: playingId === note.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 46\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 78\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-800\",\n                                                        children: [\n                                                            t('voice_note'),\n                                                            \" #\",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            formatDate(note.timestamp),\n                                                            note.duration && ` • ${formatTime(note.duration)}`\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>deleteVoiceNote(note.id),\n                                        disabled: disabled,\n                                        className: \"p-2 text-red-600 hover:bg-red-50 rounded-full transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        title: t('delete'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 15\n                            }, this)\n                        }, note.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 13\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n                lineNumber: 245,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\VoiceNotes.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/VoiceNotes.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useTranslation.ts":
/*!*************************************!*\
  !*** ./src/hooks/useTranslation.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useTranslation auto */ \n// الترجمات العربية\nconst arTranslations = {\n    // التنقل والعناوين الرئيسية\n    'dashboard': 'لوحة التحكم',\n    'orders': 'الطلبات',\n    'appointments': 'المواعيد',\n    'settings': 'الإعدادات',\n    'workers': 'العمال',\n    'reports': 'التقارير',\n    'logout': 'تسجيل الخروج',\n    'welcome': 'مرحباً',\n    'welcome_back': 'مرحباً بعودتك',\n    // الأزرار والإجراءات\n    'add_new_order': 'إضافة طلب جديد',\n    'book_appointment': 'حجز موعد',\n    'view_details': 'عرض التفاصيل',\n    'edit': 'تعديل',\n    'delete': 'حذف',\n    'save': 'حفظ',\n    'cancel': 'إلغاء',\n    'submit': 'إرسال',\n    'search': 'بحث',\n    'filter': 'تصفية',\n    'export': 'تصدير',\n    'print': 'طباعة',\n    'back': 'رجوع',\n    'next': 'التالي',\n    'previous': 'السابق',\n    'close': 'إغلاق',\n    'confirm': 'تأكيد',\n    'loading': 'جاري التحميل...',\n    'saving': 'جاري الحفظ...',\n    // حالات الطلبات\n    'pending': 'في الانتظار',\n    'in_progress': 'قيد التنفيذ',\n    'completed': 'مكتمل',\n    'delivered': 'تم التسليم',\n    'cancelled': 'ملغي',\n    // نصوص عامة\n    'name': 'الاسم',\n    'email': 'البريد الإلكتروني',\n    'phone': 'رقم الهاتف',\n    'address': 'العنوان',\n    'date': 'التاريخ',\n    'time': 'الوقت',\n    'status': 'الحالة',\n    'price': 'السعر',\n    'total': 'المجموع',\n    'description': 'الوصف',\n    'notes': 'ملاحظات',\n    'client_name': 'اسم الزبونة',\n    'client_phone': 'رقم هاتف الزبونة',\n    // رسائل النجاح والخطأ\n    'success': 'نجح',\n    'error': 'خطأ',\n    'warning': 'تحذير',\n    'info': 'معلومات',\n    'order_added_success': 'تم إضافة الطلب بنجاح',\n    'order_updated_success': 'تم تحديث الطلب بنجاح',\n    'order_deleted_success': 'تم حذف الطلب بنجاح',\n    'fill_required_fields': 'يرجى ملء جميع الحقول المطلوبة',\n    // نصوص إضافية مطلوبة\n    'admin_dashboard': 'لوحة تحكم المدير',\n    'worker_dashboard': 'لوحة تحكم العامل',\n    'admin': 'مدير',\n    'worker': 'عامل',\n    'change_language': 'تغيير اللغة',\n    'my_active_orders': 'طلباتي النشطة',\n    'completed_orders': 'الطلبات المكتملة',\n    'total_orders': 'إجمالي الطلبات',\n    'total_revenue': 'إجمالي الإيرادات',\n    'recent_orders': 'الطلبات الحديثة',\n    'quick_actions': 'إجراءات سريعة',\n    'view_all_orders': 'عرض جميع الطلبات',\n    'add_order': 'إضافة طلب',\n    'manage_workers': 'إدارة العمال',\n    'view_reports': 'عرض التقارير',\n    'client_name_required': 'اسم الزبونة *',\n    'phone_required': 'رقم الهاتف *',\n    'order_description_required': 'وصف الطلب *',\n    'delivery_date_required': 'موعد التسليم *',\n    'price_sar': 'السعر (ريال سعودي)',\n    'measurements_cm': 'المقاسات (بالسنتيمتر)',\n    'additional_notes': 'ملاحظات إضافية',\n    'voice_notes_optional': 'ملاحظات صوتية (اختيارية)',\n    'design_images': 'صور التصميم',\n    'fabric_type': 'نوع القماش',\n    'responsible_worker': 'العامل المسؤول',\n    'choose_worker': 'اختر العامل المسؤول',\n    'order_status': 'حالة الطلب',\n    'back_to_dashboard': 'العودة إلى لوحة التحكم',\n    'overview_today': 'نظرة عامة على أنشطة اليوم',\n    'welcome_worker': 'مرحباً بك في مساحة العمل',\n    // نصوص الفوتر\n    'home': 'الرئيسية',\n    'track_order': 'استعلام عن الطلب',\n    'fabrics': 'الأقمشة',\n    'contact_us': 'تواصلي معنا',\n    'yasmin_alsham': 'ياسمين الشام',\n    'custom_dress_tailoring': 'تفصيل فساتين حسب الطلب'\n};\n// الترجمات الإنجليزية\nconst enTranslations = {\n    // التنقل والعناوين الرئيسية\n    'dashboard': 'Dashboard',\n    'orders': 'Orders',\n    'appointments': 'Appointments',\n    'settings': 'Settings',\n    'workers': 'Workers',\n    'reports': 'Reports',\n    'logout': 'Logout',\n    'welcome': 'Welcome',\n    'welcome_back': 'Welcome Back',\n    // الأزرار والإجراءات\n    'add_new_order': 'Add New Order',\n    'book_appointment': 'Book Appointment',\n    'view_details': 'View Details',\n    'edit': 'Edit',\n    'delete': 'Delete',\n    'save': 'Save',\n    'cancel': 'Cancel',\n    'submit': 'Submit',\n    'search': 'Search',\n    'filter': 'Filter',\n    'export': 'Export',\n    'print': 'Print',\n    'back': 'Back',\n    'next': 'Next',\n    'previous': 'Previous',\n    'close': 'Close',\n    'confirm': 'Confirm',\n    'loading': 'Loading...',\n    'saving': 'Saving...',\n    // حالات الطلبات\n    'pending': 'Pending',\n    'in_progress': 'In Progress',\n    'completed': 'Completed',\n    'delivered': 'Delivered',\n    'cancelled': 'Cancelled',\n    // نصوص عامة\n    'name': 'Name',\n    'email': 'Email',\n    'phone': 'Phone',\n    'address': 'Address',\n    'date': 'Date',\n    'time': 'Time',\n    'status': 'Status',\n    'price': 'Price',\n    'total': 'Total',\n    'description': 'Description',\n    'notes': 'Notes',\n    'client_name': 'Client Name',\n    'client_phone': 'Client Phone',\n    // رسائل النجاح والخطأ\n    'success': 'Success',\n    'error': 'Error',\n    'warning': 'Warning',\n    'info': 'Info',\n    'order_added_success': 'Order added successfully',\n    'order_updated_success': 'Order updated successfully',\n    'order_deleted_success': 'Order deleted successfully',\n    'fill_required_fields': 'Please fill all required fields',\n    // نصوص إضافية مطلوبة\n    'admin_dashboard': 'Admin Dashboard',\n    'worker_dashboard': 'Worker Dashboard',\n    'admin': 'Admin',\n    'worker': 'Worker',\n    'change_language': 'Change Language',\n    'my_active_orders': 'My Active Orders',\n    'completed_orders': 'Completed Orders',\n    'total_orders': 'Total Orders',\n    'total_revenue': 'Total Revenue',\n    'recent_orders': 'Recent Orders',\n    'quick_actions': 'Quick Actions',\n    'view_all_orders': 'View All Orders',\n    'add_order': 'Add Order',\n    'manage_workers': 'Manage Workers',\n    'view_reports': 'View Reports',\n    'client_name_required': 'Client Name *',\n    'phone_required': 'Phone Number *',\n    'order_description_required': 'Order Description *',\n    'delivery_date_required': 'Delivery Date *',\n    'price_sar': 'Price (SAR)',\n    'measurements_cm': 'Measurements (cm)',\n    'additional_notes': 'Additional Notes',\n    'voice_notes_optional': 'Voice Notes (Optional)',\n    'design_images': 'Design Images',\n    'fabric_type': 'Fabric Type',\n    'responsible_worker': 'Responsible Worker',\n    'choose_worker': 'Choose Responsible Worker',\n    'order_status': 'Order Status',\n    'back_to_dashboard': 'Back to Dashboard',\n    'overview_today': 'Overview of today\\'s activities',\n    'welcome_worker': 'Welcome to your workspace',\n    // نصوص الفوتر\n    'home': 'Home',\n    'track_order': 'Track Order',\n    'fabrics': 'Fabrics',\n    'contact_us': 'Contact Us',\n    'yasmin_alsham': 'Yasmin Alsham',\n    'custom_dress_tailoring': 'Custom Dress Tailoring'\n};\n// Hook للترجمة\nfunction useTranslation() {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('ar');\n    // تحميل اللغة المحفوظة عند بدء التطبيق\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTranslation.useEffect\": ()=>{\n            const savedLanguage = localStorage.getItem('dashboard-language');\n            if (savedLanguage) {\n                setLanguage(savedLanguage);\n            }\n        }\n    }[\"useTranslation.useEffect\"], []);\n    // حفظ اللغة عند تغييرها\n    const changeLanguage = (newLanguage)=>{\n        setLanguage(newLanguage);\n        localStorage.setItem('dashboard-language', newLanguage);\n    };\n    // دالة الترجمة\n    const t = (key)=>{\n        const translations = language === 'ar' ? arTranslations : enTranslations;\n        const translation = translations[key];\n        if (typeof translation === 'string') {\n            return translation;\n        }\n        // إذا لم توجد الترجمة، أرجع المفتاح نفسه\n        return key;\n    };\n    // التحقق من اللغة الحالية\n    const isArabic = language === 'ar';\n    const isEnglish = language === 'en';\n    return {\n        language,\n        changeLanguage,\n        t,\n        isArabic,\n        isEnglish\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlVHJhbnNsYXRpb24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O29FQUUyQztBQU8zQyxtQkFBbUI7QUFDbkIsTUFBTUUsaUJBQStCO0lBQ25DLDRCQUE0QjtJQUM1QixhQUFhO0lBQ2IsVUFBVTtJQUNWLGdCQUFnQjtJQUNoQixZQUFZO0lBQ1osV0FBVztJQUNYLFdBQVc7SUFDWCxVQUFVO0lBQ1YsV0FBVztJQUNYLGdCQUFnQjtJQUVoQixxQkFBcUI7SUFDckIsaUJBQWlCO0lBQ2pCLG9CQUFvQjtJQUNwQixnQkFBZ0I7SUFDaEIsUUFBUTtJQUNSLFVBQVU7SUFDVixRQUFRO0lBQ1IsVUFBVTtJQUNWLFVBQVU7SUFDVixVQUFVO0lBQ1YsVUFBVTtJQUNWLFVBQVU7SUFDVixTQUFTO0lBQ1QsUUFBUTtJQUNSLFFBQVE7SUFDUixZQUFZO0lBQ1osU0FBUztJQUNULFdBQVc7SUFDWCxXQUFXO0lBQ1gsVUFBVTtJQUVWLGdCQUFnQjtJQUNoQixXQUFXO0lBQ1gsZUFBZTtJQUNmLGFBQWE7SUFDYixhQUFhO0lBQ2IsYUFBYTtJQUViLFlBQVk7SUFDWixRQUFRO0lBQ1IsU0FBUztJQUNULFNBQVM7SUFDVCxXQUFXO0lBQ1gsUUFBUTtJQUNSLFFBQVE7SUFDUixVQUFVO0lBQ1YsU0FBUztJQUNULFNBQVM7SUFDVCxlQUFlO0lBQ2YsU0FBUztJQUNULGVBQWU7SUFDZixnQkFBZ0I7SUFFaEIsc0JBQXNCO0lBQ3RCLFdBQVc7SUFDWCxTQUFTO0lBQ1QsV0FBVztJQUNYLFFBQVE7SUFDUix1QkFBdUI7SUFDdkIseUJBQXlCO0lBQ3pCLHlCQUF5QjtJQUN6Qix3QkFBd0I7SUFFeEIscUJBQXFCO0lBQ3JCLG1CQUFtQjtJQUNuQixvQkFBb0I7SUFDcEIsU0FBUztJQUNULFVBQVU7SUFDVixtQkFBbUI7SUFDbkIsb0JBQW9CO0lBQ3BCLG9CQUFvQjtJQUNwQixnQkFBZ0I7SUFDaEIsaUJBQWlCO0lBQ2pCLGlCQUFpQjtJQUNqQixpQkFBaUI7SUFDakIsbUJBQW1CO0lBQ25CLGFBQWE7SUFDYixrQkFBa0I7SUFDbEIsZ0JBQWdCO0lBQ2hCLHdCQUF3QjtJQUN4QixrQkFBa0I7SUFDbEIsOEJBQThCO0lBQzlCLDBCQUEwQjtJQUMxQixhQUFhO0lBQ2IsbUJBQW1CO0lBQ25CLG9CQUFvQjtJQUNwQix3QkFBd0I7SUFDeEIsaUJBQWlCO0lBQ2pCLGVBQWU7SUFDZixzQkFBc0I7SUFDdEIsaUJBQWlCO0lBQ2pCLGdCQUFnQjtJQUNoQixxQkFBcUI7SUFDckIsa0JBQWtCO0lBQ2xCLGtCQUFrQjtJQUVsQixjQUFjO0lBQ2QsUUFBUTtJQUNSLGVBQWU7SUFDZixXQUFXO0lBQ1gsY0FBYztJQUNkLGlCQUFpQjtJQUNqQiwwQkFBMEI7QUFDNUI7QUFFQSxzQkFBc0I7QUFDdEIsTUFBTUMsaUJBQStCO0lBQ25DLDRCQUE0QjtJQUM1QixhQUFhO0lBQ2IsVUFBVTtJQUNWLGdCQUFnQjtJQUNoQixZQUFZO0lBQ1osV0FBVztJQUNYLFdBQVc7SUFDWCxVQUFVO0lBQ1YsV0FBVztJQUNYLGdCQUFnQjtJQUVoQixxQkFBcUI7SUFDckIsaUJBQWlCO0lBQ2pCLG9CQUFvQjtJQUNwQixnQkFBZ0I7SUFDaEIsUUFBUTtJQUNSLFVBQVU7SUFDVixRQUFRO0lBQ1IsVUFBVTtJQUNWLFVBQVU7SUFDVixVQUFVO0lBQ1YsVUFBVTtJQUNWLFVBQVU7SUFDVixTQUFTO0lBQ1QsUUFBUTtJQUNSLFFBQVE7SUFDUixZQUFZO0lBQ1osU0FBUztJQUNULFdBQVc7SUFDWCxXQUFXO0lBQ1gsVUFBVTtJQUVWLGdCQUFnQjtJQUNoQixXQUFXO0lBQ1gsZUFBZTtJQUNmLGFBQWE7SUFDYixhQUFhO0lBQ2IsYUFBYTtJQUViLFlBQVk7SUFDWixRQUFRO0lBQ1IsU0FBUztJQUNULFNBQVM7SUFDVCxXQUFXO0lBQ1gsUUFBUTtJQUNSLFFBQVE7SUFDUixVQUFVO0lBQ1YsU0FBUztJQUNULFNBQVM7SUFDVCxlQUFlO0lBQ2YsU0FBUztJQUNULGVBQWU7SUFDZixnQkFBZ0I7SUFFaEIsc0JBQXNCO0lBQ3RCLFdBQVc7SUFDWCxTQUFTO0lBQ1QsV0FBVztJQUNYLFFBQVE7SUFDUix1QkFBdUI7SUFDdkIseUJBQXlCO0lBQ3pCLHlCQUF5QjtJQUN6Qix3QkFBd0I7SUFFeEIscUJBQXFCO0lBQ3JCLG1CQUFtQjtJQUNuQixvQkFBb0I7SUFDcEIsU0FBUztJQUNULFVBQVU7SUFDVixtQkFBbUI7SUFDbkIsb0JBQW9CO0lBQ3BCLG9CQUFvQjtJQUNwQixnQkFBZ0I7SUFDaEIsaUJBQWlCO0lBQ2pCLGlCQUFpQjtJQUNqQixpQkFBaUI7SUFDakIsbUJBQW1CO0lBQ25CLGFBQWE7SUFDYixrQkFBa0I7SUFDbEIsZ0JBQWdCO0lBQ2hCLHdCQUF3QjtJQUN4QixrQkFBa0I7SUFDbEIsOEJBQThCO0lBQzlCLDBCQUEwQjtJQUMxQixhQUFhO0lBQ2IsbUJBQW1CO0lBQ25CLG9CQUFvQjtJQUNwQix3QkFBd0I7SUFDeEIsaUJBQWlCO0lBQ2pCLGVBQWU7SUFDZixzQkFBc0I7SUFDdEIsaUJBQWlCO0lBQ2pCLGdCQUFnQjtJQUNoQixxQkFBcUI7SUFDckIsa0JBQWtCO0lBQ2xCLGtCQUFrQjtJQUVsQixjQUFjO0lBQ2QsUUFBUTtJQUNSLGVBQWU7SUFDZixXQUFXO0lBQ1gsY0FBYztJQUNkLGlCQUFpQjtJQUNqQiwwQkFBMEI7QUFDNUI7QUFFQSxlQUFlO0FBQ1IsU0FBU0M7SUFDZCxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR04sK0NBQVFBLENBQWM7SUFFdEQsdUNBQXVDO0lBQ3ZDQyxnREFBU0E7b0NBQUM7WUFDUixNQUFNTSxnQkFBZ0JDLGFBQWFDLE9BQU8sQ0FBQztZQUMzQyxJQUFJRixlQUFlO2dCQUNqQkQsWUFBWUM7WUFDZDtRQUNGO21DQUFHLEVBQUU7SUFFTCx3QkFBd0I7SUFDeEIsTUFBTUcsaUJBQWlCLENBQUNDO1FBQ3RCTCxZQUFZSztRQUNaSCxhQUFhSSxPQUFPLENBQUMsc0JBQXNCRDtJQUM3QztJQUVBLGVBQWU7SUFDZixNQUFNRSxJQUFJLENBQUNDO1FBQ1QsTUFBTUMsZUFBZVYsYUFBYSxPQUFPSCxpQkFBaUJDO1FBQzFELE1BQU1hLGNBQWNELFlBQVksQ0FBQ0QsSUFBSTtRQUVyQyxJQUFJLE9BQU9FLGdCQUFnQixVQUFVO1lBQ25DLE9BQU9BO1FBQ1Q7UUFFQSx5Q0FBeUM7UUFDekMsT0FBT0Y7SUFDVDtJQUVBLDBCQUEwQjtJQUMxQixNQUFNRyxXQUFXWixhQUFhO0lBQzlCLE1BQU1hLFlBQVliLGFBQWE7SUFFL0IsT0FBTztRQUNMQTtRQUNBSztRQUNBRztRQUNBSTtRQUNBQztJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xca2hhbGVcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcWUFTTUlOIEFMU0hBTVxceWFzbWluLWFsc2hhbVxcc3JjXFxob29rc1xcdXNlVHJhbnNsYXRpb24udHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcblxuLy8g2YbZiNi5INin2YTYqNmK2KfZhtin2Kog2YTZhNiq2LHYrNmF2KfYqlxudHlwZSBUcmFuc2xhdGlvbktleSA9IHN0cmluZ1xudHlwZSBUcmFuc2xhdGlvblZhbHVlID0gc3RyaW5nIHwgeyBba2V5OiBzdHJpbmddOiBzdHJpbmcgfVxudHlwZSBUcmFuc2xhdGlvbnMgPSB7IFtrZXk6IHN0cmluZ106IFRyYW5zbGF0aW9uVmFsdWUgfVxuXG4vLyDYp9mE2KrYsdis2YXYp9iqINin2YTYudix2KjZitipXG5jb25zdCBhclRyYW5zbGF0aW9uczogVHJhbnNsYXRpb25zID0ge1xuICAvLyDYp9mE2KrZhtmC2YQg2YjYp9mE2LnZhtin2YjZitmGINin2YTYsdim2YrYs9mK2KlcbiAgJ2Rhc2hib2FyZCc6ICfZhNmI2K3YqSDYp9mE2KrYrdmD2YUnLFxuICAnb3JkZXJzJzogJ9in2YTYt9mE2KjYp9iqJyxcbiAgJ2FwcG9pbnRtZW50cyc6ICfYp9mE2YXZiNin2LnZitivJyxcbiAgJ3NldHRpbmdzJzogJ9in2YTYpdi52K/Yp9iv2KfYqicsXG4gICd3b3JrZXJzJzogJ9in2YTYudmF2KfZhCcsXG4gICdyZXBvcnRzJzogJ9in2YTYqtmC2KfYsdmK2LEnLFxuICAnbG9nb3V0JzogJ9iq2LPYrNmK2YQg2KfZhNiu2LHZiNisJyxcbiAgJ3dlbGNvbWUnOiAn2YXYsdit2KjYp9mLJyxcbiAgJ3dlbGNvbWVfYmFjayc6ICfZhdix2K3YqNin2Ysg2KjYudmI2K/YqtmDJyxcbiAgXG4gIC8vINin2YTYo9iy2LHYp9ixINmI2KfZhNil2KzYsdin2KHYp9iqXG4gICdhZGRfbmV3X29yZGVyJzogJ9il2LbYp9mB2Kkg2LfZhNioINis2K/ZitivJyxcbiAgJ2Jvb2tfYXBwb2ludG1lbnQnOiAn2K3YrNiyINmF2YjYudivJyxcbiAgJ3ZpZXdfZGV0YWlscyc6ICfYudix2LYg2KfZhNiq2YHYp9i12YrZhCcsXG4gICdlZGl0JzogJ9iq2LnYr9mK2YQnLFxuICAnZGVsZXRlJzogJ9it2LDZgScsXG4gICdzYXZlJzogJ9it2YHYuCcsXG4gICdjYW5jZWwnOiAn2KXZhNi62KfYoScsXG4gICdzdWJtaXQnOiAn2KXYsdiz2KfZhCcsXG4gICdzZWFyY2gnOiAn2KjYrdirJyxcbiAgJ2ZpbHRlcic6ICfYqti12YHZitipJyxcbiAgJ2V4cG9ydCc6ICfYqti12K/ZitixJyxcbiAgJ3ByaW50JzogJ9i32KjYp9i52KknLFxuICAnYmFjayc6ICfYsdis2YjYuScsXG4gICduZXh0JzogJ9in2YTYqtin2YTZiicsXG4gICdwcmV2aW91cyc6ICfYp9mE2LPYp9io2YInLFxuICAnY2xvc2UnOiAn2KXYutmE2KfZgicsXG4gICdjb25maXJtJzogJ9iq2KPZg9mK2K8nLFxuICAnbG9hZGluZyc6ICfYrNin2LHZiiDYp9mE2KrYrdmF2YrZhC4uLicsXG4gICdzYXZpbmcnOiAn2KzYp9ix2Yog2KfZhNit2YHYuC4uLicsXG4gIFxuICAvLyDYrdin2YTYp9iqINin2YTYt9mE2KjYp9iqXG4gICdwZW5kaW5nJzogJ9mB2Yog2KfZhNin2YbYqti42KfYsScsXG4gICdpbl9wcm9ncmVzcyc6ICfZgtmK2K8g2KfZhNiq2YbZgdmK2LAnLFxuICAnY29tcGxldGVkJzogJ9mF2YPYqtmF2YQnLFxuICAnZGVsaXZlcmVkJzogJ9iq2YUg2KfZhNiq2LPZhNmK2YUnLFxuICAnY2FuY2VsbGVkJzogJ9mF2YTYutmKJyxcbiAgXG4gIC8vINmG2LXZiNi1INi52KfZhdipXG4gICduYW1lJzogJ9in2YTYp9iz2YUnLFxuICAnZW1haWwnOiAn2KfZhNio2LHZitivINin2YTYpdmE2YPYqtix2YjZhtmKJyxcbiAgJ3Bob25lJzogJ9ix2YLZhSDYp9mE2YfYp9iq2YEnLFxuICAnYWRkcmVzcyc6ICfYp9mE2LnZhtmI2KfZhicsXG4gICdkYXRlJzogJ9in2YTYqtin2LHZitiuJyxcbiAgJ3RpbWUnOiAn2KfZhNmI2YLYqicsXG4gICdzdGF0dXMnOiAn2KfZhNit2KfZhNipJyxcbiAgJ3ByaWNlJzogJ9in2YTYs9i52LEnLFxuICAndG90YWwnOiAn2KfZhNmF2KzZhdmI2LknLFxuICAnZGVzY3JpcHRpb24nOiAn2KfZhNmI2LXZgScsXG4gICdub3Rlcyc6ICfZhdmE2KfYrdi42KfYqicsXG4gICdjbGllbnRfbmFtZSc6ICfYp9iz2YUg2KfZhNiy2KjZiNmG2KknLFxuICAnY2xpZW50X3Bob25lJzogJ9ix2YLZhSDZh9in2KrZgSDYp9mE2LLYqNmI2YbYqScsXG4gIFxuICAvLyDYsdiz2KfYptmEINin2YTZhtis2KfYrSDZiNin2YTYrti32KNcbiAgJ3N1Y2Nlc3MnOiAn2YbYrNitJyxcbiAgJ2Vycm9yJzogJ9iu2LfYoycsXG4gICd3YXJuaW5nJzogJ9iq2K3YsNmK2LEnLFxuICAnaW5mbyc6ICfZhdi52YTZiNmF2KfYqicsXG4gICdvcmRlcl9hZGRlZF9zdWNjZXNzJzogJ9iq2YUg2KXYttin2YHYqSDYp9mE2LfZhNioINio2YbYrNin2K0nLFxuICAnb3JkZXJfdXBkYXRlZF9zdWNjZXNzJzogJ9iq2YUg2KrYrdiv2YrYqyDYp9mE2LfZhNioINio2YbYrNin2K0nLFxuICAnb3JkZXJfZGVsZXRlZF9zdWNjZXNzJzogJ9iq2YUg2K3YsNmBINin2YTYt9mE2Kgg2KjZhtis2KfYrScsXG4gICdmaWxsX3JlcXVpcmVkX2ZpZWxkcyc6ICfZitix2KzZiSDZhdmE2KEg2KzZhdmK2Lkg2KfZhNit2YLZiNmEINin2YTZhdi32YTZiNio2KknLFxuXG4gIC8vINmG2LXZiNi1INil2LbYp9mB2YrYqSDZhdi32YTZiNio2KlcbiAgJ2FkbWluX2Rhc2hib2FyZCc6ICfZhNmI2K3YqSDYqtit2YPZhSDYp9mE2YXYr9mK2LEnLFxuICAnd29ya2VyX2Rhc2hib2FyZCc6ICfZhNmI2K3YqSDYqtit2YPZhSDYp9mE2LnYp9mF2YQnLFxuICAnYWRtaW4nOiAn2YXYr9mK2LEnLFxuICAnd29ya2VyJzogJ9i52KfZhdmEJyxcbiAgJ2NoYW5nZV9sYW5ndWFnZSc6ICfYqti62YrZitixINin2YTZhNi62KknLFxuICAnbXlfYWN0aXZlX29yZGVycyc6ICfYt9mE2KjYp9iq2Yog2KfZhNmG2LTYt9ipJyxcbiAgJ2NvbXBsZXRlZF9vcmRlcnMnOiAn2KfZhNi32YTYqNin2Kog2KfZhNmF2YPYqtmF2YTYqScsXG4gICd0b3RhbF9vcmRlcnMnOiAn2KXYrNmF2KfZhNmKINin2YTYt9mE2KjYp9iqJyxcbiAgJ3RvdGFsX3JldmVudWUnOiAn2KXYrNmF2KfZhNmKINin2YTYpdmK2LHYp9iv2KfYqicsXG4gICdyZWNlbnRfb3JkZXJzJzogJ9in2YTYt9mE2KjYp9iqINin2YTYrdiv2YrYq9ipJyxcbiAgJ3F1aWNrX2FjdGlvbnMnOiAn2KXYrNix2KfYodin2Kog2LPYsdmK2LnYqScsXG4gICd2aWV3X2FsbF9vcmRlcnMnOiAn2LnYsdi2INis2YXZiti5INin2YTYt9mE2KjYp9iqJyxcbiAgJ2FkZF9vcmRlcic6ICfYpdi22KfZgdipINi32YTYqCcsXG4gICdtYW5hZ2Vfd29ya2Vycyc6ICfYpdiv2KfYsdipINin2YTYudmF2KfZhCcsXG4gICd2aWV3X3JlcG9ydHMnOiAn2LnYsdi2INin2YTYqtmC2KfYsdmK2LEnLFxuICAnY2xpZW50X25hbWVfcmVxdWlyZWQnOiAn2KfYs9mFINin2YTYstio2YjZhtipIConLFxuICAncGhvbmVfcmVxdWlyZWQnOiAn2LHZgtmFINin2YTZh9in2KrZgSAqJyxcbiAgJ29yZGVyX2Rlc2NyaXB0aW9uX3JlcXVpcmVkJzogJ9mI2LXZgSDYp9mE2LfZhNioIConLFxuICAnZGVsaXZlcnlfZGF0ZV9yZXF1aXJlZCc6ICfZhdmI2LnYryDYp9mE2KrYs9mE2YrZhSAqJyxcbiAgJ3ByaWNlX3Nhcic6ICfYp9mE2LPYudixICjYsdmK2KfZhCDYs9i52YjYr9mKKScsXG4gICdtZWFzdXJlbWVudHNfY20nOiAn2KfZhNmF2YLYp9iz2KfYqiAo2KjYp9mE2LPZhtiq2YrZhdiq2LEpJyxcbiAgJ2FkZGl0aW9uYWxfbm90ZXMnOiAn2YXZhNin2K3YuNin2Kog2KXYttin2YHZitipJyxcbiAgJ3ZvaWNlX25vdGVzX29wdGlvbmFsJzogJ9mF2YTYp9it2LjYp9iqINi12YjYqtmK2KkgKNin2K7YqtmK2KfYsdmK2KkpJyxcbiAgJ2Rlc2lnbl9pbWFnZXMnOiAn2LXZiNixINin2YTYqti12YXZitmFJyxcbiAgJ2ZhYnJpY190eXBlJzogJ9mG2YjYuSDYp9mE2YLZhdin2LQnLFxuICAncmVzcG9uc2libGVfd29ya2VyJzogJ9in2YTYudin2YXZhCDYp9mE2YXYs9ik2YjZhCcsXG4gICdjaG9vc2Vfd29ya2VyJzogJ9in2K7YqtixINin2YTYudin2YXZhCDYp9mE2YXYs9ik2YjZhCcsXG4gICdvcmRlcl9zdGF0dXMnOiAn2K3Yp9mE2Kkg2KfZhNi32YTYqCcsXG4gICdiYWNrX3RvX2Rhc2hib2FyZCc6ICfYp9mE2LnZiNiv2Kkg2KXZhNmJINmE2YjYrdipINin2YTYqtit2YPZhScsXG4gICdvdmVydmlld190b2RheSc6ICfZhti42LHYqSDYudin2YXYqSDYudmE2Ykg2KPZhti02LfYqSDYp9mE2YrZiNmFJyxcbiAgJ3dlbGNvbWVfd29ya2VyJzogJ9mF2LHYrdio2KfZiyDYqNmDINmB2Yog2YXYs9in2K3YqSDYp9mE2LnZhdmEJyxcblxuICAvLyDZhti12YjYtSDYp9mE2YHZiNiq2LFcbiAgJ2hvbWUnOiAn2KfZhNix2KbZitiz2YrYqScsXG4gICd0cmFja19vcmRlcic6ICfYp9iz2KrYudmE2KfZhSDYudmGINin2YTYt9mE2KgnLFxuICAnZmFicmljcyc6ICfYp9mE2KPZgtmF2LTYqScsXG4gICdjb250YWN0X3VzJzogJ9iq2YjYp9i12YTZiiDZhdi52YbYpycsXG4gICd5YXNtaW5fYWxzaGFtJzogJ9mK2KfYs9mF2YrZhiDYp9mE2LTYp9mFJyxcbiAgJ2N1c3RvbV9kcmVzc190YWlsb3JpbmcnOiAn2KrZgdi12YrZhCDZgdiz2KfYqtmK2YYg2K3Ys9ioINin2YTYt9mE2KgnXG59XG5cbi8vINin2YTYqtix2KzZhdin2Kog2KfZhNil2YbYrNmE2YrYstmK2KlcbmNvbnN0IGVuVHJhbnNsYXRpb25zOiBUcmFuc2xhdGlvbnMgPSB7XG4gIC8vINin2YTYqtmG2YLZhCDZiNin2YTYudmG2KfZiNmK2YYg2KfZhNix2KbZitiz2YrYqVxuICAnZGFzaGJvYXJkJzogJ0Rhc2hib2FyZCcsXG4gICdvcmRlcnMnOiAnT3JkZXJzJyxcbiAgJ2FwcG9pbnRtZW50cyc6ICdBcHBvaW50bWVudHMnLFxuICAnc2V0dGluZ3MnOiAnU2V0dGluZ3MnLFxuICAnd29ya2Vycyc6ICdXb3JrZXJzJyxcbiAgJ3JlcG9ydHMnOiAnUmVwb3J0cycsXG4gICdsb2dvdXQnOiAnTG9nb3V0JyxcbiAgJ3dlbGNvbWUnOiAnV2VsY29tZScsXG4gICd3ZWxjb21lX2JhY2snOiAnV2VsY29tZSBCYWNrJyxcbiAgXG4gIC8vINin2YTYo9iy2LHYp9ixINmI2KfZhNil2KzYsdin2KHYp9iqXG4gICdhZGRfbmV3X29yZGVyJzogJ0FkZCBOZXcgT3JkZXInLFxuICAnYm9va19hcHBvaW50bWVudCc6ICdCb29rIEFwcG9pbnRtZW50JyxcbiAgJ3ZpZXdfZGV0YWlscyc6ICdWaWV3IERldGFpbHMnLFxuICAnZWRpdCc6ICdFZGl0JyxcbiAgJ2RlbGV0ZSc6ICdEZWxldGUnLFxuICAnc2F2ZSc6ICdTYXZlJyxcbiAgJ2NhbmNlbCc6ICdDYW5jZWwnLFxuICAnc3VibWl0JzogJ1N1Ym1pdCcsXG4gICdzZWFyY2gnOiAnU2VhcmNoJyxcbiAgJ2ZpbHRlcic6ICdGaWx0ZXInLFxuICAnZXhwb3J0JzogJ0V4cG9ydCcsXG4gICdwcmludCc6ICdQcmludCcsXG4gICdiYWNrJzogJ0JhY2snLFxuICAnbmV4dCc6ICdOZXh0JyxcbiAgJ3ByZXZpb3VzJzogJ1ByZXZpb3VzJyxcbiAgJ2Nsb3NlJzogJ0Nsb3NlJyxcbiAgJ2NvbmZpcm0nOiAnQ29uZmlybScsXG4gICdsb2FkaW5nJzogJ0xvYWRpbmcuLi4nLFxuICAnc2F2aW5nJzogJ1NhdmluZy4uLicsXG4gIFxuICAvLyDYrdin2YTYp9iqINin2YTYt9mE2KjYp9iqXG4gICdwZW5kaW5nJzogJ1BlbmRpbmcnLFxuICAnaW5fcHJvZ3Jlc3MnOiAnSW4gUHJvZ3Jlc3MnLFxuICAnY29tcGxldGVkJzogJ0NvbXBsZXRlZCcsXG4gICdkZWxpdmVyZWQnOiAnRGVsaXZlcmVkJyxcbiAgJ2NhbmNlbGxlZCc6ICdDYW5jZWxsZWQnLFxuICBcbiAgLy8g2YbYtdmI2LUg2LnYp9mF2KlcbiAgJ25hbWUnOiAnTmFtZScsXG4gICdlbWFpbCc6ICdFbWFpbCcsXG4gICdwaG9uZSc6ICdQaG9uZScsXG4gICdhZGRyZXNzJzogJ0FkZHJlc3MnLFxuICAnZGF0ZSc6ICdEYXRlJyxcbiAgJ3RpbWUnOiAnVGltZScsXG4gICdzdGF0dXMnOiAnU3RhdHVzJyxcbiAgJ3ByaWNlJzogJ1ByaWNlJyxcbiAgJ3RvdGFsJzogJ1RvdGFsJyxcbiAgJ2Rlc2NyaXB0aW9uJzogJ0Rlc2NyaXB0aW9uJyxcbiAgJ25vdGVzJzogJ05vdGVzJyxcbiAgJ2NsaWVudF9uYW1lJzogJ0NsaWVudCBOYW1lJyxcbiAgJ2NsaWVudF9waG9uZSc6ICdDbGllbnQgUGhvbmUnLFxuICBcbiAgLy8g2LHYs9in2KbZhCDYp9mE2YbYrNin2K0g2YjYp9mE2K7Yt9ijXG4gICdzdWNjZXNzJzogJ1N1Y2Nlc3MnLFxuICAnZXJyb3InOiAnRXJyb3InLFxuICAnd2FybmluZyc6ICdXYXJuaW5nJyxcbiAgJ2luZm8nOiAnSW5mbycsXG4gICdvcmRlcl9hZGRlZF9zdWNjZXNzJzogJ09yZGVyIGFkZGVkIHN1Y2Nlc3NmdWxseScsXG4gICdvcmRlcl91cGRhdGVkX3N1Y2Nlc3MnOiAnT3JkZXIgdXBkYXRlZCBzdWNjZXNzZnVsbHknLFxuICAnb3JkZXJfZGVsZXRlZF9zdWNjZXNzJzogJ09yZGVyIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5JyxcbiAgJ2ZpbGxfcmVxdWlyZWRfZmllbGRzJzogJ1BsZWFzZSBmaWxsIGFsbCByZXF1aXJlZCBmaWVsZHMnLFxuXG4gIC8vINmG2LXZiNi1INil2LbYp9mB2YrYqSDZhdi32YTZiNio2KlcbiAgJ2FkbWluX2Rhc2hib2FyZCc6ICdBZG1pbiBEYXNoYm9hcmQnLFxuICAnd29ya2VyX2Rhc2hib2FyZCc6ICdXb3JrZXIgRGFzaGJvYXJkJyxcbiAgJ2FkbWluJzogJ0FkbWluJyxcbiAgJ3dvcmtlcic6ICdXb3JrZXInLFxuICAnY2hhbmdlX2xhbmd1YWdlJzogJ0NoYW5nZSBMYW5ndWFnZScsXG4gICdteV9hY3RpdmVfb3JkZXJzJzogJ015IEFjdGl2ZSBPcmRlcnMnLFxuICAnY29tcGxldGVkX29yZGVycyc6ICdDb21wbGV0ZWQgT3JkZXJzJyxcbiAgJ3RvdGFsX29yZGVycyc6ICdUb3RhbCBPcmRlcnMnLFxuICAndG90YWxfcmV2ZW51ZSc6ICdUb3RhbCBSZXZlbnVlJyxcbiAgJ3JlY2VudF9vcmRlcnMnOiAnUmVjZW50IE9yZGVycycsXG4gICdxdWlja19hY3Rpb25zJzogJ1F1aWNrIEFjdGlvbnMnLFxuICAndmlld19hbGxfb3JkZXJzJzogJ1ZpZXcgQWxsIE9yZGVycycsXG4gICdhZGRfb3JkZXInOiAnQWRkIE9yZGVyJyxcbiAgJ21hbmFnZV93b3JrZXJzJzogJ01hbmFnZSBXb3JrZXJzJyxcbiAgJ3ZpZXdfcmVwb3J0cyc6ICdWaWV3IFJlcG9ydHMnLFxuICAnY2xpZW50X25hbWVfcmVxdWlyZWQnOiAnQ2xpZW50IE5hbWUgKicsXG4gICdwaG9uZV9yZXF1aXJlZCc6ICdQaG9uZSBOdW1iZXIgKicsXG4gICdvcmRlcl9kZXNjcmlwdGlvbl9yZXF1aXJlZCc6ICdPcmRlciBEZXNjcmlwdGlvbiAqJyxcbiAgJ2RlbGl2ZXJ5X2RhdGVfcmVxdWlyZWQnOiAnRGVsaXZlcnkgRGF0ZSAqJyxcbiAgJ3ByaWNlX3Nhcic6ICdQcmljZSAoU0FSKScsXG4gICdtZWFzdXJlbWVudHNfY20nOiAnTWVhc3VyZW1lbnRzIChjbSknLFxuICAnYWRkaXRpb25hbF9ub3Rlcyc6ICdBZGRpdGlvbmFsIE5vdGVzJyxcbiAgJ3ZvaWNlX25vdGVzX29wdGlvbmFsJzogJ1ZvaWNlIE5vdGVzIChPcHRpb25hbCknLFxuICAnZGVzaWduX2ltYWdlcyc6ICdEZXNpZ24gSW1hZ2VzJyxcbiAgJ2ZhYnJpY190eXBlJzogJ0ZhYnJpYyBUeXBlJyxcbiAgJ3Jlc3BvbnNpYmxlX3dvcmtlcic6ICdSZXNwb25zaWJsZSBXb3JrZXInLFxuICAnY2hvb3NlX3dvcmtlcic6ICdDaG9vc2UgUmVzcG9uc2libGUgV29ya2VyJyxcbiAgJ29yZGVyX3N0YXR1cyc6ICdPcmRlciBTdGF0dXMnLFxuICAnYmFja190b19kYXNoYm9hcmQnOiAnQmFjayB0byBEYXNoYm9hcmQnLFxuICAnb3ZlcnZpZXdfdG9kYXknOiAnT3ZlcnZpZXcgb2YgdG9kYXlcXCdzIGFjdGl2aXRpZXMnLFxuICAnd2VsY29tZV93b3JrZXInOiAnV2VsY29tZSB0byB5b3VyIHdvcmtzcGFjZScsXG5cbiAgLy8g2YbYtdmI2LUg2KfZhNmB2YjYqtixXG4gICdob21lJzogJ0hvbWUnLFxuICAndHJhY2tfb3JkZXInOiAnVHJhY2sgT3JkZXInLFxuICAnZmFicmljcyc6ICdGYWJyaWNzJyxcbiAgJ2NvbnRhY3RfdXMnOiAnQ29udGFjdCBVcycsXG4gICd5YXNtaW5fYWxzaGFtJzogJ1lhc21pbiBBbHNoYW0nLFxuICAnY3VzdG9tX2RyZXNzX3RhaWxvcmluZyc6ICdDdXN0b20gRHJlc3MgVGFpbG9yaW5nJ1xufVxuXG4vLyBIb29rINmE2YTYqtix2KzZhdipXG5leHBvcnQgZnVuY3Rpb24gdXNlVHJhbnNsYXRpb24oKSB7XG4gIGNvbnN0IFtsYW5ndWFnZSwgc2V0TGFuZ3VhZ2VdID0gdXNlU3RhdGU8J2FyJyB8ICdlbic+KCdhcicpXG5cbiAgLy8g2KrYrdmF2YrZhCDYp9mE2YTYutipINin2YTZhdit2YHZiNi42Kkg2LnZhtivINio2K/YoSDYp9mE2KrYt9io2YrZglxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHNhdmVkTGFuZ3VhZ2UgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnZGFzaGJvYXJkLWxhbmd1YWdlJykgYXMgJ2FyJyB8ICdlbidcbiAgICBpZiAoc2F2ZWRMYW5ndWFnZSkge1xuICAgICAgc2V0TGFuZ3VhZ2Uoc2F2ZWRMYW5ndWFnZSlcbiAgICB9XG4gIH0sIFtdKVxuXG4gIC8vINit2YHYuCDYp9mE2YTYutipINi52YbYryDYqti62YrZitix2YfYp1xuICBjb25zdCBjaGFuZ2VMYW5ndWFnZSA9IChuZXdMYW5ndWFnZTogJ2FyJyB8ICdlbicpID0+IHtcbiAgICBzZXRMYW5ndWFnZShuZXdMYW5ndWFnZSlcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnZGFzaGJvYXJkLWxhbmd1YWdlJywgbmV3TGFuZ3VhZ2UpXG4gIH1cblxuICAvLyDYr9in2YTYqSDYp9mE2KrYsdis2YXYqVxuICBjb25zdCB0ID0gKGtleTogVHJhbnNsYXRpb25LZXkpOiBzdHJpbmcgPT4ge1xuICAgIGNvbnN0IHRyYW5zbGF0aW9ucyA9IGxhbmd1YWdlID09PSAnYXInID8gYXJUcmFuc2xhdGlvbnMgOiBlblRyYW5zbGF0aW9uc1xuICAgIGNvbnN0IHRyYW5zbGF0aW9uID0gdHJhbnNsYXRpb25zW2tleV1cbiAgICBcbiAgICBpZiAodHlwZW9mIHRyYW5zbGF0aW9uID09PSAnc3RyaW5nJykge1xuICAgICAgcmV0dXJuIHRyYW5zbGF0aW9uXG4gICAgfVxuICAgIFxuICAgIC8vINil2LDYpyDZhNmFINiq2YjYrNivINin2YTYqtix2KzZhdip2Iwg2KPYsdis2Lkg2KfZhNmF2YHYqtin2K0g2YbZgdiz2YdcbiAgICByZXR1cm4ga2V5XG4gIH1cblxuICAvLyDYp9mE2KrYrdmC2YIg2YXZhiDYp9mE2YTYutipINin2YTYrdin2YTZitipXG4gIGNvbnN0IGlzQXJhYmljID0gbGFuZ3VhZ2UgPT09ICdhcidcbiAgY29uc3QgaXNFbmdsaXNoID0gbGFuZ3VhZ2UgPT09ICdlbidcblxuICByZXR1cm4ge1xuICAgIGxhbmd1YWdlLFxuICAgIGNoYW5nZUxhbmd1YWdlLFxuICAgIHQsXG4gICAgaXNBcmFiaWMsXG4gICAgaXNFbmdsaXNoXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsImFyVHJhbnNsYXRpb25zIiwiZW5UcmFuc2xhdGlvbnMiLCJ1c2VUcmFuc2xhdGlvbiIsImxhbmd1YWdlIiwic2V0TGFuZ3VhZ2UiLCJzYXZlZExhbmd1YWdlIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImNoYW5nZUxhbmd1YWdlIiwibmV3TGFuZ3VhZ2UiLCJzZXRJdGVtIiwidCIsImtleSIsInRyYW5zbGF0aW9ucyIsInRyYW5zbGF0aW9uIiwiaXNBcmFiaWMiLCJpc0VuZ2xpc2giXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useTranslation.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/authStore.ts":
/*!********************************!*\
  !*** ./src/store/authStore.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// بيانات المستخدمين الافتراضية (سيتم استبدالها بنظام إدارة العمال)\nconst getStoredUsers = ()=>{\n    if (true) return [];\n    const stored = localStorage.getItem('yasmin-users');\n    if (stored) {\n        return JSON.parse(stored);\n    }\n    // المستخدمين الافتراضيين\n    const defaultUsers = [\n        {\n            id: '1',\n            email: '<EMAIL>',\n            password: 'admin123',\n            full_name: 'مدير النظام',\n            role: 'admin',\n            is_active: true\n        }\n    ];\n    localStorage.setItem('yasmin-users', JSON.stringify(defaultUsers));\n    return defaultUsers;\n};\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        user: null,\n        isLoading: false,\n        error: null,\n        signIn: async (email, password)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                console.log('🔐 بدء عملية تسجيل الدخول...', {\n                    email\n                });\n                // محاكاة تأخير الشبكة\n                await new Promise((resolve)=>setTimeout(resolve, 1500));\n                // البحث عن المستخدم في البيانات المحفوظة\n                const users = getStoredUsers();\n                const foundUser = users.find((user)=>user.email.toLowerCase() === email.toLowerCase() && user.password === password);\n                if (foundUser) {\n                    console.log('✅ تم العثور على المستخدم:', foundUser.full_name);\n                    const user = {\n                        id: foundUser.id,\n                        email: foundUser.email,\n                        full_name: foundUser.full_name,\n                        role: foundUser.role,\n                        is_active: foundUser.is_active,\n                        created_at: new Date().toISOString(),\n                        updated_at: new Date().toISOString(),\n                        token: `demo-token-${foundUser.id}-${Date.now()}`\n                    };\n                    // حفظ في localStorage أولاً\n                    if (false) {}\n                    // تحديث حالة المتجر\n                    set({\n                        user,\n                        isLoading: false,\n                        error: null\n                    });\n                    console.log('🎉 تم تسجيل الدخول بنجاح!');\n                    return true;\n                } else {\n                    console.log('❌ بيانات تسجيل الدخول غير صحيحة');\n                    set({\n                        error: 'بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور.',\n                        isLoading: false\n                    });\n                    return false;\n                }\n            } catch (error) {\n                console.error('💥 خطأ في تسجيل الدخول:', error);\n                set({\n                    error: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',\n                    isLoading: false\n                });\n                return false;\n            }\n        },\n        signOut: async ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                // محاكاة تأخير تسجيل الخروج\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                // مسح البيانات من localStorage\n                if (false) {}\n                set({\n                    user: null,\n                    isLoading: false,\n                    error: null\n                });\n            } catch (error) {\n                console.error('خطأ في تسجيل الخروج:', error);\n                set({\n                    isLoading: false,\n                    error: 'خطأ في تسجيل الخروج'\n                });\n            }\n        },\n        setUser: (user)=>{\n            set({\n                user\n            });\n            // تحديث localStorage\n            if (false) {}\n        },\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        checkAuth: async ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                // التحقق من وجود مستخدم محفوظ في localStorage\n                if (false) {}\n                set({\n                    user: null,\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error('خطأ في التحقق من المصادقة:', error);\n                set({\n                    user: null,\n                    isLoading: false\n                });\n            }\n        },\n        isAuthenticated: ()=>{\n            const state = get();\n            return state.user !== null && state.user.is_active;\n        }\n    }), {\n    name: 'yasmin-auth-storage',\n    partialize: (state)=>({\n            user: state.user\n        })\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/authStore.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/dataStore.ts":
/*!********************************!*\
  !*** ./src/store/dataStore.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDataStore: () => (/* binding */ useDataStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// توليد ID فريد\nconst generateId = ()=>{\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n};\nconst useDataStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // البيانات الأولية\n        appointments: [],\n        orders: [],\n        workers: [],\n        isLoading: false,\n        error: null,\n        // إدارة المواعيد\n        addAppointment: (appointmentData)=>{\n            const appointment = {\n                ...appointmentData,\n                id: generateId(),\n                status: 'pending',\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            set((state)=>({\n                    appointments: [\n                        ...state.appointments,\n                        appointment\n                    ],\n                    error: null\n                }));\n            console.log('✅ تم إضافة موعد جديد:', appointment);\n        },\n        updateAppointment: (id, updates)=>{\n            set((state)=>({\n                    appointments: state.appointments.map((appointment)=>appointment.id === id ? {\n                            ...appointment,\n                            ...updates,\n                            updatedAt: new Date().toISOString()\n                        } : appointment),\n                    error: null\n                }));\n            console.log('✅ تم تحديث الموعد:', id);\n        },\n        deleteAppointment: (id)=>{\n            set((state)=>({\n                    appointments: state.appointments.filter((appointment)=>appointment.id !== id),\n                    error: null\n                }));\n            console.log('✅ تم حذف الموعد:', id);\n        },\n        getAppointment: (id)=>{\n            const state = get();\n            return state.appointments.find((appointment)=>appointment.id === id);\n        },\n        // إدارة الطلبات\n        addOrder: (orderData)=>{\n            const order = {\n                ...orderData,\n                id: generateId(),\n                status: 'pending',\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            set((state)=>({\n                    orders: [\n                        ...state.orders,\n                        order\n                    ],\n                    error: null\n                }));\n            console.log('✅ تم إضافة طلب جديد:', order);\n        },\n        updateOrder: (id, updates)=>{\n            set((state)=>({\n                    orders: state.orders.map((order)=>order.id === id ? {\n                            ...order,\n                            ...updates,\n                            updatedAt: new Date().toISOString()\n                        } : order),\n                    error: null\n                }));\n            console.log('✅ تم تحديث الطلب:', id);\n        },\n        deleteOrder: (id)=>{\n            set((state)=>({\n                    orders: state.orders.filter((order)=>order.id !== id),\n                    error: null\n                }));\n            console.log('✅ تم حذف الطلب:', id);\n        },\n        getOrder: (id)=>{\n            const state = get();\n            return state.orders.find((order)=>order.id === id);\n        },\n        // إدارة العمال\n        addWorker: (workerData)=>{\n            const worker = {\n                ...workerData,\n                id: generateId(),\n                role: 'worker',\n                is_active: true,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            set((state)=>({\n                    workers: [\n                        ...state.workers,\n                        worker\n                    ],\n                    error: null\n                }));\n            // إضافة العامل إلى نظام المصادقة\n            if (false) {}\n            console.log('✅ تم إضافة عامل جديد:', worker);\n        },\n        updateWorker: (id, updates)=>{\n            set((state)=>({\n                    workers: state.workers.map((worker)=>worker.id === id ? {\n                            ...worker,\n                            ...updates,\n                            updatedAt: new Date().toISOString()\n                        } : worker),\n                    error: null\n                }));\n            // تحديث بيانات المصادقة إذا تم تغيير البريد أو كلمة المرور\n            if (updates.email || updates.password || updates.full_name) {\n                if (false) {}\n            }\n            console.log('✅ تم تحديث العامل:', id);\n        },\n        deleteWorker: (id)=>{\n            set((state)=>({\n                    workers: state.workers.filter((worker)=>worker.id !== id),\n                    error: null\n                }));\n            // حذف من نظام المصادقة\n            if (false) {}\n            console.log('✅ تم حذف العامل:', id);\n        },\n        getWorker: (id)=>{\n            const state = get();\n            return state.workers.find((worker)=>worker.id === id);\n        },\n        // دوال خاصة للعمال\n        startOrderWork: (orderId, workerId)=>{\n            set((state)=>({\n                    orders: state.orders.map((order)=>order.id === orderId && order.assignedWorker === workerId ? {\n                            ...order,\n                            status: 'in_progress',\n                            updatedAt: new Date().toISOString()\n                        } : order),\n                    error: null\n                }));\n            console.log('✅ تم بدء العمل في الطلب:', orderId);\n        },\n        completeOrder: (orderId, workerId, completedImages = [])=>{\n            set((state)=>({\n                    orders: state.orders.map((order)=>order.id === orderId && order.assignedWorker === workerId ? {\n                            ...order,\n                            status: 'completed',\n                            completedImages: completedImages.length > 0 ? completedImages : undefined,\n                            updatedAt: new Date().toISOString()\n                        } : order),\n                    error: null\n                }));\n            console.log('✅ تم إنهاء الطلب:', orderId);\n        },\n        // وظائف مساعدة\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        loadData: ()=>{\n            set({\n                isLoading: true\n            });\n            // البيانات محفوظة تلقائياً بواسطة persist middleware\n            set({\n                isLoading: false\n            });\n        },\n        // إحصائيات\n        getStats: ()=>{\n            const state = get();\n            return {\n                totalAppointments: state.appointments.length,\n                totalOrders: state.orders.length,\n                totalWorkers: state.workers.length,\n                pendingAppointments: state.appointments.filter((a)=>a.status === 'pending').length,\n                activeOrders: state.orders.filter((o)=>[\n                        'pending',\n                        'in_progress'\n                    ].includes(o.status)).length,\n                completedOrders: state.orders.filter((o)=>o.status === 'completed').length,\n                totalRevenue: state.orders.filter((o)=>o.status === 'completed').reduce((sum, order)=>sum + order.price, 0)\n            };\n        }\n    }), {\n    name: 'yasmin-data-storage',\n    partialize: (state)=>({\n            appointments: state.appointments,\n            orders: state.orders,\n            workers: state.workers\n        })\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/dataStore.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/inputValidation.ts":
/*!**************************************!*\
  !*** ./src/utils/inputValidation.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanIntegerInput: () => (/* binding */ cleanIntegerInput),\n/* harmony export */   cleanNumericInput: () => (/* binding */ cleanNumericInput),\n/* harmony export */   cleanPhoneInput: () => (/* binding */ cleanPhoneInput),\n/* harmony export */   getValidationErrorMessage: () => (/* binding */ getValidationErrorMessage),\n/* harmony export */   handleNumericInputChange: () => (/* binding */ handleNumericInputChange),\n/* harmony export */   isIntegerOnly: () => (/* binding */ isIntegerOnly),\n/* harmony export */   isNumericOnly: () => (/* binding */ isNumericOnly),\n/* harmony export */   isValidMeasurement: () => (/* binding */ isValidMeasurement),\n/* harmony export */   isValidPhoneNumber: () => (/* binding */ isValidPhoneNumber),\n/* harmony export */   isValidPrice: () => (/* binding */ isValidPrice)\n/* harmony export */ });\n/**\n * دوال التحقق من صحة الإدخال للحقول الرقمية\n */ // التحقق من أن النص يحتوي على أرقام فقط\nconst isNumericOnly = (value)=>{\n    return /^\\d*\\.?\\d*$/.test(value);\n};\n// التحقق من أن النص يحتوي على أرقام صحيحة فقط (بدون فواصل عشرية)\nconst isIntegerOnly = (value)=>{\n    return /^\\d*$/.test(value);\n};\n// التحقق من صحة رقم الهاتف (أرقام فقط مع إمكانية وجود + في البداية)\nconst isValidPhoneNumber = (value)=>{\n    return /^(\\+)?\\d*$/.test(value);\n};\n// تنظيف الإدخال من الأحرف غير الرقمية\nconst cleanNumericInput = (value)=>{\n    return value.replace(/[^\\d.]/g, '');\n};\n// تنظيف الإدخال من الأحرف غير الرقمية (أرقام صحيحة فقط)\nconst cleanIntegerInput = (value)=>{\n    return value.replace(/[^\\d]/g, '');\n};\n// تنظيف رقم الهاتف\nconst cleanPhoneInput = (value)=>{\n    // السماح بـ + في البداية فقط\n    if (value.startsWith('+')) {\n        return '+' + value.slice(1).replace(/[^\\d]/g, '');\n    }\n    return value.replace(/[^\\d]/g, '');\n};\n// التحقق من صحة المقاس (رقم موجب)\nconst isValidMeasurement = (value)=>{\n    const num = parseFloat(value);\n    return !isNaN(num) && num > 0;\n};\n// التحقق من صحة السعر (رقم موجب)\nconst isValidPrice = (value)=>{\n    const num = parseFloat(value);\n    return !isNaN(num) && num > 0;\n};\n// رسائل الخطأ\nconst getValidationErrorMessage = (fieldType, language = 'ar')=>{\n    const messages = {\n        ar: {\n            measurement: 'يرجى إدخال رقم صحيح للمقاس',\n            price: 'يرجى إدخال سعر صحيح',\n            phone: 'يرجى إدخال رقم هاتف صحيح',\n            orderNumber: 'يرجى إدخال رقم طلب صحيح',\n            numeric: 'يرجى إدخال أرقام فقط',\n            positive: 'يرجى إدخال رقم أكبر من الصفر'\n        },\n        en: {\n            measurement: 'Please enter a valid measurement',\n            price: 'Please enter a valid price',\n            phone: 'Please enter a valid phone number',\n            orderNumber: 'Please enter a valid order number',\n            numeric: 'Please enter numbers only',\n            positive: 'Please enter a number greater than zero'\n        }\n    };\n    return messages[language][fieldType] || messages[language].numeric;\n};\n// دالة للتعامل مع تغيير الإدخال في الحقول الرقمية\nconst handleNumericInputChange = (value, fieldType, onChange, onError)=>{\n    let cleanedValue = value;\n    let isValid = true;\n    let errorMessage = null;\n    switch(fieldType){\n        case 'measurement':\n            cleanedValue = cleanNumericInput(value);\n            isValid = isValidMeasurement(cleanedValue) || cleanedValue === '';\n            if (!isValid && cleanedValue !== '') {\n                errorMessage = getValidationErrorMessage('measurement');\n            }\n            break;\n        case 'price':\n            cleanedValue = cleanNumericInput(value);\n            isValid = isValidPrice(cleanedValue) || cleanedValue === '';\n            if (!isValid && cleanedValue !== '') {\n                errorMessage = getValidationErrorMessage('price');\n            }\n            break;\n        case 'phone':\n            cleanedValue = cleanPhoneInput(value);\n            isValid = isValidPhoneNumber(cleanedValue);\n            if (!isValid) {\n                errorMessage = getValidationErrorMessage('phone');\n            }\n            break;\n        case 'orderNumber':\n            cleanedValue = cleanIntegerInput(value);\n            isValid = isIntegerOnly(cleanedValue);\n            if (!isValid) {\n                errorMessage = getValidationErrorMessage('orderNumber');\n            }\n            break;\n        case 'integer':\n            cleanedValue = cleanIntegerInput(value);\n            isValid = isIntegerOnly(cleanedValue);\n            if (!isValid) {\n                errorMessage = getValidationErrorMessage('numeric');\n            }\n            break;\n        case 'decimal':\n            cleanedValue = cleanNumericInput(value);\n            isValid = isNumericOnly(cleanedValue);\n            if (!isValid) {\n                errorMessage = getValidationErrorMessage('numeric');\n            }\n            break;\n    }\n    onChange(cleanedValue);\n    if (onError) {\n        onError(errorMessage);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/inputValidation.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils","vendor-chunks/@swc","vendor-chunks/zustand"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fadd-order%2Fpage&page=%2Fdashboard%2Fadd-order%2Fpage&appPaths=%2Fdashboard%2Fadd-order%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fadd-order%2Fpage.tsx&appDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ckhale%5CDocuments%5Caugment-projects%5CYASMIN%20ALSHAM%5Cyasmin-alsham&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();