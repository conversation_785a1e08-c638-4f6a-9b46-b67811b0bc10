# 🌸 ياسمين الشام - <PERSON><PERSON><PERSON>

**موقع تفصيل فساتين حسب الطلب بأناقة دمشقية**

A professional custom dress tailoring website built with Next.js, featuring Arabic RTL design, appointment booking system, and comprehensive order management.

![Ya<PERSON><PERSON>](https://img.shields.io/badge/Status-Production%20Ready-brightgreen)
![Next.js](https://img.shields.io/badge/Next.js-15.3.4-black)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue)
![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-3.4-38bdf8)

## 🎯 Project Overview

Yasmin <PERSON> is a modern, responsive website for a custom dress tailoring business specializing in wedding dresses, evening gowns, and special occasion wear. The website features a complete business management system with customer-facing pages and administrative dashboards.

### 🌟 Key Features

- **🎨 Beautiful Arabic RTL Design** - Fully responsive with Damascus-inspired aesthetics
- **📱 Mobile-First Approach** - Optimized for all devices with special mobile enhancements
- **📅 Smart Appointment Booking** - Automated scheduling system with time slot management
- **🛍️ E-commerce Integration** - Shopping cart, favorites, and order management
- **👗 Design Gallery** - Showcase of ready-made and custom designs
- **📊 Admin Dashboard** - Complete order management and business analytics
- **👥 Multi-Role System** - Customer, worker, and admin interfaces
- **🔒 Secure Authentication** - Protected routes and role-based access
- **📱 WhatsApp Integration** - Direct communication with customers

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm or yarn
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/khaled5i/yasmin-alsham.git
   cd yasmin-alsham
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

4. **Start the development server**
   ```bash
   # Stable version (recommended)
   npm run dev:safe

   # Or with Turbopack (faster, experimental)
   npm run dev

   # Or use the PowerShell script for Windows
   powershell -ExecutionPolicy Bypass -File start-server.ps1
   ```

5. **Open your browser**
   - Navigate to [http://localhost:3000](http://localhost:3000)
   - For port 3001: [http://localhost:3001](http://localhost:3001)

## 📁 Project Structure

```
yasmin-alsham/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── about/             # About page
│   │   ├── book-appointment/  # Appointment booking
│   │   ├── cart/              # Shopping cart
│   │   ├── dashboard/         # Admin dashboard
│   │   ├── designs/           # Design gallery & details
│   │   ├── favorites/         # User favorites
│   │   ├── login/             # Authentication
│   │   └── page.tsx           # Homepage
│   ├── components/            # Reusable React components
│   │   ├── Header.tsx         # Navigation header
│   │   ├── Hero.tsx           # Homepage hero section
│   │   ├── ReadyDesigns.tsx   # Design showcase
│   │   ├── Services.tsx       # Services section
│   │   ├── About.tsx          # About section
│   │   ├── FAQ.tsx            # Frequently asked questions
│   │   └── Footer.tsx         # Site footer
│   ├── store/                 # Zustand state management
│   │   ├── shopStore.ts       # Shopping cart & favorites
│   │   └── appointmentStore.ts # Appointment management
│   ├── data/                  # Static data and configurations
│   └── styles/                # Global styles and Tailwind config
├── public/                    # Static assets
├── docs/                      # Documentation
└── scripts/                   # Build and deployment scripts
```

## 🛠️ Technology Stack

### Frontend
- **Next.js 15.3.4** - React framework with App Router
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Animation library
- **Lucide React** - Icon library

### State Management
- **Zustand** - Lightweight state management
- **localStorage** - Persistent data storage

### Styling & UI
- **Arabic RTL Support** - Right-to-left layout
- **Responsive Design** - Mobile-first approach
- **Custom Components** - Reusable UI elements
- **Gradient Themes** - Pink to purple color scheme

### Development Tools
- **ESLint** - Code linting
- **Prettier** - Code formatting
- **TypeScript** - Static type checking

## 🎨 Design System

### Color Palette
- **Primary**: Pink to Rose gradient (`from-pink-400 to-rose-400`)
- **Secondary**: Purple accents (`purple-600`)
- **Background**: Soft gradients (`from-rose-50 via-pink-50 to-purple-50`)
- **Text**: Gray scale (`gray-600`, `gray-800`)

### Typography
- **Arabic Font**: Cairo, Noto Kufi Arabic
- **Sizes**: Responsive text sizing (text-sm to text-6xl)
- **Weights**: Regular to Bold (400-700)

### Components
- **Buttons**: `.btn-primary`, `.btn-secondary` classes
- **Cards**: Rounded corners, shadows, hover effects
- **Forms**: Consistent styling with validation states

## 📱 Mobile Optimizations

The website includes extensive mobile-specific enhancements:

### Header & Navigation
- Simplified mobile header with larger touch targets
- Hamburger menu for mobile navigation
- Optimized icon sizes (w-7 h-7 for mobile)
- Removed background frames from icons

### Layout Improvements
- Compressed footer layout for mobile
- Fixed positioning for back-to-home buttons
- Reduced padding and margins on small screens
- Optimized card layouts and spacing

### User Experience
- Permanent favorite buttons (no hover required)
- Direct navigation instead of image galleries
- Simplified interaction patterns
- Touch-friendly button sizes

## 🔧 Available Scripts

```bash
# Development
npm run dev          # Start with Turbopack (experimental)
npm run dev:safe     # Start without Turbopack (stable)
npm run dev:fallback # Fallback development mode

# Production
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint

# Windows PowerShell
./start-server.ps1   # Automated server startup with port management
```

## 🌐 Features & Pages

### Public Pages
- **Homepage** (`/`) - Hero section, featured designs, services
- **About** (`/about`) - Company story and information
- **Designs** (`/designs`) - Gallery of available designs
- **Design Details** (`/designs/[id]`) - Individual design pages
- **Appointment Booking** (`/book-appointment`) - Schedule consultations

### User Features
- **Shopping Cart** (`/cart`) - Manage selected items
- **Favorites** (`/favorites`) - Save preferred designs
- **Order Tracking** - Monitor order status

### Admin Features
- **Dashboard** (`/dashboard`) - Business overview and analytics
- **Order Management** - Process and track orders
- **Appointment Management** - Schedule and manage bookings
- **Customer Management** - Client information and history

## 🔐 Authentication & Security

### Access Levels
- **Public** - Browse designs and book appointments
- **Customer** - Manage orders and favorites
- **Worker** - View assigned tasks and orders
- **Admin** - Full system access and management

### Security Features
- Protected routes with authentication
- Role-based access control
- Secure data handling
- Input validation and sanitization

## 📊 State Management

### Zustand Stores

#### Shop Store (`shopStore.ts`)
```typescript
// Shopping cart and favorites management
- addToCart(product)
- removeFromCart(productId)
- addToFavorites(product)
- removeFromFavorites(productId)
- formatPrice(amount)
- generateWhatsAppMessage(order)
```

#### Appointment Store (`appointmentStore.ts`)
```typescript
// Appointment booking and management
- addAppointment(appointment)
- getAppointments()
- updateAppointmentStatus(id, status)
- getAvailableTimeSlots(date)
```

## 🎯 Business Logic

### Appointment System
- **Time Slots**: 4:00, 4:45, 5:30, 6:15, 7:00, 8:00, 9:00
- **Working Days**: Saturday to Thursday
- **Closed**: Friday
- **Booking Rules**: Prevent double-booking, validate time slots

### Order Management
- **Order States**: Pending, In Progress, Completed, Cancelled
- **Measurements**: Comprehensive tailoring measurements
- **Communication**: WhatsApp integration for customer contact

### Pricing System
- **Currency**: Saudi Riyal (SAR)
- **Format**: Arabic number formatting
- **Display**: "السعر: XXX ر.س" format

## 🌍 Internationalization

### Arabic RTL Support
- **Direction**: Right-to-left layout
- **Fonts**: Arabic-optimized typography
- **Spacing**: RTL-aware spacing utilities
- **Icons**: Mirrored for RTL context

### Bilingual Features
- **Worker Interface**: Arabic/English toggle
- **Admin Dashboard**: Language switching
- **Public Pages**: Arabic-only (as per requirements)

## 📈 Performance Optimizations

### Next.js Features
- **App Router** - Latest Next.js routing system
- **Server Components** - Improved performance
- **Image Optimization** - Automatic image optimization
- **Code Splitting** - Automatic bundle optimization

### Loading & Caching
- **Lazy Loading** - Components load on demand
- **Static Generation** - Pre-built pages where possible
- **Client-side Caching** - Zustand with localStorage persistence

## 🚀 Deployment

### Development
```bash
# Local development
npm run dev:safe

# With custom port
npm run dev:safe -- --port 3001
```

### Production Build
```bash
# Build the application
npm run build

# Start production server
npm run start
```

### Environment Variables
```bash
# .env.local
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_WHATSAPP_NUMBER=+************
NEXT_PUBLIC_BUSINESS_EMAIL=<EMAIL>
```

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Standards
- Follow TypeScript best practices
- Use Tailwind CSS for styling
- Maintain RTL compatibility
- Write descriptive commit messages in English
- Comment complex logic in Arabic or English

## 📞 Contact Information

### Business Details
- **Name**: ياسمين الشام (Yasmin Alsham)
- **Specialty**: تفصيل فساتين حسب الطلب بأناقة دمشقية
- **Phone**: +************
- **Address**: الخبر الشمالية، التقاطع السادس، شارع الأمير مشعل، الخبر، السعودية
- **Working Hours**: Saturday to Thursday, Closed Friday

### Developer
- **GitHub**: [@khaled5i](https://github.com/khaled5i)
- **Email**: <EMAIL>

## 📄 License

This project is proprietary software developed for Yasmin Alsham. All rights reserved.

## 🙏 Acknowledgments

- **Next.js Team** - For the amazing React framework
- **Tailwind CSS** - For the utility-first CSS framework
- **Framer Motion** - For smooth animations
- **Lucide** - For beautiful icons
- **Vercel** - For hosting and deployment platform

---

**Built with ❤️ for Yasmin Alsham - Where dreams become beautiful dresses**
