"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useTranslation.ts":
/*!*************************************!*\
  !*** ./src/hooks/useTranslation.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useTranslation auto */ \n// الترجمات العربية\nconst arTranslations = {\n    // التنقل والعناوين الرئيسية\n    'dashboard': 'لوحة التحكم',\n    'orders': 'الطلبات',\n    'appointments': 'المواعيد',\n    'settings': 'الإعدادات',\n    'workers': 'العمال',\n    'reports': 'التقارير',\n    'logout': 'تسجيل الخروج',\n    'welcome': 'مرحباً',\n    'welcome_back': 'مرحباً بعودتك',\n    // الأزرار والإجراءات\n    'add_new_order': 'إضافة طلب جديد',\n    'book_appointment': 'حجز موعد',\n    'view_details': 'عرض التفاصيل',\n    'edit': 'تعديل',\n    'delete': 'حذف',\n    'save': 'حفظ',\n    'cancel': 'إلغاء',\n    'submit': 'إرسال',\n    'search': 'بحث',\n    'filter': 'تصفية',\n    'export': 'تصدير',\n    'print': 'طباعة',\n    'back': 'رجوع',\n    'next': 'التالي',\n    'previous': 'السابق',\n    'close': 'إغلاق',\n    'confirm': 'تأكيد',\n    'loading': 'جاري التحميل...',\n    'saving': 'جاري الحفظ...',\n    // حالات الطلبات\n    'pending': 'في الانتظار',\n    'in_progress': 'قيد التنفيذ',\n    'completed': 'مكتمل',\n    'delivered': 'تم التسليم',\n    'cancelled': 'ملغي',\n    // نصوص عامة\n    'name': 'الاسم',\n    'email': 'البريد الإلكتروني',\n    'phone': 'رقم الهاتف',\n    'address': 'العنوان',\n    'date': 'التاريخ',\n    'time': 'الوقت',\n    'status': 'الحالة',\n    'price': 'السعر',\n    'total': 'المجموع',\n    'description': 'الوصف',\n    'notes': 'ملاحظات',\n    'client_name': 'اسم الزبونة',\n    'client_phone': 'رقم هاتف الزبونة',\n    // رسائل النجاح والخطأ\n    'success': 'نجح',\n    'error': 'خطأ',\n    'warning': 'تحذير',\n    'info': 'معلومات',\n    'order_added_success': 'تم إضافة الطلب بنجاح',\n    'order_updated_success': 'تم تحديث الطلب بنجاح',\n    'order_deleted_success': 'تم حذف الطلب بنجاح',\n    'fill_required_fields': 'يرجى ملء جميع الحقول المطلوبة',\n    // نصوص إضافية مطلوبة\n    'admin_dashboard': 'لوحة تحكم المدير',\n    'worker_dashboard': 'لوحة تحكم العامل',\n    'admin': 'مدير',\n    'worker': 'عامل',\n    'change_language': 'تغيير اللغة',\n    'my_active_orders': 'طلباتي النشطة',\n    'completed_orders': 'الطلبات المكتملة',\n    'total_orders': 'إجمالي الطلبات',\n    'total_revenue': 'إجمالي الإيرادات',\n    'recent_orders': 'الطلبات الحديثة',\n    'quick_actions': 'إجراءات سريعة',\n    'view_all_orders': 'عرض جميع الطلبات',\n    'add_order': 'إضافة طلب',\n    'manage_workers': 'إدارة العمال',\n    'view_reports': 'عرض التقارير',\n    'client_name_required': 'اسم الزبونة *',\n    'phone_required': 'رقم الهاتف *',\n    'order_description_required': 'وصف الطلب *',\n    'delivery_date_required': 'موعد التسليم *',\n    'price_sar': 'السعر (ريال سعودي)',\n    'measurements_cm': 'المقاسات (بالسنتيمتر)',\n    'additional_notes': 'ملاحظات إضافية',\n    'voice_notes_optional': 'ملاحظات صوتية (اختيارية)',\n    'design_images': 'صور التصميم',\n    'fabric_type': 'نوع القماش',\n    'responsible_worker': 'العامل المسؤول',\n    'choose_worker': 'اختر العامل المسؤول',\n    'order_status': 'حالة الطلب',\n    'back_to_dashboard': 'العودة إلى لوحة التحكم',\n    'overview_today': 'نظرة عامة على أنشطة اليوم',\n    'welcome_worker': 'مرحباً بك في مساحة العمل',\n    // مفاتيح مفقودة من لوحة التحكم\n    'homepage': 'الصفحة الرئيسية',\n    'my_completed_orders': 'طلباتي المكتملة',\n    'my_total_orders': 'إجمالي طلباتي',\n    'active_orders': 'الطلبات النشطة',\n    'today_appointments': 'مواعيد اليوم',\n    'statistics': 'الإحصائيات',\n    'no_orders_found': 'لا توجد طلبات',\n    'view_all': 'عرض الكل',\n    'worker_management': 'إدارة العمال',\n    'reminder': 'تذكير',\n    'you_have': 'لديك',\n    'today_appointments_reminder': 'موعد اليوم',\n    'and': 'و',\n    'orders_need_follow': 'طلبات تحتاج متابعة',\n    'detailed_reports': 'تقارير مفصلة',\n    'worker_description': 'يمكنك هنا متابعة طلباتك المخصصة لك وتحديث حالتها',\n    // مفاتيح صفحة إضافة الطلبات\n    'order_add_error': 'حدث خطأ أثناء إضافة الطلب',\n    'cm_placeholder': 'سم',\n    'shoulder': 'الكتف',\n    'shoulder_circumference': 'محيط الكتف',\n    'chest': 'الصدر',\n    'waist': 'الخصر',\n    'hips': 'الأرداف',\n    'dart_length': 'طول الخياطة',\n    'bodice_length': 'طول الجسم',\n    'neckline': 'خط الرقبة',\n    'armpit': 'الإبط',\n    'sleeve_length': 'طول الكم',\n    'forearm': 'الساعد',\n    'cuff': 'الكم',\n    'front_length': 'الطول الأمامي',\n    'back_length': 'الطول الخلفي',\n    // مفاتيح صفحة الطلبات\n    'enter_order_number': 'أدخل رقم الطلب',\n    'all_orders': 'جميع الطلبات',\n    'no_orders_assigned': 'لا توجد طلبات مخصصة لك',\n    'no_orders_assigned_desc': 'لم يتم تخصيص أي طلبات لك بعد',\n    'price_label': 'السعر',\n    'sar': 'ريال',\n    'view': 'عرض',\n    'completing': 'جاري الإنهاء...',\n    // مفاتيح صفحة العمال\n    'worker_added_success': 'تم إضافة العامل بنجاح',\n    'error_adding_worker': 'حدث خطأ أثناء إضافة العامل',\n    'worker_updated_success': 'تم تحديث العامل بنجاح',\n    'error_updating_worker': 'حدث خطأ أثناء تحديث العامل',\n    'worker_deleted_success': 'تم حذف العامل بنجاح',\n    'worker_deactivated': 'تم إلغاء تفعيل العامل',\n    'worker_activated': 'تم تفعيل العامل',\n    'adding': 'جاري الإضافة...',\n    'add_worker': 'إضافة عامل',\n    'active': 'نشط',\n    'inactive': 'غير نشط',\n    'save_changes': 'حفظ التغييرات',\n    'search_workers_placeholder': 'البحث عن العمال...',\n    // مفاتيح صفحة التقارير\n    'engagement_dress': 'فستان خطوبة',\n    'casual_dress': 'فستان يومي',\n    'other': 'أخرى',\n    'this_week': 'هذا الأسبوع',\n    'this_month': 'هذا الشهر',\n    'this_quarter': 'هذا الربع',\n    'this_year': 'هذا العام',\n    // مفاتيح صفحة المواعيد\n    'confirmed': 'مؤكد',\n    'pm': 'مساءً',\n    'am': 'صباحاً',\n    'all_statuses': 'جميع الحالات',\n    'all_dates': 'جميع التواريخ',\n    'today': 'اليوم',\n    'tomorrow': 'غداً',\n    // مفاتيح مكونات إضافية\n    'of': 'من',\n    'images_text': 'صور',\n    // مفاتيح مكون تعديل الطلب\n    'order_update_error': 'حدث خطأ أثناء تحديث الطلب',\n    'price_sar_required': 'السعر (ريال سعودي) *',\n    'status_pending': 'في الانتظار',\n    'status_in_progress': 'قيد التنفيذ',\n    'status_completed': 'مكتمل',\n    'status_delivered': 'تم التسليم',\n    'status_cancelled': 'ملغي',\n    // نصوص الفوتر\n    'home': 'الرئيسية',\n    'track_order': 'استعلام عن الطلب',\n    'fabrics': 'الأقمشة',\n    'contact_us': 'تواصلي معنا',\n    'yasmin_alsham': 'ياسمين الشام',\n    'custom_dress_tailoring': 'تفصيل فساتين حسب الطلب'\n};\n// الترجمات الإنجليزية\nconst enTranslations = {\n    // التنقل والعناوين الرئيسية\n    'dashboard': 'Dashboard',\n    'orders': 'Orders',\n    'appointments': 'Appointments',\n    'settings': 'Settings',\n    'workers': 'Workers',\n    'reports': 'Reports',\n    'logout': 'Logout',\n    'welcome': 'Welcome',\n    'welcome_back': 'Welcome Back',\n    // الأزرار والإجراءات\n    'add_new_order': 'Add New Order',\n    'book_appointment': 'Book Appointment',\n    'view_details': 'View Details',\n    'edit': 'Edit',\n    'delete': 'Delete',\n    'save': 'Save',\n    'cancel': 'Cancel',\n    'submit': 'Submit',\n    'search': 'Search',\n    'filter': 'Filter',\n    'export': 'Export',\n    'print': 'Print',\n    'back': 'Back',\n    'next': 'Next',\n    'previous': 'Previous',\n    'close': 'Close',\n    'confirm': 'Confirm',\n    'loading': 'Loading...',\n    'saving': 'Saving...',\n    // حالات الطلبات\n    'pending': 'Pending',\n    'in_progress': 'In Progress',\n    'completed': 'Completed',\n    'delivered': 'Delivered',\n    'cancelled': 'Cancelled',\n    // نصوص عامة\n    'name': 'Name',\n    'email': 'Email',\n    'phone': 'Phone',\n    'address': 'Address',\n    'date': 'Date',\n    'time': 'Time',\n    'status': 'Status',\n    'price': 'Price',\n    'total': 'Total',\n    'description': 'Description',\n    'notes': 'Notes',\n    'client_name': 'Client Name',\n    'client_phone': 'Client Phone',\n    // رسائل النجاح والخطأ\n    'success': 'Success',\n    'error': 'Error',\n    'warning': 'Warning',\n    'info': 'Info',\n    'order_added_success': 'Order added successfully',\n    'order_updated_success': 'Order updated successfully',\n    'order_deleted_success': 'Order deleted successfully',\n    'fill_required_fields': 'Please fill all required fields',\n    // نصوص إضافية مطلوبة\n    'admin_dashboard': 'Admin Dashboard',\n    'worker_dashboard': 'Worker Dashboard',\n    'admin': 'Admin',\n    'worker': 'Worker',\n    'change_language': 'Change Language',\n    'my_active_orders': 'My Active Orders',\n    'completed_orders': 'Completed Orders',\n    'total_orders': 'Total Orders',\n    'total_revenue': 'Total Revenue',\n    'recent_orders': 'Recent Orders',\n    'quick_actions': 'Quick Actions',\n    'view_all_orders': 'View All Orders',\n    'add_order': 'Add Order',\n    'manage_workers': 'Manage Workers',\n    'view_reports': 'View Reports',\n    'client_name_required': 'Client Name *',\n    'phone_required': 'Phone Number *',\n    'order_description_required': 'Order Description *',\n    'delivery_date_required': 'Delivery Date *',\n    'price_sar': 'Price (SAR)',\n    'measurements_cm': 'Measurements (cm)',\n    'additional_notes': 'Additional Notes',\n    'voice_notes_optional': 'Voice Notes (Optional)',\n    'design_images': 'Design Images',\n    'fabric_type': 'Fabric Type',\n    'responsible_worker': 'Responsible Worker',\n    'choose_worker': 'Choose Responsible Worker',\n    'order_status': 'Order Status',\n    'back_to_dashboard': 'Back to Dashboard',\n    'overview_today': 'Overview of today\\'s activities',\n    'welcome_worker': 'Welcome to your workspace',\n    // مفاتيح مفقودة من لوحة التحكم\n    'homepage': 'Homepage',\n    'my_completed_orders': 'My Completed Orders',\n    'my_total_orders': 'My Total Orders',\n    'active_orders': 'Active Orders',\n    'today_appointments': 'Today\\'s Appointments',\n    'statistics': 'Statistics',\n    'no_orders_found': 'No orders found',\n    'view_all': 'View All',\n    'worker_management': 'Worker Management',\n    'reminder': 'Reminder',\n    'you_have': 'You have',\n    'today_appointments_reminder': 'appointments today',\n    'and': 'and',\n    'orders_need_follow': 'orders that need follow-up',\n    'detailed_reports': 'Detailed Reports',\n    'worker_description': 'Here you can track your assigned orders and update their status',\n    // مفاتيح صفحة إضافة الطلبات\n    'order_add_error': 'An error occurred while adding the order',\n    'cm_placeholder': 'cm',\n    'shoulder': 'Shoulder',\n    'shoulder_circumference': 'Shoulder Circumference',\n    'chest': 'Chest',\n    'waist': 'Waist',\n    'hips': 'Hips',\n    'dart_length': 'Dart Length',\n    'bodice_length': 'Bodice Length',\n    'neckline': 'Neckline',\n    'armpit': 'Armpit',\n    'sleeve_length': 'Sleeve Length',\n    'forearm': 'Forearm',\n    'cuff': 'Cuff',\n    'front_length': 'Front Length',\n    'back_length': 'Back Length',\n    // مفاتيح صفحة الطلبات\n    'enter_order_number': 'Enter order number',\n    'all_orders': 'All Orders',\n    'no_orders_assigned': 'No orders assigned to you',\n    'no_orders_assigned_desc': 'No orders have been assigned to you yet',\n    'price_label': 'Price',\n    'sar': 'SAR',\n    'view': 'View',\n    'completing': 'Completing...',\n    // مفاتيح صفحة العمال\n    'worker_added_success': 'Worker added successfully',\n    'error_adding_worker': 'Error adding worker',\n    'worker_updated_success': 'Worker updated successfully',\n    'error_updating_worker': 'Error updating worker',\n    'worker_deleted_success': 'Worker deleted successfully',\n    'worker_deactivated': 'Worker deactivated',\n    'worker_activated': 'Worker activated',\n    'adding': 'Adding...',\n    'add_worker': 'Add Worker',\n    'active': 'Active',\n    'inactive': 'Inactive',\n    'save_changes': 'Save Changes',\n    'search_workers_placeholder': 'Search workers...',\n    // مفاتيح صفحة التقارير\n    'engagement_dress': 'Engagement Dress',\n    'casual_dress': 'Casual Dress',\n    'other': 'Other',\n    'this_week': 'This Week',\n    'this_month': 'This Month',\n    'this_quarter': 'This Quarter',\n    'this_year': 'This Year',\n    // مفاتيح صفحة المواعيد\n    'confirmed': 'Confirmed',\n    'pm': 'PM',\n    'am': 'AM',\n    'all_statuses': 'All Statuses',\n    'all_dates': 'All Dates',\n    'today': 'Today',\n    'tomorrow': 'Tomorrow',\n    // مفاتيح مكونات إضافية\n    'of': 'of',\n    'images_text': 'images',\n    // مفاتيح مكون تعديل الطلب\n    'order_update_error': 'Error updating order',\n    'price_sar_required': 'Price (SAR) *',\n    'status_pending': 'Pending',\n    'status_in_progress': 'In Progress',\n    'status_completed': 'Completed',\n    'status_delivered': 'Delivered',\n    'status_cancelled': 'Cancelled',\n    // نصوص الفوتر\n    'home': 'Home',\n    'track_order': 'Track Order',\n    'fabrics': 'Fabrics',\n    'contact_us': 'Contact Us',\n    'yasmin_alsham': 'Yasmin Alsham',\n    'custom_dress_tailoring': 'Custom Dress Tailoring'\n};\n// Hook للترجمة\nfunction useTranslation() {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('ar');\n    // تحميل اللغة المحفوظة عند بدء التطبيق\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTranslation.useEffect\": ()=>{\n            const savedLanguage = localStorage.getItem('dashboard-language');\n            if (savedLanguage) {\n                setLanguage(savedLanguage);\n            }\n        }\n    }[\"useTranslation.useEffect\"], []);\n    // حفظ اللغة عند تغييرها\n    const changeLanguage = (newLanguage)=>{\n        setLanguage(newLanguage);\n        localStorage.setItem('dashboard-language', newLanguage);\n    };\n    // دالة الترجمة\n    const t = (key)=>{\n        const translations = language === 'ar' ? arTranslations : enTranslations;\n        const translation = translations[key];\n        if (typeof translation === 'string') {\n            return translation;\n        }\n        // إذا لم توجد الترجمة، أرجع المفتاح نفسه\n        return key;\n    };\n    // التحقق من اللغة الحالية\n    const isArabic = language === 'ar';\n    const isEnglish = language === 'en';\n    return {\n        language,\n        changeLanguage,\n        t,\n        isArabic,\n        isEnglish\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useTranslation.ts\n"));

/***/ })

});