(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[82],{1364:(e,t,r)=>{"use strict";r.d(t,{D:()=>n});var s=r(5453),a=r(6786);let l=()=>Date.now().toString(36)+Math.random().toString(36).substr(2),n=(0,s.v)()((0,a.Zr)((e,t)=>({appointments:[],orders:[],workers:[],isLoading:!1,error:null,addAppointment:t=>{let r={...t,id:l(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({appointments:[...e.appointments,r],error:null})),console.log("✅ تم إضافة موعد جديد:",r)},updateAppointment:(t,r)=>{e(e=>({appointments:e.appointments.map(e=>e.id===t?{...e,...r,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الموعد:",t)},deleteAppointment:t=>{e(e=>({appointments:e.appointments.filter(e=>e.id!==t),error:null})),console.log("✅ تم حذف الموعد:",t)},getAppointment:e=>t().appointments.find(t=>t.id===e),addOrder:t=>{let r={...t,id:l(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({orders:[...e.orders,r],error:null})),console.log("✅ تم إضافة طلب جديد:",r)},updateOrder:(t,r)=>{e(e=>({orders:e.orders.map(e=>e.id===t?{...e,...r,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الطلب:",t)},deleteOrder:t=>{e(e=>({orders:e.orders.filter(e=>e.id!==t),error:null})),console.log("✅ تم حذف الطلب:",t)},getOrder:e=>t().orders.find(t=>t.id===e),addWorker:t=>{let r={...t,id:l(),role:"worker",is_active:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({workers:[...e.workers,r],error:null}));{let e=JSON.parse(localStorage.getItem("yasmin-users")||"[]");e.push({id:r.id,email:r.email,password:r.password,full_name:r.full_name,role:"worker",is_active:!0}),localStorage.setItem("yasmin-users",JSON.stringify(e))}console.log("✅ تم إضافة عامل جديد:",r)},updateWorker:(t,r)=>{if(e(e=>({workers:e.workers.map(e=>e.id===t?{...e,...r,updatedAt:new Date().toISOString()}:e),error:null})),r.email||r.password||r.full_name){let e=JSON.parse(localStorage.getItem("yasmin-users")||"[]"),s=e.findIndex(e=>e.id===t);-1!==s&&(r.email&&(e[s].email=r.email),r.password&&(e[s].password=r.password),r.full_name&&(e[s].full_name=r.full_name),localStorage.setItem("yasmin-users",JSON.stringify(e)))}console.log("✅ تم تحديث العامل:",t)},deleteWorker:t=>{e(e=>({workers:e.workers.filter(e=>e.id!==t),error:null}));{let e=JSON.parse(localStorage.getItem("yasmin-users")||"[]").filter(e=>e.id!==t);localStorage.setItem("yasmin-users",JSON.stringify(e))}console.log("✅ تم حذف العامل:",t)},getWorker:e=>t().workers.find(t=>t.id===e),startOrderWork:(t,r)=>{e(e=>({orders:e.orders.map(e=>e.id===t&&e.assignedWorker===r?{...e,status:"in_progress",updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم بدء العمل في الطلب:",t)},completeOrder:function(t,r){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];e(e=>({orders:e.orders.map(e=>e.id===t&&e.assignedWorker===r?{...e,status:"completed",completedImages:s.length>0?s:void 0,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم إنهاء الطلب:",t)},clearError:()=>{e({error:null})},loadData:()=>{e({isLoading:!0}),e({isLoading:!1})},getStats:()=>{let e=t();return{totalAppointments:e.appointments.length,totalOrders:e.orders.length,totalWorkers:e.workers.length,pendingAppointments:e.appointments.filter(e=>"pending"===e.status).length,activeOrders:e.orders.filter(e=>["pending","in_progress"].includes(e.status)).length,completedOrders:e.orders.filter(e=>"completed"===e.status).length,totalRevenue:e.orders.filter(e=>"completed"===e.status).reduce((e,t)=>e+t.price,0)}}}),{name:"yasmin-data-storage",partialize:e=>({appointments:e.appointments,orders:e.orders,workers:e.workers})}))},2642:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var s=r(5155),a=r(2115),l=r(5339);let n=e=>/^\d*\.?\d*$/.test(e),i=e=>/^\d*$/.test(e),d=e=>/^(\+)?\d*$/.test(e),o=e=>e.replace(/[^\d.]/g,""),c=e=>e.replace(/[^\d]/g,""),m=e=>e.startsWith("+")?"+"+e.slice(1).replace(/[^\d]/g,""):e.replace(/[^\d]/g,""),p=e=>{let t=parseFloat(e);return!isNaN(t)&&t>0},x=e=>{let t=parseFloat(e);return!isNaN(t)&&t>0},u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"ar",r={ar:{measurement:"يرجى إدخال رقم صحيح للمقاس",price:"يرجى إدخال سعر صحيح",phone:"يرجى إدخال رقم هاتف صحيح",orderNumber:"يرجى إدخال رقم طلب صحيح",numeric:"يرجى إدخال أرقام فقط",positive:"يرجى إدخال رقم أكبر من الصفر"},en:{measurement:"Please enter a valid measurement",price:"Please enter a valid price",phone:"Please enter a valid phone number",orderNumber:"Please enter a valid order number",numeric:"Please enter numbers only",positive:"Please enter a number greater than zero"}};return r[t][e]||r[t].numeric},g=(e,t,r,s)=>{let a=e,l=!0,g=null;switch(t){case"measurement":p(a=o(e))||""===a||""===a||(g=u("measurement"));break;case"price":x(a=o(e))||""===a||""===a||(g=u("price"));break;case"phone":d(a=m(e))||(g=u("phone"));break;case"orderNumber":i(a=c(e))||(g=u("orderNumber"));break;case"integer":i(a=c(e))||(g=u("numeric"));break;case"decimal":n(a=o(e))||(g=u("numeric"))}r(a),s&&s(g)};function h(e){let{value:t,onChange:r,type:n,placeholder:i,className:d="",disabled:o=!1,required:c=!1,label:m,id:p}=e,[x,u]=(0,a.useState)(null),h="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors duration-200 ".concat(x?"border-red-500 bg-red-50":"border-gray-300"," ").concat(o?"bg-gray-100 cursor-not-allowed":""," ").concat(d);return(0,s.jsxs)("div",{className:"space-y-2",children:[m&&(0,s.jsxs)("label",{htmlFor:p,className:"block text-sm font-medium text-gray-700",children:[m,c&&(0,s.jsx)("span",{className:"text-red-500 mr-1",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:p,type:"text",value:t,onChange:e=>{g(e.target.value,n,r,u)},placeholder:i,className:h,disabled:o,required:c,inputMode:"phone"===n?"tel":"numeric",autoComplete:"phone"===n?"tel":"off"}),x&&(0,s.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2",children:(0,s.jsx)(l.A,{className:"w-5 h-5 text-red-500"})})]}),x&&(0,s.jsxs)("p",{className:"text-sm text-red-600 flex items-center space-x-1 space-x-reverse",children:[(0,s.jsx)(l.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:x})]})]})}},5541:(e,t,r)=>{Promise.resolve().then(r.bind(r,6244))},6244:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(5155),a=r(2115),l=r(6408),n=r(2138),i=r(9074),d=r(4186),o=r(1497),c=r(5339),m=r(646),p=r(6874),x=r.n(p),u=r(1364),g=r(2642);function h(){let[e,t]=(0,a.useState)(""),[r,p]=(0,a.useState)(""),[h,b]=(0,a.useState)(""),[f,j]=(0,a.useState)(""),[y,N]=(0,a.useState)(""),[v,w]=(0,a.useState)(!1),[k,S]=(0,a.useState)(null),[A,O]=(0,a.useState)(!1),[D,I]=(0,a.useState)({}),{addAppointment:_,appointments:P}=(0,u.D)();(0,a.useEffect)(()=>{O(!0);let e=C(),t={};e.forEach(e=>{let r=new Date(e).toLocaleDateString("ar-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"});t[e]=r}),I(t)},[]);let C=()=>{let e=[],t=new Date;for(let r=0;r<=30;r++){let s=new Date(t);s.setDate(t.getDate()+r),5!==s.getDay()&&e.push(s.toISOString().split("T")[0])}return e},W=e=>A?D[e]||"تاريخ غير متاح":"جاري تحميل التاريخ...",E=async s=>{if(s.preventDefault(),!e||!r||!h||!f)return void S({type:"error",text:"يرجى ملء جميع الحقول المطلوبة"});w(!0),S(null);try{await new Promise(e=>setTimeout(e,1500)),_({clientName:h,clientPhone:f,appointmentDate:e,appointmentTime:r,notes:y||void 0,status:"pending"}),S({type:"success",text:"تم حجز موعدك بنجاح! سنرسل لك تذكيراً قبل الموعد بساعتين."}),t(""),p(""),b(""),j(""),N("")}catch(e){console.error("خطأ في حجز الموعد:",e),S({type:"error",text:"حدث خطأ أثناء حجز الموعد. يرجى المحاولة مرة أخرى."})}finally{w(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)(x(),{href:"/",className:"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300 group",children:[(0,s.jsx)(n.A,{className:"w-5 h-5 group-hover:translate-x-1 transition-transform duration-300"}),(0,s.jsx)("span",{className:"font-medium",children:"العودة للصفحة الرئيسية"})]})}),(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-12",children:[(0,s.jsx)("h1",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6",children:(0,s.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent",children:"حجز موعد"})}),(0,s.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"احجزي موعدك بسهولة عبر نظامنا الذكي. سنقوم بتوزيع المواعيد تلقائياً على مدار أيام العمل"})]}),(0,s.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12",children:[(0,s.jsxs)(l.P.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.2},className:"space-y-8",children:[(0,s.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100",children:[(0,s.jsxs)("h3",{className:"text-2xl font-bold text-gray-800 mb-6 flex items-center space-x-3 space-x-reverse",children:[(0,s.jsx)(i.A,{className:"w-6 h-6 text-pink-600"}),(0,s.jsx)("span",{children:"معلومات المواعيد"})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-4 space-x-reverse",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-pink-400 to-rose-400 rounded-xl flex items-center justify-center flex-shrink-0",children:(0,s.jsx)(d.A,{className:"w-6 h-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-bold text-gray-800 mb-2",children:"أوقات العمل"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:"نعمل 6 أيام في الأسبوع (عدا الجمعة)"})]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-4 space-x-reverse",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-rose-400 to-purple-400 rounded-xl flex items-center justify-center flex-shrink-0",children:(0,s.jsx)(o.A,{className:"w-6 h-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-bold text-gray-800 mb-2",children:"التذكيرات"}),(0,s.jsxs)("p",{className:"text-gray-600 text-sm leading-relaxed",children:["سنرسل لك تذكيراً تلقائياً",(0,s.jsx)("br",{}),"قبل موعدك بساعتين عبر الرسائل النصية"]})]})]})]})]}),(0,s.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100",children:[(0,s.jsxs)("h3",{className:"text-2xl font-bold text-gray-800 mb-6 flex items-center space-x-3 space-x-reverse",children:[(0,s.jsx)(d.A,{className:"w-6 h-6 text-pink-600"}),(0,s.jsx)("span",{children:"معلومات زمن التفصيل"})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-4 space-x-reverse",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-400 rounded-xl flex items-center justify-center flex-shrink-0",children:(0,s.jsx)(i.A,{className:"w-6 h-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-bold text-gray-800 mb-2",children:"مدة التفصيل"}),(0,s.jsxs)("p",{className:"text-gray-600 text-sm leading-relaxed",children:["يستغرق تفصيل الفستان من ",(0,s.jsx)("span",{className:"font-semibold text-pink-600",children:"7 إلى 14 يوم عمل"})]})]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-4 space-x-reverse",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-400 to-red-400 rounded-xl flex items-center justify-center flex-shrink-0",children:(0,s.jsx)(c.A,{className:"w-6 h-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-bold text-gray-800 mb-2",children:"ملاحظة مهمة"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:"قد تختلف مدة التفصيل في المواسم بسبب الضغط، يرجى التواصل عبر الواتساب لمزيد من المعلومات"})]})]})]})]})]}),(0,s.jsx)(l.P.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.4},children:(0,s.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-800 mb-6",children:"احجزي موعدك الآن"}),k&&(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-4 rounded-lg flex items-center space-x-3 space-x-reverse ".concat("success"===k.type?"bg-green-50 text-green-800 border border-green-200":"bg-red-50 text-red-800 border border-red-200"),children:["success"===k.type?(0,s.jsx)(m.A,{className:"w-5 h-5 text-green-600"}):(0,s.jsx)(c.A,{className:"w-5 h-5 text-red-600"}),(0,s.jsx)("span",{children:k.text})]}),(0,s.jsxs)("form",{onSubmit:E,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"اختاري التاريخ *"}),(0,s.jsxs)("select",{value:e,onChange:e=>{t(e.target.value),p("")},className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",required:!0,disabled:!A,children:[(0,s.jsx)("option",{value:"",children:A?"اختاري التاريخ":"جاري تحميل التواريخ..."}),C().map(e=>(0,s.jsx)("option",{value:e,children:W(e)},e))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"اختاري الوقت *"}),e?(0,s.jsx)("div",{className:"grid grid-cols-2 gap-3",children:(e=>{let t=[{time:"16:00",display:"4:00"},{time:"16:45",display:"4:45"},{time:"17:30",display:"5:30"},{time:"18:15",display:"6:15"},{time:"19:00",display:"7:00"},{time:"20:00",display:"8:00"},{time:"21:00",display:"9:00"}],r=new Date().toISOString().split("T")[0],s=t;if(e===r){let e=new Date,r=60*e.getHours()+e.getMinutes();s=t.filter(e=>{let[t,s]=e.time.split(":").map(Number);return 60*t+s>r+30})}let a=P.filter(t=>t.appointmentDate===e&&"cancelled"!==t.status).map(e=>e.appointmentTime);return s.map(e=>({...e,isBooked:a.includes(e.time)}))})(e).map(e=>(0,s.jsx)("button",{type:"button",onClick:()=>!e.isBooked&&p(e.time),disabled:e.isBooked,className:"p-3 rounded-lg border-2 transition-all duration-300 text-sm font-medium ".concat(r===e.time?"border-pink-500 bg-pink-50 text-pink-700":e.isBooked?"border-red-300 bg-red-100 text-red-600 cursor-not-allowed":"border-gray-300 bg-white text-gray-700 hover:border-pink-300 hover:bg-pink-50"),children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"font-bold",children:e.display}),e.isBooked&&(0,s.jsx)("div",{className:"text-xs mt-1",children:"محجوز"})]})},e.time))}):(0,s.jsx)("div",{className:"p-4 border-2 border-dashed border-gray-300 rounded-lg text-center text-gray-500",children:"يرجى اختيار التاريخ أولاً"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"الاسم الكامل *"}),(0,s.jsx)("input",{type:"text",value:h,onChange:e=>b(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:"أدخلي اسمك الكامل",required:!0})]}),(0,s.jsx)("div",{children:(0,s.jsx)(g.A,{value:f,onChange:j,type:"phone",label:"رقم الهاتف *",placeholder:"أدخلي رقم هاتفك",required:!0,disabled:v})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"ملاحظات إضافية (اختياري)"}),(0,s.jsx)("textarea",{value:y,onChange:e=>N(e.target.value),rows:4,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:"أي ملاحظات أو طلبات خاصة..."})]}),(0,s.jsx)("button",{type:"submit",disabled:v,className:"w-full btn-primary py-4 text-lg disabled:opacity-50 disabled:cursor-not-allowed",children:v?(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[(0,s.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,s.jsx)("span",{children:"جاري الحجز..."})]}):(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[(0,s.jsx)(i.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"احجزي الموعد"})]})})]})]})})]})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[165,874,886,441,684,358],()=>t(5541)),_N_E=e.O()}]);