"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/designs/page",{

/***/ "(app-pages-browser)/./src/app/designs/page.tsx":
/*!**********************************!*\
  !*** ./src/app/designs/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DesignsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-2x2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _data_designs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/designs */ \"(app-pages-browser)/./src/data/designs.ts\");\n/* harmony import */ var _store_shopStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/shopStore */ \"(app-pages-browser)/./src/store/shopStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DesignsPage() {\n    _s();\n    const [selectedImageIndex, setSelectedImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGalleryOpen, setIsGalleryOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentImageIndexes, setCurrentImageIndexes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        1: 0,\n        2: 0,\n        3: 0,\n        4: 0,\n        5: 0,\n        6: 0,\n        7: 0,\n        8: 0,\n        9: 0,\n        10: 0,\n        11: 0,\n        12: 0\n    });\n    // حالة عرض البطاقات للهواتف المحمولة\n    const [isSingleColumn, setIsSingleColumn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // متجر التسوق\n    const { addToFavorites, removeFromFavorites, isFavorite, addToCart } = (0,_store_shopStore__WEBPACK_IMPORTED_MODULE_4__.useShopStore)();\n    const [addedToCart, setAddedToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // تحميل حالة العرض من localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DesignsPage.useEffect\": ()=>{\n            const savedViewMode = localStorage.getItem('yasmin-designs-view-mode');\n            if (savedViewMode === 'single') {\n                setIsSingleColumn(true);\n            }\n        }\n    }[\"DesignsPage.useEffect\"], []);\n    // حفظ حالة العرض في localStorage\n    const toggleViewMode = ()=>{\n        const newMode = !isSingleColumn;\n        setIsSingleColumn(newMode);\n        localStorage.setItem('yasmin-designs-view-mode', newMode ? 'single' : 'double');\n    };\n    // دوال التعامل مع المفضلة والسلة\n    const handleToggleFavorite = (design, e)=>{\n        e.stopPropagation();\n        const product = {\n            id: design.id.toString(),\n            name: design.title,\n            price: design.price || 299,\n            image: design.images[0],\n            description: design.description,\n            category: design.category\n        };\n        if (isFavorite(product.id)) {\n            removeFromFavorites(product.id);\n        } else {\n            addToFavorites(product);\n        }\n    };\n    const handleAddToCart = (design, e)=>{\n        e.stopPropagation();\n        const product = {\n            id: design.id.toString(),\n            name: design.title,\n            price: design.price || 299,\n            image: design.images[0],\n            description: design.description,\n            category: design.category\n        };\n        addToCart(product);\n        setAddedToCart((prev)=>[\n                ...prev,\n                design.id\n            ]);\n        setTimeout(()=>{\n            setAddedToCart((prev)=>prev.filter((id)=>id !== design.id));\n        }, 2000);\n    };\n    // دوال التنقل بين صور البطاقة\n    const nextCardImage = (designId, e)=>{\n        e.stopPropagation();\n        setCurrentImageIndexes((prev)=>({\n                ...prev,\n                [designId]: (prev[designId] + 1) % 3\n            }));\n    };\n    const prevCardImage = (designId, e)=>{\n        e.stopPropagation();\n        setCurrentImageIndexes((prev)=>({\n                ...prev,\n                [designId]: prev[designId] === 0 ? 2 : prev[designId] - 1\n            }));\n    };\n    const setCardImage = (designId, imageIndex, e)=>{\n        e.stopPropagation();\n        setCurrentImageIndexes((prev)=>({\n                ...prev,\n                [designId]: imageIndex\n            }));\n    };\n    // دوال إدارة المعرض\n    const openGallery = (index)=>{\n        setSelectedImageIndex(index);\n        setIsGalleryOpen(true);\n        document.body.style.overflow = 'hidden';\n    };\n    const closeGallery = ()=>{\n        setIsGalleryOpen(false);\n        setSelectedImageIndex(null);\n        document.body.style.overflow = 'unset';\n    };\n    const nextImage = ()=>{\n        if (selectedImageIndex !== null) {\n            const designId = _data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].id;\n            setCurrentImageIndexes((prev)=>({\n                    ...prev,\n                    [designId]: (prev[designId] + 1) % 3\n                }));\n        }\n    };\n    const prevImage = ()=>{\n        if (selectedImageIndex !== null) {\n            const designId = _data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].id;\n            setCurrentImageIndexes((prev)=>({\n                    ...prev,\n                    [designId]: prev[designId] === 0 ? 2 : prev[designId] - 1\n                }));\n        }\n    };\n    // إدارة مفتاح Escape\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DesignsPage.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"DesignsPage.useEffect.handleKeyDown\": (event)=>{\n                    if (event.key === 'Escape' && isGalleryOpen) {\n                        closeGallery();\n                    }\n                    if (event.key === 'ArrowRight' && isGalleryOpen) {\n                        nextImage();\n                    }\n                    if (event.key === 'ArrowLeft' && isGalleryOpen) {\n                        prevImage();\n                    }\n                }\n            }[\"DesignsPage.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"DesignsPage.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleKeyDown);\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"DesignsPage.useEffect\"];\n        }\n    }[\"DesignsPage.useEffect\"], [\n        isGalleryOpen,\n        selectedImageIndex\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-16 lg:pt-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6\n                },\n                className: \"fixed top-20 lg:top-24 left-4 lg:left-8 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300 bg-white/80 backdrop-blur-sm px-3 py-2 rounded-lg shadow-sm border border-pink-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4 lg:w-5 lg:h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm lg:text-base\",\n                            children: \"العودة إلى الرئيسية\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl sm:text-5xl lg:text-6xl font-bold mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"تصاميمنا الجاهزة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed mb-6\",\n                                children: \"استكشفي مجموعتنا الكاملة من التصاميم الجاهزة واختاري ما يناسب ذوقك ومناسبتك\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 max-w-2xl mx-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-800 font-medium text-center\",\n                                    children: \"✨ الفساتين الجاهزة متوفرة للشراء المباشر - لا يتطلب حجز موعد\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.2\n                        },\n                        className: \"sm:hidden mb-6 flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toggleViewMode,\n                            className: \"bg-white/80 backdrop-blur-sm border border-pink-200 rounded-xl p-3 flex items-center space-x-2 space-x-reverse hover:bg-white hover:shadow-lg transition-all duration-300\",\n                            \"aria-label\": isSingleColumn ? 'تبديل إلى العرض الثنائي' : 'تبديل إلى العرض الفردي',\n                            children: isSingleColumn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5 text-pink-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"عرض ثنائي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-5 h-5 text-pink-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"عرض فردي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-8 mb-12 \".concat(isSingleColumn ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'),\n                        children: _data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns.map((design, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: index * 0.1\n                                },\n                                className: \"group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-[4/5] bg-gradient-to-br from-pink-100 via-rose-100 to-purple-100 relative overflow-hidden cursor-pointer\",\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                openGallery(index);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: design.images[currentImageIndexes[design.id]],\n                                                    alt: \"\".concat(design.title, \" - صورة \").concat(currentImageIndexes[design.id] + 1),\n                                                    className: \"w-full h-full object-cover transition-opacity duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>prevCardImage(design.id, e),\n                                                    className: \"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-all duration-300 z-10\",\n                                                    \"aria-label\": \"الصورة السابقة\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>nextCardImage(design.id, e),\n                                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-all duration-300 z-10\",\n                                                    \"aria-label\": \"الصورة التالية\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1 space-x-reverse opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                    children: design.images.map((_, imgIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>setCardImage(design.id, imgIndex, e),\n                                                            className: \"w-2 h-2 rounded-full transition-colors duration-300 \".concat(currentImageIndexes[design.id] === imgIndex ? 'bg-white' : 'bg-white/50'),\n                                                            \"aria-label\": \"عرض الصورة \".concat(imgIndex + 1)\n                                                        }, imgIndex, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white/90 rounded-full p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-6 h-6 text-pink-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>handleToggleFavorite(design, e),\n                                                    className: \"absolute top-3 left-3 rounded-full p-2 opacity-0 group-hover:opacity-100 transition-all duration-300 hover:scale-110 \".concat(isFavorite(design.id.toString()) ? 'bg-red-500 text-white' : 'bg-white/80 hover:bg-white text-pink-500'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 \".concat(isFavorite(design.id.toString()) ? 'fill-current' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/designs/\".concat(design.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"cursor-pointer hover:bg-gray-50 transition-colors duration-300 p-2 -m-2 rounded-lg mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-gradient-to-r from-pink-100 to-rose-100 text-pink-700 px-2 py-1 rounded-full text-xs font-medium\",\n                                                                    children: design.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-bold text-gray-800 mb-2 group-hover:text-pink-600 transition-colors duration-300\",\n                                                                children: design.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 leading-relaxed mb-3\",\n                                                                children: design.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-pink-600 mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600 font-normal ml-2\",\n                                                                        children: \"السعر:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    (0,_store_shopStore__WEBPACK_IMPORTED_MODULE_4__.formatPrice)(design.price)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>handleAddToCart(design, e),\n                                                            disabled: addedToCart.includes(design.id),\n                                                            className: \"flex-1 flex items-center justify-center space-x-1 space-x-reverse py-2 px-3 rounded-lg text-sm font-medium transition-all duration-300 \".concat(addedToCart.includes(design.id) ? 'bg-green-500 text-white' : 'bg-gradient-to-r from-pink-500 to-purple-500 text-white hover:shadow-lg'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: addedToCart.includes(design.id) ? 'تم الإضافة' : 'أضف للسلة'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"https://wa.me/+966598862609?text=أريد استفسار عن \".concat(design.title),\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"px-3 py-2 border border-pink-300 text-pink-600 rounded-lg hover:bg-pink-50 transition-colors duration-300 text-sm font-medium\",\n                                                            children: \"استفسار\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, this)\n                            }, design.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    isGalleryOpen && selectedImageIndex !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4\",\n                        onClick: closeGallery,\n                        role: \"dialog\",\n                        \"aria-modal\": \"true\",\n                        \"aria-labelledby\": \"gallery-title\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative max-w-4xl w-full\",\n                            onClick: (e)=>e.stopPropagation(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: closeGallery,\n                                    className: \"absolute top-4 right-4 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-2 transition-colors duration-300\",\n                                    \"aria-label\": \"إغلاق المعرض\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: prevImage,\n                                    className: \"absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-colors duration-300\",\n                                    \"aria-label\": \"الصورة السابقة\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: nextImage,\n                                    className: \"absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-colors duration-300\",\n                                    \"aria-label\": \"الصورة التالية\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    className: \"bg-white rounded-2xl overflow-hidden shadow-2xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-[4/5] bg-gradient-to-br from-pink-100 via-rose-100 to-purple-100 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: _data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].images[currentImageIndexes[_data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].id]],\n                                            alt: \"\".concat(_data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].title, \" - صورة \").concat(currentImageIndexes[_data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].id] + 1),\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 17\n                                    }, this)\n                                }, selectedImageIndex, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n_s(DesignsPage, \"Jo//TfkgCnar91prmNOYQwjQkbg=\", false, function() {\n    return [\n        _store_shopStore__WEBPACK_IMPORTED_MODULE_4__.useShopStore\n    ];\n});\n_c = DesignsPage;\nvar _c;\n$RefreshReg$(_c, \"DesignsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/designs/page.tsx\n"));

/***/ })

});