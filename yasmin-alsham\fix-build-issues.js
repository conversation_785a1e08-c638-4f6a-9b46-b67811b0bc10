#!/usr/bin/env node

/**
 * Build Issues Fix Script for Yasmin <PERSON>sham Project
 * Automatically fixes common ESLint and TypeScript issues
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Starting build issues fix for Yasmin <PERSON> project...\n');

// Common fixes to apply
const fixes = {
  // Remove unused imports
  removeUnusedImports: (content) => {
    // Remove unused React import when only using JSX
    if (content.includes("import React") && !content.includes("React.")) {
      content = content.replace(/import React,?\s*\{([^}]+)\}\s*from\s*['"]react['"];?\n?/g, 
        (match, imports) => `import {${imports}} from 'react';\n`);
      content = content.replace(/import React\s*from\s*['"]react['"];?\n?/g, '');
    }
    return content;
  },

  // Fix Arabic text escaping
  fixArabicText: (content) => {
    // Common Arabic text that needs escaping
    const arabicFixes = [
      { from: /'/g, to: "'" }, // Replace smart quotes
      { from: /"/g, to: '"' }, // Replace smart quotes
      { from: /'/g, to: "'" }, // Replace smart quotes
    ];
    
    arabicFixes.forEach(fix => {
      content = content.replace(fix.from, fix.to);
    });
    
    return content;
  },

  // Fix img tags to Image components
  fixImageTags: (content) => {
    // Add Next.js Image import if img tags are found
    if (content.includes('<img') && !content.includes('import Image')) {
      content = content.replace(
        /(import.*from ['"]react['"];?\n)/,
        '$1import Image from \'next/image\';\n'
      );
    }
    
    // Convert img tags to Image components (basic conversion)
    content = content.replace(
      /<img\s+([^>]*)\s*\/?>/g,
      (match, attrs) => {
        if (attrs.includes('src=') && attrs.includes('alt=')) {
          return `<Image ${attrs} />`;
        }
        return match; // Keep original if missing required props
      }
    );
    
    return content;
  },

  // Fix TypeScript any types
  fixAnyTypes: (content) => {
    // Add basic type annotations for common patterns
    content = content.replace(/const \[(\w+), set\w+\] = useState\(\)/g, 
      'const [$1, set$1] = useState<any>()');
    content = content.replace(/const \[(\w+), set\w+\] = useState\(null\)/g, 
      'const [$1, set$1] = useState<any>(null)');
    
    return content;
  },

  // Fix useEffect dependencies
  fixUseEffectDeps: (content) => {
    // Add empty dependency arrays where missing
    content = content.replace(
      /useEffect\(\(\) => \{[^}]+\}\)/g,
      (match) => {
        if (!match.includes(', [')) {
          return match.replace(/\)$/, ', [])');
        }
        return match;
      }
    );
    
    return content;
  }
};

// Files to process
const filesToProcess = [
  'src/app/**/*.tsx',
  'src/app/**/*.ts',
  'src/components/**/*.tsx',
  'src/components/**/*.ts',
  'src/hooks/**/*.ts',
  'src/utils/**/*.ts',
];

// Get all TypeScript/React files
function getAllFiles(dir, extension) {
  const files = [];
  
  function walkDir(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        walkDir(fullPath);
      } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
        files.push(fullPath);
      }
    }
  }
  
  walkDir(dir);
  return files;
}

// Process files
function processFiles() {
  const srcDir = path.join(__dirname, 'src');
  const files = getAllFiles(srcDir);
  
  console.log(`📁 Found ${files.length} TypeScript/React files to process\n`);
  
  let fixedFiles = 0;
  
  files.forEach(filePath => {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      const originalContent = content;
      
      // Apply all fixes
      Object.values(fixes).forEach(fix => {
        content = fix(content);
      });
      
      // Write back if changed
      if (content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ Fixed: ${path.relative(__dirname, filePath)}`);
        fixedFiles++;
      }
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  });
  
  console.log(`\n🎉 Fixed ${fixedFiles} files successfully!`);
}

// Main execution
try {
  processFiles();
  
  console.log('\n📋 Next steps:');
  console.log('1. Run: npm run lint -- --fix');
  console.log('2. Run: npm run build');
  console.log('3. Check for any remaining issues');
  
} catch (error) {
  console.error('❌ Script failed:', error.message);
  process.exit(1);
}
