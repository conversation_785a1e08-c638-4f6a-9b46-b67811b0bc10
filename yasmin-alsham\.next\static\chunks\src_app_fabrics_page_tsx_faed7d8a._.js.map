{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/YASMIN%20ALSHAM/yasmin-alsham/src/app/fabrics/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport Link from 'next/link'\nimport { ArrowR<PERSON>, Sparkles, <PERSON>, Heart, Palette, Clock, Calendar } from 'lucide-react'\n\nexport default function FabricsPage() {\n  // تعطيل مؤقت للقسم - عرض رسالة \"قريباً\"\n  const isComingSoon = true // تغيير هذا إلى false لتفعيل القسم\n\n  if (isComingSoon) {\n    return (\n    <div className=\"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* زر العودة للصفحة الرئيسية */}\n        <div className=\"mb-8\">\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300 group\"\n          >\n            <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform duration-300\" />\n            <span className=\"font-medium\">العودة للصفحة الرئيسية</span>\n          </Link>\n        </div>\n\n        {/* محتوى \"قريباً\" */}\n        <div className=\"flex items-center justify-center min-h-[60vh]\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"text-center max-w-2xl mx-auto\"\n          >\n            {/* أيقونة متحركة */}\n            <motion.div\n              animate={{\n                scale: [1, 1.1, 1],\n                rotate: [0, 5, -5, 0]\n              }}\n              transition={{\n                duration: 3,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              }}\n              className=\"mb-8\"\n            >\n              <div className=\"w-32 h-32 mx-auto bg-gradient-to-br from-pink-400 to-purple-500 rounded-full flex items-center justify-center shadow-2xl\">\n                <Palette className=\"w-16 h-16 text-white\" />\n              </div>\n            </motion.div>\n\n            {/* العنوان الرئيسي */}\n            <motion.h1\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n              className=\"text-4xl lg:text-5xl font-bold mb-6\"\n            >\n              <span className=\"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\">\n                قسم الأقمشة\n              </span>\n            </motion.h1>\n\n            {/* الرسالة */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-pink-100 shadow-xl\"\n            >\n              <div className=\"flex items-center justify-center mb-6\">\n                <Clock className=\"w-8 h-8 text-pink-600 ml-3\" />\n                <h2 className=\"text-2xl font-bold text-gray-800\">هذا القسم سيتم افتتاحه قريباً</h2>\n              </div>\n\n              <p className=\"text-lg text-gray-600 leading-relaxed mb-6\">\n                نحن نعمل بجد لتقديم مجموعة رائعة من الأقمشة الفاخرة والعصرية لك.\n                سيتم افتتاح هذا القسم قريباً مع تشكيلة متنوعة من أجود أنواع الأقمشة.\n              </p>\n\n              <div className=\"flex items-center justify-center space-x-4 space-x-reverse text-pink-600\">\n                <Calendar className=\"w-5 h-5\" />\n                <span className=\"font-medium\">ترقبي الافتتاح قريباً</span>\n              </div>\n            </motion.div>\n\n            {/* أزرار التنقل */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n              className=\"mt-8 flex flex-col sm:flex-row gap-4 justify-center\"\n            >\n              <Link\n                href=\"/designs\"\n                className=\"bg-gradient-to-r from-pink-600 to-purple-600 text-white px-8 py-4 rounded-full font-bold inline-flex items-center justify-center space-x-3 space-x-reverse group transform hover:scale-105 transition-all duration-300 shadow-xl hover:shadow-pink-500/25\"\n              >\n                <Sparkles className=\"w-5 h-5 group-hover:scale-110 transition-transform duration-300\" />\n                <span>تصفحي الفساتين الجاهزة</span>\n              </Link>\n\n              <Link\n                href=\"/book-appointment\"\n                className=\"border-2 border-pink-600 text-pink-600 px-8 py-4 rounded-full font-bold inline-flex items-center justify-center space-x-3 space-x-reverse group hover:bg-pink-600 hover:text-white transition-all duration-300\"\n              >\n                <Calendar className=\"w-5 h-5 group-hover:scale-110 transition-transform duration-300\" />\n                <span>احجزي موعد</span>\n              </Link>\n            </motion.div>\n\n            {/* عناصر زخرفية */}\n            <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n              <motion.div\n                animate={{\n                  y: [-20, 20, -20],\n                  rotate: [0, 180, 360]\n                }}\n                transition={{\n                  duration: 8,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }}\n                className=\"absolute top-1/4 left-1/4 w-6 h-6 text-pink-300\"\n              >\n                <Star className=\"w-full h-full\" />\n              </motion.div>\n\n              <motion.div\n                animate={{\n                  y: [20, -20, 20],\n                  rotate: [360, 180, 0]\n                }}\n                transition={{\n                  duration: 10,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }}\n                className=\"absolute top-1/3 right-1/4 w-8 h-8 text-rose-300\"\n              >\n                <Heart className=\"w-full h-full\" />\n              </motion.div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </div>\n    )\n  }\n\n  // الكود الأصلي لقسم الأقمشة (معطل حالياً - سيتم تفعيله لاحقاً)\n  const [selectedCategory, setSelectedCategory] = useState('all')\n  const [favorites, setFavorites] = useState<number[]>([])\n\n  const categories = [\n    { id: 'all', name: 'جميع الأقمشة', count: 24 },\n    { id: 'silk', name: 'حرير', count: 8 },\n    { id: 'cotton', name: 'قطن', count: 6 },\n    { id: 'chiffon', name: 'شيفون', count: 5 },\n    { id: 'satin', name: 'ساتان', count: 5 }\n  ]\n\n  const fabrics = [\n    {\n      id: 1,\n      name: 'حرير وردي فاتح',\n      category: 'silk',\n      price: 150,\n      image: '/fabrics/silk-pink.jpg',\n      description: 'حرير طبيعي بلون وردي فاتح، مثالي للفساتين الناعمة',\n      rating: 4.8,\n      inStock: true\n    },\n    {\n      id: 2,\n      name: 'شيفون أزرق سماوي',\n      category: 'chiffon',\n      price: 120,\n      image: '/fabrics/chiffon-blue.jpg',\n      description: 'شيفون خفيف وأنيق بلون أزرق سماوي',\n      rating: 4.6,\n      inStock: true\n    },\n    {\n      id: 3,\n      name: 'ساتان ذهبي',\n      category: 'satin',\n      price: 180,\n      image: '/fabrics/satin-gold.jpg',\n      description: 'ساتان فاخر بلون ذهبي لامع',\n      rating: 4.9,\n      inStock: false\n    }\n    // المزيد من الأقمشة يمكن إضافتها هنا\n  ]\n\n  const toggleFavorite = (fabricId: number) => {\n    setFavorites(prev =>\n      prev.includes(fabricId)\n        ? prev.filter(id => id !== fabricId)\n        : [...prev, fabricId]\n    )\n  }\n\n  const filteredFabrics = selectedCategory === 'all'\n    ? fabrics\n    : fabrics.filter(fabric => fabric.category === selectedCategory)\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20\">\n      {/* الكود الكامل لقسم الأقمشة سيكون هنا عند التفعيل */}\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <h1 className=\"text-4xl font-bold text-center mb-8\">قسم الأقمشة</h1>\n        <p className=\"text-center text-gray-600\">هذا القسم جاهز للتفعيل عند الحاجة</p>\n      </div>\n    </div>\n  )\n}\n\n// الكود الأصلي محفوظ في ملف page-original.tsx للتفعيل لاحقاً\n\n\n\n\n\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,wCAAwC;IACxC,MAAM,eAAe,KAAK,mCAAmC;;IAE7D,wCAAkB;QAChB,qBACA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,6LAAC;oCAAK,WAAU;8CAAc;;;;;;;;;;;;;;;;;kCAKlC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;wCAClB,QAAQ;4CAAC;4CAAG;4CAAG,CAAC;4CAAG;yCAAE;oCACvB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,MAAM;oCACR;oCACA,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAKvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CAEV,cAAA,6LAAC;wCAAK,WAAU;kDAA6E;;;;;;;;;;;8CAM/F,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;;sDAGnD,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;sDAK1D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;;;;;;;8CAKlC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;8DAAK;;;;;;;;;;;;sDAGR,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAKV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDACP,GAAG;oDAAC,CAAC;oDAAI;oDAAI,CAAC;iDAAG;gDACjB,QAAQ;oDAAC;oDAAG;oDAAK;iDAAI;4CACvB;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;4CACR;4CACA,WAAU;sDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAGlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDACP,GAAG;oDAAC;oDAAI,CAAC;oDAAI;iDAAG;gDAChB,QAAQ;oDAAC;oDAAK;oDAAK;iDAAE;4CACvB;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;4CACR;4CACA,WAAU;sDAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ/B;;IAEA,+DAA+D;IAC/D,MAAO,8BAAkB;IACzB,MAAO,uBAAW;IAElB,MAAM;IAQN,MAAM;IAkCN,MAAM;IAQN,MAAM;AAaR,EAEA,6DAA6D;GApNrC;KAAA", "debugId": null}}]}