"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[312],{760:(e,t,n)=>{n.d(t,{N:()=>x});var r=n(5155),l=n(2115),a=n(869),i=n(2885),s=n(7494),o=n(845),h=n(7351),d=n(1508);class c extends l.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,h.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function p(e){let{children:t,isPresent:n,anchorX:a,root:i}=e,s=(0,l.useId)(),o=(0,l.useRef)(null),h=(0,l.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:p}=(0,l.useContext)(d.Q);return(0,l.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:l,right:d}=h.current;if(n||!o.current||!e||!t)return;o.current.dataset.motionPopId=s;let c=document.createElement("style");p&&(c.nonce=p);let u=null!=i?i:document.head;return u.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(s,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===a?"left: ".concat(l):"right: ".concat(d),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{u.removeChild(c),u.contains(c)&&u.removeChild(c)}},[n]),(0,r.jsx)(c,{isPresent:n,childRef:o,sizeRef:h,children:l.cloneElement(t,{ref:o})})}let u=e=>{let{children:t,initial:n,isPresent:a,onExitComplete:s,custom:h,presenceAffectsLayout:d,mode:c,anchorX:u,root:y}=e,m=(0,i.M)(f),k=(0,l.useId)(),x=!0,g=(0,l.useMemo)(()=>(x=!1,{id:k,initial:n,isPresent:a,custom:h,onExitComplete:e=>{for(let t of(m.set(e,!0),m.values()))if(!t)return;s&&s()},register:e=>(m.set(e,!1),()=>m.delete(e))}),[a,m,s]);return d&&x&&(g={...g}),(0,l.useMemo)(()=>{m.forEach((e,t)=>m.set(t,!1))},[a]),l.useEffect(()=>{a||m.size||!s||s()},[a]),"popLayout"===c&&(t=(0,r.jsx)(p,{isPresent:a,anchorX:u,root:y,children:t})),(0,r.jsx)(o.t.Provider,{value:g,children:t})};function f(){return new Map}var y=n(2082);let m=e=>e.key||"";function k(e){let t=[];return l.Children.forEach(e,e=>{(0,l.isValidElement)(e)&&t.push(e)}),t}let x=e=>{let{children:t,custom:n,initial:o=!0,onExitComplete:h,presenceAffectsLayout:d=!0,mode:c="sync",propagate:p=!1,anchorX:f="left",root:x}=e,[g,v]=(0,y.xQ)(p),A=(0,l.useMemo)(()=>k(t),[t]),M=p&&!g?[]:A.map(m),w=(0,l.useRef)(!0),E=(0,l.useRef)(A),C=(0,i.M)(()=>new Map),[j,z]=(0,l.useState)(A),[R,b]=(0,l.useState)(A);(0,s.E)(()=>{w.current=!1,E.current=A;for(let e=0;e<R.length;e++){let t=m(R[e]);M.includes(t)?C.delete(t):!0!==C.get(t)&&C.set(t,!1)}},[R,M.length,M.join("-")]);let P=[];if(A!==j){let e=[...A];for(let t=0;t<R.length;t++){let n=R[t],r=m(n);M.includes(r)||(e.splice(t,0,n),P.push(n))}return"wait"===c&&P.length&&(e=P),b(k(e)),z(A),null}let{forceRender:L}=(0,l.useContext)(a.L);return(0,r.jsx)(r.Fragment,{children:R.map(e=>{let t=m(e),l=(!p||!!g)&&(A===R||M.includes(t));return(0,r.jsx)(u,{isPresent:l,initial:(!w.current||!!o)&&void 0,custom:n,presenceAffectsLayout:d,mode:c,root:x,onExitComplete:l?void 0:()=>{if(!C.has(t))return;C.set(t,!0);let e=!0;C.forEach(t=>{t||(e=!1)}),e&&(null==L||L(),b(E.current),p&&(null==v||v()),h&&h())},anchorX:f,children:e},t)})})}},1497:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},1951:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("mic-off",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2",key:"80xlxr"}],["path",{d:"M5 10v2a7 7 0 0 0 12 5",key:"p2k8kg"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]])},2178:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]])},4229:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5339:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5690:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},6140:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("ruler",[["path",{d:"M21.3 15.3a2.4 2.4 0 0 1 0 3.4l-2.6 2.6a2.4 2.4 0 0 1-3.4 0L2.7 8.7a2.41 2.41 0 0 1 0-3.4l2.6-2.6a2.41 2.41 0 0 1 3.4 0Z",key:"icamh8"}],["path",{d:"m14.5 12.5 2-2",key:"inckbg"}],["path",{d:"m11.5 9.5 2-2",key:"fmmyf7"}],["path",{d:"m8.5 6.5 2-2",key:"vc6u1g"}],["path",{d:"m17.5 15.5 2-2",key:"wo5hmg"}]])},7213:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},9588:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("mic",[["path",{d:"M12 19v3",key:"npa21l"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["rect",{x:"9",y:"2",width:"6",height:"13",rx:"3",key:"s6n7sd"}]])},9869:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])}}]);