(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[876],{1838:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>f});var s=a(5155),r=a(2115),n=a(6408),l=a(5695),i=a(6874),o=a.n(i),c=a(3294),d=a(1364),x=a(9137),p=a(4186),m=a(646),h=a(4861),u=a(2138),g=a(4616),b=a(7924),j=a(6932),y=a(9074),v=a(9420);function f(){let{user:e}=(0,c.n)(),{appointments:t,updateAppointment:a,deleteAppointment:i}=(0,d.D)(),{t:f,isArabic:N}=(0,x.B)(),w=(0,l.useRouter)();(0,r.useEffect)(()=>{e?"admin"!==e.role&&w.push("/dashboard"):w.push("/login")},[e,w]);let[k,_]=(0,r.useState)(""),[D,C]=(0,r.useState)("all"),[A,S]=(0,r.useState)("all"),P=e=>{let t={pending:{label:f("pending"),color:"text-blue-600",bgColor:"bg-blue-100",icon:p.A},confirmed:{label:f("confirmed"),color:"text-green-600",bgColor:"bg-green-100",icon:m.A},completed:{label:f("completed"),color:"text-purple-600",bgColor:"bg-purple-100",icon:m.A},cancelled:{label:f("cancelled"),color:"text-red-600",bgColor:"bg-red-100",icon:h.A}};return t[e]||t.pending},L=e=>{a(e,{status:"confirmed"})},E=e=>{a(e,{status:"completed"})},O=e=>{a(e,{status:"cancelled"})},I=e=>new Date(e).toLocaleDateString("ar-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),T=e=>{let[t,a]=e.split(":"),s=parseInt(t),r=s>=12?f("pm"):f("am");return"".concat(s>12?s-12:0===s?12:s,":").concat(a," ").concat(r)},U=e=>e===new Date().toISOString().split("T")[0],B=e=>{let t=new Date;return t.setDate(t.getDate()+1),e===t.toISOString().split("T")[0]},R=t.filter(e=>{let t=e.clientName.toLowerCase().includes(k.toLowerCase())||e.clientPhone.includes(k)||e.id.toLowerCase().includes(k.toLowerCase()),a="all"===D||e.status===D,s=!0;if("today"===A)s=U(e.appointmentDate);else if("tomorrow"===A)s=B(e.appointmentDate);else if("week"===A){let t=new Date(e.appointmentDate),a=new Date,r=new Date;r.setDate(a.getDate()+7),s=t>=a&&t<=r}return t&&a&&s});return e?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-8",children:(0,s.jsxs)(o(),{href:"/dashboard",className:"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300",children:[(0,s.jsx)(u.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:f("back_to_dashboard")})]})}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold mb-2",children:(0,s.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent",children:f("appointments_management")})}),(0,s.jsx)("p",{className:"text-lg text-gray-600",children:f("view_manage_appointments")})]}),(0,s.jsxs)(o(),{href:"/book-appointment",className:"btn-primary inline-flex items-center justify-center space-x-2 space-x-reverse px-6 py-3 group",children:[(0,s.jsx)(g.A,{className:"w-5 h-5 group-hover:scale-110 transition-transform duration-300"}),(0,s.jsx)("span",{children:f("book_new_appointment")})]})]}),(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 mb-8",children:(0,s.jsxs)("div",{className:"grid md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(b.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,s.jsx)("input",{type:"text",value:k,onChange:e=>_(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:f("search_appointments_placeholder")})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(j.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,s.jsxs)("select",{value:D,onChange:e=>C(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",children:[(0,s.jsx)("option",{value:"all",children:f("all_statuses")}),(0,s.jsx)("option",{value:"pending",children:f("pending")}),(0,s.jsx)("option",{value:"confirmed",children:f("confirmed")}),(0,s.jsx)("option",{value:"completed",children:f("completed")}),(0,s.jsx)("option",{value:"cancelled",children:f("cancelled")})]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(y.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,s.jsxs)("select",{value:A,onChange:e=>S(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",children:[(0,s.jsx)("option",{value:"all",children:f("all_dates")}),(0,s.jsx)("option",{value:"today",children:f("today")}),(0,s.jsx)("option",{value:"tomorrow",children:f("tomorrow")}),(0,s.jsx)("option",{value:"week",children:f("this_week")})]})]})]})}),(0,s.jsx)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"space-y-6",children:0===R.length?(0,s.jsxs)("div",{className:"text-center py-12 bg-white/80 backdrop-blur-sm rounded-2xl border border-pink-100",children:[(0,s.jsx)(y.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-medium text-gray-600 mb-2",children:f("no_appointments")}),(0,s.jsx)("p",{className:"text-gray-500",children:f("no_appointments_found")})]}):R.map((e,t)=>(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*t},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border transition-all duration-300 hover:shadow-lg ".concat(U(e.appointmentDate)?"border-pink-300 bg-pink-50/50":"border-pink-100"),children:(0,s.jsxs)("div",{className:"grid lg:grid-cols-4 gap-6",children:[(0,s.jsxs)("div",{className:"lg:col-span-2",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-1",children:e.clientName}),(0,s.jsxs)("div",{className:"space-y-1 text-sm text-gray-600",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)(v.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.clientPhone})]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["#",e.id]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat(P(e.status).bgColor," ").concat(P(e.status).color),children:P(e.status).label}),U(e.appointmentDate)&&(0,s.jsx)("span",{className:"px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs font-medium",children:f("today")})]})]}),e.notes&&(0,s.jsx)("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:(0,s.jsx)("p",{className:"text-sm text-gray-700",children:e.notes})})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-1",children:f("date_time")}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse text-sm text-gray-600",children:[(0,s.jsx)(y.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:I(e.appointmentDate)})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse text-sm text-gray-600",children:[(0,s.jsx)(p.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:T(e.appointmentTime)})]})]})]}),(0,s.jsx)("div",{className:"flex items-center space-x-2 space-x-reverse",children:(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:[f("created_on")," ",new Date(e.createdAt).toLocaleDateString(N?"ar-US":"en-US")]})})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-2",children:["pending"===e.status&&(0,s.jsx)("button",{onClick:()=>L(e.id),className:"btn-primary py-2 px-4 text-sm",children:f("confirm_appointment")}),"confirmed"===e.status&&(0,s.jsx)("button",{onClick:()=>E(e.id),className:"btn-secondary py-2 px-4 text-sm",children:f("mark_attended")}),"cancelled"!==e.status&&"completed"!==e.status&&(0,s.jsx)("button",{onClick:()=>O(e.id),className:"text-red-600 hover:text-red-700 py-2 px-4 text-sm border border-red-200 rounded-lg hover:bg-red-50 transition-all duration-300",children:f("cancel_appointment")})]})]})},e.id))}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"mt-12 grid grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:t.filter(e=>"pending"===e.status).length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:f("pending")})]}),(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-1",children:t.filter(e=>"confirmed"===e.status).length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:f("confirmed")})]}),(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-orange-600 mb-1",children:t.filter(e=>U(e.appointmentDate)).length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:f("today")})]}),(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-purple-600 mb-1",children:t.filter(e=>"completed"===e.status).length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:f("completed")})]})]})]})}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:f("loading")})]})})}},4554:(e,t,a)=>{Promise.resolve().then(a.bind(a,1838))}},e=>{var t=t=>e(e.s=t);e.O(0,[165,874,480,764,441,684,358],()=>t(4554)),_N_E=e.O()}]);