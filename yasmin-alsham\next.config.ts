// next.config.ts
import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  // ESLint configuration for production builds
  eslint: {
    ignoreDuringBuilds: false, // Enable linting but with relaxed rules
    dirs: ['src'], // Only lint src directory
  },

  // TypeScript configuration
  typescript: {
    ignoreBuildErrors: false, // Enable type checking but with relaxed settings
  },

  // Performance optimizations
  experimental: {
    optimizePackageImports: ['lucide-react', 'framer-motion'],
  },

  // Image optimization for Arabic content
  images: {
    domains: ['localhost'],
    formats: ['image/webp', 'image/avif'],
  },

  // Note: i18n config removed as it's not supported in App Router
  // Using custom translation hook instead

  // Output configuration
  output: 'standalone',

  // Webpack configuration for Arabic RTL support
  webpack: (config) => {
    // Handle Arabic font files
    config.module.rules.push({
      test: /\.(woff|woff2|eot|ttf|otf)$/,
      use: {
        loader: 'file-loader',
        options: {
          publicPath: '/_next/static/fonts/',
          outputPath: 'static/fonts/',
        },
      },
    });

    return config;
  },
};

export default nextConfig;
