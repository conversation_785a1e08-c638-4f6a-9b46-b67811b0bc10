"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/designs/page",{

/***/ "(app-pages-browser)/./src/app/designs/page.tsx":
/*!**********************************!*\
  !*** ./src/app/designs/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DesignsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-2x2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronLeft,ChevronRight,Eye,Grid2X2,Grid3X3,Heart,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _data_designs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/designs */ \"(app-pages-browser)/./src/data/designs.ts\");\n/* harmony import */ var _store_shopStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/shopStore */ \"(app-pages-browser)/./src/store/shopStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DesignsPage() {\n    _s();\n    const [selectedImageIndex, setSelectedImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isGalleryOpen, setIsGalleryOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentImageIndexes, setCurrentImageIndexes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        1: 0,\n        2: 0,\n        3: 0,\n        4: 0,\n        5: 0,\n        6: 0,\n        7: 0,\n        8: 0,\n        9: 0,\n        10: 0,\n        11: 0,\n        12: 0\n    });\n    // حالة عرض البطاقات للهواتف المحمولة\n    const [isSingleColumn, setIsSingleColumn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // متجر التسوق\n    const { addToFavorites, removeFromFavorites, isFavorite, addToCart } = (0,_store_shopStore__WEBPACK_IMPORTED_MODULE_4__.useShopStore)();\n    const [addedToCart, setAddedToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // تحميل حالة العرض من localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DesignsPage.useEffect\": ()=>{\n            const savedViewMode = localStorage.getItem('yasmin-designs-view-mode');\n            if (savedViewMode === 'single') {\n                setIsSingleColumn(true);\n            }\n        }\n    }[\"DesignsPage.useEffect\"], []);\n    // حفظ حالة العرض في localStorage\n    const toggleViewMode = ()=>{\n        const newMode = !isSingleColumn;\n        setIsSingleColumn(newMode);\n        localStorage.setItem('yasmin-designs-view-mode', newMode ? 'single' : 'double');\n    };\n    // دوال التعامل مع المفضلة والسلة\n    const handleToggleFavorite = (design, e)=>{\n        e.stopPropagation();\n        const product = {\n            id: design.id.toString(),\n            name: design.title,\n            price: design.price || 299,\n            image: design.images[0],\n            description: design.description,\n            category: design.category\n        };\n        if (isFavorite(product.id)) {\n            removeFromFavorites(product.id);\n        } else {\n            addToFavorites(product);\n        }\n    };\n    const handleAddToCart = (design, e)=>{\n        e.stopPropagation();\n        const product = {\n            id: design.id.toString(),\n            name: design.title,\n            price: design.price || 299,\n            image: design.images[0],\n            description: design.description,\n            category: design.category\n        };\n        addToCart(product);\n        setAddedToCart((prev)=>[\n                ...prev,\n                design.id\n            ]);\n        setTimeout(()=>{\n            setAddedToCart((prev)=>prev.filter((id)=>id !== design.id));\n        }, 2000);\n    };\n    // دوال التنقل بين صور البطاقة\n    const nextCardImage = (designId, e)=>{\n        e.stopPropagation();\n        setCurrentImageIndexes((prev)=>({\n                ...prev,\n                [designId]: (prev[designId] + 1) % 3\n            }));\n    };\n    const prevCardImage = (designId, e)=>{\n        e.stopPropagation();\n        setCurrentImageIndexes((prev)=>({\n                ...prev,\n                [designId]: prev[designId] === 0 ? 2 : prev[designId] - 1\n            }));\n    };\n    const setCardImage = (designId, imageIndex, e)=>{\n        e.stopPropagation();\n        setCurrentImageIndexes((prev)=>({\n                ...prev,\n                [designId]: imageIndex\n            }));\n    };\n    // دوال إدارة المعرض\n    const openGallery = (index)=>{\n        setSelectedImageIndex(index);\n        setIsGalleryOpen(true);\n        document.body.style.overflow = 'hidden';\n    };\n    const closeGallery = ()=>{\n        setIsGalleryOpen(false);\n        setSelectedImageIndex(null);\n        document.body.style.overflow = 'unset';\n    };\n    const nextImage = ()=>{\n        if (selectedImageIndex !== null) {\n            const designId = _data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].id;\n            setCurrentImageIndexes((prev)=>({\n                    ...prev,\n                    [designId]: (prev[designId] + 1) % 3\n                }));\n        }\n    };\n    const prevImage = ()=>{\n        if (selectedImageIndex !== null) {\n            const designId = _data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].id;\n            setCurrentImageIndexes((prev)=>({\n                    ...prev,\n                    [designId]: prev[designId] === 0 ? 2 : prev[designId] - 1\n                }));\n        }\n    };\n    // إدارة مفتاح Escape\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DesignsPage.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"DesignsPage.useEffect.handleKeyDown\": (event)=>{\n                    if (event.key === 'Escape' && isGalleryOpen) {\n                        closeGallery();\n                    }\n                    if (event.key === 'ArrowRight' && isGalleryOpen) {\n                        nextImage();\n                    }\n                    if (event.key === 'ArrowLeft' && isGalleryOpen) {\n                        prevImage();\n                    }\n                }\n            }[\"DesignsPage.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"DesignsPage.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleKeyDown);\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"DesignsPage.useEffect\"];\n        }\n    }[\"DesignsPage.useEffect\"], [\n        isGalleryOpen,\n        selectedImageIndex\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-16 lg:pt-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6\n                },\n                className: \"fixed top-20 lg:top-24 left-4 lg:left-8 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300 bg-white/80 backdrop-blur-sm px-3 py-2 rounded-lg shadow-sm border border-pink-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4 lg:w-5 lg:h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm lg:text-base\",\n                            children: \"العودة إلى الرئيسية\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl sm:text-5xl lg:text-6xl font-bold mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"تصاميمنا الجاهزة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed mb-6\",\n                                children: \"استكشفي مجموعتنا الكاملة من التصاميم الجاهزة واختاري ما يناسب ذوقك ومناسبتك\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 max-w-2xl mx-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-800 font-medium text-center\",\n                                    children: \"✨ الفساتين الجاهزة متوفرة للشراء المباشر - لا يتطلب حجز موعد\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.2\n                        },\n                        className: \"sm:hidden mb-6 flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toggleViewMode,\n                            className: \"bg-white/80 backdrop-blur-sm border border-pink-200 rounded-xl p-3 flex items-center space-x-2 space-x-reverse hover:bg-white hover:shadow-lg transition-all duration-300\",\n                            \"aria-label\": isSingleColumn ? 'تبديل إلى العرض الثنائي' : 'تبديل إلى العرض الفردي',\n                            children: isSingleColumn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5 text-pink-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"عرض ثنائي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-5 h-5 text-pink-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"عرض فردي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-8 mb-12 \".concat(isSingleColumn ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'),\n                        children: _data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns.map((design, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: index * 0.1\n                                },\n                                className: \"group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-[4/5] bg-gradient-to-br from-pink-100 via-rose-100 to-purple-100 relative overflow-hidden cursor-pointer\",\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                openGallery(index);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: design.images[currentImageIndexes[design.id]],\n                                                    alt: \"\".concat(design.title, \" - صورة \").concat(currentImageIndexes[design.id] + 1),\n                                                    className: \"w-full h-full object-cover transition-opacity duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>prevCardImage(design.id, e),\n                                                    className: \"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-all duration-300 z-10\",\n                                                    \"aria-label\": \"الصورة السابقة\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>nextCardImage(design.id, e),\n                                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-all duration-300 z-10\",\n                                                    \"aria-label\": \"الصورة التالية\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1 space-x-reverse opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                    children: design.images.map((_, imgIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>setCardImage(design.id, imgIndex, e),\n                                                            className: \"w-2 h-2 rounded-full transition-colors duration-300 \".concat(currentImageIndexes[design.id] === imgIndex ? 'bg-white' : 'bg-white/50'),\n                                                            \"aria-label\": \"عرض الصورة \".concat(imgIndex + 1)\n                                                        }, imgIndex, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white/90 rounded-full p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-6 h-6 text-pink-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>handleToggleFavorite(design, e),\n                                                    className: \"absolute top-3 left-3 rounded-full p-2 opacity-0 group-hover:opacity-100 transition-all duration-300 hover:scale-110 \".concat(isFavorite(design.id.toString()) ? 'bg-red-500 text-white' : 'bg-white/80 hover:bg-white text-pink-500'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 \".concat(isFavorite(design.id.toString()) ? 'fill-current' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/designs/\".concat(design.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"cursor-pointer hover:bg-gray-50 transition-colors duration-300 p-2 -m-2 rounded-lg mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-gradient-to-r from-pink-100 to-rose-100 text-pink-700 px-2 py-1 rounded-full text-xs font-medium\",\n                                                                    children: design.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-bold text-gray-800 mb-2 group-hover:text-pink-600 transition-colors duration-300\",\n                                                                children: design.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 leading-relaxed mb-3\",\n                                                                children: design.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-pink-600 mb-3\",\n                                                                children: (0,_store_shopStore__WEBPACK_IMPORTED_MODULE_4__.formatPrice)(design.price)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>handleAddToCart(design, e),\n                                                            disabled: addedToCart.includes(design.id),\n                                                            className: \"flex-1 flex items-center justify-center space-x-1 space-x-reverse py-2 px-3 rounded-lg text-sm font-medium transition-all duration-300 \".concat(addedToCart.includes(design.id) ? 'bg-green-500 text-white' : 'bg-gradient-to-r from-pink-500 to-purple-500 text-white hover:shadow-lg'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: addedToCart.includes(design.id) ? 'تم الإضافة' : 'أضف للسلة'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"https://wa.me/+966598862609?text=أريد استفسار عن \".concat(design.title),\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"px-3 py-2 border border-pink-300 text-pink-600 rounded-lg hover:bg-pink-50 transition-colors duration-300 text-sm font-medium\",\n                                                            children: \"استفسار\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, this)\n                            }, design.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    isGalleryOpen && selectedImageIndex !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4\",\n                        onClick: closeGallery,\n                        role: \"dialog\",\n                        \"aria-modal\": \"true\",\n                        \"aria-labelledby\": \"gallery-title\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative max-w-4xl w-full\",\n                            onClick: (e)=>e.stopPropagation(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: closeGallery,\n                                    className: \"absolute top-4 right-4 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-2 transition-colors duration-300\",\n                                    \"aria-label\": \"إغلاق المعرض\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: prevImage,\n                                    className: \"absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-colors duration-300\",\n                                    \"aria-label\": \"الصورة السابقة\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: nextImage,\n                                    className: \"absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-colors duration-300\",\n                                    \"aria-label\": \"الصورة التالية\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronLeft_ChevronRight_Eye_Grid2X2_Grid3X3_Heart_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    className: \"bg-white rounded-2xl overflow-hidden shadow-2xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-[4/5] bg-gradient-to-br from-pink-100 via-rose-100 to-purple-100 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: _data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].images[currentImageIndexes[_data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].id]],\n                                            alt: \"\".concat(_data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].title, \" - صورة \").concat(currentImageIndexes[_data_designs__WEBPACK_IMPORTED_MODULE_3__.allDesigns[selectedImageIndex].id] + 1),\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 17\n                                    }, this)\n                                }, selectedImageIndex, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\designs\\\\page.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n_s(DesignsPage, \"Jo//TfkgCnar91prmNOYQwjQkbg=\", false, function() {\n    return [\n        _store_shopStore__WEBPACK_IMPORTED_MODULE_4__.useShopStore\n    ];\n});\n_c = DesignsPage;\nvar _c;\n$RefreshReg$(_c, \"DesignsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/designs/page.tsx\n"));

/***/ })

});