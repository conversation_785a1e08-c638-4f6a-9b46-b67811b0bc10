(()=>{var e={};e.id=589,e.ids=[589],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26123:(e,s,t)=>{Promise.resolve().then(t.bind(t,96579))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30779:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\reports\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\reports\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},44628:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=t(65239),a=t(48088),n=t(88170),i=t.n(n),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o={children:["",{children:["dashboard",{children:["reports",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,30779)),"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\reports\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\reports\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/reports/page",pathname:"/dashboard/reports",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},80462:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},89171:(e,s,t)=>{Promise.resolve().then(t.bind(t,30779))},96579:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var r=t(60687),a=t(43210),n=t(26001),i=t(16189),l=t(85814),d=t.n(l),o=t(99720),c=t(34270),x=t(99182),m=t(70334),p=t(80462),h=t(62688);let u=(0,h.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),g=(0,h.A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);var b=t(25541);let j=(0,h.A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]);var v=t(19080),y=t(53411),f=t(41312);function N(){let{user:e}=(0,o.n)(),{orders:s,workers:t,appointments:l,getStats:h}=(0,c.D)(),{t:N}=(0,x.B)();(0,i.useRouter)();let[w,k]=(0,a.useState)("month"),_=h(),A={totalRevenue:_.totalRevenue,revenueChange:0,totalOrders:_.totalOrders,ordersChange:0,completedOrders:_.completedOrders,completionRate:_.totalOrders>0?Number((_.completedOrders/_.totalOrders*100).toFixed(1)):0,averageOrderValue:_.totalOrders>0?Math.round(_.totalRevenue/_.totalOrders):0,topWorkers:t.map(e=>{let t=s.filter(s=>s.assignedWorker===e.id&&"completed"===s.status),r=t.reduce((e,s)=>e+s.price,0);return{name:e.full_name,orders:t.length,revenue:r}}).filter(e=>e.orders>0).sort((e,s)=>s.revenue-e.revenue).slice(0,3),ordersByType:(()=>{let e={};s.forEach(s=>{let t=s.description.includes("زفاف")?N("wedding_dress"):s.description.includes("سهرة")?N("evening_dress"):s.description.includes("خطوبة")?N("engagement_dress"):s.description.includes("يومي")?N("casual_dress"):N("other");e[t]=(e[t]||0)+1});let t=s.length;return Object.entries(e).map(([e,s])=>({type:e,count:s,percentage:t>0?Number((s/t*100).toFixed(1)):0}))})(),monthlyTrend:(()=>{let e=["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"],t=[],r=new Date;for(let a=3;a>=0;a--){let n=new Date(r.getFullYear(),r.getMonth()-a,1),i=e[n.getMonth()],l=s.filter(e=>{let s=new Date(e.createdAt);return s.getMonth()===n.getMonth()&&s.getFullYear()===n.getFullYear()}),d=l.filter(e=>"completed"===e.status).reduce((e,s)=>e+s.price,0);t.push({month:i,revenue:d,orders:l.length})}return t})()};return e&&"admin"===e.role?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-8",children:(0,r.jsxs)(d(),{href:"/dashboard",className:"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300",children:[(0,r.jsx)(m.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:N("back_to_dashboard")})]})}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold mb-2",children:(0,r.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent",children:N("reports_analytics")})}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:N("comprehensive_analysis")})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,r.jsxs)("select",{value:w,onChange:e=>k(e.target.value),className:"pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",children:[(0,r.jsx)("option",{value:"week",children:N("this_week")}),(0,r.jsx)("option",{value:"month",children:N("this_month")}),(0,r.jsx)("option",{value:"quarter",children:N("this_quarter")}),(0,r.jsx)("option",{value:"year",children:N("this_year")})]})]}),(0,r.jsxs)("button",{className:"btn-secondary inline-flex items-center space-x-2 space-x-reverse px-4 py-2",children:[(0,r.jsx)(u,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:N("export")})]})]})]}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-500 rounded-lg flex items-center justify-center",children:(0,r.jsx)(g,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{className:`flex items-center space-x-1 space-x-reverse text-sm ${A.revenueChange>0?"text-green-600":"text-red-600"}`,children:[A.revenueChange>0?(0,r.jsx)(b.A,{className:"w-4 h-4"}):(0,r.jsx)(j,{className:"w-4 h-4"}),(0,r.jsxs)("span",{children:[Math.abs(A.revenueChange),"%"]})]})]}),(0,r.jsxs)("h3",{className:"text-2xl font-bold text-gray-800 mb-1",children:[A.totalRevenue.toLocaleString()," ر.س"]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:N("total_revenue")})]}),(0,r.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-500 rounded-lg flex items-center justify-center",children:(0,r.jsx)(v.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{className:`flex items-center space-x-1 space-x-reverse text-sm ${A.ordersChange>0?"text-green-600":"text-red-600"}`,children:[A.ordersChange>0?(0,r.jsx)(b.A,{className:"w-4 h-4"}):(0,r.jsx)(j,{className:"w-4 h-4"}),(0,r.jsxs)("span",{children:[Math.abs(A.ordersChange),"%"]})]})]}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-800 mb-1",children:A.totalOrders}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:N("total_orders")})]}),(0,r.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-purple-400 to-purple-500 rounded-lg flex items-center justify-center",children:(0,r.jsx)(y.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{className:"text-sm text-purple-600",children:[A.completionRate,"%"]})]}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-800 mb-1",children:A.completedOrders}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:N("completed_orders")})]}),(0,r.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100",children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-pink-400 to-rose-500 rounded-lg flex items-center justify-center",children:(0,r.jsx)(g,{className:"w-6 h-6 text-white"})})}),(0,r.jsxs)("h3",{className:"text-2xl font-bold text-gray-800 mb-1",children:[A.averageOrderValue.toLocaleString()," ر.س"]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:N("average_order_value")})]})]}),(0,r.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 mb-8",children:[(0,r.jsxs)(n.P.div,{initial:{opacity:0,x:-30},animate:{opacity:1,x:0},transition:{duration:.6,delay:.4},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100",children:[(0,r.jsxs)("h3",{className:"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",children:[(0,r.jsx)(f.A,{className:"w-5 h-5 text-pink-600"}),(0,r.jsx)("span",{children:N("top_workers_month")})]}),(0,r.jsx)("div",{className:"space-y-4",children:A.topWorkers.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,r.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ${0===s?"bg-yellow-500":1===s?"bg-gray-400":"bg-orange-500"}`,children:s+1}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-800",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.orders," ",N("orders_count")]})]})]}),(0,r.jsx)("div",{className:"text-right",children:(0,r.jsxs)("p",{className:"font-bold text-green-600",children:[e.revenue.toLocaleString()," ر.س"]})})]},s))})]}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,x:30},animate:{opacity:1,x:0},transition:{duration:.6,delay:.6},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100",children:[(0,r.jsxs)("h3",{className:"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",children:[(0,r.jsx)(v.A,{className:"w-5 h-5 text-pink-600"}),(0,r.jsx)("span",{children:N("orders_by_type")})]}),(0,r.jsx)("div",{className:"space-y-4",children:A.ordersByType.map((e,s)=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-gray-700 font-medium",children:e.type}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:e.count}),(0,r.jsxs)("span",{className:"text-sm font-medium text-pink-600",children:[e.percentage,"%"]})]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-gradient-to-r from-pink-500 to-rose-500 h-2 rounded-full transition-all duration-500",style:{width:`${e.percentage}%`}})})]},s))})]})]}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100",children:[(0,r.jsxs)("h3",{className:"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",children:[(0,r.jsx)(y.A,{className:"w-5 h-5 text-pink-600"}),(0,r.jsx)("span",{children:N("monthly_trend")})]}),(0,r.jsx)("div",{className:"grid md:grid-cols-4 gap-6",children:A.monthlyTrend.map((e,s)=>(0,r.jsxs)("div",{className:"text-center p-4 bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:e.month}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-lg font-bold text-green-600",children:[e.revenue.toLocaleString()," ر.س"]}),(0,r.jsx)("p",{className:"text-xs text-gray-600",children:N("revenue_label")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-lg font-bold text-blue-600",children:e.orders}),(0,r.jsx)("p",{className:"text-xs text-gray-600",children:N("orders_label")})]})]})]},s))})]})]})}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:N("checking_permissions")})]})})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,507,146,814,115,985],()=>t(44628));module.exports=r})();