(()=>{var e={};e.id=589,e.ids=[589],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26123:(e,r,t)=>{Promise.resolve().then(t.bind(t,96579))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30779:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\app\\\\dashboard\\\\reports\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\reports\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},34270:(e,r,t)=>{"use strict";t.d(r,{D:()=>i});var s=t(26787),a=t(59350);let n=()=>Date.now().toString(36)+Math.random().toString(36).substr(2),i=(0,s.v)()((0,a.Zr)((e,r)=>({appointments:[],orders:[],workers:[],isLoading:!1,error:null,addAppointment:r=>{let t={...r,id:n(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({appointments:[...e.appointments,t],error:null})),console.log("✅ تم إضافة موعد جديد:",t)},updateAppointment:(r,t)=>{e(e=>({appointments:e.appointments.map(e=>e.id===r?{...e,...t,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الموعد:",r)},deleteAppointment:r=>{e(e=>({appointments:e.appointments.filter(e=>e.id!==r),error:null})),console.log("✅ تم حذف الموعد:",r)},getAppointment:e=>r().appointments.find(r=>r.id===e),addOrder:r=>{let t={...r,id:n(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({orders:[...e.orders,t],error:null})),console.log("✅ تم إضافة طلب جديد:",t)},updateOrder:(r,t)=>{e(e=>({orders:e.orders.map(e=>e.id===r?{...e,...t,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الطلب:",r)},deleteOrder:r=>{e(e=>({orders:e.orders.filter(e=>e.id!==r),error:null})),console.log("✅ تم حذف الطلب:",r)},getOrder:e=>r().orders.find(r=>r.id===e),addWorker:r=>{let t={...r,id:n(),role:"worker",is_active:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({workers:[...e.workers,t],error:null})),console.log("✅ تم إضافة عامل جديد:",t)},updateWorker:(r,t)=>{e(e=>({workers:e.workers.map(e=>e.id===r?{...e,...t,updatedAt:new Date().toISOString()}:e),error:null})),t.email||t.password||t.full_name,console.log("✅ تم تحديث العامل:",r)},deleteWorker:r=>{e(e=>({workers:e.workers.filter(e=>e.id!==r),error:null})),console.log("✅ تم حذف العامل:",r)},getWorker:e=>r().workers.find(r=>r.id===e),startOrderWork:(r,t)=>{e(e=>({orders:e.orders.map(e=>e.id===r&&e.assignedWorker===t?{...e,status:"in_progress",updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم بدء العمل في الطلب:",r)},completeOrder:(r,t,s=[])=>{e(e=>({orders:e.orders.map(e=>e.id===r&&e.assignedWorker===t?{...e,status:"completed",completedImages:s.length>0?s:void 0,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم إنهاء الطلب:",r)},clearError:()=>{e({error:null})},loadData:()=>{e({isLoading:!0}),e({isLoading:!1})},getStats:()=>{let e=r();return{totalAppointments:e.appointments.length,totalOrders:e.orders.length,totalWorkers:e.workers.length,pendingAppointments:e.appointments.filter(e=>"pending"===e.status).length,activeOrders:e.orders.filter(e=>["pending","in_progress"].includes(e.status)).length,completedOrders:e.orders.filter(e=>"completed"===e.status).length,totalRevenue:e.orders.filter(e=>"completed"===e.status).reduce((e,r)=>e+r.price,0)}}}),{name:"yasmin-data-storage",partialize:e=>({appointments:e.appointments,orders:e.orders,workers:e.workers})}))},46597:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(65239),a=t(48088),n=t(88170),i=t.n(n),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let d={children:["",{children:["dashboard",{children:["reports",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,30779)),"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\reports\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\YASMIN ALSHAM\\yasmin-alsham\\src\\app\\dashboard\\reports\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/reports/page",pathname:"/dashboard/reports",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},80462:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},89171:(e,r,t)=>{Promise.resolve().then(t.bind(t,30779))},96579:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>w});var s=t(60687),a=t(43210),n=t(26001),i=t(16189),l=t(85814),o=t.n(l),d=t(99720),c=t(34270),p=t(99182),m=t(70334),x=t(80462),u=t(62688);let h=(0,u.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),g=(0,u.A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);var b=t(25541);let v=(0,u.A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]);var j=t(19080),f=t(53411),y=t(41312);function w(){let{user:e}=(0,d.n)(),{orders:r,workers:t,appointments:l,getStats:u}=(0,c.D)(),{t:w}=(0,p.B)();(0,i.useRouter)();let[N,k]=(0,a.useState)("month"),A=u(),_={totalRevenue:A.totalRevenue,revenueChange:0,totalOrders:A.totalOrders,ordersChange:0,completedOrders:A.completedOrders,completionRate:A.totalOrders>0?Number((A.completedOrders/A.totalOrders*100).toFixed(1)):0,averageOrderValue:A.totalOrders>0?Math.round(A.totalRevenue/A.totalOrders):0,topWorkers:t.map(e=>{let t=r.filter(r=>r.assignedWorker===e.id&&"completed"===r.status),s=t.reduce((e,r)=>e+r.price,0);return{name:e.full_name,orders:t.length,revenue:s}}).filter(e=>e.orders>0).sort((e,r)=>r.revenue-e.revenue).slice(0,3),ordersByType:(()=>{let e={};r.forEach(r=>{let t=r.description.includes("زفاف")?w("wedding_dress"):r.description.includes("سهرة")?w("evening_dress"):r.description.includes("خطوبة")?w("engagement_dress"):r.description.includes("يومي")?w("casual_dress"):w("other");e[t]=(e[t]||0)+1});let t=r.length;return Object.entries(e).map(([e,r])=>({type:e,count:r,percentage:t>0?Number((r/t*100).toFixed(1)):0}))})(),monthlyTrend:(()=>{let e=["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"],t=[],s=new Date;for(let a=3;a>=0;a--){let n=new Date(s.getFullYear(),s.getMonth()-a,1),i=e[n.getMonth()],l=r.filter(e=>{let r=new Date(e.createdAt);return r.getMonth()===n.getMonth()&&r.getFullYear()===n.getFullYear()}),o=l.filter(e=>"completed"===e.status).reduce((e,r)=>e+r.price,0);t.push({month:i,revenue:o,orders:l.length})}return t})()};return e&&"admin"===e.role?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-8",children:(0,s.jsxs)(o(),{href:"/dashboard",className:"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300",children:[(0,s.jsx)(m.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:w("back_to_dashboard")})]})}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold mb-2",children:(0,s.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent",children:w("reports_analytics")})}),(0,s.jsx)("p",{className:"text-lg text-gray-600",children:w("comprehensive_analysis")})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(x.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,s.jsxs)("select",{value:N,onChange:e=>k(e.target.value),className:"pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",children:[(0,s.jsx)("option",{value:"week",children:w("this_week")}),(0,s.jsx)("option",{value:"month",children:w("this_month")}),(0,s.jsx)("option",{value:"quarter",children:w("this_quarter")}),(0,s.jsx)("option",{value:"year",children:w("this_year")})]})]}),(0,s.jsxs)("button",{className:"btn-secondary inline-flex items-center space-x-2 space-x-reverse px-4 py-2",children:[(0,s.jsx)(h,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:w("export")})]})]})]}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,s.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-500 rounded-lg flex items-center justify-center",children:(0,s.jsx)(g,{className:"w-6 h-6 text-white"})}),(0,s.jsxs)("div",{className:`flex items-center space-x-1 space-x-reverse text-sm ${_.revenueChange>0?"text-green-600":"text-red-600"}`,children:[_.revenueChange>0?(0,s.jsx)(b.A,{className:"w-4 h-4"}):(0,s.jsx)(v,{className:"w-4 h-4"}),(0,s.jsxs)("span",{children:[Math.abs(_.revenueChange),"%"]})]})]}),(0,s.jsxs)("h3",{className:"text-2xl font-bold text-gray-800 mb-1",children:[_.totalRevenue.toLocaleString()," ر.س"]}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:w("total_revenue")})]}),(0,s.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-500 rounded-lg flex items-center justify-center",children:(0,s.jsx)(j.A,{className:"w-6 h-6 text-white"})}),(0,s.jsxs)("div",{className:`flex items-center space-x-1 space-x-reverse text-sm ${_.ordersChange>0?"text-green-600":"text-red-600"}`,children:[_.ordersChange>0?(0,s.jsx)(b.A,{className:"w-4 h-4"}):(0,s.jsx)(v,{className:"w-4 h-4"}),(0,s.jsxs)("span",{children:[Math.abs(_.ordersChange),"%"]})]})]}),(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-800 mb-1",children:_.totalOrders}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:w("total_orders")})]}),(0,s.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-purple-400 to-purple-500 rounded-lg flex items-center justify-center",children:(0,s.jsx)(f.A,{className:"w-6 h-6 text-white"})}),(0,s.jsxs)("div",{className:"text-sm text-purple-600",children:[_.completionRate,"%"]})]}),(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-800 mb-1",children:_.completedOrders}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:w("completed_orders")})]}),(0,s.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100",children:[(0,s.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-pink-400 to-rose-500 rounded-lg flex items-center justify-center",children:(0,s.jsx)(g,{className:"w-6 h-6 text-white"})})}),(0,s.jsxs)("h3",{className:"text-2xl font-bold text-gray-800 mb-1",children:[_.averageOrderValue.toLocaleString()," ر.س"]}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:w("average_order_value")})]})]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 mb-8",children:[(0,s.jsxs)(n.P.div,{initial:{opacity:0,x:-30},animate:{opacity:1,x:0},transition:{duration:.6,delay:.4},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100",children:[(0,s.jsxs)("h3",{className:"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)(y.A,{className:"w-5 h-5 text-pink-600"}),(0,s.jsx)("span",{children:w("top_workers_month")})]}),(0,s.jsx)("div",{className:"space-y-4",children:_.topWorkers.map((e,r)=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,s.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ${0===r?"bg-yellow-500":1===r?"bg-gray-400":"bg-orange-500"}`,children:r+1}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-800",children:e.name}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:[e.orders," ",w("orders_count")]})]})]}),(0,s.jsx)("div",{className:"text-right",children:(0,s.jsxs)("p",{className:"font-bold text-green-600",children:[e.revenue.toLocaleString()," ر.س"]})})]},r))})]}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,x:30},animate:{opacity:1,x:0},transition:{duration:.6,delay:.6},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100",children:[(0,s.jsxs)("h3",{className:"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)(j.A,{className:"w-5 h-5 text-pink-600"}),(0,s.jsx)("span",{children:w("orders_by_type")})]}),(0,s.jsx)("div",{className:"space-y-4",children:_.ordersByType.map((e,r)=>(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-gray-700 font-medium",children:e.type}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:e.count}),(0,s.jsxs)("span",{className:"text-sm font-medium text-pink-600",children:[e.percentage,"%"]})]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-gradient-to-r from-pink-500 to-rose-500 h-2 rounded-full transition-all duration-500",style:{width:`${e.percentage}%`}})})]},r))})]})]}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100",children:[(0,s.jsxs)("h3",{className:"text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)(f.A,{className:"w-5 h-5 text-pink-600"}),(0,s.jsx)("span",{children:w("monthly_trend")})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-4 gap-6",children:_.monthlyTrend.map((e,r)=>(0,s.jsxs)("div",{className:"text-center p-4 bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:e.month}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-lg font-bold text-green-600",children:[e.revenue.toLocaleString()," ر.س"]}),(0,s.jsx)("p",{className:"text-xs text-gray-600",children:w("revenue_label")})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-lg font-bold text-blue-600",children:e.orders}),(0,s.jsx)("p",{className:"text-xs text-gray-600",children:w("orders_label")})]})]})]},r))})]})]})}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:w("checking_permissions")})]})})}},99720:(e,r,t)=>{"use strict";t.d(r,{n:()=>i});var s=t(26787),a=t(59350);let n=()=>[],i=(0,s.v)()((0,a.Zr)((e,r)=>({user:null,isLoading:!1,error:null,signIn:async(r,t)=>{e({isLoading:!0,error:null});try{console.log("\uD83D\uDD10 بدء عملية تسجيل الدخول...",{email:r}),await new Promise(e=>setTimeout(e,1500));let s=n().find(e=>e.email.toLowerCase()===r.toLowerCase()&&e.password===t);if(!s)return console.log("❌ بيانات تسجيل الدخول غير صحيحة"),e({error:"بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور.",isLoading:!1}),!1;{console.log("✅ تم العثور على المستخدم:",s.full_name);let r={id:s.id,email:s.email,full_name:s.full_name,role:s.role,is_active:s.is_active,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),token:`demo-token-${s.id}-${Date.now()}`};return e({user:r,isLoading:!1,error:null}),console.log("\uD83C\uDF89 تم تسجيل الدخول بنجاح!"),!0}}catch(r){return console.error("\uD83D\uDCA5 خطأ في تسجيل الدخول:",r),e({error:"حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.",isLoading:!1}),!1}},signOut:async()=>{e({isLoading:!0});try{await new Promise(e=>setTimeout(e,500)),e({user:null,isLoading:!1,error:null})}catch(r){console.error("خطأ في تسجيل الخروج:",r),e({isLoading:!1,error:"خطأ في تسجيل الخروج"})}},setUser:r=>{e({user:r})},clearError:()=>{e({error:null})},checkAuth:async()=>{e({isLoading:!0});try{e({user:null,isLoading:!1})}catch(r){console.error("خطأ في التحقق من المصادقة:",r),e({user:null,isLoading:!1})}},isAuthenticated:()=>{let e=r();return null!==e.user&&e.user.is_active}}),{name:"yasmin-auth-storage",partialize:e=>({user:e.user})}))}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,507,146,814,115,59],()=>t(46597));module.exports=s})();