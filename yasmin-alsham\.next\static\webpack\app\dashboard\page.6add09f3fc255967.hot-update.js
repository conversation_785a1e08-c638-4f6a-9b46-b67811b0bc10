"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useTranslation.ts":
/*!*************************************!*\
  !*** ./src/hooks/useTranslation.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useTranslation auto */ \n// الترجمات العربية\nconst arTranslations = {\n    // التنقل والعناوين الرئيسية\n    'dashboard': 'لوحة التحكم',\n    'orders': 'الطلبات',\n    'appointments': 'المواعيد',\n    'settings': 'الإعدادات',\n    'workers': 'العمال',\n    'reports': 'التقارير',\n    'logout': 'تسجيل الخروج',\n    'welcome': 'مرحباً',\n    'welcome_back': 'مرحباً بعودتك',\n    // الأزرار والإجراءات\n    'add_new_order': 'إضافة طلب جديد',\n    'book_appointment': 'حجز موعد',\n    'view_details': 'عرض التفاصيل',\n    'edit': 'تعديل',\n    'delete': 'حذف',\n    'save': 'حفظ',\n    'cancel': 'إلغاء',\n    'submit': 'إرسال',\n    'search': 'بحث',\n    'filter': 'تصفية',\n    'export': 'تصدير',\n    'print': 'طباعة',\n    'back': 'رجوع',\n    'next': 'التالي',\n    'previous': 'السابق',\n    'close': 'إغلاق',\n    'confirm': 'تأكيد',\n    'loading': 'جاري التحميل...',\n    'saving': 'جاري الحفظ...',\n    // حالات الطلبات\n    'pending': 'في الانتظار',\n    'in_progress': 'قيد التنفيذ',\n    'completed': 'مكتمل',\n    'delivered': 'تم التسليم',\n    'cancelled': 'ملغي',\n    // نصوص عامة\n    'name': 'الاسم',\n    'email': 'البريد الإلكتروني',\n    'phone': 'رقم الهاتف',\n    'address': 'العنوان',\n    'date': 'التاريخ',\n    'time': 'الوقت',\n    'status': 'الحالة',\n    'price': 'السعر',\n    'total': 'المجموع',\n    'description': 'الوصف',\n    'notes': 'ملاحظات',\n    'client_name': 'اسم الزبونة',\n    'client_phone': 'رقم هاتف الزبونة',\n    // رسائل النجاح والخطأ\n    'success': 'نجح',\n    'error': 'خطأ',\n    'warning': 'تحذير',\n    'info': 'معلومات',\n    'order_added_success': 'تم إضافة الطلب بنجاح',\n    'order_updated_success': 'تم تحديث الطلب بنجاح',\n    'order_deleted_success': 'تم حذف الطلب بنجاح',\n    'fill_required_fields': 'يرجى ملء جميع الحقول المطلوبة',\n    // نصوص إضافية مطلوبة\n    'admin_dashboard': 'لوحة تحكم المدير',\n    'worker_dashboard': 'لوحة تحكم العامل',\n    'admin': 'مدير',\n    'worker': 'عامل',\n    'change_language': 'تغيير اللغة',\n    'my_active_orders': 'طلباتي النشطة',\n    'completed_orders': 'الطلبات المكتملة',\n    'total_orders': 'إجمالي الطلبات',\n    'total_revenue': 'إجمالي الإيرادات',\n    'recent_orders': 'الطلبات الحديثة',\n    'quick_actions': 'إجراءات سريعة',\n    'view_all_orders': 'عرض جميع الطلبات',\n    'add_order': 'إضافة طلب',\n    'manage_workers': 'إدارة العمال',\n    'view_reports': 'عرض التقارير',\n    'client_name_required': 'اسم الزبونة *',\n    'phone_required': 'رقم الهاتف *',\n    'order_description_required': 'وصف الطلب *',\n    'delivery_date_required': 'موعد التسليم *',\n    'price_sar': 'السعر (ريال سعودي)',\n    'measurements_cm': 'المقاسات (بالسنتيمتر)',\n    'additional_notes': 'ملاحظات إضافية',\n    'voice_notes_optional': 'ملاحظات صوتية (اختيارية)',\n    'design_images': 'صور التصميم',\n    'fabric_type': 'نوع القماش',\n    'responsible_worker': 'العامل المسؤول',\n    'choose_worker': 'اختر العامل المسؤول',\n    'order_status': 'حالة الطلب',\n    'back_to_dashboard': 'العودة إلى لوحة التحكم',\n    'overview_today': 'نظرة عامة على أنشطة اليوم',\n    'welcome_worker': 'مرحباً بك في مساحة العمل',\n    // مفاتيح مفقودة من لوحة التحكم\n    'homepage': 'الصفحة الرئيسية',\n    'my_completed_orders': 'طلباتي المكتملة',\n    'my_total_orders': 'إجمالي طلباتي',\n    'active_orders': 'الطلبات النشطة',\n    'today_appointments': 'مواعيد اليوم',\n    'statistics': 'الإحصائيات',\n    'no_orders_found': 'لا توجد طلبات',\n    'view_all': 'عرض الكل',\n    'worker_management': 'إدارة العمال',\n    'reminder': 'تذكير',\n    'you_have': 'لديك',\n    'today_appointments_reminder': 'موعد اليوم',\n    'and': 'و',\n    'orders_need_follow': 'طلبات تحتاج متابعة',\n    'detailed_reports': 'تقارير مفصلة',\n    'worker_description': 'يمكنك هنا متابعة طلباتك المخصصة لك وتحديث حالتها',\n    // مفاتيح صفحة إضافة الطلبات\n    'order_add_error': 'حدث خطأ أثناء إضافة الطلب',\n    'cm_placeholder': 'سم',\n    'shoulder': 'الكتف',\n    'shoulder_circumference': 'محيط الكتف',\n    'chest': 'الصدر',\n    'waist': 'الخصر',\n    'hips': 'الأرداف',\n    'dart_length': 'طول الخياطة',\n    'bodice_length': 'طول الجسم',\n    'neckline': 'خط الرقبة',\n    'armpit': 'الإبط',\n    'sleeve_length': 'طول الكم',\n    'forearm': 'الساعد',\n    'cuff': 'الكم',\n    'front_length': 'الطول الأمامي',\n    'back_length': 'الطول الخلفي',\n    // مفاتيح صفحة الطلبات\n    'enter_order_number': 'أدخل رقم الطلب',\n    'all_orders': 'جميع الطلبات',\n    'no_orders_assigned': 'لا توجد طلبات مخصصة لك',\n    'no_orders_assigned_desc': 'لم يتم تخصيص أي طلبات لك بعد',\n    'price_label': 'السعر',\n    'sar': 'ريال',\n    'view': 'عرض',\n    'completing': 'جاري الإنهاء...',\n    'start_work': 'بدء العمل',\n    'complete_order': 'إنهاء الطلب',\n    'complete_order_modal_title': 'إنهاء الطلب وتحميل صور العمل المكتمل',\n    'important_warning': 'تحذير مهم',\n    'complete_order_warning': 'بمجرد إنهاء الطلب، لن تتمكن من تعديل حالته مرة أخرى. تأكد من تحميل جميع صور العمل المكتمل قبل المتابعة.',\n    // مفاتيح صفحة العمال\n    'worker_added_success': 'تم إضافة العامل بنجاح',\n    'error_adding_worker': 'حدث خطأ أثناء إضافة العامل',\n    'worker_updated_success': 'تم تحديث العامل بنجاح',\n    'error_updating_worker': 'حدث خطأ أثناء تحديث العامل',\n    'worker_deleted_success': 'تم حذف العامل بنجاح',\n    'worker_deactivated': 'تم إلغاء تفعيل العامل',\n    'worker_activated': 'تم تفعيل العامل',\n    'adding': 'جاري الإضافة...',\n    'add_worker': 'إضافة عامل',\n    'active': 'نشط',\n    'inactive': 'غير نشط',\n    'save_changes': 'حفظ التغييرات',\n    'search_workers_placeholder': 'البحث عن العمال...',\n    // مفاتيح صفحة التقارير\n    'engagement_dress': 'فستان خطوبة',\n    'casual_dress': 'فستان يومي',\n    'other': 'أخرى',\n    'this_week': 'هذا الأسبوع',\n    'this_month': 'هذا الشهر',\n    'this_quarter': 'هذا الربع',\n    'this_year': 'هذا العام',\n    // مفاتيح صفحة المواعيد\n    'confirmed': 'مؤكد',\n    'pm': 'مساءً',\n    'am': 'صباحاً',\n    'all_statuses': 'جميع الحالات',\n    'all_dates': 'جميع التواريخ',\n    'today': 'اليوم',\n    'tomorrow': 'غداً',\n    // مفاتيح مكونات إضافية\n    'of': 'من',\n    'images_text': 'صور',\n    // مفاتيح مكون تعديل الطلب\n    'order_update_error': 'حدث خطأ أثناء تحديث الطلب',\n    'price_sar_required': 'السعر (ريال سعودي) *',\n    'status_pending': 'في الانتظار',\n    'status_in_progress': 'قيد التنفيذ',\n    'status_completed': 'مكتمل',\n    'status_delivered': 'تم التسليم',\n    'status_cancelled': 'ملغي',\n    // نصوص الفوتر\n    'home': 'الرئيسية',\n    'track_order': 'استعلام عن الطلب',\n    'fabrics': 'الأقمشة',\n    'contact_us': 'تواصلي معنا',\n    'yasmin_alsham': 'ياسمين الشام',\n    'custom_dress_tailoring': 'تفصيل فساتين حسب الطلب'\n};\n// الترجمات الإنجليزية\nconst enTranslations = {\n    // التنقل والعناوين الرئيسية\n    'dashboard': 'Dashboard',\n    'orders': 'Orders',\n    'appointments': 'Appointments',\n    'settings': 'Settings',\n    'workers': 'Workers',\n    'reports': 'Reports',\n    'logout': 'Logout',\n    'welcome': 'Welcome',\n    'welcome_back': 'Welcome Back',\n    // الأزرار والإجراءات\n    'add_new_order': 'Add New Order',\n    'book_appointment': 'Book Appointment',\n    'view_details': 'View Details',\n    'edit': 'Edit',\n    'delete': 'Delete',\n    'save': 'Save',\n    'cancel': 'Cancel',\n    'submit': 'Submit',\n    'search': 'Search',\n    'filter': 'Filter',\n    'export': 'Export',\n    'print': 'Print',\n    'back': 'Back',\n    'next': 'Next',\n    'previous': 'Previous',\n    'close': 'Close',\n    'confirm': 'Confirm',\n    'loading': 'Loading...',\n    'saving': 'Saving...',\n    // حالات الطلبات\n    'pending': 'Pending',\n    'in_progress': 'In Progress',\n    'completed': 'Completed',\n    'delivered': 'Delivered',\n    'cancelled': 'Cancelled',\n    // نصوص عامة\n    'name': 'Name',\n    'email': 'Email',\n    'phone': 'Phone',\n    'address': 'Address',\n    'date': 'Date',\n    'time': 'Time',\n    'status': 'Status',\n    'price': 'Price',\n    'total': 'Total',\n    'description': 'Description',\n    'notes': 'Notes',\n    'client_name': 'Client Name',\n    'client_phone': 'Client Phone',\n    // رسائل النجاح والخطأ\n    'success': 'Success',\n    'error': 'Error',\n    'warning': 'Warning',\n    'info': 'Info',\n    'order_added_success': 'Order added successfully',\n    'order_updated_success': 'Order updated successfully',\n    'order_deleted_success': 'Order deleted successfully',\n    'fill_required_fields': 'Please fill all required fields',\n    // نصوص إضافية مطلوبة\n    'admin_dashboard': 'Admin Dashboard',\n    'worker_dashboard': 'Worker Dashboard',\n    'admin': 'Admin',\n    'worker': 'Worker',\n    'change_language': 'Change Language',\n    'my_active_orders': 'My Active Orders',\n    'completed_orders': 'Completed Orders',\n    'total_orders': 'Total Orders',\n    'total_revenue': 'Total Revenue',\n    'recent_orders': 'Recent Orders',\n    'quick_actions': 'Quick Actions',\n    'view_all_orders': 'View All Orders',\n    'add_order': 'Add Order',\n    'manage_workers': 'Manage Workers',\n    'view_reports': 'View Reports',\n    'client_name_required': 'Client Name *',\n    'phone_required': 'Phone Number *',\n    'order_description_required': 'Order Description *',\n    'delivery_date_required': 'Delivery Date *',\n    'price_sar': 'Price (SAR)',\n    'measurements_cm': 'Measurements (cm)',\n    'additional_notes': 'Additional Notes',\n    'voice_notes_optional': 'Voice Notes (Optional)',\n    'design_images': 'Design Images',\n    'fabric_type': 'Fabric Type',\n    'responsible_worker': 'Responsible Worker',\n    'choose_worker': 'Choose Responsible Worker',\n    'order_status': 'Order Status',\n    'back_to_dashboard': 'Back to Dashboard',\n    'overview_today': 'Overview of today\\'s activities',\n    'welcome_worker': 'Welcome to your workspace',\n    // مفاتيح مفقودة من لوحة التحكم\n    'homepage': 'Homepage',\n    'my_completed_orders': 'My Completed Orders',\n    'my_total_orders': 'My Total Orders',\n    'active_orders': 'Active Orders',\n    'today_appointments': 'Today\\'s Appointments',\n    'statistics': 'Statistics',\n    'no_orders_found': 'No orders found',\n    'view_all': 'View All',\n    'worker_management': 'Worker Management',\n    'reminder': 'Reminder',\n    'you_have': 'You have',\n    'today_appointments_reminder': 'appointments today',\n    'and': 'and',\n    'orders_need_follow': 'orders that need follow-up',\n    'detailed_reports': 'Detailed Reports',\n    'worker_description': 'Here you can track your assigned orders and update their status',\n    // مفاتيح صفحة إضافة الطلبات\n    'order_add_error': 'An error occurred while adding the order',\n    'cm_placeholder': 'cm',\n    'shoulder': 'Shoulder',\n    'shoulder_circumference': 'Shoulder Circumference',\n    'chest': 'Chest',\n    'waist': 'Waist',\n    'hips': 'Hips',\n    'dart_length': 'Dart Length',\n    'bodice_length': 'Bodice Length',\n    'neckline': 'Neckline',\n    'armpit': 'Armpit',\n    'sleeve_length': 'Sleeve Length',\n    'forearm': 'Forearm',\n    'cuff': 'Cuff',\n    'front_length': 'Front Length',\n    'back_length': 'Back Length',\n    // مفاتيح صفحة الطلبات\n    'enter_order_number': 'Enter order number',\n    'all_orders': 'All Orders',\n    'no_orders_assigned': 'No orders assigned to you',\n    'no_orders_assigned_desc': 'No orders have been assigned to you yet',\n    'price_label': 'Price',\n    'sar': 'SAR',\n    'view': 'View',\n    'completing': 'Completing...',\n    'start_work': 'Start Work',\n    'complete_order': 'Complete Order',\n    'complete_order_modal_title': 'Complete Order and Upload Finished Work Images',\n    'important_warning': 'Important Warning',\n    'complete_order_warning': 'Once you complete the order, you will not be able to modify its status again. Make sure to upload all finished work images before proceeding.',\n    // مفاتيح صفحة العمال\n    'worker_added_success': 'Worker added successfully',\n    'error_adding_worker': 'Error adding worker',\n    'worker_updated_success': 'Worker updated successfully',\n    'error_updating_worker': 'Error updating worker',\n    'worker_deleted_success': 'Worker deleted successfully',\n    'worker_deactivated': 'Worker deactivated',\n    'worker_activated': 'Worker activated',\n    'adding': 'Adding...',\n    'add_worker': 'Add Worker',\n    'active': 'Active',\n    'inactive': 'Inactive',\n    'save_changes': 'Save Changes',\n    'search_workers_placeholder': 'Search workers...',\n    // مفاتيح صفحة التقارير\n    'engagement_dress': 'Engagement Dress',\n    'casual_dress': 'Casual Dress',\n    'other': 'Other',\n    'this_week': 'This Week',\n    'this_month': 'This Month',\n    'this_quarter': 'This Quarter',\n    'this_year': 'This Year',\n    // مفاتيح صفحة المواعيد\n    'confirmed': 'Confirmed',\n    'pm': 'PM',\n    'am': 'AM',\n    'all_statuses': 'All Statuses',\n    'all_dates': 'All Dates',\n    'today': 'Today',\n    'tomorrow': 'Tomorrow',\n    // مفاتيح مكونات إضافية\n    'of': 'of',\n    'images_text': 'images',\n    // مفاتيح مكون تعديل الطلب\n    'order_update_error': 'Error updating order',\n    'price_sar_required': 'Price (SAR) *',\n    'status_pending': 'Pending',\n    'status_in_progress': 'In Progress',\n    'status_completed': 'Completed',\n    'status_delivered': 'Delivered',\n    'status_cancelled': 'Cancelled',\n    // نصوص الفوتر\n    'home': 'Home',\n    'track_order': 'Track Order',\n    'fabrics': 'Fabrics',\n    'contact_us': 'Contact Us',\n    'yasmin_alsham': 'Yasmin Alsham',\n    'custom_dress_tailoring': 'Custom Dress Tailoring'\n};\n// Hook للترجمة\nfunction useTranslation() {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('ar');\n    // تحميل اللغة المحفوظة عند بدء التطبيق\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTranslation.useEffect\": ()=>{\n            const savedLanguage = localStorage.getItem('dashboard-language');\n            if (savedLanguage) {\n                setLanguage(savedLanguage);\n            }\n        }\n    }[\"useTranslation.useEffect\"], []);\n    // حفظ اللغة عند تغييرها\n    const changeLanguage = (newLanguage)=>{\n        setLanguage(newLanguage);\n        localStorage.setItem('dashboard-language', newLanguage);\n    };\n    // دالة الترجمة\n    const t = (key)=>{\n        const translations = language === 'ar' ? arTranslations : enTranslations;\n        const translation = translations[key];\n        if (typeof translation === 'string') {\n            return translation;\n        }\n        // إذا لم توجد الترجمة، أرجع المفتاح نفسه\n        return key;\n    };\n    // التحقق من اللغة الحالية\n    const isArabic = language === 'ar';\n    const isEnglish = language === 'en';\n    return {\n        language,\n        changeLanguage,\n        t,\n        isArabic,\n        isEnglish\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useTranslation.ts\n"));

/***/ })

});