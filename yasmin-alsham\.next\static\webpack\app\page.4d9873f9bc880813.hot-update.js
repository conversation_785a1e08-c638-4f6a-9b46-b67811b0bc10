"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_Home_Menu_Palette_Scissors_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,Home,Menu,Palette,Scissors,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_Home_Menu_Palette_Scissors_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,Home,Menu,Palette,Scissors,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_Home_Menu_Palette_Scissors_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,Home,Menu,Palette,Scissors,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_Home_Menu_Palette_Scissors_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,Home,Menu,Palette,Scissors,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_Home_Menu_Palette_Scissors_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,Home,Menu,Palette,Scissors,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scissors.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_Home_Menu_Palette_Scissors_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,Home,Menu,Palette,Scissors,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_Home_Menu_Palette_Scissors_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,Home,Menu,Palette,Scissors,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_Home_Menu_Palette_Scissors_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,Home,Menu,Palette,Scissors,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Heart_Home_Menu_Palette_Scissors_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Heart,Home,Menu,Palette,Scissors,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _store_shopStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/shopStore */ \"(app-pages-browser)/./src/store/shopStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Header() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clickCount, setClickCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const clickTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // استخدام متجر التسوق\n    const { favorites, cart, getCartItemsCount } = (0,_store_shopStore__WEBPACK_IMPORTED_MODULE_4__.useShopStore)();\n    const cartItemsCount = getCartItemsCount();\n    const favoritesCount = favorites.length;\n    const toggleMenu = ()=>setIsMenuOpen(!isMenuOpen);\n    const handleLogoClick = (e)=>{\n        e.preventDefault();\n        setClickCount((prev)=>prev + 1);\n        // إذا كان هذا النقر الثالث، انتقل لصفحة تسجيل الدخول\n        if (clickCount === 2) {\n            router.push('/login');\n            setClickCount(0);\n            if (clickTimeoutRef.current) {\n                clearTimeout(clickTimeoutRef.current);\n            }\n            return;\n        }\n        // إعادة تعيين العداد بعد ثانيتين\n        if (clickTimeoutRef.current) {\n            clearTimeout(clickTimeoutRef.current);\n        }\n        clickTimeoutRef.current = setTimeout(()=>{\n            setClickCount(0);\n        }, 2000);\n    };\n    const menuItems = [\n        {\n            href: '/',\n            label: 'الرئيسية',\n            icon: _barrel_optimize_names_Calendar_Heart_Home_Menu_Palette_Scissors_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            href: '/designs',\n            label: 'الفساتين الجاهزة',\n            icon: _barrel_optimize_names_Calendar_Heart_Home_Menu_Palette_Scissors_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            href: '/book-appointment',\n            label: 'حجز موعد',\n            icon: _barrel_optimize_names_Calendar_Heart_Home_Menu_Palette_Scissors_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            href: '/track-order',\n            label: 'تتبع الطلب',\n            icon: _barrel_optimize_names_Calendar_Heart_Home_Menu_Palette_Scissors_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            href: '/fabrics',\n            label: 'الأقمشة',\n            icon: _barrel_optimize_names_Calendar_Heart_Home_Menu_Palette_Scissors_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-pink-100 shadow-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                            initial: {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            onClick: toggleMenu,\n                            className: \"lg:hidden p-2 rounded-lg bg-gradient-to-r from-pink-100 to-rose-100 text-pink-600 hover:from-pink-200 hover:to-rose-200 transition-all duration-300\",\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_Home_Menu_Palette_Scissors_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 27\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_Home_Menu_Palette_Scissors_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 55\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: -10\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            className: \"cursor-pointer hover:opacity-80 transition-opacity duration-300 lg:flex lg:items-center lg:space-x-2 lg:space-x-reverse\",\n                            onClick: handleLogoClick,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center lg:text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl lg:text-2xl font-bold bg-gradient-to-r from-pink-600 to-rose-600 bg-clip-text text-transparent\",\n                                        children: \"ياسمين الشام\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"hidden lg:block text-xs lg:text-sm text-gray-600 font-medium\",\n                                        children: \"تفصيل فساتين حسب الطلب\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 space-x-reverse lg:hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/favorites\",\n                                    className: \"relative p-2 rounded-lg bg-gradient-to-r from-pink-100 to-rose-100 text-pink-600 hover:from-pink-200 hover:to-rose-200 transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_Home_Menu_Palette_Scissors_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        favoritesCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold\",\n                                            children: favoritesCount > 9 ? '9+' : favoritesCount\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/cart\",\n                                    className: \"relative p-2 rounded-lg bg-gradient-to-r from-pink-100 to-rose-100 text-pink-600 hover:from-pink-200 hover:to-rose-200 transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Heart_Home_Menu_Palette_Scissors_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        cartItemsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold\",\n                                            children: cartItemsCount > 9 ? '9+' : cartItemsCount\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden lg:flex items-center space-x-8 space-x-reverse\",\n                            children: menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: -10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: index * 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: \"icon-text-spacing text-gray-700 hover:text-pink-600 transition-colors duration-300 font-medium group\",\n                                        children: [\n                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"w-4 h-4 menu-item-icon group-hover:scale-110 transition-transform duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    item.label,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-pink-400 to-rose-400 group-hover:w-full transition-all duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this)\n                                }, item.href, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: isMenuOpen ? 1 : 0,\n                        height: isMenuOpen ? 'auto' : 0\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    className: \"lg:hidden overflow-hidden bg-white border-t border-pink-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"py-4 space-y-2\",\n                        children: menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -20\n                                },\n                                animate: {\n                                    opacity: isMenuOpen ? 1 : 0,\n                                    x: isMenuOpen ? 0 : -20\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    delay: index * 0.1\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    className: \"flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 hover:text-pink-600 hover:bg-pink-50 rounded-lg transition-all duration-300 font-medium\",\n                                    children: [\n                                        item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 17\n                                }, this)\n                            }, item.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\YASMIN ALSHAM\\\\yasmin-alsham\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"ERqcK5y7Qlm1wJyMKdly3Pd0QZo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _store_shopStore__WEBPACK_IMPORTED_MODULE_4__.useShopStore\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Header.tsx\n"));

/***/ })

});