@import "tailwindcss";

:root {
  --background: #fefefe;
  --foreground: #2d2d2d;
  --primary: #f472b6;
  --primary-dark: #ec4899;
  --secondary: #f3e8ff;
  --accent: #fbbf24;
  --muted: #f5f5f4;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-cairo);
  --font-arabic: var(--font-noto-kufi);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-cairo), 'Cairo', sans-serif;
  direction: rtl;
}

/* تأثيرات مخصصة للموقع */
.gradient-bg {
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 50%, #f3e8ff 100%);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* تأثير Parallax */
.parallax {
  transform: translateZ(0);
  will-change: transform;
}

/* تأثيرات الحركة */
.fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.8s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* أنماط الأزرار المخصصة */
.btn-primary {
  @apply bg-gradient-to-r from-pink-400 to-rose-400 hover:from-pink-500 hover:to-rose-500 text-white font-semibold py-3 px-6 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105;
}

.btn-secondary {
  @apply bg-gradient-to-r from-purple-100 to-pink-100 hover:from-purple-200 hover:to-pink-200 text-purple-800 font-semibold py-2 px-4 rounded-lg border border-purple-200 hover:border-purple-300 transition-all duration-300;
}

/* أنماط البطاقات */
.card {
  @apply bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100;
}

.card-hover {
  @apply transform hover:scale-105 transition-transform duration-300;
}

/* تحسين تباعد الأيقونات */
.icon-text-spacing {
  @apply flex items-center space-x-2 space-x-reverse;
}

.icon-text-spacing svg,
.icon-text-spacing .lucide {
  @apply ml-2 mr-0;
}

/* تباعد خاص للأيقونات في الأزرار */
.btn-primary .lucide,
.btn-secondary .lucide,
.btn-primary svg,
.btn-secondary svg {
  @apply ml-2 mr-0;
}

/* تباعد للأيقونات في القوائم */
.menu-item-icon {
  @apply ml-2 mr-0 flex-shrink-0;
}

/* تخصيص شريط التمرير */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #f472b6, #ec4899);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #ec4899, #db2777);
}
