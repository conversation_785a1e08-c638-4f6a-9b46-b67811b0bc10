(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[161],{1364:(e,r,t)=>{"use strict";t.d(r,{D:()=>i});var s=t(5453),a=t(6786);let l=()=>Date.now().toString(36)+Math.random().toString(36).substr(2),i=(0,s.v)()((0,a.Zr)((e,r)=>({appointments:[],orders:[],workers:[],isLoading:!1,error:null,addAppointment:r=>{let t={...r,id:l(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({appointments:[...e.appointments,t],error:null})),console.log("✅ تم إضافة موعد جديد:",t)},updateAppointment:(r,t)=>{e(e=>({appointments:e.appointments.map(e=>e.id===r?{...e,...t,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الموعد:",r)},deleteAppointment:r=>{e(e=>({appointments:e.appointments.filter(e=>e.id!==r),error:null})),console.log("✅ تم حذف الموعد:",r)},getAppointment:e=>r().appointments.find(r=>r.id===e),addOrder:r=>{let t={...r,id:l(),status:"pending",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({orders:[...e.orders,t],error:null})),console.log("✅ تم إضافة طلب جديد:",t)},updateOrder:(r,t)=>{e(e=>({orders:e.orders.map(e=>e.id===r?{...e,...t,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم تحديث الطلب:",r)},deleteOrder:r=>{e(e=>({orders:e.orders.filter(e=>e.id!==r),error:null})),console.log("✅ تم حذف الطلب:",r)},getOrder:e=>r().orders.find(r=>r.id===e),addWorker:r=>{let t={...r,id:l(),role:"worker",is_active:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(e=>({workers:[...e.workers,t],error:null}));{let e=JSON.parse(localStorage.getItem("yasmin-users")||"[]");e.push({id:t.id,email:t.email,password:t.password,full_name:t.full_name,role:"worker",is_active:!0}),localStorage.setItem("yasmin-users",JSON.stringify(e))}console.log("✅ تم إضافة عامل جديد:",t)},updateWorker:(r,t)=>{if(e(e=>({workers:e.workers.map(e=>e.id===r?{...e,...t,updatedAt:new Date().toISOString()}:e),error:null})),t.email||t.password||t.full_name){let e=JSON.parse(localStorage.getItem("yasmin-users")||"[]"),s=e.findIndex(e=>e.id===r);-1!==s&&(t.email&&(e[s].email=t.email),t.password&&(e[s].password=t.password),t.full_name&&(e[s].full_name=t.full_name),localStorage.setItem("yasmin-users",JSON.stringify(e)))}console.log("✅ تم تحديث العامل:",r)},deleteWorker:r=>{e(e=>({workers:e.workers.filter(e=>e.id!==r),error:null}));{let e=JSON.parse(localStorage.getItem("yasmin-users")||"[]").filter(e=>e.id!==r);localStorage.setItem("yasmin-users",JSON.stringify(e))}console.log("✅ تم حذف العامل:",r)},getWorker:e=>r().workers.find(r=>r.id===e),startOrderWork:(r,t)=>{e(e=>({orders:e.orders.map(e=>e.id===r&&e.assignedWorker===t?{...e,status:"in_progress",updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم بدء العمل في الطلب:",r)},completeOrder:function(r,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];e(e=>({orders:e.orders.map(e=>e.id===r&&e.assignedWorker===t?{...e,status:"completed",completedImages:s.length>0?s:void 0,updatedAt:new Date().toISOString()}:e),error:null})),console.log("✅ تم إنهاء الطلب:",r)},clearError:()=>{e({error:null})},loadData:()=>{e({isLoading:!0}),e({isLoading:!1})},getStats:()=>{let e=r();return{totalAppointments:e.appointments.length,totalOrders:e.orders.length,totalWorkers:e.workers.length,pendingAppointments:e.appointments.filter(e=>"pending"===e.status).length,activeOrders:e.orders.filter(e=>["pending","in_progress"].includes(e.status)).length,completedOrders:e.orders.filter(e=>"completed"===e.status).length,totalRevenue:e.orders.filter(e=>"completed"===e.status).reduce((e,r)=>e+r.price,0)}}}),{name:"yasmin-data-storage",partialize:e=>({appointments:e.appointments,orders:e.orders,workers:e.workers})}))},3294:(e,r,t)=>{"use strict";t.d(r,{n:()=>i});var s=t(5453),a=t(6786);let l=()=>{let e=localStorage.getItem("yasmin-users");if(e)return JSON.parse(e);let r=[{id:"1",email:"<EMAIL>",password:"admin123",full_name:"مدير النظام",role:"admin",is_active:!0}];return localStorage.setItem("yasmin-users",JSON.stringify(r)),r},i=(0,s.v)()((0,a.Zr)((e,r)=>({user:null,isLoading:!1,error:null,signIn:async(r,t)=>{e({isLoading:!0,error:null});try{console.log("\uD83D\uDD10 بدء عملية تسجيل الدخول...",{email:r}),await new Promise(e=>setTimeout(e,1500));let s=l().find(e=>e.email.toLowerCase()===r.toLowerCase()&&e.password===t);if(!s)return console.log("❌ بيانات تسجيل الدخول غير صحيحة"),e({error:"بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور.",isLoading:!1}),!1;{console.log("✅ تم العثور على المستخدم:",s.full_name);let r={id:s.id,email:s.email,full_name:s.full_name,role:s.role,is_active:s.is_active,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),token:"demo-token-".concat(s.id,"-").concat(Date.now())};return localStorage.setItem("yasmin-auth-user",JSON.stringify(r)),console.log("\uD83D\uDCBE تم حفظ المستخدم في localStorage"),e({user:r,isLoading:!1,error:null}),console.log("\uD83C\uDF89 تم تسجيل الدخول بنجاح!"),!0}}catch(r){return console.error("\uD83D\uDCA5 خطأ في تسجيل الدخول:",r),e({error:"حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.",isLoading:!1}),!1}},signOut:async()=>{e({isLoading:!0});try{await new Promise(e=>setTimeout(e,500)),localStorage.removeItem("yasmin-auth-user"),e({user:null,isLoading:!1,error:null})}catch(r){console.error("خطأ في تسجيل الخروج:",r),e({isLoading:!1,error:"خطأ في تسجيل الخروج"})}},setUser:r=>{e({user:r}),r?localStorage.setItem("yasmin-auth-user",JSON.stringify(r)):localStorage.removeItem("yasmin-auth-user")},clearError:()=>{e({error:null})},checkAuth:async()=>{e({isLoading:!0});try{{let r=localStorage.getItem("yasmin-auth-user");if(r){let t=JSON.parse(r);e({user:t,isLoading:!1});return}}e({user:null,isLoading:!1})}catch(r){console.error("خطأ في التحقق من المصادقة:",r),e({user:null,isLoading:!1})}},isAuthenticated:()=>{let e=r();return null!==e.user&&e.user.is_active}}),{name:"yasmin-auth-storage",partialize:e=>({user:e.user})}))},3717:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4535:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>N});var s=t(5155),a=t(2115),l=t(6408),i=t(5695),n=t(6874),o=t.n(n),d=t(3294),c=t(1364),m=t(9137),p=t(2138),u=t(4616),x=t(7924),g=t(646),h=t(4861),y=t(7580),b=t(1007),f=t(8883),v=t(9420),w=t(3717),j=t(2525);function N(){let{user:e}=(0,d.n)(),{workers:r,addWorker:t,updateWorker:n,deleteWorker:N,orders:k}=(0,c.D)(),{t:_,isArabic:S}=(0,m.B)(),A=(0,i.useRouter)();(0,a.useEffect)(()=>{e&&"admin"===e.role||A.push("/dashboard")},[e,A]);let[O,C]=(0,a.useState)(""),[I,q]=(0,a.useState)(!1),[L,D]=(0,a.useState)({email:"",password:"",full_name:"",phone:"",specialty:""}),[P,J]=(0,a.useState)(null),[W,z]=(0,a.useState)(!1),[M,E]=(0,a.useState)(!1),[T,H]=(0,a.useState)(null),R=async e=>{if(e.preventDefault(),!L.email||!L.password||!L.full_name||!L.phone||!L.specialty)return void H({type:"error",text:_("fill_required_fields")});E(!0),H(null);try{await new Promise(e=>setTimeout(e,1e3)),t({email:L.email,password:L.password,full_name:L.full_name,phone:L.phone,specialty:L.specialty}),H({type:"success",text:_("worker_added_success")}),D({email:"",password:"",full_name:"",phone:"",specialty:""}),q(!1)}catch(e){H({type:"error",text:_("error_adding_worker")})}finally{E(!1)}},U=e=>{J({...e,password:""}),z(!0)},Z=async e=>{if(e.preventDefault(),!P.email||!P.full_name||!P.phone||!P.specialty)return void H({type:"error",text:_("fill_required_fields")});E(!0),H(null);try{await new Promise(e=>setTimeout(e,1e3));let e={email:P.email,full_name:P.full_name,phone:P.phone,specialty:P.specialty,is_active:P.is_active};P.password&&(e.password=P.password),n(P.id,e),H({type:"success",text:_("worker_updated_success")}),z(!1),J(null)}catch(e){H({type:"error",text:_("error_updating_worker")})}finally{E(!1)}},B=e=>{confirm(_("confirm_delete_worker"))&&(N(e),H({type:"success",text:_("worker_deleted_success")}))},F=(e,r)=>{n(e,{is_active:!r}),H({type:"success",text:r?_("worker_deactivated"):_("worker_activated")})},G=e=>k.filter(r=>r.assignedWorker===e&&"completed"===r.status).length,K=e=>new Date(e).toLocaleDateString("ar-US",{year:"numeric",month:"long",day:"numeric"}),Q=r.filter(e=>e.full_name.toLowerCase().includes(O.toLowerCase())||e.email.toLowerCase().includes(O.toLowerCase())||e.specialty.toLowerCase().includes(O.toLowerCase()));return e&&"admin"===e.role?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 pt-20",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-8",children:(0,s.jsxs)(o(),{href:"/dashboard",className:"inline-flex items-center space-x-2 space-x-reverse text-pink-600 hover:text-pink-700 transition-colors duration-300",children:[(0,s.jsx)(p.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:_("back_to_dashboard")})]})}),(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold mb-2",children:(0,s.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent",children:_("workers_management")})}),(0,s.jsx)("p",{className:"text-lg text-gray-600",children:_("view_manage_team")})]}),(0,s.jsxs)("button",{onClick:()=>q(!0),className:"btn-primary inline-flex items-center justify-center space-x-2 space-x-reverse px-6 py-3 group",children:[(0,s.jsx)(u.A,{className:"w-5 h-5 group-hover:scale-110 transition-transform duration-300"}),(0,s.jsx)("span",{children:_("add_new_worker")})]})]}),(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 mb-8",children:(0,s.jsxs)("div",{className:"relative max-w-md",children:[(0,s.jsx)(x.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,s.jsx)("input",{type:"text",value:O,onChange:e=>C(e.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-300",placeholder:_("search_workers_placeholder")})]})}),T&&(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-4 rounded-lg flex items-center space-x-3 space-x-reverse ".concat("success"===T.type?"bg-green-50 text-green-800 border border-green-200":"bg-red-50 text-red-800 border border-red-200"),children:["success"===T.type?(0,s.jsx)(g.A,{className:"w-5 h-5 text-green-600"}):(0,s.jsx)(h.A,{className:"w-5 h-5 text-red-600"}),(0,s.jsx)("span",{children:T.text})]}),I&&(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-100 mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:_("add_new_worker_form")}),(0,s.jsx)("button",{onClick:()=>q(!1),className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)(h.A,{className:"w-6 h-6"})})]}),(0,s.jsxs)("form",{onSubmit:R,className:"grid md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("full_name_required")}),(0,s.jsx)("input",{type:"text",value:L.full_name,onChange:e=>D(r=>({...r,full_name:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("enter_full_name"),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("email_required")}),(0,s.jsx)("input",{type:"email",value:L.email,onChange:e=>D(r=>({...r,email:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("enter_email"),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("password_required")}),(0,s.jsx)("input",{type:"password",value:L.password,onChange:e=>D(r=>({...r,password:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("enter_password"),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("phone_required")}),(0,s.jsx)("input",{type:"tel",value:L.phone,onChange:e=>D(r=>({...r,phone:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("enter_phone"),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("specialty_required")}),(0,s.jsx)("input",{type:"text",value:L.specialty,onChange:e=>D(r=>({...r,specialty:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("specialty_example"),required:!0})]}),(0,s.jsxs)("div",{className:"md:col-span-2 flex gap-4 pt-4",children:[(0,s.jsx)("button",{type:"submit",disabled:M,className:"btn-primary py-3 px-6 disabled:opacity-50 disabled:cursor-not-allowed",children:M?_("adding"):_("add_worker")}),(0,s.jsx)("button",{type:"button",onClick:()=>q(!1),className:"btn-secondary py-3 px-6",children:_("cancel")})]})]})]}),W&&P&&(0,s.jsxs)(l.P.div,{initial:{opacity:0},animate:{opacity:1},className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm",onClick:()=>z(!1)}),(0,s.jsxs)(l.P.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},className:"relative bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,s.jsx)("div",{className:"sticky top-0 bg-white border-b border-gray-200 p-6 rounded-t-2xl",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:_("edit_worker")}),(0,s.jsx)("button",{onClick:()=>z(!1),className:"text-gray-400 hover:text-gray-600",children:(0,s.jsx)(h.A,{className:"w-6 h-6"})})]})}),(0,s.jsxs)("form",{onSubmit:Z,className:"p-6 space-y-4",children:[(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("full_name_required")}),(0,s.jsx)("input",{type:"text",value:P.full_name,onChange:e=>J(r=>({...r,full_name:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("email_required")}),(0,s.jsx)("input",{type:"email",value:P.email,onChange:e=>J(r=>({...r,email:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("new_password")}),(0,s.jsx)("input",{type:"password",value:P.password,onChange:e=>J(r=>({...r,password:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",placeholder:_("leave_empty_no_change")})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("phone_required")}),(0,s.jsx)("input",{type:"tel",value:P.phone,onChange:e=>J(r=>({...r,phone:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("specialty_required")}),(0,s.jsx)("input",{type:"text",value:P.specialty,onChange:e=>J(r=>({...r,specialty:e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:_("status")}),(0,s.jsxs)("select",{value:P.is_active?"active":"inactive",onChange:e=>J(r=>({...r,is_active:"active"===e.target.value})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent",children:[(0,s.jsx)("option",{value:"active",children:_("active")}),(0,s.jsx)("option",{value:"inactive",children:_("inactive")})]})]})]}),(0,s.jsxs)("div",{className:"flex gap-4 pt-4",children:[(0,s.jsx)("button",{type:"submit",disabled:M,className:"btn-primary py-3 px-6 disabled:opacity-50 disabled:cursor-not-allowed",children:M?_("saving"):_("save_changes")}),(0,s.jsx)("button",{type:"button",onClick:()=>z(!1),className:"btn-secondary py-3 px-6",children:_("cancel")})]})]})]})]}),(0,s.jsx)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"grid lg:grid-cols-2 gap-6 mb-12",children:0===Q.length?(0,s.jsxs)("div",{className:"lg:col-span-2 text-center py-12 bg-white/80 backdrop-blur-sm rounded-2xl border border-pink-100",children:[(0,s.jsx)(y.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-medium text-gray-600 mb-2",children:_("no_workers")}),(0,s.jsx)("p",{className:"text-gray-500",children:_("no_workers_found")})]}):Q.map((e,r)=>(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*r},className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 border transition-all duration-300 hover:shadow-lg ".concat(e.is_active?"border-pink-100":"border-gray-200 opacity-75"),children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full flex items-center justify-center",children:(0,s.jsx)(b.A,{className:"w-6 h-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-gray-800",children:e.full_name}),(0,s.jsx)("p",{className:"text-sm text-pink-600 font-medium",children:e.specialty})]})]}),(0,s.jsx)("div",{className:"flex items-center space-x-2 space-x-reverse",children:e.is_active?(0,s.jsxs)("span",{className:"px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium flex items-center space-x-1 space-x-reverse",children:[(0,s.jsx)(g.A,{className:"w-3 h-3"}),(0,s.jsx)("span",{children:_("active")})]}):(0,s.jsxs)("span",{className:"px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium flex items-center space-x-1 space-x-reverse",children:[(0,s.jsx)(h.A,{className:"w-3 h-3"}),(0,s.jsx)("span",{children:_("inactive")})]})})]}),(0,s.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse text-sm text-gray-600",children:[(0,s.jsx)(f.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.email})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse text-sm text-gray-600",children:[(0,s.jsx)(v.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.phone})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 gap-4 mb-6",children:(0,s.jsxs)("div",{className:"text-center p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg",children:[(0,s.jsx)("div",{className:"text-lg font-bold text-green-600",children:G(e.id)}),(0,s.jsx)("div",{className:"text-xs text-gray-600",children:_("completed_orders")})]})}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 mb-4",children:[_("joined_on")," ",K(e.createdAt)]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("button",{onClick:()=>U(e),className:"flex-1 btn-secondary py-2 text-sm inline-flex items-center justify-center space-x-1 space-x-reverse",children:[(0,s.jsx)(w.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:_("edit")})]}),(0,s.jsx)("button",{onClick:()=>F(e.id,e.is_active),className:"px-3 py-2 text-sm rounded-lg transition-all duration-300 ".concat(e.is_active?"bg-red-100 text-red-700 hover:bg-red-200":"bg-green-100 text-green-700 hover:bg-green-200"),children:e.is_active?(0,s.jsx)(h.A,{className:"w-4 h-4"}):(0,s.jsx)(g.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{onClick:()=>B(e.id),className:"px-3 py-2 text-red-600 hover:text-red-700 border border-red-200 rounded-lg hover:bg-red-50 transition-all duration-300",children:(0,s.jsx)(j.A,{className:"w-4 h-4"})})]})]},e.id))}),(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"grid grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:r.length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:_("total_workers")})]}),(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-1",children:r.filter(e=>e.is_active).length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:_("active_workers")})]}),(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-purple-600 mb-1",children:r.reduce((e,r)=>e+G(r.id),0)}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:_("total_completed_orders")})]}),(0,s.jsxs)("div",{className:"text-center p-6 bg-white/80 backdrop-blur-sm rounded-xl border border-pink-100",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-600 mb-1",children:r.filter(e=>e.is_active).length}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:_("active_workers")})]})]})]})}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-pink-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:_("checking_permissions")})]})})}},4861:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5977:(e,r,t)=>{Promise.resolve().then(t.bind(t,4535))},7580:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7924:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8883:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9420:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[165,874,69,518,441,684,358],()=>r(5977)),_N_E=e.O()}]);