"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/orders/page",{

/***/ "(app-pages-browser)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCurrentUserId: () => (/* binding */ getCurrentUserId),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://lgwujnpxvrcryebhjdtc.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imxnd3VqbnB4dnJjcnllYmhqZHRjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNzEyNjQsImV4cCI6MjA2NzY0NzI2NH0.-VbkK1R4AXWS6UqzAYL_nKx4yNQ7A-EmF8Z0O24eOWg\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// دالة للحصول على معرف المستخدم الحالي\nconst getCurrentUserId = async ()=>{\n    try {\n        // محاولة الحصول من Supabase أولاً\n        const { data: { user }, error } = await supabase.auth.getUser();\n        if (user && !error) {\n            return user.id;\n        }\n        // إذا لم يكن هناك مستخدم في Supabase، نتحقق من localStorage\n        if (true) {\n            const savedUser = localStorage.getItem('yasmin-auth-user');\n            if (savedUser) {\n                const user = JSON.parse(savedUser);\n                return user.id || null;\n            }\n        }\n        return null;\n    } catch (error) {\n        console.error('خطأ في الحصول على معرف المستخدم:', error);\n        return null;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/store/dataStore.ts":
/*!********************************!*\
  !*** ./src/store/dataStore.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDataStore: () => (/* binding */ useDataStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/database */ \"(app-pages-browser)/./src/lib/database.ts\");\n\n\n\n// دالة مساعدة لتحويل الأخطاء\nconst handleError = (error)=>{\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        return error.message;\n    }\n    if (typeof error === 'string') {\n        return error;\n    }\n    return 'خطأ غير معروف';\n};\n// دالة مساعدة لمعالجة الأخطاء في المتجر\nconst setError = (set, error)=>{\n    set({\n        error: handleError(error),\n        isLoading: false\n    });\n};\n// دالة تحويل من قاعدة البيانات إلى الواجهة المحلية\nconst mapDBAppointmentToLocal = (dbAppointment)=>({\n        id: dbAppointment.id,\n        clientName: dbAppointment.client_name,\n        clientPhone: dbAppointment.client_phone,\n        appointmentDate: dbAppointment.appointment_date,\n        appointmentTime: dbAppointment.appointment_time,\n        notes: dbAppointment.notes || undefined,\n        status: dbAppointment.status,\n        createdAt: dbAppointment.created_at,\n        updatedAt: dbAppointment.updated_at\n    });\nconst mapLocalAppointmentToDB = (localAppointment)=>({\n        client_name: localAppointment.clientName,\n        client_phone: localAppointment.clientPhone,\n        appointment_date: localAppointment.appointmentDate,\n        appointment_time: localAppointment.appointmentTime,\n        status: localAppointment.status,\n        notes: localAppointment.notes\n    });\nconst mapDBOrderToLocal = (dbOrder)=>({\n        id: dbOrder.id,\n        clientName: dbOrder.client_name,\n        clientPhone: dbOrder.client_phone,\n        description: dbOrder.description,\n        fabric: dbOrder.fabric || '',\n        measurements: dbOrder.measurements || {},\n        price: dbOrder.price,\n        status: dbOrder.status,\n        assignedWorker: dbOrder.assigned_worker_id || undefined,\n        dueDate: dbOrder.due_date,\n        notes: dbOrder.notes || undefined,\n        voiceNotes: dbOrder.voice_notes || undefined,\n        images: dbOrder.images || undefined,\n        completedImages: dbOrder.completed_images || undefined,\n        createdAt: dbOrder.created_at,\n        updatedAt: dbOrder.updated_at\n    });\nconst mapLocalOrderToDB = (localOrder)=>({\n        client_name: localOrder.clientName,\n        client_phone: localOrder.clientPhone,\n        description: localOrder.description,\n        fabric: localOrder.fabric,\n        measurements: localOrder.measurements,\n        price: localOrder.price,\n        status: localOrder.status,\n        assigned_worker_id: localOrder.assignedWorker,\n        due_date: localOrder.dueDate,\n        notes: localOrder.notes,\n        voice_notes: localOrder.voiceNotes,\n        images: localOrder.images,\n        completed_images: localOrder.completedImages\n    });\nconst mapDBWorkerToLocal = (dbWorker)=>({\n        id: dbWorker.id,\n        email: dbWorker.email,\n        password: '',\n        full_name: dbWorker.full_name,\n        phone: dbWorker.phone || '',\n        specialty: dbWorker.specialty || '',\n        role: 'worker',\n        is_active: dbWorker.is_active,\n        createdAt: dbWorker.created_at,\n        updatedAt: dbWorker.updated_at\n    });\nconst mapLocalWorkerToDB = (localWorker)=>({\n        user_id: '',\n        email: localWorker.email,\n        full_name: localWorker.full_name,\n        phone: localWorker.phone,\n        specialty: localWorker.specialty,\n        role: 'worker',\n        is_active: localWorker.is_active,\n        experience_years: undefined,\n        hourly_rate: undefined,\n        performance_rating: undefined,\n        total_completed_orders: undefined\n    });\nconst useDataStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.persist)((set, get)=>({\n        // البيانات الأولية\n        appointments: [],\n        orders: [],\n        workers: [],\n        isLoading: false,\n        error: null,\n        // إدارة المواعيد\n        addAppointment: async (appointmentData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const dbAppointment = mapLocalAppointmentToDB(appointmentData);\n                const { appointment, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.appointmentService.createAppointment(dbAppointment);\n                if (error) {\n                    set({\n                        error: handleError(error),\n                        isLoading: false\n                    });\n                    return;\n                }\n                if (appointment) {\n                    const localAppointment = mapDBAppointmentToLocal(appointment);\n                    set((state)=>({\n                            appointments: [\n                                ...state.appointments,\n                                localAppointment\n                            ],\n                            error: null,\n                            isLoading: false\n                        }));\n                    console.log('✅ تم إضافة موعد جديد:', localAppointment);\n                }\n            } catch (error) {\n                set({\n                    error: 'خطأ في إضافة الموعد',\n                    isLoading: false\n                });\n                console.error('خطأ في إضافة الموعد:', error);\n            }\n        },\n        updateAppointment: async (id, updates)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const dbUpdates = {};\n                if (updates.clientName) dbUpdates.client_name = updates.clientName;\n                if (updates.clientPhone) dbUpdates.client_phone = updates.clientPhone;\n                if (updates.appointmentDate) dbUpdates.appointment_date = updates.appointmentDate;\n                if (updates.appointmentTime) dbUpdates.appointment_time = updates.appointmentTime;\n                if (updates.status) dbUpdates.status = updates.status;\n                if (updates.notes !== undefined) dbUpdates.notes = updates.notes;\n                const { appointment, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.appointmentService.updateAppointment(id, dbUpdates);\n                if (error) {\n                    set({\n                        error: handleError(error),\n                        isLoading: false\n                    });\n                    return;\n                }\n                if (appointment) {\n                    const localAppointment = mapDBAppointmentToLocal(appointment);\n                    set((state)=>({\n                            appointments: state.appointments.map((apt)=>apt.id === id ? localAppointment : apt),\n                            error: null,\n                            isLoading: false\n                        }));\n                    console.log('✅ تم تحديث الموعد:', id);\n                }\n            } catch (error) {\n                set({\n                    error: 'خطأ في تحديث الموعد',\n                    isLoading: false\n                });\n                console.error('خطأ في تحديث الموعد:', error);\n            }\n        },\n        deleteAppointment: async (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.appointmentService.deleteAppointment(id);\n                if (error) {\n                    setError(set, error);\n                    return;\n                }\n                set((state)=>({\n                        appointments: state.appointments.filter((appointment)=>appointment.id !== id),\n                        error: null,\n                        isLoading: false\n                    }));\n                console.log('✅ تم حذف الموعد:', id);\n            } catch (error) {\n                set({\n                    error: 'خطأ في حذف الموعد',\n                    isLoading: false\n                });\n                console.error('خطأ في حذف الموعد:', error);\n            }\n        },\n        getAppointment: (id)=>{\n            const state = get();\n            return state.appointments.find((appointment)=>appointment.id === id);\n        },\n        loadAppointments: async ()=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { appointments, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.appointmentService.getAllAppointments();\n                if (error) {\n                    setError(set, error);\n                    return;\n                }\n                const localAppointments = (appointments === null || appointments === void 0 ? void 0 : appointments.map(mapDBAppointmentToLocal)) || [];\n                set({\n                    appointments: localAppointments,\n                    error: null,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: 'خطأ في تحميل المواعيد',\n                    isLoading: false\n                });\n                console.error('خطأ في تحميل المواعيد:', error);\n            }\n        },\n        // إدارة الطلبات\n        addOrder: async (orderData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const dbOrder = mapLocalOrderToDB(orderData);\n                const { order, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.orderService.createOrder(dbOrder);\n                if (error) {\n                    setError(set, error);\n                    return;\n                }\n                if (order) {\n                    const localOrder = mapDBOrderToLocal(order);\n                    set((state)=>({\n                            orders: [\n                                ...state.orders,\n                                localOrder\n                            ],\n                            error: null,\n                            isLoading: false\n                        }));\n                    console.log('✅ تم إضافة طلب جديد:', localOrder);\n                }\n            } catch (error) {\n                set({\n                    error: 'خطأ في إضافة الطلب',\n                    isLoading: false\n                });\n                console.error('خطأ في إضافة الطلب:', error);\n            }\n        },\n        updateOrder: async (id, updates)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const dbUpdates = {};\n                if (updates.clientName) dbUpdates.client_name = updates.clientName;\n                if (updates.clientPhone) dbUpdates.client_phone = updates.clientPhone;\n                if (updates.description) dbUpdates.description = updates.description;\n                if (updates.fabric) dbUpdates.fabric = updates.fabric;\n                if (updates.measurements) dbUpdates.measurements = updates.measurements;\n                if (updates.price) dbUpdates.price = updates.price;\n                if (updates.status) dbUpdates.status = updates.status;\n                if (updates.assignedWorker) dbUpdates.assigned_worker_id = updates.assignedWorker;\n                if (updates.dueDate) dbUpdates.due_date = updates.dueDate;\n                if (updates.notes !== undefined) dbUpdates.notes = updates.notes;\n                if (updates.voiceNotes) dbUpdates.voice_notes = updates.voiceNotes;\n                if (updates.images) dbUpdates.images = updates.images;\n                if (updates.completedImages) dbUpdates.completed_images = updates.completedImages;\n                const { order, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.orderService.updateOrder(id, dbUpdates);\n                if (error) {\n                    setError(set, error);\n                    return;\n                }\n                if (order) {\n                    const localOrder = mapDBOrderToLocal(order);\n                    set((state)=>({\n                            orders: state.orders.map((ord)=>ord.id === id ? localOrder : ord),\n                            error: null,\n                            isLoading: false\n                        }));\n                    console.log('✅ تم تحديث الطلب:', id);\n                }\n            } catch (error) {\n                set({\n                    error: 'خطأ في تحديث الطلب',\n                    isLoading: false\n                });\n                console.error('خطأ في تحديث الطلب:', error);\n            }\n        },\n        deleteOrder: async (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.orderService.deleteOrder(id);\n                if (error) {\n                    setError(set, error);\n                    return;\n                }\n                set((state)=>({\n                        orders: state.orders.filter((order)=>order.id !== id),\n                        error: null,\n                        isLoading: false\n                    }));\n                console.log('✅ تم حذف الطلب:', id);\n            } catch (error) {\n                set({\n                    error: 'خطأ في حذف الطلب',\n                    isLoading: false\n                });\n                console.error('خطأ في حذف الطلب:', error);\n            }\n        },\n        getOrder: (id)=>{\n            const state = get();\n            return state.orders.find((order)=>order.id === id);\n        },\n        loadOrders: async ()=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { orders, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.orderService.getAllOrders();\n                if (error) {\n                    set({\n                        error: handleError(error),\n                        isLoading: false\n                    });\n                    return;\n                }\n                const localOrders = (orders === null || orders === void 0 ? void 0 : orders.map(mapDBOrderToLocal)) || [];\n                set({\n                    orders: localOrders,\n                    error: null,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: 'خطأ في تحميل الطلبات',\n                    isLoading: false\n                });\n                console.error('خطأ في تحميل الطلبات:', error);\n            }\n        },\n        // دوال خاصة للعمال\n        startOrderWork: async (orderId, workerId)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { order, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.orderService.updateOrder(orderId, {\n                    status: 'in_progress',\n                    assigned_worker_id: workerId\n                });\n                if (error) {\n                    set({\n                        error: handleError(error),\n                        isLoading: false\n                    });\n                    return;\n                }\n                if (order) {\n                    const localOrder = mapDBOrderToLocal(order);\n                    set((state)=>({\n                            orders: state.orders.map((ord)=>ord.id === orderId ? localOrder : ord),\n                            error: null,\n                            isLoading: false\n                        }));\n                    console.log('✅ تم بدء العمل على الطلب:', orderId);\n                }\n            } catch (error) {\n                set({\n                    error: 'خطأ في بدء العمل على الطلب',\n                    isLoading: false\n                });\n                console.error('خطأ في بدء العمل على الطلب:', error);\n            }\n        },\n        completeOrder: async (orderId, workerId, completedImages)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { order, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.orderService.updateOrder(orderId, {\n                    status: 'completed',\n                    completed_images: completedImages\n                });\n                if (error) {\n                    set({\n                        error: handleError(error),\n                        isLoading: false\n                    });\n                    return;\n                }\n                if (order) {\n                    const localOrder = mapDBOrderToLocal(order);\n                    set((state)=>({\n                            orders: state.orders.map((ord)=>ord.id === orderId ? localOrder : ord),\n                            error: null,\n                            isLoading: false\n                        }));\n                    console.log('✅ تم إكمال الطلب:', orderId);\n                }\n            } catch (error) {\n                set({\n                    error: 'خطأ في إكمال الطلب',\n                    isLoading: false\n                });\n                console.error('خطأ في إكمال الطلب:', error);\n            }\n        },\n        // إدارة العمال\n        addWorker: async (workerData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const dbWorker = mapLocalWorkerToDB(workerData);\n                const { worker, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.workerService.createWorker(dbWorker);\n                if (error) {\n                    set({\n                        error: handleError(error),\n                        isLoading: false\n                    });\n                    return;\n                }\n                if (worker) {\n                    const localWorker = mapDBWorkerToLocal(worker);\n                    set((state)=>({\n                            workers: [\n                                ...state.workers,\n                                localWorker\n                            ],\n                            error: null,\n                            isLoading: false\n                        }));\n                    console.log('✅ تم إضافة عامل جديد:', localWorker);\n                }\n            } catch (error) {\n                set({\n                    error: 'خطأ في إضافة العامل',\n                    isLoading: false\n                });\n                console.error('خطأ في إضافة العامل:', error);\n            }\n        },\n        updateWorker: async (id, updates)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const dbUpdates = {};\n                if (updates.email) dbUpdates.email = updates.email;\n                if (updates.full_name) dbUpdates.full_name = updates.full_name;\n                if (updates.phone) dbUpdates.phone = updates.phone;\n                if (updates.specialty) dbUpdates.specialty = updates.specialty;\n                if (updates.is_active !== undefined) dbUpdates.is_active = updates.is_active;\n                const { worker, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.workerService.updateWorker(id, dbUpdates);\n                if (error) {\n                    set({\n                        error: handleError(error),\n                        isLoading: false\n                    });\n                    return;\n                }\n                if (worker) {\n                    const localWorker = mapDBWorkerToLocal(worker);\n                    set((state)=>({\n                            workers: state.workers.map((wrk)=>wrk.id === id ? localWorker : wrk),\n                            error: null,\n                            isLoading: false\n                        }));\n                    console.log('✅ تم تحديث العامل:', id);\n                }\n            } catch (error) {\n                set({\n                    error: 'خطأ في تحديث العامل',\n                    isLoading: false\n                });\n                console.error('خطأ في تحديث العامل:', error);\n            }\n        },\n        deleteWorker: async (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.workerService.deleteWorker(id);\n                if (error) {\n                    set({\n                        error: handleError(error),\n                        isLoading: false\n                    });\n                    return;\n                }\n                set((state)=>({\n                        workers: state.workers.filter((worker)=>worker.id !== id),\n                        error: null,\n                        isLoading: false\n                    }));\n                console.log('✅ تم حذف العامل:', id);\n            } catch (error) {\n                set({\n                    error: 'خطأ في حذف العامل',\n                    isLoading: false\n                });\n                console.error('خطأ في حذف العامل:', error);\n            }\n        },\n        getWorker: (id)=>{\n            const state = get();\n            return state.workers.find((worker)=>worker.id === id);\n        },\n        loadWorkers: async ()=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { workers, error } = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.workerService.getAllWorkers();\n                if (error) {\n                    set({\n                        error: handleError(error),\n                        isLoading: false\n                    });\n                    return;\n                }\n                const localWorkers = (workers === null || workers === void 0 ? void 0 : workers.map(mapDBWorkerToLocal)) || [];\n                set({\n                    workers: localWorkers,\n                    error: null,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: 'خطأ في تحميل العمال',\n                    isLoading: false\n                });\n                console.error('خطأ في تحميل العمال:', error);\n            }\n        },\n        // وظائف مساعدة\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        loadData: async ()=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                await Promise.all([\n                    get().loadAppointments(),\n                    get().loadOrders(),\n                    get().loadWorkers()\n                ]);\n                set({\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: 'خطأ في تحميل البيانات',\n                    isLoading: false\n                });\n                console.error('خطأ في تحميل البيانات:', error);\n            }\n        },\n        // إحصائيات\n        getStats: ()=>{\n            const state = get();\n            const totalAppointments = state.appointments.length;\n            const totalOrders = state.orders.length;\n            const totalWorkers = state.workers.length;\n            const pendingAppointments = state.appointments.filter((apt)=>apt.status === 'pending').length;\n            const activeOrders = state.orders.filter((order)=>order.status === 'in_progress').length;\n            const completedOrders = state.orders.filter((order)=>order.status === 'completed').length;\n            const totalRevenue = state.orders.filter((order)=>order.status === 'completed' || order.status === 'delivered').reduce((sum, order)=>sum + order.price, 0);\n            return {\n                totalAppointments,\n                totalOrders,\n                totalWorkers,\n                pendingAppointments,\n                activeOrders,\n                completedOrders,\n                totalRevenue\n            };\n        }\n    }), {\n    name: 'yasmin-alsham-data',\n    partialize: (state)=>({\n            appointments: state.appointments,\n            orders: state.orders,\n            workers: state.workers\n        })\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/dataStore.ts\n"));

/***/ })

});